.button_box {
  padding: 6px 0 0 0;
  border-bottom: 1px solid @color_line2;
}

.form_box {
  padding: 3px 0 3px 0;
}

.table_box {
  //height: calc(100vh - 300px);
  // padding: 10px 0;
  :global .art-table-body {
    min-height: calc(100vh - 250px);
  }
}
.table_fold_box {
  :global .art-table-body {
    min-height: calc(100vh - 200px);
  }
}
.form_header_box {
  box-sizing: border-box;
  padding: 3px 0;
  :global .ant-form-inline {
    min-width: 1400px;
  }
}
.box {
  :global .xlb-ant-form-item {
    .xlb-ant-form-item-row .xlb-ant-form-item-label {
      flex: 0 1 auto;
      width: 120px !important;
    }
    &.select-with-button .xlb-ant-form-item-control-input-content {
      display: flex;
    }
  }
  :global .select-with-checkbox {
    align-items: baseline;
    .xlb-ant-checkbox-wrapper {
      align-items: center;
      width: 120px;
    }
    .xlb-ant-form-item {
      margin-bottom: 0 !important;
    }
  }
  :global .ant-radio-wrapper {
    line-height: 30px;
  }
  :global .ant-input-affix-wrapper > .ant-input {
    // height: 26px !important;
  }

  :global .ant-select {
    display: inline-block;
  }
  :global .ant-input-number-wrapper {
    height: 28px;
  }
  :global {
    .ml-16 {
      margin-left: 16px;
    }
    .w {
      width: 100%;
    }
  }

  .radio1,
  .radio2 {
    display: flex;
    flex: 1 1 0%;
    white-space: nowrap;
  }
  .radio1 > span:nth-child(2) {
    display: flex;
    flex: 1;
    align-items: baseline;
    padding-left: 2px;
  }
  .radio2 > span:nth-child(2) {
    flex: 1;
    padding-left: 16px;
  }
}
.modal_table_box {
  height: 460px;
  :global .xlb-table-root {
    height: 445px !important;
    overflow-y: auto;
  }
}

.add-modal-goods {
  :global(.ant-modal-footer) {
    border-top: 1px solid #e5e6ea;
  }
  :global(.ant-modal-body) {
    border-top: 1px solid #e5e6ea;
  }
}
.batch-opt {
  :global(.ant-modal-header) {
    border-bottom: 1px solid #e5e6ea;
  }
  :global(.ant-modal-footer) {
    border-top: 1px solid #e5e6ea;
  }
}

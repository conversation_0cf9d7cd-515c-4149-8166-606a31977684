import { getAllStoreList } from '@/api/common';
import { MAX_INT } from '@/constants';
import NiceModal from '@ebay/nice-modal-react';
import {
  XlbBasicForm,
  XlbInputDialog,
  XlbModal,
  XlbSelect,
} from '@xlb/components';
import { message } from 'antd';
import { useEffect, useState } from 'react';
import {
  saveWavePickingConfig,
  saveWavePickingDetailConfig,
  updateWavePickingConfig,
  updateWavePickingDetailConfig,
} from '../server';

interface IProps {
  title: string;
  type?: string;
  minTime?: number;
  maxTime?: number;
  updateData?: any;
  config_id?: any;
  fetchData: () => void;
}
export const AddItemModal = NiceModal.create((props: IProps) => {
  const {
    title,
    type = 'store',
    minTime = 1,
    maxTime = 24,
    fetchData,
    config_id,
    updateData,
  } = props;
  const { visible, hide } = NiceModal.useModal();

  // init
  const [form] = XlbBasicForm.useForm();
  interface OptionType {
    label: string;
    value: number;
  }
  // 门店筛选
  const [storeList, setStoreList] = useState<OptionType[]>([]);
  const getStoreList = async () => {
    const res = await getAllStoreList({
      center_flag: true,
      page_size: MAX_INT,
    });
    if (res.code === 0) {
      const formatList =
        res.data?.content?.map((item: any) => ({
          label: item.store_name,
          value: item.id,
        })) || [];
      setStoreList(formatList);
    }
  };
  // 波次筛选
  const [timeList, setTimeList] = useState<OptionType[]>([]);
  const getTimeList = () => {
    const arr = [];
    for (let i = minTime; i <= maxTime; i++) {
      arr.push({
        label: `${i}`,
        value: i,
      });
    }
    setTimeList(arr);
  };

  useEffect(() => {
    if (visible) {
      if (type === 'store') {
        getStoreList();
      }

      getTimeList();
      if (updateData) {
        form.setFieldsValue({
          times: updateData.config.times,
          store_id: type === 'store' ? updateData.store_id : null,
          supplier_id: type === 'supplier' ? updateData.supplier_id : null,
        });
      }
    } else {
      form.resetFields();
    }
  }, [visible]);

  // confrim
  const [confirmLoading, setConfirmLoading] = useState(false);
  const onOk = async () => {
    await form.validateFields();
    setConfirmLoading(true);
    const { store_id, supplier_id, times } = form.getFieldsValue();
    const res = updateData
      ? type === 'store'
        ? await updateWavePickingConfig({
            ...updateData,
            store_id,
            config: { times },
          })
        : await updateWavePickingDetailConfig({
            ...updateData,
            supplier_id: supplier_id?.[0],
            config: { times },
            config_id: config_id,
          })
      : type === 'store'
        ? await saveWavePickingConfig({ store_id, config: { times } })
        : await saveWavePickingDetailConfig({
            config_id: config_id,
            supplier_id: supplier_id?.[0],
            config: { times },
          });
    if (res?.code === 0) {
      message.success(updateData ? '更新成功' : '添加成功');
      hide();
      fetchData();
    }
    setConfirmLoading(false);
  };

  return (
    <XlbModal
      title={title}
      keyboard={false}
      centered
      open={visible}
      maskClosable={false}
      confirmLoading={confirmLoading}
      isCancel
      onOk={onOk}
      onCancel={hide}
      width={450}
    >
      <XlbBasicForm
        form={form}
        layout="horizontal"
        style={{ padding: '20px 0' }}
      >
        {type === 'store' && (
          <XlbBasicForm.Item
            label="收货门店"
            name="store_id"
            rules={[{ message: '请选择收货门店！', required: true }]}
          >
            <XlbSelect
              showSearch
              disabled={updateData}
              allowClear
              options={storeList}
              filterOption={(input, option) =>
                `${option?.label ? option.label.toString() : ''}`
                  .toLowerCase()
                  .includes(input.toLowerCase())
              }
            />
          </XlbBasicForm.Item>
        )}
        {type === 'supplier' && (
          <XlbBasicForm.Item
            label="供应商"
            name="supplier_id"
            rules={[{ message: '请选择收货供应商！', required: true }]}
          >
            <XlbInputDialog
              disabled={updateData}
              dialogParams={{
                type: 'supplier',
                dataType: 'lists',
                isMultiple: false,

                // data: {
                //   center_flag: true,
                // },
              }}
              width={240}
            />
          </XlbBasicForm.Item>
        )}
        <XlbBasicForm.Item
          label="波次配置"
          name="times"
          rules={[{ message: '请选择波次配置！', required: true }]}
        >
          <XlbSelect showSearch allowClear mode="multiple" options={timeList} />
        </XlbBasicForm.Item>
      </XlbBasicForm>
    </XlbModal>
  );
});

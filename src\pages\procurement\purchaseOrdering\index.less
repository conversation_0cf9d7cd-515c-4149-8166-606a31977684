.tableGroupWrap {
  height: calc(100vh - 78px);
  padding: 0 20px 20px 0;
  :global {
    .toolBtn-noStyle {
      flex: 1;
    }
  }
}

.formWrapCus {
  :global {
    .ant-row {
      align-items: center;
    }
  }
}
.formWrapCus {
  :global {
    .ant-form-item:last-child .ant-form-item-label {
      width: 48px;
    }
  }
}
.myDropDown {
  // width:268px !important;
  overflow: inherit;
  :global {
    .rc-virtual-list {
      // overflow: unset !important;
      width: 264px !important;
      background-color: #fff;
    }
  }
}
.show-active {
  background: #00b341;
  color: #ffffff;
  line-height: 14px;
  font-size: 11px;
  font-weight: normal;
  padding: 1px 2px;
  border-radius: 2px;
  margin-right: 4px;
  user-select: none;
}

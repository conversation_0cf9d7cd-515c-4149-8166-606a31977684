.xlb-sub-container {
  display: flex;
  height: calc(100%);

  .xlb_main {
    overflow: auto;

    :global .ka-wrapper {
      height: 100%;

      .ka-content {
        height: 100%;
        :global {
          .xlbOldPageContianer {
            padding-top: 12px !important;
          }
          .xlb-pageContainer-toolBtn {
            margin-top: 0 !important;
          }
          .withHr {
            padding-top: 0 !important;
          }
          .xlb-pro-page-container-with-memo {
            .withHr {
              padding-top: 12px !important;
            }
            .xlbOldPageContianer {
              padding-top: 0 !important;
            }
          }
        }
      }
    }
  }
}
.erp-is-wujie {
  padding: 12px;
  background-color: #f3f4f7;
  .xlb_main {
    background: #fff;
    border-radius: 4px;
    overflow: hidden;
    :global {
      .xlbOldPageContianer {
        padding-top: 12px !important;
      }
      .xlb-pageContainer-toolBtn {
        margin-top: 0 !important;
      }
      .withHr {
        padding-top: 0 !important;
      }
      .xlb-pro-page-container-with-memo {
        .withHr {
          padding-top: 12px !important;
        }
        .xlbOldPageContianer {
          padding-top: 0 !important;
        }
      }
    }
  }
}
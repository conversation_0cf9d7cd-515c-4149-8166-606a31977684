import { hasAuth } from '@/utils';
import {
  XlbBasicForm,
  XlbButton,
  XlbForm,
  XlbIcon,
  XlbImportModal,
  XlbMessage,
  XlbPageContainer,
  XlbTipsModal,
} from '@xlb/components';
import { XlbPageContainerRef } from '@xlb/components/dist/lowcodes/XlbPageContainer';
import { message } from 'antd';
import { useRef } from 'react';
import { editFormList, formList, tableList } from './data';
import {
  addStoreSupplierConfig,
  deleteStoreSupplierConfig,
  updateStoreSupplierConfig,
} from './server';

const { Table, ToolBtn, SearchForm } = XlbPageContainer;

const StorehouseParamsIndex = () => {
  const [form] = XlbBasicForm.useForm<any>();
  const [formEdit] = XlbBasicForm.useForm<any>();
  const pageRef = useRef<XlbPageContainerRef>(null);

  const prevPost = () => {
    const data = form.getFieldsValue(true);
    return { ...data };
  };

  //   编辑
  const editItem = async (selectRow: any, fetchData: any, isAdd: boolean) => {
    const i_curent = selectRow?.[0];

    if (!isAdd) {
      formEdit.setFieldsValue({
        ...i_curent,
        purchase_frequency:
          i_curent?.frequency_type === 'ODD_EVEN'
            ? i_curent?.purchase_frequency
            : JSON.parse(i_curent?.purchase_frequency),
      });
    } else {
      formEdit.setFieldsValue({
        is_frequency_strict: 0,
        reservation_whitelist: 0,
        order_whitelist: 0,
      });
    }
    XlbTipsModal({
      title: isAdd ? '新增' : '编辑',
      isCancel: true,
      bordered: true,
      tips: (
        <div
          style={{
            padding: 20,
          }}
        >
          <XlbForm
            form={formEdit}
            isHideDate={true}
            formList={editFormList(isAdd)}
          />
        </div>
      ),
      onOkBeforeFunction: async () => {
        try {
          await formEdit.validateFields();
        } catch (err: any) {
          return false;
        }

        const params = { ...formEdit.getFieldsValue(true) };
        const data = {
          ...params,
          supplier_id: params?.supplier_id?.[0],
          store_id: params?.store_id?.[0],
          purchase_frequency:
            params?.frequency_type === 'ODD_EVEN'
              ? params?.purchase_frequency
              : JSON.stringify(params.purchase_frequency),
        };
        console.log('-----:', data);
        const res = isAdd
          ? await addStoreSupplierConfig(data)
          : await updateStoreSupplierConfig(data);
        if (res?.code === 0) {
          XlbMessage.success('操作成功');
          formEdit.resetFields();
          pageRef?.current?.fetchData();
          return true;
        }
      },
    });
  };

  // 删除
  const deleteItem = async (
    fetchData: any,
    rowData?: any[],
    selectRowKeys?: string[],
  ) => {
    const sItem = rowData?.find((v) => v?.id === selectRowKeys?.[0]);
    XlbTipsModal({
      tips: `是否确定删除?`,
      isCancel: true,
      onOkBeforeFunction: async () => {
        const data = { ids: selectRowKeys };
        const res = await deleteStoreSupplierConfig(data);
        if (res.code == 0) {
          message.success('操作成功');
          fetchData();
        }
        return true;
      },
    });
  };

  // 导入
  const importItem = async (fetchData: any) => {
    const res = await XlbImportModal({
      templateUrl: `${process.env.ERP_URL}/erp/hxl.erp.storesupplierconfig.download`,
      importUrl: `${process.env.ERP_URL}/erp/hxl.erp.storesupplierconfig.import`,
      templateName: '下载导入模板',
      callback: (res) => {
        if (res.code !== 0) return;
        fetchData();
      },
    });
  };

  return (
    <XlbPageContainer
      ref={pageRef}
      url={'/erp/hxl.erp.storesupplierconfig.page'}
      tableColumn={tableList}
      prevPost={prevPost}
      immediatePost={true}
    >
      <ToolBtn showColumnsSetting>
        {({ fetchData, selectRowKeys = [], dataSource, selectRow }) => {
          return (
            <XlbButton.Group>
              {hasAuth(['仓供配置', '查询']) && (
                <XlbButton
                  label="查询"
                  type="primary"
                  onClick={() => {
                    fetchData();
                  }}
                  icon={<XlbIcon name="sousuo" />}
                />
              )}
              {hasAuth(['仓供配置', '编辑']) && (
                <XlbButton
                  label="新增"
                  type="primary"
                  onClick={() => {
                    editItem(selectRow, fetchData, true);
                  }}
                  icon={<XlbIcon name="jia" />}
                />
              )}

              {hasAuth(['仓供配置', '编辑']) && (
                <XlbButton
                  label="编辑"
                  type="primary"
                  disabled={!selectRowKeys.length}
                  onClick={() => {
                    editItem(selectRow, fetchData, false);
                  }}
                  icon={<XlbIcon name="shanchu" />}
                />
              )}
              {hasAuth(['仓供配置', '删除']) && (
                <XlbButton
                  style={{ order: 2 }}
                  type="primary"
                  label="删除"
                  disabled={!selectRowKeys || !selectRowKeys?.length}
                  onClick={() =>
                    deleteItem(fetchData, dataSource, selectRowKeys)
                  }
                  icon={<span className="iconfont icon-shanchu" />}
                />
              )}
              {hasAuth(['仓供配置', '导入']) && (
                <XlbButton
                  type="primary"
                  label="导入"
                  onClick={() => {
                    importItem(fetchData);
                  }}
                  icon={<XlbIcon name="daoru" />}
                />
              )}
            </XlbButton.Group>
          );
        }}
      </ToolBtn>
      <SearchForm>
        <XlbForm formList={formList} form={form} isHideDate={true} />
      </SearchForm>
      <Table primaryKey="id" selectMode="single" />
    </XlbPageContainer>
  );
};

export default StorehouseParamsIndex;

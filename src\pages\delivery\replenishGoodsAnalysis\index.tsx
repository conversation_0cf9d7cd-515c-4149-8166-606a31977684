import { hasAuth, LStorage } from '@/utils';
import { useState } from 'react';
import { formList, tableList } from './data';
import { wujieBus } from '@/wujie/utils';
import { useBaseParams } from '@/hooks/useBaseParams';
import {
  XlbBasicForm,
  XlbButton,
  XlbForm,
  XlbIcon,
  XlbMessage,
  XlbPageContainer,
  XlbTipsModal,
} from '@xlb/components';
import dayjs from 'dayjs';
import { getDetail,exportAll } from './server';
const { Table, ToolBtn, SearchForm } = XlbPageContainer;

const Index = () => {
  const [form] = XlbBasicForm.useForm<any>();
  const userInfo = LStorage.get('userInfo');
  const enable_delivery_center =
    userInfo?.store?.enable_delivery_center || false;
  const { enable_organization } = useBaseParams((state) => state);
  const prevPost = () => {
    const { ...rest } = form.getFieldsValue(true);
    return {
      ...rest,
      date: [
        dayjs(rest?.date?.[0])?.format('YYYY-MM-DD'),
        dayjs(rest?.date?.[1])?.format('YYYY-MM-DD'),
      ],
    };
  };

  //导出
  const exportData = async (setLoading: any, e: any) => {
    setLoading(true);
    const { ...rest } = form.getFieldsValue(true);
    const res = await exportAll(
      { ...rest,
        page_number: 0,
        page_size: 200000,
        date: [
          dayjs(rest?.date?.[0])?.format('YYYY-MM-DD'),
          dayjs(rest?.date?.[1])?.format('YYYY-MM-DD'),
        ],
       },
    );
    if (res?.code == 0) {
      wujieBus?.$emit('xlb_erp-event', { code: 'downloadEnd', target: e });
      XlbMessage.success('导出受理成功，请前往下载中心查看');
    }
    setLoading(false);
  };

  const tableRender = (item: any) => {
    switch (item.code) {
      case 'allow_business':
      case 'stop_purchase':
      case 'stop_request':
      case 'center_has_stock':
        item.render = (value: any, record: any, index: number) => {
          return record.index != '合计' ? (
            value === true ? (
              <span>是</span>
            ) : (
              <span>否</span>
            )
          ) : (
            <div> </div>
          );
        };
        return item;
    }
    return item;
  };

  const openItem = async (selectRow: any,setLoading:any,fetchData:any) => {
    // 获取门店数
    const num1 = selectRow?.reduce((acc: any[], curr: any) => {
      if (!acc.some((item) => item.store_id === curr.store_id)) {
        acc.push(curr);
      }
      return acc;
    }, []);
    // 获取商品数
    const num2 = selectRow?.reduce((acc: any[], curr: any) => {
      if (!acc.some((item) => item.item_id === curr.item_id)) {
        acc.push(curr);
      }
      return acc;
    }, []);
    XlbTipsModal({
      title: '生成门店补货单',
      isCancel: true,
      bordered: true,
      tips: (
        <div
          style={{
            padding: 20,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            flexDirection: 'column',
          }}
        >
          <div>
            <span>门店数：{num1?.length}</span>
            <span style={{ marginLeft: 30 }}>商品数：{num2?.length}</span>
          </div>
          <div>是否确认生成门店补货单?</div>
        </div>
      ),
      onOkBeforeFunction: async () => {
        const newList = selectRow.map((v: any) => {
          return {
            item_id: v.item_id,
            store_id: v.store_id,
            quantity: 0,
          };
        });
        setLoading(true);
        const res = await getDetail({
          details: newList,
          memo: '根据补货商品分析生成',
        });
        setLoading(false);
        if (res?.code == 0) {
          XlbMessage.success(`已生成${res?.data?.length}张门店补货单!`);
          fetchData();
          return true;
        }
      },
    });
  };

  return (
    <>
      <XlbPageContainer
        url={'/erp/hxl.erp.deliveryreport.storerequestanalysis.find'}
        tableColumn={tableList
          .filter((v) => {
            if (v.code === 'org_name' && !enable_organization) return false;
            if (v.code === 'center_has_stock' && !enable_delivery_center)
              return false;
            return true;
          })
          .map((v) => tableRender(v))}
        prevPost={prevPost}
        afterPost={(data) => {
          return data?.content?.map((v: any) => {
            v.fidKey = v.store_id + '_' + v.item_id;
            return v;
          });
        }}
        footerDataSource={(data) => {
          const footerData = [
            {
              _index: '合计',
              stock_quantity: data?.stock_quantity || 0,
              basic_stock_quantity: data?.basic_stock_quantity || 0,
              sale_quantity: data?.sale_quantity || 0,
              basic_sale_quantity: data?.basic_sale_quantity || 0,
              request_count: data?.request_count || 0,
              delivery_count: data?.delivery_count || 0,
              out_of_stock_count: data?.out_of_stock_count || 0,
              un_request_day: data?.un_request_day || 0,
              avg_sale_quantity: data?.avg_sale_quantity || 0,
              basic_avg_sale_quantity: data?.basic_avg_sale_quantity || 0,
              valid_days: data?.valid_days || 0,
            },
          ];
          return footerData;
        }}
        immediatePost={false}
      >
        <ToolBtn showColumnsSetting>
          {({
            fetchData,
            dataSource,
            requestForm,
            loading,
            setLoading,
            selectRow,
          }) => {
            return (
              <XlbButton.Group>
                {hasAuth(['补货商品分析', '查询']) && (
                  <XlbButton
                    key="query"
                    label="查询"
                    type="primary"
                    disabled={loading}
                    onClick={() => {
                      fetchData();
                    }}
                    icon={<XlbIcon name="sousuo" />}
                  />
                )}
                {hasAuth(['门店补货单', '编辑']) ? (
                  <XlbButton
                    label="生成门店补货单"
                    type="primary"
                    disabled={loading || selectRow?.length === 0}
                    onClick={() => openItem(selectRow,setLoading,fetchData)}
                    icon={<XlbIcon name="shenqing" />}
                  />
                ) : null}
                {hasAuth(['补货商品分析', '导出']) && (
                  <XlbButton
                    key="exports"
                    label="导出"
                    type="primary"
                    disabled={!dataSource?.length || loading}
                    onClick={(e) => exportData( setLoading, e)}
                    icon={<XlbIcon name="daochu" />}
                  />
                )}
              </XlbButton.Group>
            );
          }}
        </ToolBtn>
        <SearchForm>
          <XlbForm
            formList={formList.filter((v) => {
              if (!enable_delivery_center && v.name === 'center_has_stock')
                return false;
              if (!enable_organization && v.name === 'org_ids') return false;
              return true;
            })}
            form={form}
            initialValues={{
              time_desc: 0,
              unit_type: 'BASIC',
              date: [dayjs(), dayjs()],
              store_names: LStorage.get('userInfo')?.store_name,
              store_ids: [LStorage.get('userInfo')?.store_id],
            }}
            isHideDate={true}
          />
        </SearchForm>
        <Table key="fidKey" selectMode="multiple" keepDataSource={false} primaryKey="fidKey" />
      </XlbPageContainer>
    </>
  );
};
export default Index;

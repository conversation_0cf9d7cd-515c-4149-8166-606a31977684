import { columnWidthEnum } from '@/data/common/constant';
import { hasAuth } from '@/utils/kit';
import toFixed from '@/utils/toFixed';
import {
  SearchFormType,
  XlbBasicForm,
  XlbForm,
  XlbTable,
  XlbTableColumnProps,
} from '@xlb/components';
import { Tooltip } from 'antd';
import dayjs from 'dayjs';
import _ from 'lodash';
import { useEffect, useState } from 'react';
import { readStockCheckOrder } from '../server';

const StateType = [
  {
    label: '制单',
    value: 'INIT',
  },
  {
    label: '审核',
    value: 'AUDIT',
  },
  {
    label: '盘点完成',
    value: 'FINISH',
  },
  {
    label: '作废',
    value: 'INVALID',
  },
];
const RangeType = [
  {
    label: '单品盘点',
    value: 'ITEM',
  },
  {
    label: '类别盘点',
    value: 'CATEGORY',
  },
  {
    label: '部门盘点',
    value: 'DEPT',
  },
  {
    label: '全场盘点',
    value: 'ALL',
  },
  {
    label: '库存记录盘点',
    value: 'STOCK',
  },
];
const UnitType = [
  {
    label: '基本单位',
    value: 'BASIC',
  },
  {
    label: '配送单位',
    value: 'DELIVERY',
  },
  /*{
    label: '单据单位',
    value: "ORDER"
  },*/
  {
    label: '采购单位',
    value: 'PURCHASE',
  },
  {
    label: '库存单位',
    value: 'STOCK',
  },
  {
    label: '批发单位',
    value: 'WHOLESALE',
  },
];
const formList: SearchFormType[] = [
  {
    type: 'input',
    disabled: true,
    name: 'store_name',
    label: '盘点门店',
  },
  {
    type: 'input',
    disabled: true,
    name: 'storehouse_name',
    label: '盘点仓库',
  },
  {
    type: 'input',
    disabled: true,
    name: 'fid',
    label: '单据号',
  },
  {
    type: 'select',
    disabled: true,
    name: 'state',
    label: '单据状态',
    options: [
      { label: '制单', value: 'INIT' },
      { label: '审核', value: 'AUDIT' },
      { label: '已完成', value: 'FINISH' },
      { label: '已作废', value: 'CANCEL' },
    ],
  },
  {
    type: 'input',
    disabled: true,
    name: 'check_type',
    label: '盘点范围',
  },
  {
    type: 'input',
    disabled: true,
    name: 'category_names',
    label: '盘点类别',
  },
  {
    type: 'datePicker',
    disabled: true,
    name: 'operate_date',
    label: '盘点日期',
  },
  {
    type: 'input',
    disabled: true,
    name: 'unit_type',
    label: '盘点单位',
  },
  {
    type: 'input',
    disabled: true,
    name: 'item_dept_names',
    label: '商品部门',
  },
  {
    type: 'input',
    disabled: true,
    name: 'memo',
    label: '留言备注',
    width: 648,
  },
];
const columnsList: XlbTableColumnProps<any>[] = [
  {
    name: '序号',
    code: '_index',
    width: columnWidthEnum.INDEX,
    align: 'center',
  },
  {
    name: '操作',
    code: 'operation',
    width: 80,
    align: 'center',
  },
  {
    name: '商品代码',
    code: 'item_code',
    width: columnWidthEnum.ITEM_CODE,
    features: { sortable: true },
  },
  {
    name: '商品条码',
    code: 'item_bar_code',
    width: columnWidthEnum.ITEM_BAR_CODE,
    features: { sortable: true },
  },
  {
    name: '商品名称',
    code: 'item_name',
    width: 160,
    features: { sortable: true },
  },
  {
    name: '商品类别',
    code: 'item_category_name',
    width: 140,
    features: { sortable: true },
  },
  {
    name: '采购规格',
    code: 'item_spec',
    width: columnWidthEnum.ITEM_SPEC,
    features: { sortable: true },
  },
  {
    name: '单位',
    code: 'use_unit',
    width: 70,
    features: { sortable: true },
  },
  {
    name: '期间进出量',
    code: 'basic_period_quantity',
    width: 120,
    features: { sortable: true },
    hidden: true,
  },
  {
    name: '盘点数量',
    code: 'quantity',
    width: 100,
    features: { sortable: true },
  },
  {
    name: '单价',
    code: 'price',
    width: 90,
    features: { sortable: true },
  },
  {
    name: '盘点金额',
    code: 'money',
    width: 100,
    features: { sortable: true },
  },
  {
    name: '盘点金额(去税)',
    code: 'no_tax_money',
    width: 150,
    features: { sortable: true },
  },
  {
    name: '基本单位',
    code: 'basic_unit',
    width: 90,
    features: { sortable: true },
  },
  {
    name: '盘点基本数量',
    code: 'basic_quantity',
    width: 120,
    features: { sortable: true },
  },
  {
    name: '基本单价',
    code: 'basic_price',
    width: 100,
    features: { sortable: true },
  },
  {
    name: '零售金额',
    code: 'sale_money',
    width: 120,
    features: { sortable: true },
  },
  {
    name: '零售单价',
    code: 'sale_price',
    width: 120,
    features: { sortable: true },
  },
  {
    name: '盘点时间',
    code: 'check_time',
    width: 120,
    features: { sortable: true },
    hidden: true,
  },
  {
    name: '生产日期',
    code: 'producing_date',
    width: columnWidthEnum.DATE,
    features: { sortable: true },
  },
  {
    name: '到期日期',
    code: 'expire_date',
    width: columnWidthEnum.DATE,
    features: { sortable: true },
  },
  {
    name: '批次号',
    code: 'batch_number',
    width: 160,
    features: { sortable: true },
  },
  {
    name: '盈亏金额',
    code: 'diff_money',
    width: 120,
    features: { sortable: true },
  },
  {
    name: '盈亏零售金额',
    code: 'diff_sale_money',
    width: 120,
    features: { sortable: true },
  },
  {
    name: '库存数量',
    code: 'stock_quantity',
    width: 120,
    features: { sortable: true },
  },
  {
    name: '供应商',
    code: 'supplier_name',
    width: 120,
    features: { sortable: true },
  },
  {
    name: '备注',
    code: 'memo',
    width: columnWidthEnum.MEMO,
    features: { sortable: true },
  },
];

const StoreCheckOrder = (props: any) => {
  const { fid } = props;
  const [formModel] = XlbBasicForm.useForm<any>();
  const [tableLoading, setTableLoading] = useState<any>(false);
  // 列表数据
  const [fidDataList, setFidDataList] = useState<any>([]);
  const [itemArrdetail] = useState<XlbTableColumnProps<any>[]>(
    columnsList.map((i) => ({
      ...i,
      code: i.code === 'index' ? '_index' : i.code,
      hidden: i.code === 'operation' ? true : i.hidden,
    })) as any[],
  );

  const tableRender = (item: any) => {
    const filter = formModel.getFieldValue('filter')
      ? formModel.getFieldValue('filter')
      : [];
    const blinded = filter.includes('blinded');
    switch (item.code) {
      case 'quantity':
        item.render = (value: any) => (
          <div style={{ textAlign: 'right' }}>
            {value ? parseFloat(value).toFixed(3) : '0.000'}
          </div>
        );
        break;
      case 'expire_type_num':
        item.render = (value: any, record: any) => {
          return record[item.code] ? (
            <div>
              {value}
              {record.expire_type == 1 ? '天' : '月'}
            </div>
          ) : null;
        };
        break;
      case 'diff_money':
      case 'no_tax_money':
      case 'money':
        item.render = (value: any) => {
          return hasAuth(['库存盘点/价格', '查询']) ? (
            <div style={{ textAlign: 'right' }}>
              {value ? parseFloat(value).toFixed(2) : '0.00'}
            </div>
          ) : (
            <div style={{ textAlign: 'right' }}>****</div>
          );
        };
        break;
      case 'diff_sale_money':
      case 'sale_money':
      case 'sale_price':
        item.render = (value: any) => {
          return hasAuth(['库存盘点/零售价', '查询']) ? (
            <div style={{ textAlign: 'right' }}>
              {parseFloat(value).toFixed(4)}
            </div>
          ) : (
            <div style={{ textAlign: 'right' }}>****</div>
          );
        };
        break;
      case 'price':
        item.render = (value: any) => {
          return hasAuth(['库存盘点/价格', '查询']) ? (
            <div style={{ textAlign: 'right' }}>
              {parseFloat(value).toFixed(4)}
            </div>
          ) : (
            <div style={{ textAlign: 'right' }}>****</div>
          );
        };
        break;
      case 'diff_quantity':
      case 'basic_diff_quantity':
      case 'basic_stock_quantity':
        item.render = (value: any) => {
          return (
            <div style={{ textAlign: 'right' }}>
              {toFixed(value, 'QUANTITY', item.code === 'basic_stock_quantity')}
            </div>
          );
        };
        break;
      case 'basic_quantity':
        item.render = (value: any) => (
          <div style={{ textAlign: 'right' }}>
            {value ? parseFloat(value).toFixed(3) : '0.000'}
          </div>
        );
        break;
      case 'stock_quantity':
        item.render = (value: any) => {
          return (
            <div style={{ textAlign: 'right' }}>
              {blinded ? '****' : toFixed(value, 'QUANTITY', true)}
            </div>
          );
        };
        break;
      case 'basic_price':
        item.render = (value: any, record: any) => {
          return hasAuth(['库存盘点/价格', '查询']) ? (
            record.item_id ? (
              <div style={{ textAlign: 'right' }}>
                {value ? parseFloat(value).toFixed(4) : '0.0000'}
              </div>
            ) : null
          ) : (
            <div style={{ textAlign: 'right' }}>****</div>
          );
        };
        break;
      case 'basic_stock_quantity':
        item.render = (value: any, record: any) => {
          return record[item.code] ? (
            <div style={{ textAlign: 'right' }}>
              {toFixed(value, 'QUANTITY', true)}
            </div>
          ) : null;
        };
        break;
      case 'memo':
        item.render = (value: any) => {
          return (
            <Tooltip placement="topLeft" autoAdjustOverflow title={value}>
              <div>{value}</div>
            </Tooltip>
          );
        };
        break;
    }
    return item;
  };

  const openRefOrder = _.debounce(async (fid, summary: boolean = false) => {
    setTableLoading(true);
    setFidDataList([]);
    formModel.setFieldsValue({});
    const res = await readStockCheckOrder({ fid: fid, summary: summary });
    if (res?.code == 0) {
      // 表单的值
      switch (res.data.check_type) {
        case 'CATEGORY':
          formModel.setFieldsValue({
            category_names: res.data.check_type_memo,
          });
          break;
        case 'DEPT':
          formModel.setFieldsValue({ dept_names: res.data.check_type_memo });
          break;
        case 'ITEM':
          formModel.setFieldsValue({ item_names: res.data.check_type_memo });
          break;
      }
      const obj: any = StateType.find((v) => v.value === res.data.state);
      const obj2: any = RangeType.find((v) => v.value === res.data.check_type);
      const obj3: any = UnitType.find((v) => v.value === res.data.unit_type);
      formModel.setFieldsValue({
        ...res.data,
        operate_date: dayjs(res.data.operate_date),
        state: obj.label,
        check_type: obj2.label,
        unit_type: obj3.label,
      });
      // 列表的值
      const goodsList: any[] = [];
      res.data.details.map((item: any) => {
        const batch_ids: string[] = [];
        res.data.details.map((v: any) => {
          if (v.item_id === item.item_id) {
            batch_ids.push(v.batch_id);
          }
        });
        item.batch_ids = batch_ids;
        item.id = item.item_id;
        let tmp = [];
        tmp.push({ label: item.unit, ratio: 1 });
        if (item.stock_unit != '' && item.stock_ratio != 0)
          tmp.push({
            label: item.stock_unit,
            ratio: item.stock_ratio,
          });
        if (item.purchase_unit != '' && item.purchase_ratio != 0)
          tmp.push({
            label: item.purchase_unit,
            ratio: item.purchase_ratio,
          });
        if (item.wholesale_unit != '' && item.wholesale_ratio != 0)
          tmp.push({
            label: item.wholesale_unit,
            ratio: item.wholesale_ratio,
          });
        if (item.delivery_unit != '' && item.delivery_ratio != 0)
          tmp.push({
            label: item.delivery_unit,
            ratio: item.delivery_ratio,
          });
        const obj = {};
        tmp.forEach((vo: any) => (obj[vo.label] = vo));
        tmp = [];
        for (const i in obj) {
          tmp.push(obj[i]);
        }
        item.operation = 1;
        item.stock_details = [];
        item.quantity = parseFloat(item.quantity).toFixed(3);
        item.price = parseFloat(item.price).toFixed(4);

        item.use_unit = item.unit;
        item.use_unit_ratio = item.ratio;
        const unitIndex = tmp.findIndex((e) => e.ratio == item.ratio);
        if (unitIndex === -1) {
          const uIndex = tmp.findIndex((e) => e.label == item.unit);
          if (uIndex != -1) {
            tmp[uIndex].ratio = item.ratio;
          } else {
            tmp.push({
              label: item.unit,
              ratio: item.ratio,
            });
          }
        }
        item.unit_list = tmp;
        if (!item.basic_quantity) item.basic_quantity = 0;
        switch (res.data.unit_type) {
          case 'BASIC':
            item.use_unit_ratio = item.ratio;
            break;
          case 'DELIVERY':
            if (item.delivery_unit != '' && item.delivery_ratio != 0)
              item.use_unit_ratio = item.delivery_ratio;
            break;
          case 'PURCHASE':
            if (item.purchase_unit != '' && item.purchase_ratio != 0)
              item.use_unit_ratio = item.purchase_ratio;
            break;
          case 'STOCK':
            if (item.stock_unit != '' && item.stock_ratio != 0)
              item.use_unit_ratio = item.stock_ratio;
            break;
          case 'WHOLESALE':
            if (item.wholesale_unit != '' && item.wholesale_ratio != 0)
              item.use_unit_ratio = item.wholesale_ratio;
            break;
        }
        goodsList.push(item);
      });
      setFidDataList(goodsList);
    }
    setTableLoading(false);
  }, 50);

  useEffect(() => {
    openRefOrder(fid);
    itemArrdetail.map((v) => tableRender(v));
  }, []);

  return (
    <>
      <XlbForm
        style={{ marginTop: 15 }}
        formList={formList}
        form={formModel}
        isHideDate={true}
      />
      <XlbTable
        isLoading={tableLoading}
        style={{ height: 400, maxHeight: 400, overflowY: 'scroll' }}
        hideOnSinglePage={false}
        showSearch={true}
        columns={itemArrdetail}
        total={fidDataList?.length}
        dataSource={fidDataList}
        key={fidDataList?.length}
      ></XlbTable>
    </>
  );
};

export default StoreCheckOrder;

import { SelectType} from '@xlb/components';
//经营类型
export const goodsType: SelectType[] = [
  {
    label: '主规格商品',
    value: 'MAINSPEC'
  },
  {
    label: '多规格商品',
    value: 'MULTIPLESPEC'
  },
  {
    label: '标准商品',
    value: 'STANDARD',
  },
  {
    label: '组合商品',
    value: 'COMBINATION',
  },
  {
    label: '成分商品',
    value: 'COMPONENT',
  },
  {
    label: '制单组合',
    value: 'MAKEBILL',
  },
  {
    label: '分级商品',
    value: 'GRADING',
  }
]

export const columnWidthEnum = {
  tel : 120,
  fid : 160,
  TIME : 120,
  DATE : 100,
  ITEM_CODE : 124,
  ITEM_BAR_CODE : 124,
  SHORTHAND_CODE : 110,
  STORE_NAME : 140,
  MEMO : 140,
  STOP_SALE : 90,
  ORDER_STATE : 90,
  INDEX : 50,
  ITEM_SPEC : 110,
  BY : 110,
  ORDER_FID : 140
}
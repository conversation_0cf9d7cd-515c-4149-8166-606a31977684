export const DeliveryCenterStoreKeyMap = {
  /**上游配送中心 */
  erpCenterStoreId: 'erpCenterStoreId',
  /**共享仓--共享配送中心 */
  erpShareDeliverCenter: 'erpShareDeliverCenter',
  /**共享仓--中转配送中心 */
  erpTransitDistributionCenter: 'erpTransitDistributionCenter'
}

export const deliveryCenterStoreConfig: any[] = [
  {
    tag: 'ERP',
    componentType: 'select',
    label: '',
    id: DeliveryCenterStoreKeyMap?.erpCenterStoreId,
    request: async (formValues: any, anybaseURL: any, globalFetch: any) => {
      const res = await globalFetch.post(`${anybaseURL}/erp-mdm/hxl.erp.store.short.page`, {
        center_flag: true
      })
      if (res.code == 0) {
        return res.data?.content?.map((i: any) => ({ value: i.id, label: i.store_name }))
      }
      return []
    },
    fieldProps: {
      mode: 'multiple',
      allowClear: true
    }
  },
  {
    tag: 'ERP',
    componentType: 'select',
    label: '',
    id: DeliveryCenterStoreKeyMap?.erpShareDeliverCenter,
    request: async (formValues: any, anybaseURL: any, globalFetch: any) => {
      const res = await globalFetch.post(
        `${anybaseURL}/erp-mdm/hxl.erp.store.sharedeliverycenter.find`,
        {}
      )
      if (res.code == 0) {
        return res.data?.map((i: any) => ({ value: i.id, label: i.store_name }))
      }
      return []
    },
    fieldProps: {
      mode: 'multiple',
      allowClear: true
    }
  },
  // 中转配送中心
  {
    tag: 'ERP',
    componentType: 'select',
    name: 'transit_store_ids',
    label: '',
    id: DeliveryCenterStoreKeyMap?.erpTransitDistributionCenter,
    request: async (formValues: any, anybaseURL: any, globalFetch: any) => {
      const res = await globalFetch.post(`${anybaseURL}/erp-mdm/hxl.erp.store.all.shortfind`, {
        center_flag: true
      })
      if (res.code == 0) {
        return res.data?.content?.map((i: any) => ({ value: i.id, label: i.store_name }))
      }
      return []
    },
    fieldProps: {
      mode: 'multiple',
      allowClear: true
    }
  }
]
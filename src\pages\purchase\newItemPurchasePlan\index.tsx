import Download from '@/utils/downloadBlobFile';
import { hasAuth } from '@/utils/kit';
import {
  ContextState,
  XlbBasicForm,
  XlbButton,
  XlbDropdownButton,
  XlbForm,
  XlbIcon,
  XlbInput,
  XlbPageContainer,
  XlbTipsModal,
} from '@xlb/components';
import { message } from 'antd';
import dayjs from 'dayjs';
import { cloneDeep } from 'lodash-es';
import { FC, useEffect, useState } from 'react';
import { FORM_LIST, PurchasePlanState, tableColumn } from './data';
import {
  batchAuditPurchaseOrder,
  batchRejectPurchaseOrder,
  exportPurchasePlan,
} from './server';

const Index: FC = () => {
  // form
  const [form] = XlbBasicForm.useForm();
  const [formlist, setFormList] = useState(cloneDeep(FORM_LIST));
  // form修改
  const onValuesChange = (changedValues: any) => {
    if (Object.keys(changedValues).includes('org_ids')) {
      form.setFieldsValue({ store_ids: [] });
      (FORM_LIST.find((i) => i.name === 'store_ids')!
        .dialogParams as any)!.data.org_ids = changedValues.org_ids;
      setFormList([...FORM_LIST]);
    }
  };
  const prevPost = () => ({
    ...form.getFieldsValue(true),
    flag: form.getFieldsValue(true)?.checkbox?.includes('flag')
      ? Number(!form.getFieldsValue(true)?.checkbox?.includes('flag'))
      : void 0,
  });

  useEffect(() => {
    form.setFieldsValue({
      date: [dayjs().format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')],
    });
  }, []);

  // operate
  const [exportLoading, setExportLoading] = useState(false);
  const exportItem = async (requestForm: any) => {
    setExportLoading(true);
    const res = await exportPurchasePlan({ ...requestForm });
    setExportLoading(false);
    const download = new Download();
    download.filename = '新品采购计划.xlsx';
    download.xlsx(res.data);
  };
  const [auditForm] = XlbBasicForm.useForm();
  const auditItem = async (
    chooseFids: string[],
    fetchData: any,
    operate: PurchasePlanState,
  ) => {
    if (operate === PurchasePlanState.AUDIT) {
      const res = await batchAuditPurchaseOrder({ fids: chooseFids });
      if (res.code === 0) {
        message.success('操作成功');
        fetchData();
      }
      return;
    }
    await XlbTipsModal({
      title: '审核拒绝',
      tips: (
        <XlbBasicForm form={auditForm}>
          <XlbBasicForm.Item
            label="备注"
            name={'memo'}
            rules={[{ required: true, message: '请输入备注' }]}
          >
            <XlbInput.TextArea style={{ width: 294, height: 100 }} />
          </XlbBasicForm.Item>
        </XlbBasicForm>
      ),
      isCancel: true,
      onCancel: () => auditForm.resetFields(),
      onOkBeforeFunction: async () => {
        return auditForm
          .validateFields()
          .then(async () => {
            const { memo } = auditForm.getFieldsValue(true);
            const res = await batchRejectPurchaseOrder({
              fids: chooseFids,
              memo,
            });
            if (res.code === 0) {
              message.success('操作成功');
              fetchData();
              return true;
            }
            return false;
          })
          .catch(() => false);
      },
    });
  };

  return (
    <XlbPageContainer
      url={'/erp/hxl.erp.newitempurchaseplan.page'}
      tableColumn={tableColumn}
      immediatePost
      prevPost={prevPost}
      footerDataSource={(data) => {
        return [
          {
            _index: '合计',
            psd: data?.psd,
            allocated_quantity: data?.allocated_quantity,
            quantity: data?.quantity,
          },
        ];
      }}
    >
      <XlbPageContainer.ToolBtn showColumnsSetting>
        {({
          fetchData,
          loading,
          dataSource,
          requestForm,
          selectRow,
          selectRowKeys,
        }: ContextState<any>) => {
          return (
            <XlbButton.Group>
              {hasAuth(['新品采购计划', '查询']) && (
                <XlbButton
                  label="查询"
                  type="primary"
                  loading={loading}
                  onClick={() => {
                    fetchData();
                  }}
                  icon={<XlbIcon name="sousuo" />}
                />
              )}
              {hasAuth(['新品采购计划', '审核']) && (
                <XlbDropdownButton
                  icon={<XlbIcon name="pinglun" size={16} />}
                  dropList={[
                    { label: '审核通过', value: PurchasePlanState.AUDIT },
                    { label: '审核拒绝', value: PurchasePlanState.REJECT },
                  ]}
                  disabled={
                    !selectRowKeys?.length ||
                    selectRow?.some(
                      (item) => item.state !== PurchasePlanState.INIT,
                    )
                  }
                  dropdownItemClick={(_index: number, item: any) => {
                    auditItem(selectRowKeys || [], fetchData, item.value);
                  }}
                  label={'审核'}
                />
              )}
              {hasAuth(['新品采购计划', '导出']) && (
                <XlbButton
                  label="导出"
                  type="primary"
                  disabled={!dataSource?.length}
                  loading={exportLoading}
                  icon={<XlbIcon name="daochu" />}
                  onClick={() => exportItem(requestForm)}
                />
              )}
            </XlbButton.Group>
          );
        }}
      </XlbPageContainer.ToolBtn>

      <XlbPageContainer.SearchForm>
        <XlbForm
          form={form}
          formList={formlist}
          isHideDate
          onValuesChange={onValuesChange}
        />
      </XlbPageContainer.SearchForm>

      <XlbPageContainer.Table
        selectMode="multiple"
        key="fid"
        primaryKey="fid"
      />
    </XlbPageContainer>
  );
};
export default Index;

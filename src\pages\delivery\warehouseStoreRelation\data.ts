import type { XlbTableColumnProps } from '@xlb/components'
export const statusList = [
  {
    label: '已审核',
    value: 'APPROVED'
  },
  {
    label: '已签约',
    value: 'SIGNED'
  },
  {
    label: '已装修',
    value: 'DECORATED'
  },
  {
    label: '已铺货',
    value: 'STOCKED'
  },
  {
    label: '已开业',
    value: 'OPENED'
  },
  {
    label: '已停业',
    value: 'TEMPORARILY_CLOSED'
  },
  {
    label: '已闭店',
    value: 'PERMANENTLY_CLOSED'
  },
  {
    label: '已转让',
    value: 'TRANSFERRED'
  }
]
export const tableColumn: XlbTableColumnProps<any>[] = [
  {
    name: '序号',
    code: '_index',
    width: 50,
    align: 'center',
    lock: true
  },
  {
    name: '门店代码',
    code: 'store_code',
    width: 180,
    lock: true,
    features: { sortable: true, details: true }
  },
  {
    name: '门店名称',
    code: 'store_id',
    width: 180,
    render: (value, row) => row.store_name,
    features: { sortable: true, details: true }
  },
  {
    name: '门店二级组织',
    code: 'store_center_org_name',
    width: 200
  },
  {
    name: '门店状态',
    code: 'store_state',
    width: 180,
    render: (value, row) => {
      const status = statusList.find((item) => item.value === value)
      return status?.label || ''
    }
  },
  {
    name: '门店所在省',
    code: 'store_province',
    width: 180
  },
  {
    name: '门店所在市',
    code: 'store_city',
    width: 180
  },
  {
    name: '门店地址',
    code: 'store_address',
    width: 180
  },

  {
    name: '实发配送中心',
    code: 'actual_delivery_store_name',
    width: 180
  },
  {
    name: '实发配送中心二级组织',
    code: 'actual_delivery_store_center_org_name',
    width: 180
  },
  // {
  //   name: '实发配送中心名称',
  //   code: 'actual_delivery_store_name',
  //   width: 180
  // },
  {
    name: '实发配送中心省',
    code: 'actual_delivery_store_province',
    width: 180
  },
  {
    name: '实发配送中心市',
    code: 'actual_delivery_store_city',
    width: 180
  },
  {
    name: '实发配送中心地址',
    code: 'actual_delivery_store_address',
    width: 180
  },
  {
    name: '签约配送中心代码',
    code: 'upstream_delivery_store_code',
    width: 180
  },
  {
    name: '签约配送中心名称',
    code: 'upstream_delivery_store_name',
    width: 180
  },
  {
    name: '仓店距离(km)',
    code: 'actual_delivery_store_distance',
    width: 180
  },
  {
    name: '承诺时效',
    code: 'actual_delivery_store_commitment_time',
    width: 180
  },
  {
    name: '记录日期',
    code: 'create_time',
    width: 180,
    features: { format: 'TIME' }
  }
]

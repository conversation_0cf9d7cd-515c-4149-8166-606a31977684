import { columnWidthEnum } from '@/data/common/constant';
import {
  SearchFormType,
  SelectType,
  XlbBasicForm,
  XlbCheckbox,
  XlbInputNumber,
  XlbTableColumnProps,
} from '@xlb/components';
import './index.less';

export const UnitType: SelectType[] = [
  {
    label: '基本单位',
    value: 'BASIC',
  },
  {
    label: '采购单位',
    value: 'PURCHASE',
  },
  {
    label: '配送单位',
    value: 'DELIVERY',
  },

  {
    label: '库存单位',
    value: 'STOCK',
  },
  {
    label: '批发单位',
    value: 'WHOLESALE',
  },
];

export const searchFormList: SearchFormType[] = [
  {
    label: '门店',
    name: 'store_ids',
    type: 'inputDialog',
    allowClear: false,
    width: 150,
    dialogParams: {
      type: 'store',
      dataType: 'lists',
      isLeftColumn: true,
      isMultiple: true,
      data: {
        status: true,
        storeStatus: true,
        enabled: true,
      },
    },

    fieldNames: {
      idKey: 'id',
      nameKey: 'store_name',
    } as any,
  },
  {
    label: '仓库',
    name: 'storehouse_id',
    type: 'select',
    width: 150,
    multiple: false,
    dependencies: ['store_ids'],
    dropdownStyle: { width: 200 },
    handleDefaultValue: (data: any, formData: any) => {
      if (data?.length === 0) {
        return null;
      }
      const defaultStoreHouse =
        data.find((item: any) => item.default_flag) || data[0];
      return defaultStoreHouse?.value;
    },
    // @ts-ignore
    selectRequestParams: (params: any, form) => {
      form?.setFieldsValue({
        storehouse_ids: null,
      });
      if (params?.store_ids?.length == 1) {
        return {
          url: '/erp/hxl.erp.storehouse.store.find',
          postParams: {
            store_id: params?.store_ids?.[0],
          },
          responseTrans(data: any) {
            const options = data
              .filter((v: any) => v.distribution)
              .map((item: any) => ({
                label: item.name,
                value: item.id,
                default_flag: item.default_flag,
              }));
            return options;
          },
        };
      }
    },
  },
  {
    label: '商品类别',
    name: 'item_category_ids',
    type: 'inputDialog',
    width: 150,
    treeModalConfig: {
      // @ts-ignore
      title: '选择商品分类', // 标题
      url: '/erp/hxl.erp.category.find', // 请求地址
      dataType: 'lists',
      checkable: true, // 是否多选
      primaryKey: 'id',
      data: {
        enabled: true,
      },
    },
  },
  {
    label: '商品部门',
    name: 'item_dept_ids',
    type: 'inputDialog',
    width: 150,
    dialogParams: {
      type: 'productDept',
      dataType: 'lists',
      isLeftColumn: false,
      isMultiple: true,
      primaryKey: 'id',
      data: {
        enabled: true,
      },
    },
  },
  {
    label: '供应商',
    name: 'supplier_ids',
    type: 'inputDialog',
    width: 150,
    dialogParams: {
      type: 'supplier',
      dataType: 'lists',
      isLeftColumn: true,
      isMultiple: true,
      primaryKey: 'id',
      data: {
        enabled: true,
      },
    },
  },
  {
    label: '查询单位',
    name: 'unit_type',
    type: 'select',
    width: 150,
    initialValue: 'BASIC',
    options: UnitType,
  },
  {
    label: '最近入库日期早于',
    name: 'latest_in_date',
    type: 'datePicker',
    width: 150,
    clear: true,
  },
  {
    label: '',
    name: 'inputInterval',
    type: 'custom',
    render(itemData, form, itemSetting) {
      return (
        <>
          <XlbBasicForm.Item shouldUpdate noStyle>
            <span
              style={{
                display: 'inline-flex',
                alignItems: 'center',
                margin: '0 5px 0 32px',
                lineHeight: '32px',
                height: '28px',
              }}
            >
              {'最近'}
            </span>
            <XlbBasicForm.Item
              style={{
                display: 'inline-block',
                marginBottom: 0,
              }}
              name="day"
            >
              <XlbInputNumber
                width={60}
                min={0}
                precision={0}
                size="small"
                controls={false}
              />
            </XlbBasicForm.Item>
            <span
              style={{
                display: 'inline-flex',
                alignItems: 'center',
                margin: '0 5px',
                lineHeight: '32px',
                height: '28px',
              }}
            >
              {'天内出库量≤'}
            </span>
            <XlbBasicForm.Item
              style={{ display: 'inline-block', marginBottom: 0 }}
              name="total_out_quantity"
            >
              <XlbInputNumber
                width={100}
                min={0}
                size="small"
                controls={false}
              />
            </XlbBasicForm.Item>
          </XlbBasicForm.Item>

          <XlbBasicForm.Item shouldUpdate noStyle>
            <XlbBasicForm.Item
              style={{
                display: 'inline-block',
                marginBottom: 0,
                marginLeft: 36,
              }}
              name="filter1"
            >
              <XlbCheckbox.Group>
                <XlbCheckbox value={'out_quantity_less_than_stock_quantity'}>
                  调出量小于库存量
                </XlbCheckbox>
              </XlbCheckbox.Group>
            </XlbBasicForm.Item>
          </XlbBasicForm.Item>

          <XlbBasicForm.Item shouldUpdate noStyle>
            <XlbBasicForm.Item
              style={{
                display: 'inline-block',
                marginBottom: 0,
                marginLeft: 36,
              }}
              name="stock_day"
            >
              <XlbInputNumber
                width={60}
                min={0}
                precision={0}
                size="small"
                controls={false}
                value={form?.getFieldValue('stock_day')?.[0]}
                onChange={(e) => {
                  const value = form?.getFieldValue('stock_day') || [
                    null,
                    null,
                  ];
                  value[0] = e;
                  form?.setFieldValue('stock_day', value);
                }}
              />

              <span
                style={{
                  display: 'inline-flex',
                  alignItems: 'center',
                  margin: '0 5px',
                  lineHeight: '32px',
                  height: '28px',
                }}
              >
                {'≤在库天数≤'}
              </span>

              <XlbInputNumber
                width={100}
                min={0}
                precision={0}
                size="small"
                controls={false}
                value={form?.getFieldValue('stock_day')?.[1]}
                onChange={(e) => {
                  const value = form?.getFieldValue('stock_day') || [
                    null,
                    null,
                  ];
                  value[1] = e;
                  form?.setFieldValue('stock_day', value);
                }}
              />
            </XlbBasicForm.Item>
          </XlbBasicForm.Item>
        </>
      );
    },
  },
  {
    label: '过滤',
    name: 'filter2',
    type: 'checkbox',
    disabled: false,
    options: [
      { value: 'filter_no_stock_item', label: '从未出入库商品' },
      { value: 'filter_zero_stock_item', label: '0库存商品' },
      { value: 'filter_stop_purchase', label: '停购商品' },
      { value: 'filter_stop_sale', label: '停售商品' },
      { value: 'open_news_cycle', label: '新品' },
      { value: 'stop_request', label: '停止要货' },
    ],
  },
];

export const tableColumn: XlbTableColumnProps<any>[] = [
  {
    name: '基本信息',
    code: 'basicInfo',
    align: 'center',
    children: [
      {
        name: '序号',
        code: '_index',
        width: columnWidthEnum.INDEX,
        align: 'center',
      },
      {
        name: '门店',
        code: 'store_name',
        width: columnWidthEnum.STORE_NAME,
        features: { sortable: true },
      },
      {
        name: '商品代码',
        code: 'item_code',
        width: columnWidthEnum.ITEM_CODE,
        features: { sortable: true },
      },
      {
        name: '商品条码',
        code: 'item_bar_code',
        width: columnWidthEnum.ITEM_BAR_CODE,
        features: { sortable: true },
      },
      {
        name: '商品名称',
        code: 'item_name',
        width: 120,
        features: { sortable: true },
      },
      {
        name: '采购规格',
        code: 'item_spec',
        width: columnWidthEnum.ITEM_SPEC,
        features: { sortable: true },
      },
      {
        name: '单位',
        code: 'unit',
        width: 80,
        features: { sortable: true },
      },
      {
        name: '商品类别',
        code: 'item_category_name',
        width: 120,
        features: { sortable: true },
      },
      {
        name: '保质期',
        code: 'period',
        width: 120,
        features: { sortable: true },
      },
      {
        name: '最近入库日期',
        code: 'latest_in_date',
        width: 120,
        features: { sortable: true },
      },
      {
        name: '停购',
        code: 'stop_purchase',
        width: 80,
        features: { sortable: true },
      },
      {
        name: '停售',
        code: 'stop_sale',
        width: 80,
        features: { sortable: true },
      },
      {
        name: '停止要货',
        code: 'stop_request',
        width: 120,
        features: { sortable: true },
      },
      {
        name: '新品',
        code: 'open_news_cycle',
        width: 80,
        features: { sortable: true },
      },
      {
        name: '库存数量',
        code: 'stock_quantity',
        width: 120,
        features: { sortable: true },
      },
      {
        name: '供应商',
        code: 'supplier_names',
        width: 120,
        features: { sortable: true },
      },
      {
        name: '在库天数',
        code: 'stock_day',
        width: 120,
        features: { sortable: true },
      },
    ],
  },
  {
    name: '出库量统计',
    code: 'outboundStatistics',
    align: 'center',
    children: [
      {
        name: '最近调出量',
        code: 'out_quantity',
        width: 120,
        align: 'right',
        features: { sortable: true },
      },
      {
        name: '最近批发量',
        code: 'wholesale_quantity',
        width: 120,
        align: 'right',
        features: { sortable: true },
      },
      {
        name: '最近POS销量',
        code: 'pos_sale_quantity',
        width: 120,
        align: 'right',
        features: { sortable: true },
      },
      {
        name: '出库合计',
        code: 'total_out_quantity',
        width: 120,
        align: 'right',
        features: { sortable: true },
      },
    ],
  },
  {
    name: '入库量统计',
    code: 'inventoryStatistics',
    align: 'center',
    children: [
      {
        name: '最近收货量',
        code: 'receive_quantity',
        width: 120,
        align: 'right',
        features: { sortable: true },
      },
      {
        name: '最近调入量',
        code: 'in_quantity',
        width: 120,
        align: 'right',
        features: { sortable: true },
      },
      {
        name: '入库合计',
        code: 'total_in_quantity',
        width: 120,
        align: 'right',
        features: { sortable: true },
      },
    ],
  },
];

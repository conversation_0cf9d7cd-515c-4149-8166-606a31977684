import { hasAuth } from '@/utils/kit';
import NiceModal from '@ebay/nice-modal-react';
import {
  type BaseModalProps,
  XlbBasicForm,
  XlbButton,
  XlbCheckbox,
  XlbImportModal,
  XlbInputDialog,
  XlbMessage,
  XlbModal,
  XlbRadio,
  XlbSelect,
  XlbTipsModal,
} from '@xlb/components';
import { type FC, useState } from 'react';
import { batch } from '../../data';
import { batchUpdate } from '../../server';
import style from './batchChange.less';

interface Props extends Pick<BaseModalProps, 'title'> {
  getData?: any;
  client_ids?: string | number[];
}

const CustomerGoodsBatchChange: FC<Props> = ({ getData, client_ids }) => {
  const modal = NiceModal.useModal();
  const [form] = XlbBasicForm.useForm();
  const [loading, setLoading] = useState(false);

  const handleOk = async () => {
    if (!form.getFieldValue('client_ids')) {
      XlbTipsModal({
        tips: '请选择批发客户！',
      });
      return;
    }
    if (form.getFieldValue('modify_scope') === undefined) {
      XlbTipsModal({
        tips: '请选择设置商品！',
      });
      return;
    } else if (
      form.getFieldValue('modify_scope') === 1 &&
      !form.getFieldValue('item_category_ids')
    ) {
      XlbTipsModal({
        tips: '请选择商品类别！',
      });
      return;
    } else if (
      form.getFieldValue('modify_scope') === 2 &&
      !form.getFieldValue('item_ids')
    ) {
      XlbTipsModal({
        tips: '请选择指定商品！',
      });
      return;
    }
    if (!form.getFieldValue('checkValue')?.length) {
      XlbTipsModal({
        tips: '请选择设置内容！',
      });
      return;
    }
    const modify_scope = form.getFieldValue('modify_scope');
    const data = {
      ...form.getFieldsValue(),
      modify_scope: modify_scope,
      item_category_ids:
        modify_scope === 1 ? form.getFieldValue('item_category_ids') : [],
      item_ids: modify_scope === 2 ? form.getFieldValue('item_ids') : [],
    };
    batch.forEach((item: any) => {
      // 筛选勾选项并赋值
      if (form.getFieldValue('checkValue')?.includes(item.value)) {
        data[item.value] = form.getFieldValue(item.value);
      } else {
        data[item.value] = undefined;
      }
    });
    setLoading(true);
    const res = await batchUpdate(data);
    setLoading(false);
    if (res?.code === 0) {
      XlbMessage.success('操作成功');
      modal.hide();
      form.resetFields();
      if (client_ids) {
        getData();
      }
    }
  };

  // 导入批发客户
  const importStores = async () => {
    await XlbImportModal({
      importUrl: `${process.env.ERP_URL}/erp/hxl.erp.clientname.import`,
      templateUrl: `${process.env.ERP_URL}/erp/hxl.erp.clientnametemplate.download`,
      templateName: '批发客户导入模板',
      callback: (res: any) => {
        if (res?.code !== 0) return;
        form.setFieldsValue({
          client_ids: res?.data?.client_ids ? res?.data?.client_ids : [],
          client_names: res?.data?.client_names_str
            ? res?.data?.client_names_str
            : '',
        });
      },
    });
  };
  // 导入商品
  const importShort = async () => {
    await XlbImportModal({
      importUrl: `${process.env.ERP_URL}/erp/hxl.erp.items.batchimport`,
      templateUrl: `${process.env.ERP_URL}/erp/hxl.erp.item.shorttemplate.download`,
      templateName: '商品导入模板',
      callback: (res: any) => {
        if (res?.code !== 0) return;
        form.setFieldsValue({
          item_ids: res?.data?.items?.map((v: any) => v.id),
          item_names: res?.data?.items?.map((v: any) => v.name)?.join(','),
        });
      },
    });
  };

  const renderCheckBox = (label: any) => {
    return (
      <XlbSelect
        size="small"
        width={168}
        disabled={!hasAuth(['客户商品属性', '编辑'])}
        options={[
          { value: true, label: '是' },
          { value: false, label: '否' },
          { value: null, label: '　' },
        ]}
      ></XlbSelect>
    );
  };

  return (
    <XlbModal
      title={'批量修改'}
      open={modal.visible}
      maskClosable={false}
      keyboard={false}
      isCancel={true}
      onOk={handleOk}
      onCancel={() => {
        form.resetFields();
        modal.resolve(false);
        modal.hide();
      }}
      width={500}
      confirmLoading={loading}
    >
      <XlbBasicForm form={form}>
        <div className={style.box}>
          <p className={style.title}>设置门店</p>
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <XlbBasicForm.Item
              name="client_ids"
              label="批发客户"
              colon={false}
              labelCol={{ style: { width: 95 } }}
              rules={[{ required: true, message: '请选择批发客户' }]}
            >
              <XlbInputDialog
                fieldNames={{
                  idKey: 'id',
                  nameKey: 'name',
                }}
                dialogParams={{
                  type: 'wholesaler',
                  dataType: 'lists',
                  isMultiple: true,
                  primaryKey: 'id',
                  data: {
                    enabled: true,
                  },
                  onOkBeforeFunction: (_val: any, data: any[]) => {
                    form.setFieldsValue({
                      client_ids: _val,
                      client_names: data.map((v) => v.name),
                    });
                    return true;
                  },
                }}
                width={168}
              />
            </XlbBasicForm.Item>
            <XlbButton
              size="small"
              onClick={() => importStores()}
              // type="primary"
              // icon={<XlbIcon name="daoru" />}
            >
              导入
            </XlbButton>
          </div>
        </div>
        <div className={style.box}>
          <p className={style.title}>设置商品</p>
          <XlbBasicForm.Item name="modify_scope">
            <XlbRadio.Group>
              <XlbRadio value={0}>全部商品</XlbRadio>
              <div
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  margin: '10px 0',
                }}
              >
                <XlbRadio value={1}>商品类别</XlbRadio>
                <XlbBasicForm.Item name="item_category_ids" noStyle>
                  <XlbInputDialog
                    width={168}
                    treeModalConfig={{
                      title: '选择商品分类', // 标题
                      url: '/erp/hxl.erp.category.find', // 请求地址
                      dataType: 'lists',
                      checkable: true, // 是否多选
                      primaryKey: 'id',
                      data: {
                        enabled: true,
                      },
                    }}
                  />
                </XlbBasicForm.Item>
              </div>
              <div style={{ display: 'flex', alignItems: 'center' }}>
                <XlbRadio value={2}>指定商品</XlbRadio>
                <XlbBasicForm.Item name="item_ids" noStyle>
                  <XlbInputDialog
                    fieldNames={{
                      idKey: 'id',
                      nameKey: 'name',
                    }}
                    dialogParams={{
                      type: 'supplierContractItem',
                      dataType: 'lists',
                      isMultiple: true,
                      data: {
                        show_sale_unit: true,
                      },
                    }}
                    width={168}
                  />
                </XlbBasicForm.Item>
                <XlbButton
                  size="small"
                  onClick={() => importShort()}
                  style={{ marginLeft: '12px' }}
                  // type="primary"
                  // icon={<XlbIcon name="daoru" />}
                >
                  导入
                </XlbButton>
              </div>
            </XlbRadio.Group>
          </XlbBasicForm.Item>
        </div>
        <div className={style.box}>
          <p className={style.title}>设置内容</p>
          <XlbBasicForm.Item name="checkValue">
            <XlbCheckbox.Group>
              {batch.map((item) => {
                return (
                  <div
                    key={item.value}
                    style={{ display: 'flex', alignItems: 'center' }}
                  >
                    <XlbCheckbox
                      value={item.value}
                      disabled={!hasAuth(['客户商品属性', '编辑'])}
                      style={{
                        width: 'auto',
                        display: 'inline-flex',
                        alignItems: 'center',
                      }}
                    />
                    <span style={{ margin: '0 10px 0 8px' }}>{item.label}</span>
                    <XlbBasicForm.Item
                      style={{ margin: '10px 0' }}
                      name={item.value}
                      key={item.value}
                    >
                      {renderCheckBox(item.label)}
                    </XlbBasicForm.Item>
                  </div>
                );
              })}
            </XlbCheckbox.Group>
          </XlbBasicForm.Item>
        </div>
      </XlbBasicForm>
    </XlbModal>
  );
};
export default NiceModal.create(CustomerGoodsBatchChange);

export const TransferBasketKeyMap = {
  erpBasketEnabled: 'erpBasketEnabled',
  erpDefaultOrg: 'erpDefaultOrg',
};

export const transferBasketConfig: any[] = [
  {
    tag: 'ERP',
    label: '是否启用',
    id: TransferBasketKeyMap.erpBasketEnabled,
    name: 'enabled',
    componentType: 'checkbox',
    fieldProps: {
      options: [{ label: '启用', value: 'enabled' }],
    },
    formItemProps: {
      label: ' ',
      colon: false,
    },
    group: false,
    colon: false,
  },
  {
    tag: 'ERP',
    label: '    ',
    id: TransferBasketKeyMap.erpDefaultOrg,
    name: 'enabled',
    componentType: 'checkbox',
    fieldProps: {
      options: [{ label: '默认组织', value: 'default_org' }],
    },
    formItemProps: {
      label: ' ',
      colon: false,
    },
    group: false,
    colon: false,
  },
];

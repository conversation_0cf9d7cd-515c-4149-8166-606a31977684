
export interface ReasonFindReqDTO {

    /**
     * @name 查询关键字
     */
    keyword?: string;

    /**
     * @name 原因类型
     */
    type: string;

    /**
     * @name 更新时间
     */
    update_time?: string;

}


export interface ReasonSaveReqDTO {

    /**
     * @name 原因名称
     */
    name: string;

    /**
     * @name 原因类型
     */
    type: string;

}



export interface ReasonUpdateReqDTO {

    /**
     * @name ID
     */
    id?: number;

    /**
     * @name 原因名称
     */
    name: string;

    /**
     * @name 原因类型
     */
    type: string;

}



import type { XlbTableColumnProps } from '@xlb/components';
import { selectType } from './types';

const columnWidthEnum = {
  tel: 120,
  fid: 160,
  TIME: 120,
  DATE: 100,
  ITEM_CODE: 124,
  ITEM_BAR_CODE: 124,
  SHORTHAND_CODE: 110,
  STORE_NAME: 140,
  MEMO: 140,
  STOP_SALE: 90,
  ORDER_STATE: 90,
  INDEX: 50,
  ITEM_SPEC: 110,
  BY: 110,
  ORDER_FID: 140,
};

export const unitTypes: selectType[] = [
  {
    label: '配送单位',
    value: 'DELIVERY',
  },
  {
    label: '采购单位',
    value: 'PURCHASE',
  },
];

interface SummaryType extends selectType {
  code: string;
}
export const summaryTypes: SummaryType[] = [
  {
    label: '调出门店',
    value: 'DELIVERY_STORE',
    code: 'delivery_store_name',
  },
  {
    label: '调入门店',
    value: 'STORE',
    code: 'store_name',
  },
  {
    label: '统配日期',
    value: 'DELIVERY_DATE',
    code: 'delivery_date',
  },
];

export const enableItem: selectType[] = [
  { label: '启用', value: true },
  { label: '禁用', value: false },
];

export const filterArr = [
  {
    label: '过滤未调出的门店补货单数据',
    value: 'filter_not_out_store_request_order',
  },
];

export const tableList: XlbTableColumnProps<any>[] = [
  {
    name: '序号',
    code: '_index',
    width: columnWidthEnum.INDEX,
    align: 'center',
  },
  {
    name: '统配日期',
    code: 'delivery_date',
    width: columnWidthEnum.STORE_NAME,
    align: 'left',
    hidden: true,
  },
  {
    name: '调出组织',
    code: 'out_org_name',
    hiddenInXlbColumns: true,
    width: columnWidthEnum.STORE_NAME,
    features: { sortable: true },
  },
  {
    name: '调出门店',
    code: 'delivery_store_name',
    width: columnWidthEnum.STORE_NAME,
    align: 'left',
    hidden: true,
  },
  {
    name: '调入组织',
    code: 'in_org_name',
    hiddenInXlbColumns: true,
    width: columnWidthEnum.STORE_NAME,
    features: { sortable: true },
  },
  {
    name: '调入门店',
    code: 'store_name',
    width: columnWidthEnum.STORE_NAME,
    align: 'left',
    hidden: true,
  },
  {
    name: '商品代码',
    code: 'item_code',
    width: columnWidthEnum.ITEM_CODE,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '商品条码',
    code: 'item_bar_code',
    width: columnWidthEnum.ITEM_BAR_CODE,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '商品名称',
    code: 'item_name',
    width: 280,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '商品类别',
    code: 'item_category_name',
    width: 140,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '单位',
    code: 'unit',
    width: 80,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '统配数量',
    code: 'force_delivery_quantity',
    width: 110,
    features: { sortable: true, format: 'QUANTITY' },
    align: 'right',
  },
  {
    name: '统配基本数量',
    code: 'basic_force_delivery_quantity',
    width: 120,
    features: { sortable: true, format: 'QUANTITY' },
    align: 'right',
  },
  {
    name: '统配金额',
    code: 'force_delivery_money',
    width: 110,
    features: { sortable: true, format: 'MONEY' },
    align: 'right',
  },
];

import { SearchFormType, XlbTableColumnProps } from '@xlb/components'

export const dateSelect: any[] = [
  {
    label: '近一月',
    value: 1
  },
  {
    label: '近两月',
    value: 2
  },
  {
    label: '近三月',
    value: 3
  }
]

export const formList: SearchFormType[] = [
  {
    label: '备货类型',
    name: 'prepare_type_id',
    type: 'select',
    options: [],
    Change: (val: any) => {},
    clear: true,
    check: true,
    rules: [{ required: true, message: '请选择备货类型' }]
  },
  {
    label: '销量取值范围',
    name: 'query_date',
    check: true,
    type: 'rangePicker',
    width: 200
    // options: dateSelect,
    // disabled: true
  },
  {
    label: '备货日期',
    name: 'prepare_date',
    width: 200,
    check: true,
    type: 'rangePicker'
  },
  {
    label: '销售日期',
    name: 'sale_date',
    width: 240,
    type: 'rangePicker',
    check: true
  },
  {
    label: '门店',
    name: 'store_ids',
    type: 'inputDialog',
    dialogParams: {
      type: 'store',
      dataType: 'lists',
      isLeftColumn: true,
      isMultiple: true,
      primaryKey: 'id',
      data: {
        enabled: true
      },
      fieldName: {
        id: 'id',
        name: 'store_name',
      }
    },
    fieldNames: {
      idKey: 'id',
      nameKey: 'store_name',
    } as any,
  },
  {
    label: '汇总维度',
    name: 'summary_types',
    type: 'select',
    multiple: true,
    allowClear:false,
    options: [
      {
        label: '商品档案',
        value: 'ITEM'
      },
      {
        label: '商品类别',
        value: 'CATEGORY',
        disabled: true
      }
    ],
    check: true
  },
  {
    label: '类别等级',
    name: 'category_level',
    type: 'select',
    initialValue: '',
    check: true,
    options: [
      {
        label: '按当前类别',
        value: ''
      },
      {
        label: '按一级类别',
        value: 1
      },
      {
        label: '按二级类别',
        value: 2
      },
      {
        label: '按三级类别',
        value: 3
      }
    ]
  },
  {
    label: '商品分类',
    name: 'item_category_ids',
    type: 'inputDialog',
    treeModalConfig: {
      title: '选择商品分类', // 标题
      url: '/erp-mdm/hxl.erp.category.find', // 请求地址
      dataType: 'lists',
      checkable: true, // 是否多选
      primaryKey: 'id',
      data: {
        enabled: true
      },
      width: 360 // 模态框宽度
    },
    clear: true,
    check: true
  },
  {
    label: '商品档案',
    name: 'item_ids',
    type: 'inputDialog',
    dialogParams: {
      type: 'goods',
      dataType: 'lists',
      isMultiple: true,
      data: {
        enabled: true
      },
    }
  },
  {
    label: '预测系数',
    name: 'coefficient',
    type: 'inputNumber',
    max: 99.99,
    min: 1.0,
    step: 0.01,
    defaultValue: 1.0,
    check: true,
    rules: [{ required: true, message: '请输入预测系数' }]
  }
]
// 列表数据
export const tableList: XlbTableColumnProps<any>[] = [
  {
    name: '序号',
    code: '_index',
    width: 80,
    align: 'center',
    lock: true
  },
  {
    name: '商品分类',
    code: 'item_category_name',
    hidden: false,
    width: 120,
    features: { sortable: true }
  },
  {
    name: '一级类别',
    code: 'one_item_category_name',
    width: 120,
    hidden: false,
    features: { sortable: true },
    align: 'left'
  },
  {
    name: '二级类别',
    code: 'two_item_category_name',
    width: 120,
    hidden: false,
    features: { sortable: true },
    align: 'left'
  },
  {
    name: '三级类别',
    code: 'three_item_category_name',
    width: 120,
    hidden: false,
    features: { sortable: true },
    align: 'left'
  },
  {
    name: '商品代码',
    code: 'item_code',
    width: 130,
    hidden: false,
    features: { sortable: true }
  },
  {
    name: '商品条码',
    code: 'item_bar_code',
    hidden: false,
    width: 140,
    features: { sortable: true }
  },
  {
    name: '商品名称',
    code: 'item_name',
    hidden: false,
    width: 260,
    features: { sortable: true }
  },
  {
    name: '单位',
    code: 'unit',
    hidden: false,
    width: 100,
    align: 'left',
    features: { sortable: true }
  },
  {
    name: '日均销量',
    code: 'avg_sale_quantity',
    width: 110,
    align: 'right',
    features: { sortable: true }
  },
  {
    name: '日均销量占比',
    code: 'avg_sale_quantity_ratio_str',
    width: 130,
    align: 'right',
    features: { sortable: true }
  },
  {
    name: '备货期入库量',
    code: 'prepare_quantity',
    width: 130,
    align: 'right',
    features: { sortable: true }
  },
  {
    name: '备货期销售量',
    code: 'prepare_sale_quantity',
    width: 130,
    align: 'right',
    features: { sortable: true }
  },
  {
    name: '备货期预发量',
    code: 'prepare_delivery_quantity',
    width: 130,
    align: 'right',
    features: { sortable: true }
  },
  {
    name: '理论备货量',
    code: 'theory_prepare_quantity',
    width: 130,
    align: 'right',
    features: { sortable: true }
  },
  {
    name: '备货差',
    code: 'prepare_difference',
    width: 160,
    align: 'right',
    features: { sortable: true }
  }
]
import { useBaseParams } from '@/hooks/useBaseParams';
import Download from '@/utils/downloadBlobFile';
import { hasAuth } from '@/utils/kit';
import { LStorage } from '@/utils/storage';
import {
  XlbBasicForm,
  XlbButton,
  XlbForm,
  XlbIcon,
  XlbPageContainer,
} from '@xlb/components';
import { XlbFetch } from '@xlb/utils';
import { message } from 'antd';
import dayjs from 'dayjs';
import { useState, type FC } from 'react';
import { columnWidthEnum, goodsType } from './data';

const { ToolBtn, Table, SearchForm } = XlbPageContainer;
const PurchaseLatestPrice: FC = () => {
  const { enable_organization, enable_cargo_owner } = useBaseParams(
    (state) => state,
  );
  const tableColumn = [
    {
      name: '序号',
      code: '_index',
      width: columnWidthEnum.INDEX,
      align: 'center',
    },
    {
      name: '组织',
      code: 'org_name',
      width: 140,
      lock: true,
      hidden: !enable_organization,
    },
    {
      name: '门店',
      code: 'store_name',
      width: columnWidthEnum.STORE_NAME,
      features: { sortable: true },
      align: 'left',
    },
    {
      name: '货主',
      code: 'cargo_owner_id',
      width: 120,
      features: { sortable: true },
      align: 'left',
      hidden: !enable_cargo_owner,
      render: (_text: string, record: any) => (
        <span>{record?.cargo_owner_name}</span>
      ),
    },
    {
      name: '供应商',
      code: 'supplier_name',
      width: 250,
      features: { sortable: true },
      align: 'left',
    },
    {
      name: '商品代码',
      code: 'item_code',
      width: columnWidthEnum.ITEM_CODE,
      features: { sortable: true },
      align: 'left',
    },
    {
      name: '商品条码',
      code: 'item_bar_code',
      width: columnWidthEnum.ITEM_BAR_CODE,
      features: { sortable: true },
      align: 'left',
    },
    {
      name: '商品名称',
      code: 'item_name',
      width: 280,
      features: { sortable: true },
      align: 'left',
    },
    {
      name: '采购规格',
      code: 'item_spec',
      width: columnWidthEnum.ITEM_SPEC,
      features: { sortable: true },
      align: 'left',
    },
    {
      name: '商品类型',
      code: 'item_type',
      width: 100,
      features: { sortable: true },
      align: 'left',
      render: (text: string, record: any) => {
        return <span>{goodsType.find((e) => e.value == text)?.label}</span>;
      },
    },
    {
      name: '商品类别',
      code: 'item_category_name',
      width: 140,
      features: { sortable: true },
      align: 'left',
    },
    {
      name: '入库日期',
      code: 'audit_time',
      width: columnWidthEnum.TIME,
      features: { sortable: true },
      align: 'left',
      render: (text: string, record: any) => {
        return <span>{dayjs(text).format('YYYY-MM-DD')}</span>;
      },
    },
    {
      name: '采购单位',
      code: 'unit',
      width: 100,
      features: { sortable: true },
      align: 'left',
    },
    {
      name: '单价',
      code: 'price',
      width: 110,
      features: { sortable: true, format: 'MONEY' },
      align: 'right',
    },
    {
      name: '基本单位',
      code: 'basic_unit',
      width: 100,
      features: { sortable: true },
      align: 'left',
    },
    {
      name: '基本单价',
      code: 'basic_price',
      width: 110,
      features: { sortable: true, format: 'MONEY' },
      align: 'right',
    },
    {
      name: '关联采购收货单',
      code: 'fid',
      width: columnWidthEnum.fid,
      features: { sortable: true },
      align: 'left',
    },
  ];

  const formList = [
    {
      label: '组织',
      name: 'org_ids',
      type: 'select',
      multiple: true,
      allowClear: true,
      hidden: !enable_organization,
      selectRequestParams: {
        url: '/erp-mdm/hxl.erp.org.find',
        responseTrans: {
          label: 'name',
          value: 'id',
        },
      },
    },
    {
      label: '门店',
      name: 'store_ids',
      type: 'inputDialog',
      allowClear: true,
      rules: [{ required: true, message: '门店不能为空' }],
      dialogParams: {
        type: 'store',
        dataType: 'lists',
        isLeftColumn: true,
        isMultiple: true,
        data: {
          status: true,
        },
      },

      fieldNames: {
        idKey: 'id',
        nameKey: 'store_name',
      } as any,
    },

    {
      label: '货主',
      name: 'cargo_owner_ids',
      type: 'inputDialog',
      allowClear: true,
      hidden: !enable_cargo_owner,
      dialogParams: {
        type: 'cargoOwner',
        dataType: 'lists',
        isLeftColumn: false,
        isMultiple: true,
        data: {
          owner_type: 'ORGANIZATION',
        },
      },
      fieldNames: {
        idKey: 'id',
        nameKey: 'source_name',
      } as any,
    },
    {
      label: '供应商',
      name: 'supplier_ids',
      type: 'inputDialog',
      allowClear: true,
      dialogParams: {
        type: 'supplier',
        dataType: 'lists',
        isLeftColumn: true,
        isMultiple: true,
        primaryKey: 'id',
        data: {
          enabled: true,
        },
      },
    },

    {
      label: '商品档案',
      name: 'item_ids',
      type: 'inputDialog',
      allowClear: true,

      dialogParams: {
        type: 'goods',
        dataType: 'lists',
        isLeftColumn: true,
        isMultiple: true,
      },
    },

    {
      label: '商品分类',
      name: 'item_category_ids',
      type: 'inputDialog',
      allowClear: true,
      treeModalConfig: {
        title: '选择商品分类', // 标题
        url: '/erp-mdm/hxl.erp.category.find', // 请求地址
        dataType: 'lists',
        checkable: true, // 是否多选
        primaryKey: 'id',
        data: {
          enabled: true,
        },
        width: 360, // 模态框宽度
      },
    },
    {
      label: '截止日期',
      name: 'deadline',
      type: 'datePicker',
      rules: [{ required: true, message: '门店不能为空' }],
      allowClear: false,
      check: true,
    },
  ];
  const [form] = XlbBasicForm.useForm();
  const [exportParams, setExportParams] = useState({});
  const [stateFormList, setStateFormList] = useState(formList);
  const [stateTableColumn, setStateTableColumn] = useState(tableColumn);

  let refresh = () => {};

  const onValuesChange = (e: any) => {
    if (e?.org_ids?.length) {
      stateFormList.find(
        (i) => i.name === 'store_ids',
      )!.dialogParams!.data!.org_ids = e?.org_ids ?? [];
      setStateFormList([...stateFormList]);
      form.setFieldsValue({
        store_ids: [],
      });
    }
  };

  const receiveOrderRender = (item: any) => {
    switch (item.code) {
      case 'item_type':
        item.render = (value: any) => {
          return (
            <div className="info overwidth">
              {goodsType.find((e) => e.value === value)?.label}
            </div>
          );
        };
        break;
      case 'price':
      case 'basic_price':
        item.render = (value: any) => (
          <div className="info overwidth">
            {hasAuth(['采购最新进价/采购价', '查询'])
              ? Number(value).toFixed(2)
              : '****'}
          </div>
        );
        break;
    }
    return item;
  };

  // 导出
  const exportItem = async (requestForm: any, setLoading: Function, e: any) => {
    const data = { ...exportParams };
    setLoading(true);

    const res = await XlbFetch(
      '/erp/hxl.erp.purchasereport.latestprice.export',
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        data: data,
        ...{ responseType: 'blob' },
      },
    );

    setLoading(false);
    const download = new Download();
    download.filename = '采购最新进价.xlsx';
    download.xlsx(res.data);
  };

  //处理查询参数
  const prevPost = (pageInfo: any) => {
    const data = {
      ...form.getFieldsValue(),
      company_id: LStorage.get('userInfo').company_id,
      operator_store_id: LStorage.get('userInfo').store_id,
    };
    setExportParams({ ...data });
    return { ...data };
  };

  const queryData = (e: Event, fetchData: Function) => {
    e.stopPropagation();
    form
      .validateFields()
      .then((res) => {
        fetchData();
      })
      .catch((res) => {
        message.error('请检查查询字段');
      });
  };

  return (
    <XlbPageContainer
      url={'/erp/hxl.erp.purchasereport.latestprice.page'}
      tableColumn={stateTableColumn.map((item) => receiveOrderRender(item))}
      immediatePost={false}
      prevPost={prevPost}
    >
      <SearchForm>
        <XlbForm
          isHideDate
          formList={stateFormList}
          form={form}
          onValuesChange={onValuesChange}
          getFormRecord={refresh}
          initialValues={{
            store_ids: [LStorage.get('userInfo').store_id],
            deadline: dayjs().format('YYYY-MM-DD'),
          }}
        />
      </SearchForm>
      <ToolBtn>
        {({
          fetchData,
          dataSource,
          requestForm,
          loading,
          setLoading,
        }: // setCurrentIndex,
        any) => {
          refresh = fetchData;

          return (
            <XlbButton.Group>
              <XlbButton
                loading={loading}
                label="查询"
                type="primary"
                onClick={(e) => {
                  queryData(e, fetchData);
                }}
                icon={<XlbIcon name="sousuo" color="currentColor" size={16} />}
              />

              {hasAuth(['采购最新进价', '导出']) ? (
                <XlbButton
                  label="导出"
                  type="primary"
                  loading={loading}
                  disabled={loading || !dataSource?.length}
                  onClick={(e) => exportItem(requestForm, setLoading, e)}
                  icon={<XlbIcon size={16} name="daochu" />}
                />
              ) : null}
            </XlbButton.Group>
          );
        }}
      </ToolBtn>
      <Table primaryKey={'id'} />
    </XlbPageContainer>
  );
};

export default PurchaseLatestPrice;

import NiceModal from '@ebay/nice-modal-react';
import type { BaseModalProps } from '@xlb/components';
import {
  XlbBasicForm,
  XlbButton,
  XlbImportModal,
  XlbInput,
  XlbInputDialog,
  XlbModal,
  XlbSelect,
  XlbTable,
  XlbTipsModal,
} from '@xlb/components';
import { cloneDeep } from 'lodash';
import type { FC } from 'react';
import { useEffect, useRef, useState } from 'react';
import { deveveryPriceValues, keyMap } from './data';
import styles from './index.less';
import Api from './server';

interface Props extends Pick<BaseModalProps, 'title'> {
  type: 'add' | 'edit' | 'view';
  initialValues?: any;
  fid?: string;
  onOk: (values: any) => Promise<boolean>;
}

export const Modal: FC<Props> = ({
  title = '弹窗',
  type,
  initialValues,
  fid,
  onOk,
}) => {
  const [form] = XlbBasicForm.useForm();
  const modal = NiceModal.useModal();
  const [rowData, setRowData] = useState<any[]>([{ default_flag: true }]);
  const [centerStore, setCenterStore] = useState<any[]>([]);
  const [shareStore, setShareStore] = useState<any[]>([]);
  const [transitStore, setTransitStore] = useState<any[]>([]);
  const [selectedRowKeys, setSelectedRowKeys] = useState<any[]>([]);
  const [key, setKey] = useState<string>('1');
  const tableRef = useRef<any>(null);
  const centerStoreRef = useRef<any[]>([]);
  const getTreeData = async (store_id: string) => {
    Promise.all([
      await Api.getCenterStore({ store_id }),
      await Api.getShareStore({ store_id }),
    ]).then((res) => {
      if (res.every((i) => i.code === 0)) {
        setCenterStore(
          res[0].data?.map((i: any) => ({
            value: i.id,
            label: i.store_name,
            enable_cargo_owner: i.enable_cargo_owner,
          })),
        );
        centerStoreRef.current = res[0].data?.map((i: any) => ({
          value: i.id,
          label: i.store_name,
          enable_cargo_owner: i.enable_cargo_owner,
        }));
        setShareStore(
          res[1].data?.map((i: any) => ({ value: i.id, label: i.store_name })),
        );
      }
    });
  };

  const handleClick = async () => {
    const res = await XlbImportModal({
      importUrl: `${process.env.BASE_URL}/erp-mdm/hxl.erp.commonstorename.import`,
      templateUrl: `${process.env.BASE_URL}/erp-mdm/hxl.erp.storenametemplate.download`,
    });
    if (res.code === 0) {
      form.setFieldValue('store_ids', res.data?.store_ids);
      setKey(res.data?.store_ids?.join('_') + Math.random().toString());
    }
  };

  const getInfo = async (fid: string) => {
    const res = await Api.getDetails({ store_id: fid });
    if (res.code === 0) {
      setCenterStore(
        res.data?.map((i: any) => ({
          value: i.center_store_id,
          label: i.center_store_name,
        })),
      );
      setShareStore(
        res.data?.map((i: any) => ({
          value: i.share_store_id,
          label: i.share_store_name,
        })),
      );
      const bool = await Api.getCenterStore({
        store_id: form?.getFieldValue('store_ids')?.[0],
      });
      const centerStoreList = bool?.data;
      setRowData(
        res?.data?.map((i: any) => {
          // 查找与 center_store_id 匹配的下拉框项
          const matchedStore = centerStoreList.find(
            (store: any) => store.id === i.center_store_id,
          );

          return {
            default_flag: i.default_flag,
            center_store_id: i.center_store_id,
            center_store_name: i.center_store_name,
            share_store_id: i.share_store_id,
            share_store_name: i.share_store_name,
            transit_store_name: i.transit_store_name,
            transit_store_id: i.transit_store_id,
            delivery_price_val: i.delivery_price_val,
            disabledEnabledCargoOwner:
              matchedStore?.enable_cargo_owner ?? false, // 默认 false
          };
        }),
      );
    }
  };

  const addDetails = () => {
    setRowData([...rowData, { default_flag: false }]);
  };

  const delDetails = () => {
    let flag = false;
    rowData.find((i, y) => {
      if (i.default_flag && selectedRowKeys.includes(y + '')) {
        flag = true;
      }
    });
    if (flag) {
      XlbTipsModal({ tips: '默认配送中心不允许删除' });
      return;
    }
    if (!selectedRowKeys?.length || rowData?.length < 2) {
      return;
    }
    const li = rowData.filter((i, y) => !selectedRowKeys.includes(y + ''));
    setRowData(li);
    setSelectedRowKeys([]);
  };
  const changeFlag = (flag: boolean, index: number) => {
    const updatedRowData = [...rowData];

    if (!flag) {
      const hasOtherDefault = updatedRowData.some(
        (item, idx) => item.default_flag && idx !== index,
      );
      if (!hasOtherDefault) {
        XlbTipsModal({ tips: '请至少保留一个默认配送中心' });
        return;
      }
    }
    if (flag) {
      updatedRowData.forEach((item) => (item.default_flag = false));
    }
    updatedRowData[index].default_flag = flag;
    if (flag) {
      const [selectedItem] = updatedRowData.splice(index, 1);
      updatedRowData.unshift(selectedItem);
    }
    setRowData(updatedRowData);
  };

  // 配送价取值下拉选项
  const getDeliveryPriceValues = (record: any[]) => {
    let deveveryPriceValuesTmp = cloneDeep(deveveryPriceValues);
    const keys = deveveryPriceValues?.map((i: any) => i.value);
    keys.forEach((v) => {
      const curKey = keyMap[v];
      if (!record[curKey]) {
        deveveryPriceValuesTmp = deveveryPriceValuesTmp.filter(
          (i) => i.value !== v,
        );
      }
    });
    return deveveryPriceValuesTmp;
  };

  const getTransitStores = async () => {
    const res = await Api.getTransitStore({
      center_flag: true,
      enable_organization: 0,
    });
    if (res.code == 0) {
      setTransitStore(
        res.data?.content?.map((i: any) => ({
          value: i.id,
          label: i.store_name,
        })),
      );
    }
  };

  const handleClear = (v: string, index: any) => {
    if (rowData[index.index].delivery_price_val == v) {
      rowData[index.index].delivery_price_val = '';
      setRowData([...rowData]);
    }
  };

  useEffect(() => {
    getTransitStores();
    if (fid) {
      if (type !== 'view') {
        form.setFieldsValue({
          store_ids: [fid],
        });
      }
      getInfo(fid);
    }
  }, []);

  return (
    <XlbModal
      width={1050}
      open={modal.visible}
      title={title}
      isCancel={true}
      keyboard={false}
      onOk={async () => {
        if (type === 'view') {
          modal.resolve(true);
          modal.hide();
        } else {
          form.submit();
        }
      }}
      onCancel={() => {
        modal.resolve(false);
        modal.hide();
      }}
    >
      <div
        style={{ display: 'flex', flexDirection: 'column', paddingBottom: 5 }}
      >
        {type !== 'view' ? (
          <XlbBasicForm
            form={form}
            style={{ width: '100%', position: 'relative', marginTop: 16 }}
            labelCol={{ span: 8 }}
            onFinish={async (formValues) => {
              const params = { ...formValues, fid };
              const rowDataSort = rowData.map((i, index) => {
                return {
                  ...i,
                  sort: index + 1,
                };
              });
              params.details = rowDataSort.filter(
                (i) => Object.keys(i)?.length > 0,
              );
              const headerMap = {
                center_store_id: '上游配送中心',
                delivery_price_val: '配送价取值',
              };
              console.log(rowData, 'rowData====>');

              const incompleteFieldsList = rowData
                .map((item: any, index: number) => {
                  const missingFields = [];
                  if (!item.center_store_id && !item.disabledEnabledCargoOwner)
                    missingFields.push(headerMap.center_store_id);
                  if (!item.delivery_price_val)
                    missingFields.push(headerMap.delivery_price_val);

                  return { ...item, rowIndex: index + 1, missingFields };
                })
                .filter((item: any) => item.missingFields.length > 0);
              console.log(incompleteFieldsList, 'incompleteFieldsList===>');

              if (incompleteFieldsList.length) {
                XlbTipsModal({
                  tips: (
                    <>
                      <div>以下数据未填写完整，请检查！</div>
                      <div>
                        {incompleteFieldsList?.map((v) => {
                          return (
                            <div>
                              {`【第${v.rowIndex}行】缺少：${v.missingFields.join('、')}`}
                            </div>
                          );
                        })}
                      </div>
                    </>
                  ),
                });
                return;
              }
              // 检查第一项是否是默认
              const hasDefault = rowDataSort.find((v) => v.default_flag);
              //重复上游配送中心校验
              const nameLinesMap = {};

              rowData.forEach((item, index) => {
                const name = item.center_store_name;
                if (!nameLinesMap[name]) {
                  nameLinesMap[name] = [];
                }
                nameLinesMap[name].push(index + 1); // 从1开始计数行号
              });

              // 检查重复的行号
              const duplicates = [];
              for (const name in nameLinesMap) {
                const lines = nameLinesMap[name];
                if (lines.length > 1) {
                  duplicates.push({ name, lines });
                }
              }
              if (duplicates.length) {
                XlbTipsModal({
                  tips: (
                    <>
                      <div>以下数据重复，请检查！</div>
                      <div>
                        {duplicates?.map((v) => {
                          return (
                            <div>
                              {`上游配送中心${v.name}" 在【第${v.lines.join(', ')}行】重复`}
                            </div>
                          );
                        })}
                      </div>
                    </>
                  ),
                });
                return;
              }
              modal.resolve(params);
              // modal.hide()

              const success = await onOk(params);

              // 只有保存成功才关闭弹窗
              if (success) {
                modal.hide();
              }
            }}
            initialValues={initialValues}
          >
            <XlbBasicForm.Item
              name="store_ids"
              label="设置门店"
              rules={[{ required: true, message: '门店不能为空' }]}
            >
              <XlbInputDialog
                dialogParams={{
                  type: 'store',
                  dataType: 'lists',
                  data: {
                    center_flag: false,
                  },
                  isMultiple: true,
                  onOkBeforeFunction: (_val: any, data: any[]) => {
                    let flag = true;
                    data?.reduce((pre, cur) => {
                      if (
                        Object.keys(pre)?.length &&
                        pre?.org_id !== cur?.org_id
                      ) {
                        flag = false;
                      }
                      return cur;
                    }, {});
                    if (!flag) {
                      XlbTipsModal({
                        tips: '请选择同一组织下的门店',
                        zIndex: 2111,
                      });
                    }
                    return flag;
                  },
                }}
                key={key}
                fieldNames={{
                  idKey: 'id',
                  nameKey: 'store_name',
                }}
                style={{ width: 150 }}
                onChange={(val: any, option: any, form: any) => {
                  if (val?.length) {
                    form.setFieldsValue({
                      org_name: option[0]?.org_name,
                    });
                    getTreeData(val[0]);
                  }
                }}
                disabled={!!fid}
              />
            </XlbBasicForm.Item>
            {!fid ? (
              <XlbBasicForm.Item noStyle>
                <XlbButton className={styles.importBtn} onClick={handleClick}>
                  导入
                </XlbButton>
              </XlbBasicForm.Item>
            ) : null}
            <XlbBasicForm.Item
              name="org_name"
              style={{ marginLeft: 50 }}
              label="组织"
            >
              <XlbInput style={{ width: 150 }} disabled />
            </XlbBasicForm.Item>
          </XlbBasicForm>
        ) : null}
        {type !== 'view' ? <div>设置配送中心</div> : null}
        {type !== 'view' ? (
          <XlbButton.Group>
            <XlbButton
              style={{ marginTop: 10, marginBottom: 10 }}
              type="primary"
              onClick={addDetails}
            >
              新增
            </XlbButton>
            <XlbButton
              type="primary"
              onClick={delDetails}
              disabled={!selectedRowKeys?.length}
            >
              删除
            </XlbButton>
          </XlbButton.Group>
        ) : null}
        <XlbTable
          ref={tableRef}
          dragSort={true}
          dragSortMode="move"
          onDragSortEnd={(dataSource) => {
            setRowData(dataSource);
          }}
          style={{ flex: 1, minHeight: 285, maxHeight: 285, overflowY: 'auto' }}
          columns={[
            { name: '序号', code: '_index', width: 50, align: 'center' },
            {
              name: '上游配送中心',
              code: 'center_store_id',
              width: 200,
              render: (text, record, index) => {
                return (
                  <div onClick={(e) => e.stopPropagation()}>
                    <XlbSelect
                      style={{ width: 180 }}
                      options={centerStore ?? []}
                      showSearch
                      filterOption={(input, option) => {
                        return (
                          (
                            `${option!.label ? option!.label.toString() : ''}` as unknown as string
                          )
                            .toLowerCase()
                            .includes(input.toLowerCase()) ||
                          (
                            `${option!.value ? option!.value.toString() : ''}` as unknown as string
                          )
                            .toLowerCase()
                            .includes(input.toLowerCase())
                        );
                      }}
                      disabled={type === 'view'}
                      allowClear={false}
                      value={text}
                      onChange={(value, option: any) => {
                        if (type == 'add' && !record?.delivery_price_val) {
                          record.delivery_price_val = 'UPSTREAM_DELIVERY_PRICE';
                        }
                        record.disabledEnabledCargoOwner =
                          option?.enable_cargo_owner;
                        rowData[index.index].center_store_id = value;
                        rowData[index.index].center_store_name = option?.label;
                        rowData[index.index].transit_store_id = '';
                        rowData[index.index].share_store_id = '';

                        setRowData([...rowData]);
                      }}
                    />
                  </div>
                );
              },
            },
            {
              name: '中转配送中心',
              code: 'transit_store_id',
              width: 200,
              render: (text, record, index) => {
                return (
                  <div onClick={(e) => e.stopPropagation()}>
                    <XlbSelect
                      style={{ width: 180 }}
                      options={transitStore ?? []}
                      showSearch
                      filterOption={(input, option) => {
                        return (
                          (
                            `${option!.label ? option!.label.toString() : ''}` as unknown as string
                          )
                            .toLowerCase()
                            .includes(input.toLowerCase()) ||
                          (
                            `${option!.value ? option!.value.toString() : ''}` as unknown as string
                          )
                            .toLowerCase()
                            .includes(input.toLowerCase())
                        );
                      }}
                      disabled={
                        type === 'view' || record?.disabledEnabledCargoOwner
                      }
                      allowClear={true}
                      onClear={() => {
                        handleClear('TRANSIT_DELIVERY_PRICE', index);
                      }}
                      value={text}
                      onChange={(value, option: any) => {
                        rowData[index.index].transit_store_id = value;
                        rowData[index.index].transit_store_name = option?.label;
                        rowData[index.index].share_store_id = '';
                        rowData[index.index].share_store_name = '';
                        handleClear('SHARE_DELIVERY_PRICE', index);
                        setRowData([...rowData]);
                      }}
                    />
                  </div>
                );
              },
            },
            {
              name: '是否默认',
              code: 'default_flag',
              width: 112,
              render: (text, record, index) => {
                return (
                  <div onClick={(e) => e.stopPropagation()}>
                    <XlbSelect
                      style={{ width: 95 }}
                      options={[
                        { label: '是', value: true },
                        { label: '否', value: false },
                      ]}
                      disabled={type === 'view'}
                      allowClear={false}
                      value={text}
                      onChange={(val) => changeFlag(val, index.index)}
                    />
                  </div>
                );
              },
            },
            {
              name: '共享配送中心',
              code: 'share_store_id',
              width: 200,
              render: (text, record, index) => {
                return (
                  <div onClick={(e) => e.stopPropagation()}>
                    <XlbSelect
                      style={{ width: 180 }}
                      options={shareStore ?? []}
                      showSearch
                      filterOption={(input, option) => {
                        return (
                          (
                            `${option!.label ? option!.label.toString() : ''}` as unknown as string
                          )
                            .toLowerCase()
                            .includes(input.toLowerCase()) ||
                          (
                            `${option!.value ? option!.value.toString() : ''}` as unknown as string
                          )
                            .toLowerCase()
                            .includes(input.toLowerCase())
                        );
                      }}
                      disabled={
                        type === 'view' || record?.disabledEnabledCargoOwner
                      }
                      allowClear={true}
                      onClear={() => {
                        handleClear('SHARE_DELIVERY_PRICE', index);
                      }}
                      value={text}
                      onChange={(value, option: any) => {
                        rowData[index.index].share_store_id = value;
                        rowData[index.index].share_store_name = option?.label;
                        setRowData([...rowData]);
                      }}
                    />
                  </div>
                );
              },
            },
            {
              name: '配送价取值',
              code: 'delivery_price_val',
              width: 200,
              render: (text, record, index) => {
                return (
                  <div onClick={(e) => e.stopPropagation()}>
                    <XlbSelect
                      style={{ width: 180 }}
                      options={getDeliveryPriceValues(record) ?? []}
                      showSearch
                      disabled={type === 'view'}
                      allowClear={false}
                      value={text}
                      onChange={(value, option: any) => {
                        rowData[index.index].delivery_price_val = value;
                        // rowData[index.index].delivery_price_name = option?.label
                        setRowData([...rowData]);
                      }}
                    />
                  </div>
                );
              },
            },
          ]}
          dataSource={rowData}
          showSizeChanger={false}
          total={rowData?.length ?? 0}
          selectMode="multiple"
          disabled={type === 'view'}
          selectedRowKeys={selectedRowKeys}
          onSelectRow={setSelectedRowKeys}
        />
      </div>
    </XlbModal>
  );
};

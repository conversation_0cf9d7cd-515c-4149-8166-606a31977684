import { XlbFetch } from '@xlb/utils';

// 物资进出单
export const basketorder = async (data: any) => {
  return await XlbFetch.post('/erp/hxl.erp.basketorder.read', data);
};

// 采购收货单
export const readReceiveOrder = async (data: any) => {
  return await XlbFetch.post('/erp/hxl.erp.receiveorder.read', data);
};

//采购退货单
export const readReturnOrder = async (data: any) => {
  return await XlbFetch.post('/erp/hxl.erp.returnorder.read', data);
};

//调入单
export const readInfoIN = async (data: any) => {
  return await XlbFetch.post('/erp/hxl.erp.deliveryinorder.read', data);
};

// 调出单
export const readInfoOut = async (data: any) => {
  return await XlbFetch.post('/erp/hxl.erp.deliveryoutorder.read', data);
};

// 批发销售单
export const getWholeSale = async (data: any) => {
  return await XlbFetch.post('/erp/hxl.erp.wholesaleorder.read', data);
};

// 批发销售单-获取制单批发参数
export const getBuyparam = async (data: any) => {
  return await XlbFetch.post('/erp/hxl.erp.wholesaleparam.read', data);
};

// 批发退货单
export const getWholeReturn = async (data: any) => {
  return await XlbFetch.post('/erp/hxl.erp.wholesalereturnorder.read', data);
};

// 库存盘点单
export const readStockCheckOrder = async (data: any) => {
  return await XlbFetch.post('/erp/hxl.erp.stockcheckorder.read', data);
};

// 库存调整单
export const stockReadInfo = async (data: any) => {
  return await XlbFetch.post('/erp/hxl.erp.stockadjustorder.read', data);
};

// 同店转仓单
export const stockTransFormOrderReadInfo = async (data: any) => {
  return await XlbFetch.post('/erp/hxl.erp.stocktransformorder.read', data);
};

// 库存成本调整单
export const stockPickChangeReadInfo = async (data: any) => {
  return await XlbFetch.post('/erp/hxl.erp.stockcostadjustorder.read', data);
};

// 前台销售单
export const PostOrderReadInfo = async (data: any) => {
  return await XlbFetch.post(
    '/retail/hxl.retail.posorder.readbyserialnumber',
    data,
  );
};

//领用进出单
export const readInfoCol = async (data: any) => {
  return await XlbFetch.post('/erp/hxl.erp.requisitioninoutorder.read', data);
};

import { exportPage } from '@/services/system';
import { hasAuth } from '@/utils';
import Download from '@/utils/downloadBlobFile';
import { useIRouter } from '@/wujie/utils';
import {
  XlbButton,
  XlbForm,
  XlbIcon,
  XlbModal,
  XlbPageContainer,
  XlbTipsModal,
} from '@xlb/components';
import { Form, Image, message } from 'antd';
import dayjs from 'dayjs';
import moment from 'moment';
import { useState } from 'react';
import { formList, statusType, tableColumn } from './data';
import Api from './server';

const { ToolBtn, Table, SearchForm } = XlbPageContainer;

const PurchasePriceIndex = () => {
  const [form] = Form.useForm<any>();
  const { navigate } = useIRouter();
  const [photolist, setPhotoList] = useState([]);
  const [modalOpen, setModalOpen] = useState(false);

  const render = (item: any) => {
    switch (item.code) {
      case 'state':
        item.render = (value: any, record: any, index: any) => {
          const item = statusType.find((v) => v.value === value);
          return (
            <div className={`overwidth ${item ? item.type : ''}`}>
              {item ? item.label : ''}
            </div>
          );
        };
        break;
      case 'fid':
        item.render = (value: any, record: any, index: number) => {
          return (
            <div
              className="link overwidth cursors"
              onClick={(e) => {
                e.stopPropagation();
                console.log('????', record.fid);
                navigate('/xlb_erp/PurchasePrice/item', { id: record.fid });
              }}
            >
              {value}
            </div>
          );
        };
        break;

      case 'files':
        item.render = (value: any, record: any) => {
          return (
            <div className="info overwidth">
              <span
                className="link cursors"
                onClick={(e) => {
                  e.stopPropagation();
                  setModalOpen(true);
                  setPhotoList(value);
                  // await NiceModal.show(XlbNewUploadFileWithModal, {
                  //   action: '',
                  //   fileList: record?.files || [],
                  //   showUpload: false,
                  //   disabled: true,
                  //   isCancel: false,
                  //   listType: 'picture',
                  // });
                }}
              >
                查看({record?.files?.length || 0})
              </span>
            </div>
          );
        };
        break;
      case 'statistic_time':
        item.render = (value: any, record: any, index: any) => {
          return (
            <div className="info overwidth">
              {value ? moment(value).format('MM-DD hh:mm') : ''}
            </div>
          );
        };
        break;
      case 'contrast_item_price':
      case 'item_price':
        item.render = (value: any, record: any, index: any) => {
          return (
            <div className="info overwidth">
              {hasAuth(['采价计划/零售价', '查询'])
                ? value
                  ? Number(value).toFixed(4)
                  : '0.0000'
                : '****'}
            </div>
          );
        };
        break;
      default:
        return (item.render = (value: any) => (
          <div className="info overwidth">{value}</div>
        ));
    }
  };

  // 提交参数
  const prevPost = () => {
    const values = form.getFieldsValue(true);
    return {
      ...values,
      item_brand_id: values.item_brand_id?.[0] || null,
    };
  };

  //导出
  const exportData = async (requestForm: any, setLoading: Function) => {
    setLoading(true);
    const res = await exportPage(
      '/erp/hxl.erp.priceorder.export',
      { ...requestForm },
      { responseType: 'blob' },
    );
    const download = new Download();
    download.filename = '采价报表导出.xlsx';
    download.xlsx(res?.data);
    setLoading(false);
  };

  //删除
  const deleteRow = async (
    dataSource: any[],
    selectRowKeys: React.Key[],
    fetchData: Function,
  ) => {
    await XlbTipsModal({
      tips: `是否确认删除${selectRowKeys?.length}条数据`,
      onOk: async () => {
        const { code } = await Api.deleteItems({
          fids: selectRowKeys,
        });
        if (code === 0) {
          message.success(`已删除${selectRowKeys?.length}条数据`);
          fetchData();
        }
        return true;
      },
    });
  };

  tableColumn.map((v) => render(v));

  const photoRender = () => {
    return (
      <div
        style={{
          width: '100%',
          display: 'flex',
          flexWrap: 'wrap',
          justifyContent: 'left',
          alignItems: 'center',
          minHeight: '200px',
        }}
      >
        {photolist?.length
          ? photolist.map((e: any) => {
              return (
                <div style={{ display: 'inline-block', marginLeft: '12px' }}>
                  <Image src={e.url} width={65} key={e.id} />
                </div>
              );
            })
          : null}
      </div>
    );
  };

  return (
    <XlbPageContainer
      url={'/erp/hxl.erp.priceorder.page'}
      tableColumn={tableColumn}
      immediatePost
      prevPost={prevPost}
    >
      <XlbModal
        open={modalOpen}
        width={600}
        title={'附件'}
        isCancel={false}
        keyboard={false}
        onOk={() => {
          setModalOpen(false);
        }}
        onCancel={() => {
          setModalOpen(false);
        }}
      >
        {photoRender()}
      </XlbModal>
      <SearchForm>
        <XlbForm
          formList={formList}
          form={form}
          isHideDate
          initialValues={{
            date: [dayjs().format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')],
          }}
        />
      </SearchForm>
      <ToolBtn>
        {({
          dataSource,
          fetchData,
          selectRowKeys,
          loading,
          setLoading,
          requestForm,
          setDataSource,
        }: any) => {
          return (
            <XlbButton.Group>
              {hasAuth(['采价计划', '查询']) ? (
                <XlbButton
                  key="query"
                  label="查询"
                  loading={loading}
                  type="primary"
                  onClick={() => {
                    fetchData();
                  }}
                  icon={
                    <XlbIcon name="sousuo" color="currentColor" size={16} />
                  }
                />
              ) : null}

              {hasAuth(['采价计划', '删除']) ? (
                <XlbButton
                  key="delete"
                  label="删除"
                  loading={loading}
                  type="primary"
                  disabled={!selectRowKeys?.length}
                  onClick={() =>
                    deleteRow(dataSource, selectRowKeys, fetchData)
                  }
                  icon={
                    <XlbIcon name="shanchu" color="currentColor" size={16} />
                  }
                />
              ) : null}

              {hasAuth(['采价计划', '导出']) ? (
                <XlbButton
                  key="exports"
                  label="导出"
                  type="primary"
                  loading={loading}
                  disabled={!dataSource?.length}
                  onClick={() => exportData(requestForm, setLoading)}
                  icon={
                    <XlbIcon name="daochu" color="currentColor" size={16} />
                  }
                />
              ) : null}
            </XlbButton.Group>
          );
        }}
      </ToolBtn>
      <Table primaryKey="fid" selectMode="multiple" />
    </XlbPageContainer>
  );
};

export default PurchasePriceIndex;

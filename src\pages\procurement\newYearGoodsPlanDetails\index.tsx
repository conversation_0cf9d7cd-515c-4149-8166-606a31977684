import { ErpFieldKeyMap } from '@/data/common/fieldListConfig'
import type { XlbProDetailProps, XlbTableColumnProps } from '@xlb/components'
import { XlbProPageContainer } from '@xlb/components'
import dayjs from 'dayjs'
import type { FC } from 'react'
import type { PurchasePlanResDTO } from '../newYearGoodsPlan/type'
import Total from './components/total'
import styles from './index.less'
import { hasAuth } from '@/utils'

const tableColumn: XlbTableColumnProps<any>[] = [
  {
    code: '_index',
    name: '序号'
  },
  {
    code: 'org_name',
    name: '所属组织',
    features: { sortable: true }
  },
  {
    code: 'store_name',
    name: '所属门店',
    features: { sortable: true }
  },
  {
    code: 'supplier_name',
    name: '供应商',
    features: { sortable: true }
  },
  {
    code: 'item_name',
    name: '商品名称',
    features: { sortable: true }
  },
  {
    code: 'item_code',
    name: '商品代码',
    features: { sortable: true }
  },
  {
    code: 'item_barcode',
    name: '商品条码',
    features: { sortable: true }
  },
  {
    code: 'item_spec',
    name: '采购规格',
    features: { sortable: true }
  },
  {
    code: 'purchase_unit',
    name: '单位',
    features: { sortable: true }
  },
  {
    code: 'purchaser',
    name: '订单员',
    features: { sortable: true }
  },
  {
    code: 'quantity',
    name: '采购计划',
    features: { sortable: true, format: 'QUANTITY' },
    align: 'right'
  },
  {
    code: 'money',
    name: '金额（元）',
    features: { sortable: true, format: 'MONEY' },
    align: 'right'
  },
  {
    code: 'create_by',
    name: '创建人',
    features: { sortable: true }
  },
  {
    code: 'create_time',
    name: '创建时间',
    features: { sortable: true, format: 'TIME' }
  }
]

const detailsFormList: XlbProDetailProps['formList'] = [
  {
    componentType: 'form',
    fieldProps: {
      formList: [
        {
          id: ErpFieldKeyMap.erpPurchase,
          label: '所属组织',
          rules: [{ required: true, message: '所属组织必填' }],
          required: true
        },
        {
          id: ErpFieldKeyMap.erpStoreIdName,
          required: true,
          rules: [{ required: true, message: '所属门店必填' }],
          label: '所属门店'
        },
        {
          id: ErpFieldKeyMap.erpSupplierId,
          required: true,
          rules: [{ required: true, message: '供应商必填' }],
          label: '供应商'
        },
        {
          id: ErpFieldKeyMap.erpItem,
          required: true,
          rules: [{ required: true, message: '商品必填' }],
          label: '商品'
        },
        {
          id: ErpFieldKeyMap.erpHiddenItemId
        },
        {
          id: ErpFieldKeyMap.purchasePlanBuyNumber,
          required: true,
          rules: [{ required: true, message: '采购计划必填' }],
          label: '采购计划'
        },
        {
          id: ErpFieldKeyMap.purchasePlanMoney,
          required: true,
          rules: [{ required: true, message: '金额必填' }],
          label: '金额（元）'
        },

        {
          id: ErpFieldKeyMap.purchaseManager,
          name: 'purchaser_id',
          required: true,
          rules: [{ required: true, message: '订单员必填' }],
          label: '订单员',
          onChange: (e, form, op) => {
            const target = op?.[0]
            form?.setFieldValue('purchaser', target?.name)
          }
        }
      ]
    }
  }
]

interface NewYearGoodsPlanDetailsProps {
  /**
   * 行详情
   */
  rowData?: PurchasePlanResDTO
}

const NewYearGoodsPlanDetails: FC<NewYearGoodsPlanDetailsProps> = ({ rowData }) => {
  const { id } = rowData || {}

  if (!id) {
    console.error('id必传')
    return null
  }

  return (
    <div style={{ height: 500 }} className={styles.newYearGoodsPlanDetails}>
      <XlbProPageContainer
        searchFieldProps={{
          order: 0,
          initialValues: {
            create_date: [
              dayjs().format('YYYY-MM-DD HH:mm:ss'),
              dayjs().format('YYYY-MM-DD HH:mm:ss')
            ],
            purchase_plan_id: id
          },
          formList: [
            {
              id: ErpFieldKeyMap.purchasePlanOrg,
              label: '所属组织'
            },
            {
              id: ErpFieldKeyMap.erpStoreIds,
              label: '所属门店'
            },
            {
              id: ErpFieldKeyMap.erpSupplierId,
              label: '供应商'
            },
            {
              id: ErpFieldKeyMap.erpItemIds,
              label: '商品'
            },
            {
              id: 'commonInput',
              name: 'purchase',
              label: '订单员'
            }
          ]
        }}
        extraNoStyle={() => {
          return <Total id={id} />
        }}
        addFieldProps={{
          url: hasAuth(['采购计划', '编辑']) ? '/erp/hxl.erp.purchaseplandetail.save' : '',
          order: 10,
          beforePost: (formValues) => {
            return {
              ...formValues,
              store_id: formValues?.store_id ? formValues?.store_id?.[0] : null
            }
          }
        }}
        uploadFieldProps={{
          order: 25,
          url: hasAuth(['采购计划', '导入']) ? '/erp/hxl.erp.purchaseplandetail.import' : '',
          templateUrl: '/erp/hxl.erp.purchaseplandetail.template.download',
          params: {
            purchase_plan_id: id
          }
        }}
        details={{
          hiddenSaveBtn: true,
          primaryKey: 'id',
          initialValues: {
            purchase_plan_id: id
          },
          saveFieldProps: {
            url: hasAuth(['采购计划', '编辑']) ? '/erp/hxl.erp.purchaseplandetail.save' : '',
            beforePost: (formValues) => {
              return {
                ...formValues,
                store_id: formValues?.store_id ? formValues?.store_id?.[0] : null
              }
            }
          },
          mode: 'modal',
          isCancel: true,
          title: (formValues?: Record<any, any>) => (formValues?.id ? '编辑' : '新增'),
          width: 500,
          itemSpan: 24,
          formList: detailsFormList
        }}
        exportFieldProps={{
          url: hasAuth(['采购计划', '导出']) ? '/erp/hxl.erp.purchaseplandetail.export' : '',
          fileName: '采购计划明细.xlsx',
          order: 30
        }}
        tableFieldProps={{
          url: '/erp/hxl.erp.purchaseplandetail.page',
          tableColumn: tableColumn,
          immediatePost: true,
          selectMode: 'single'
        }}
      ></XlbProPageContainer>
    </div>
  )
}

export default NewYearGoodsPlanDetails

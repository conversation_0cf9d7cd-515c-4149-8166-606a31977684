import { columnWidthEnum } from '@/data/common/constant';
import { SearchFormType, XlbTableColumnProps } from '@xlb/components';

export const queryUnit = [
  {
    label: '单据单位',
    value: 'ORDER',
  },
  {
    label: '配送单位',
    value: 'DELIVERY',
  },
  {
    label: '库存单位',
    value: 'STOCK',
  },
  {
    label: '采购单位',
    value: 'PURCHASE',
  },
  {
    label: '基本单位',
    value: 'BASIC',
  },
  {
    label: '批发单位',
    value: 'WHOLESALE',
  },
];

// 结算状态
export const settlementState = [
  {
    label: '未结算',
    value: 'UNPAY',
    type: 'danger',
  },
  {
    label: '部分结算',
    value: 'PARTPAY',
    type: 'info',
  },
  {
    label: '已结算',
    value: 'ALLPAY',
    type: 'success',
  },
];

export const formListData: SearchFormType[] = [
  {
    type: 'compactDatePicker',
    label: '日期选择',
    name: 'compactDatePicker',
    allowClear: false,
    format: 'YYYY-MM-DD',
    width: 392,
  },
  {
    label: '时间类型',
    name: 'time_type',
    type: 'select',
    allowClear: false,
    check: true,
    options: [
      {
        label: '制单时间',
        value: 'create_date',
      },
      {
        label: '审核时间',
        value: 'audit_date',
      },
      {
        label: '销售日期',
        value: 'operate_date',
      },
      {
        label: '签收日期',
        value: 'receive_date',
      },
    ],
  },
  {
    label: '发货组织',
    name: 'org_ids',
    type: 'select',
    hidden: true,
    check: true,
    multiple: true,
    options: [],
  },
  {
    label: '发货门店',
    name: 'store_ids',
    type: 'inputDialog',
    allowClear: false,
    dialogParams: {
      type: 'store',
      dataType: 'lists',
      isLeftColumn: true,
      isMultiple: true,
      data: {
        status: true,
      },
    },
    fieldNames: {
      // @ts-ignore
      idKey: 'id',
      nameKey: 'store_name',
    },
  },
  {
    label: '发货仓库',
    name: 'storehouse_ids',
    type: 'select',
    multiple: false,
    dependencies: ['store_ids'],
    disabled: (form: any) => {
      const store_ids = form.getFieldValue('store_ids');
      return !store_ids || store_ids?.length > 1;
    },
    handleDefaultValue: (data: any, formData: any) => {
      if (data?.length === 0) {
        return null;
      }
      const defaultStoreHouse =
        data.find((item: any) => item.default_flag) || data[0];
      return defaultStoreHouse?.value;
    },
    // @ts-ignore
    selectRequestParams: (params: any, form) => {
      form?.setFieldsValue({
        storehouse_ids: null,
      });
      if (params?.store_ids?.length === 1) {
        return {
          url: '/erp/hxl.erp.storehouse.store.find',
          postParams: {
            store_id: params?.store_ids?.[0],
          },
          responseTrans(data: any) {
            const options = data.map((item: any) => ({
              label: item.name,
              value: item.id,
              default_flag: item.default_flag,
            }));
            return options;
          },
        };
      }
    },
  },
  {
    label: '批发客户',
    name: 'client_ids',
    type: 'inputDialog',
    check: true,
    dialogParams: {
      type: 'wholesaler',
      dataType: 'lists',
      isLeftColumn: true,
      isMultiple: true,
      primaryKey: 'id',
      data: {
        enabled: true,
      },
    },
  },
  {
    label: '商品档案',
    name: 'item_ids',
    type: 'inputDialog',
    dialogParams: {
      type: 'goods',
      dataType: 'lists',
      isLeftColumn: true,
      isMultiple: true,
      primaryKey: 'id',
      data: {
        enabled: true,
      },
    },
  },
  {
    label: '商品类别',
    name: 'item_category_ids',
    type: 'inputDialog',
    treeModalConfig: {
      title: '选择商品分类', // 标题
      url: '/erp/hxl.erp.category.find', // 请求地址
      dataType: 'lists',
      checkable: true, // 是否多选
      primaryKey: 'id',
      data: {
        enabled: true,
      },
    },
  },
  {
    label: '查询单位',
    name: 'unit_type',
    type: 'select',
    allowClear: false,
    check: true,
    options: queryUnit,
  },
  {
    label: '汇总条件',
    name: 'summary_types',
    type: 'select',
    allowClear: false,
    check: true,
    multiple: true,
    initialValue: ['STORE'],
    options: [
      {
        label: '发货门店',
        value: 'STORE',
      },
      {
        label: '批发客户',
        value: 'CLIENT',
      },
      {
        label: '商品类别',
        value: 'CATEGORY',
      },
      {
        label: '商品档案',
        value: 'ITEM',
      },
      {
        label: '批发日期',
        value: 'WHOLESALE_DATE',
      },
      {
        label: '业财核算分类',
        value: 'FINANCECODE',
      },
      {
        label: '发货组织',
        value: 'DELIVERY_ORG',
      },
    ],
  },
  {
    label: '结算状态',
    name: 'settlement_state',
    type: 'select',
    check: true,
    options: settlementState,
  },
  // {
  //   label: '类别等级',
  //   name: 'category_level',
  //   type: 'select',
  //   check: true,
  //   hidden: true,
  //   options: [
  //     {
  //       label: '按当前类别',
  //       value: 0,
  //     },
  //     {
  //       label: '按顶级类别',
  //       value: 1,
  //     },
  //   ],
  // },
];

export const basicTable: XlbTableColumnProps<any>[] = [
  {
    name: '销售数量',
    code: 'quantity',
    width: 140,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '销售单价',
    code: 'price',
    width: 140,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '销售单价（去税）',
    code: 'no_tax_price',
    width: 160,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '销售金额',
    code: 'money',
    align: 'right',
    width: 140,
    features: { sortable: true },
  },

  {
    name: '销售金额（去税）',
    code: 'no_tax_money',
    width: 160,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '销售税额',
    code: 'tax_money',
    width: 140,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '销售税率',
    code: 'tax_rate',
    width: 100,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '销售成本单价',
    code: 'cost_price',
    width: 150,
    align: 'right',
    features: { sortable: true },
  },

  {
    name: '销售成本',
    code: 'cost_money',
    width: 130,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '销售成本（去税）',
    code: 'no_tax_cost_money',
    width: 160,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '销售成本（税额）',
    code: 'cost_tax_money',
    width: 160,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '时点销售成本',
    code: 'original_cost_money',
    width: 150,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '时点销售成本（去税）',
    code: 'original_no_tax_cost_money',
    width: 180,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '退货数量',
    code: 'return_quantity',
    width: 130,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '退货基本数量',
    code: 'basic_return_quantity',
    width: 130,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '退货金额',
    code: 'return_money',
    width: 140,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '退货金额（去税）',
    code: 'return_no_tax_money',
    width: 160,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '退货单价（含税）',
    code: 'return_price',
    width: 160,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '退货单价（去税）',
    code: 'return_no_tax_price',
    width: 160,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '退货成本单价',
    code: 'return_cost_price',
    width: 150,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '退货税额',
    code: 'return_tax_money',
    width: 140,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '退货成本',
    code: 'return_cost_money',
    width: 130,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '退货成本（去税）',
    code: 'return_no_tax_cost_money',
    width: 160,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '退货成本（税额）',
    code: 'return_cost_tax_money',
    width: 160,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '时点退货成本',
    code: 'return_original_cost_money',
    width: 150,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '时点退货成本（去税）',
    code: 'return_original_no_tax_cost_money',
    width: 180,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '金额合计',
    code: 'money_total',
    width: 150,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '金额合计（去税）',
    code: 'no_tax_money_total',
    width: 160,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '金额合计（税额）',
    code: 'tax_money_total',
    width: 160,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '成本合计',
    code: 'cost_money_total',
    width: 140,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '成本合计（去税）',
    code: 'no_tax_cost_money_total',
    width: 160,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '成本合计（税额）',
    code: 'cost_tax_money_total',
    width: 160,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '毛利',
    code: 'wholesale_gross_profit',
    width: 130,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '毛利（去税）',
    code: 'no_tax_wholesale_gross_profit',
    width: 140,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '毛利（税额）',
    code: 'wholesale_gross_profit_tax_money',
    width: 140,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '毛利率',
    code: 'wholesale_gross_profit_rate',
    width: 150,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '毛利率(去税)',
    code: 'no_tax_wholesale_gross_profit_rate',
    width: 150,
    align: 'right',
    features: { sortable: true },
  },
];

export const outStore: XlbTableColumnProps<any>[] = [
  {
    name: '发货门店',
    code: 'store_name',
    width: columnWidthEnum.STORE_NAME,
    features: { sortable: true },
  },
];

export const inStore: XlbTableColumnProps<any>[] = [
  {
    name: '批发客户',
    code: 'client_name',
    width: 150,
    features: { sortable: true },
  },
];

export const categoryName: XlbTableColumnProps<any>[] = [
  {
    name: '商品类别',
    code: 'item_category_name',
    width: 150,
    features: { sortable: true },
  },
];
export const delivery_org_name: XlbTableColumnProps<any>[] = [
  {
    name: '货主',
    code: 'delivery_org_name',
    width: 150,
    features: { sortable: true },
  },
];

export const financeName: XlbTableColumnProps<any>[] = [
  {
    name: '业财核算分类',
    code: 'finance_name',
    width: 150,
    features: { sortable: true },
  },
];

export const wholesaleDate: XlbTableColumnProps<any>[] = [
  {
    name: '批发日期',
    code: 'wholesale_date',
    width: 150,
    features: { sortable: true },
  },
];
// 商品档案
export const itemTable: XlbTableColumnProps<any>[] = [
  {
    name: '商品代码',
    code: 'item_code',
    width: columnWidthEnum.ITEM_CODE,
    features: { sortable: true },
  },
  {
    name: '商品条码',
    code: 'item_bar_code',
    width: columnWidthEnum.ITEM_BAR_CODE,
    features: { sortable: true },
  },
  {
    name: '商品名称',
    code: 'item_name',
    width: 150,
    features: { sortable: true },
  },
  {
    name: '商品规格',
    code: 'item_spec',
    width: columnWidthEnum.ITEM_SPEC,
    features: { sortable: true },
  },
];

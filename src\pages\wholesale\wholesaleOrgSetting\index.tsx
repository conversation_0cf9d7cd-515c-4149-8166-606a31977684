import { ErpFieldKeyMap } from '@/data/common/fieldListConfig'
import { hasAuth } from '@/utils'
import Download from '@/utils/downloadBlobFile'
import NiceModal from '@ebay/nice-modal-react'
import type { ContextState, XlbTableColumnProps } from '@xlb/components'
import {
  XlbBasicForm,
  XlbButton,
  XlbCheckbox,
  XlbIcon,
  XlbImportModal,
  XlbProPageContainer,
  XlbTipsModal
} from '@xlb/components'
import { DataType } from '@xlb/components/dist/components/XlbTree/type'
import { message } from 'antd'
import { type FC } from 'react'
import { Modal } from './modal'
import Api from './server'
import { wholesalePriceValues } from './data'
const ProForm: FC<{ title: string }> = () => {
  const tableList: XlbTableColumnProps<any>[] = [
    {
      name: '序号',
      code: '_index',
      width: 50,
      lock: true,
      align: 'center'
    },
    {
      name: '所属组织',
      code: 'org_name',
      width: 140,
      hidden: true
    },
    {
      name: '批发客户代码',
      code: 'client_code',
      width: 160,
      features: { sortable: true },
      align: 'left',
      render: (text: any, record: any) => (
        <div
          className="cursor link"
          onClick={(e) => {
            e.stopPropagation()
            batchEdit(record.client_id)
          }}
        >
          {text}
        </div>
      )
    },
    {
      name: '批发客户名称',
      code: 'client_name',
      width: 280,
      features: { sortable: true },
      align: 'left'
    },
    {
      name: '批发共享中心',
      code: 'share_center_store_name',
      width: 160,
      features: { sortable: true },
      align: 'left'
    },
    // {
    //   name: '是否默认',
    //   code: 'default_flag',
    //   width: 100,
    //   features: { sortable: true },
    //   align: 'left',
    //   render: (text: any, record: any, index: RenderExtra) => {
    //     return <div>{text ? '是' : '否'}</div>
    //   }
    // },
    {
      name: '合作门店',
      code: 'cooperate_store_name',
      width: 170,
      features: { sortable: true }
    },
    {
      name: '批发价取值',
      code: 'wholesale_price_val',
      width: 170,
      features: { sortable: true },
      render: (text) => {
        const findObj = wholesalePriceValues.find((i) => i?.value == text)
        return <div>{findObj?.label || ''}</div>
      }
    }
  ]
  const [form] = XlbBasicForm.useForm()
  let ref: ContextState

  const openModal = (type: 'add' | 'edit', initialValues?: any) => {
    return NiceModal.show(NiceModal.create(Modal), {
      title: type === 'add' ? '新增' : type === 'edit' ? '批量编辑' : '批发中心',
      type,
      initialValues
    })
  }

  const addItem = async (values: any) => {
    form.resetFields()
    const flag: any = await openModal('add', {})
    // console.log('addItem', flag)
    if (!flag) return
    ref?.setLoading!(true)
    flag.client_id = flag.client_ids[0]
    delete flag?.client_ids
    const res = await Api.update(flag)
    ref?.setLoading!(false)
    if (res.code === 0) {
      message.success('操作成功')
      ref?.fetchData()
    }
  }

  const batchEdit = async (client_id: string) => {
    form.resetFields()
    const flag: any = await openModal('edit', { client_id })
    if (!flag) return
    ref?.setLoading!(true)
    flag.client_id = flag.client_ids[0]
    delete flag?.client_ids
    const res = await Api.update(flag)
    ref?.setLoading!(false)
    if (res.code === 0) {
      message.success('操作成功')
      ref?.fetchData()
    }
  }

  // 导入
  const dropdownItemClick = async () => {
    const res = await XlbImportModal({
      importUrl: `${process.env.ERP_URL}/erp/hxl.erp.wholesalecenterstore.import`,
      templateUrl: `${process.env.ERP_URL}/erp/hxl.erp.wholesalecenterstore.download`,
      templateName: '批发组织设置导入模板'
    })
    if (res.code === 0) {
      if (res.data?.state) {
        message.success('操作成功')
        ref?.fetchData()
      }
    }
  }

  const deleteItem = async (list?: any[], keys?: string[]) => {
    const flag = await XlbTipsModal({
      tips: `已选择${keys?.length}条数据，是否确认？`
    })
    if (!flag) return
    ref?.setLoading!(true)
    const res = await Api.delete({
      delete_list: list
        ?.filter((_, i) => keys?.includes(`${i}`))
        ?.map((i: any) => ({
          client_id: i?.client_id,
          cooperate_store_id: i?.cooperate_store_id,
          share_center_store_id: i?.share_center_store_id
        }))
    })
    ref?.setLoading!(false)
    if (res.code === 0) {
      message.success('操作成功')
      ref?.fetchData()
    }
  }

  const exportItem = async (values: any) => {
    values.responseType = 'blob'
    const res = await Api.detailsExport(values)
    const download = new Download()
    download.filename = '批发组织设置导出.xlsx'
    download.xlsx(res)
  }

  return (
    <XlbProPageContainer
      treeFieldProps={{
        leftUrl: '/erp-mdm/hxl.erp.org.tree',
        dataType: DataType.LISTS,
        leftKey: 'org_id'
      }}
      searchFieldProps={{
        formList: [
          { id: 'commonInput', label: '关键字', name: 'keyword' },
          // {
          //   id: 'commonSelect',
          //   label: '是否默认',
          //   name: 'default_flag',
          //   options: [
          //     { label: '是', value: true },
          //     { label: '否', value: false }
          //   ]
          // },
          {
            id: ErpFieldKeyMap.cooperateStoreIds,
            label: '合作门店'
          },
          {
            id: ErpFieldKeyMap.shareCenterStoreIds,
            label: '批发共享中心'
          }
        ]
      }}
      extra={(content: ContextState) => {
        ref = content
        return (
          <>
            {hasAuth(['批发组织设置', '编辑']) ? (
              <XlbButton
                type="primary"
                onClick={() => addItem(content.requestForm)}
                icon={<XlbIcon name="jia" />}
              >
                新增
              </XlbButton>
            ) : null}
            {hasAuth(['批发组织设置', '删除']) ? (
              <XlbButton
                type="primary"
                key={content?.selectRow?.join(',')}
                disabled={!content?.selectRowKeys?.length}
                onClick={() => deleteItem(content.dataSource, content?.selectRowKeys)}
                icon={<XlbIcon name="shanchu" />}
              >
                删除
              </XlbButton>
            ) : null}
            {hasAuth(['批发组织设置', '导入']) ? (
              <XlbButton
                type="primary"
                disabled={!content?.dataSource?.length}
                onClick={() => dropdownItemClick()}
                icon={<XlbIcon name="daoru" />}
              >
                导入
              </XlbButton>
            ) : null}
            {/* {hasAuth(['批发组织设置', '导出']) ? (
              <XlbButton
                type="primary"
                disabled={!content?.dataSource?.length}
                onClick={() => exportItem(content.requestForm)}
                icon={<XlbIcon name="daochu" />}
              >
                导出
              </XlbButton>
            ) : null} */}
          </>
        )
      }}
      exportFieldProps={{
        url: hasAuth(['批发组织设置', '导出'])
          ? '/erp/hxl.erp.wholesalecenterstore.export'
          : '',
        fileName: '批发组织设置导出.xlsx'
      }}
      tableFieldProps={{
        url: '/erp/hxl.erp.wholesalecenterstore.find',
        tableColumn: tableList,
        selectMode: 'multiple',
        showColumnsSetting: false,
        changeColumnAndResetDataSource: false,
        keepDataSource: false,
        immediatePost: true
      }}
    />
  )
}
export default ProForm

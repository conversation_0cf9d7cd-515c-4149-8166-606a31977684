import { XlbFetch } from '@xlb/utils';
export default class Api {
  //获取数据
  static getAllList = async (data: any) => {
    return await XlbFetch.post('/erp/hxl.erp.deliveryspecialprice.page', data);
  };
  //新增
  static addInfo = async (data: any) => {
    return await XlbFetch.post('/erp/hxl.erp.deliveryspecialprice.save', data);
  };
  //删除
  static deleteInfo = async (data: any) => {
    return await XlbFetch.post(
      '/erp/hxl.erp.deliveryspecialprice.batchdelete',
      data,
    );
  };
  //复制
  static copyInfo = async (data: any) => {
    return await XlbFetch.post('/erp/hxl.erp.deliveryspecialprice.copy', data);
  };
  //生成费用单
  static getCreateSupplierFee = async (data: any) => {
    return await XlbFetch.post(
      '/erp/hxl.erp.deliveryspecialprice.createsupplierfee',
      data,
    );
  };
  //读取
  static readInfo = async (data: any) => {
    return await XlbFetch.post('/erp/hxl.erp.deliveryspecialprice.read', data);
  };
  //审核
  static auditInfo = async (data: any) => {
    return await XlbFetch.post('/erp/hxl.erp.deliveryspecialprice.audit', data);
  };
  //反审核
  static reaudit = async (data: any) => {
    return await XlbFetch.post(
      '/erp/hxl.erp.deliveryspecialprice.reaudit',
      data,
    );
  };
  //处理通过
  static handle = async (data: any) => {
    return await XlbFetch.post(
      '/erp/hxl.erp.deliveryspecialprice.handle',
      data,
    );
  };
  //处理拒绝
  static handlerefuse = async (data: any) => {
    return await XlbFetch.post(
      '/erp/hxl.erp.deliveryspecialprice.handlerefuse',
      data,
    );
  };
  //更新
  static updateInfo = async (data: any) => {
    return await XlbFetch.post(
      '/erp/hxl.erp.deliveryspecialprice.update',
      data,
    );
  };
  //作废
  static invalidInfo = async (data: any) => {
    return await XlbFetch.post(
      '/erp/hxl.erp.deliveryspecialprice.invalid',
      data,
    );
  };
  //查询中心配送门店
  static getCenterStore = async (data: any) => {
    return await XlbFetch.post('/erp-mdm/hxl.erp.store.center.find', data);
  };
  //查询门店详情
  static shortPage = async (data: any) => {
    return await XlbFetch.post('/erp-mdm/hxl.erp.store.short.page', data);
  };

  static orgFind = async (data?: any) => {
    return await XlbFetch.POST('/erp-mdm/hxl.erp.org.find', data);
  };

  static findbylevel = async (data: { level: number | string }) => {
    return await XlbFetch.post('/erp-mdm/hxl.erp.org.findbylevel', data);
  };

  static autodeliveryspecialpriceconf = async (data: any) => {
    return await XlbFetch.post(
      '/erp/hxl.erp.autodeliveryspecialpriceconf.find',
      data,
    );
  };

  static autodeliveryspecialpriceconfSave = async (data: any) => {
    return await XlbFetch.post(
      '/erp/hxl.erp.autodeliveryspecialpriceconf.save',
      data,
    );
  };
}
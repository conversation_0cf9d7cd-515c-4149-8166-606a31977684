import {XlbFetch as ErpRequest } from '@xlb/utils'

// 获取商品汇总数据
export  const getgoodsway = async (data: any) => {
    return await ErpRequest.post('/erp/hxl.erp.deliveryreport.itemonway.itemsummary.page', data)
}

// 获取门店汇总数据
export  const getstoretotal = async (data: any) => {
    return await ErpRequest.post('/erp/hxl.erp.deliveryreport.itemonway.storesummary.page', data)
}

// 获取门店-商品汇总数据
export  const getstoregoodstotal = async (data: any) => {
    return await ErpRequest.post('/erp/hxl.erp.deliveryreport.itemonway.storeitemsummary.page', data)
}

// 获取单据汇总数据
export  const getdjtotal = async (data: any) => {
    return await ErpRequest.post('/erp/hxl.erp.deliveryreport.itemonway.ordersummary.page', data)
}
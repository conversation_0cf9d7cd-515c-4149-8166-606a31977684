import XlbFetch from '@/utils/XlbFetch';

//  查询
const exportSupplierPriceAdjust = async (data: any) => {
  return await XlbFetch('/scm/hxl.scm.supplierpriceadjust.export', {
    ...data,
  });
};

// 分配店铺
const allcation = async (data: any) => {
  return await XlbFetch('/scm/hxl.scm.supplierpriceadjust.allocation', {
    ...data,
  });
};

// 拒绝商品
const denyGood = async (data: any) => {
  return await XlbFetch('/scm/hxl.scm.supplierpriceadjustdetail.deny', {
    ...data,
  });
};

// 读取
const readPriceAdjustment = async (data: any) => {
  return await XlbFetch('/scm/hxl.scm.supplierpriceadjust.read', { ...data });
};

// 拒绝
const priceAdjustmentDeny = async (data: any) => {
  return await XlbFetch('/scm/hxl.scm.supplierpriceadjust.deny', { ...data });
};

// 通过
const priceAdjustmentPass = async (data: any) => {
  return await XlbFetch('/scm/hxl.scm.supplierpriceadjust.pass', { ...data });
};

// 设置配送价
const setDeliveryPrice = async (data: any) => {
  return await XlbFetch(
    '/scm/hxl.scm.supplierpriceadjustdeliveryPrice.allocation',
    { ...data },
  );
};

// 设置零售价
const setRetailPrice = async (data: any) => {
  return await XlbFetch(
    '/scm/hxl.scm.supplierpriceadjustretailprice.allocation',
    { ...data },
  );
};

// 查询零售价
const getRetailPrice = async (data: any) => {
  return await XlbFetch('/erp/hxl.erp.retailadjustorder.item.page', {
    ...data,
  });
};

export default {
  allcation,
  exportSupplierPriceAdjust,
  denyGood,
  readPriceAdjustment,
  priceAdjustmentDeny,
  priceAdjustmentPass,
  setDeliveryPrice,
  setRetailPrice,
  getRetailPrice,
};

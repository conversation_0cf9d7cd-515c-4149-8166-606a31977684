export const TiktokcouponrefundKeyMap = {
  /**消费券名称 监听 */
  erpCouponInput: 'erpCouponInput',
  orgIds: 'orgIds'
}
export const tiktokcouponrefundConfig: any[] = [
  {
    tag: 'ERP',
    componentType: 'input',
    label: '消费券',
    name: 'coupon_name',
    id: TiktokcouponrefundKeyMap?.erpCouponInput,
    dependencies: ['summary_types']
  },
  {
    tag: 'ERP',
    label: '组织',
    id: TiktokcouponrefundKeyMap.orgIds,
    name: 'org_ids',
    componentType: 'select',
    fieldProps: {
      mode: 'multiple',
      allowClear: true
    },
    request: async (formValues: any, anybaseURL: any, globalFetch: any) => {
      const res = await globalFetch.post(anybaseURL + '/erp-mdm/hxl.erp.org.find', {
        company_ids: formValues.company_ids || []
      })
      if (res.code == 0) {
        return res.data.map((item: any) => {
          return {
            label: item.name,
            value: item.id
          }
        })
      }
      return []
    }
  }
]
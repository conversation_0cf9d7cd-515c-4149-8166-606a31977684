import { XlbFetch as ErpRequest } from '@xlb/utils';

// 查询
export const allQuery = async (data: any) => {
  return await ErpRequest.post(
    '/erp/hxl.erp.itemstocklog.storeitemsummary.find',
    data,
  );
};

//明细查询
export const stockDetail = async (data: any) => {
  return await ErpRequest.post('/erp/hxl.erp.itemstocklog.page', data);
};

/**
 * @description: 查询业财结算分类枚举
 * @param {any} data
 */
export const queryFinanceCode = async (data: any) => {
  return await ErpRequest.post('/erp-mdm/hxl.erp.settlementcategory.center.find', {
    data,
  });
};
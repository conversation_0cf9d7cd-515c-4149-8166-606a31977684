import { columnWidthEnum } from '@/data/common/constant'
import { ErpFieldKeyMap } from '@/data/common/fieldListConfig'
import { ProFormDependency } from '@ant-design/pro-components'
import { XlbProDetailProps, XlbTableColumnProps } from '@xlb/components'
import NewYearGoodsPlanDetails from '../newYearGoodsPlanDetails'
import { PurchasePlanResDTO } from './type'

/**
 * 新增弹窗
 */
export const detailsFormList:any = [
  {
    componentType: 'form',
    fieldProps: {
      formList: [
        {
          id: ErpFieldKeyMap.purchasePlanOrg,
          label: '所属组织',
          name: 'org_id',
          required: true,
          rules: [{ required: true, message: '所属组织必填' }],
          fieldProps: {
            mode: ''
          }
        },
        {
          id: 'commonInput',
          name: 'name',
          required: true,
          label: '计划名称',
          rules: [{ required: true, message: '计划名称必填' }]
        },
        {
          id: 'commonRangePicker',
          name: 'range',
          required: true,
          label: '时间周期',
          rules: [{ required: true, message: '时间周期必填' }]
        },
        {
          id: 'statusByBoolean',
          name: 'enable',
          label: '状态'
        }
      ]
    }
  }
]

/**
 * 列表页
 */
export const tableColumn: XlbTableColumnProps<PurchasePlanResDTO>[] = [
  {
    code: '_index',
    name: '序号',
    align: 'center'
  },
  {
    code: 'name',
    name: '计划名称',
    features: {
      sortable: true,
      details: {
        mode: 'modal',
        width: 1000,
        title: '采购明细',
        formList: [
          {
            componentType: 'customer',
            render: () => {
              return (
                <ProFormDependency name={['id']}>
                  {(obj, form) => {
                    return <NewYearGoodsPlanDetails rowData={form.getFieldsValue(true)} />
                  }}
                </ProFormDependency>
              )
            }
          }
        ]
      }
    },
    width: columnWidthEnum.STORE_NAME,
    align: 'center'
  },
  {
    code: 'org_name',
    name: '所属组织',
    features: { sortable: true },
    width: columnWidthEnum.STORE_NAME,
    align: 'center'
  },
  {
    code: 'quantity',
    name: '采购计划',
    features: { sortable: true, format: 'QUANTITY' },
    align: 'right'
  },
  {
    code: 'money',
    name: '金额（元）',
    features: { sortable: true, format: 'MONEY' },
    align: 'right'
  },
  {
    code: 'begin_time',
    name: '开始时间',
    features: { sortable: true, format: 'TIME' },
    align: 'center',
    width: columnWidthEnum.DATE
  },
  {
    code: 'end_time',
    name: '结束时间',
    features: { sortable: true, format: 'TIME' },
    align: 'center',
    width: columnWidthEnum.DATE
  },
  {
    code: 'enable',
    name: '状态',
    features: { sortable: true },
    align: 'center',
    render: (text) => {
      return <span className={text ? 'success' : 'danger'}>{text ? '启用' : '未启用'}</span>
    }
  },
  {
    code: 'create_by',
    name: '创建人',
    features: { sortable: true },
    align: 'center'
  },
  {
    code: 'create_time',
    name: '创建时间',
    features: { sortable: true, format: 'TIME' },
    width: columnWidthEnum.DATE,
    align: 'center'
  }
]

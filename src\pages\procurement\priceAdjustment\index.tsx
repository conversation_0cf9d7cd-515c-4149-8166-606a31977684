import { hasAuth } from '@/utils';
import { wujieBus } from '@/wujie/utils';
import {
  XlbBasicForm,
  XlbButton,
  XlbFetch,
  XlbForm,
  XlbInputDialog,
  XlbMessage,
  XlbPageContainer,
  XlbProPageModal,
  XlbProPageModalRef,
  XlbTipsModal,
} from '@xlb/components';
import IconFont from '@xlb/components/dist/components/icon';
import dayjs from 'dayjs';
import { useRef, useState } from 'react';
import { formList, tableColumn } from './data';
import Item from './item';
import Api from './server';
const { ToolBtn, Table, SearchForm } = XlbPageContainer;

const PriceAdjustmentIndex = () => {
  const [form] = XlbBasicForm.useForm();
  const [basicForm] = XlbBasicForm.useForm();
  const pageModalRef = useRef<XlbProPageModalRef>(null);
  const [record, setRecord] = useState<any>({});
  const pageConatainerRef = useRef(null);

  const prevPost = () => {
    const data = {
      ...form.getFieldsValue(),
      not_states: ['AUDIT', 'PASS', 'DENY'],
    };
    return { ...data };
  };

  //   导出
  const exportItem = async (setLoading: any, e: any) => {
    setLoading(true);
    const { ...rest } = form.getFieldsValue(true);
    const res = await Api.exportSupplierPriceAdjust({ ...rest });
    if (res?.code == 0) {
      wujieBus?.$emit('xlb_erp-event', { code: 'downloadEnd', target: e });
      XlbMessage.success('导出受理成功，请前往下载中心查看');
    }
    setLoading(false);
  };

  //   批量处理
  const batchChange = async (selectKeys: string[], fetchData: any) => {
    basicForm.resetFields();
    await XlbTipsModal({
      title: '选择应用门店',
      width: 450,
      isCancel: true,
      tips: (
        <>
          <XlbBasicForm
            form={basicForm}
            style={{ width: '100%', margin: '12px 0 -8px 0' }}
            layout="horizontal"
          >
            <XlbBasicForm.Item name="store_ids" label="应用门店">
              <XlbInputDialog
                dialogParams={{
                  type: 'store',
                  dataType: 'lists',
                  isMultiple: true,
                  selectTooltipContainer: 'body',
                  onOkBeforeFunction: (_val: any, data: any[]) => {
                    basicForm.setFieldsValue({
                      stores: data.map((v: any) => {
                        return {
                          store_name: v.name,
                          store_id: v.id,
                        };
                      }),
                    });
                    return true;
                  },
                }}
                fieldNames={{
                  idKey: 'id',
                  nameKey: 'store_name',
                }}
              ></XlbInputDialog>
            </XlbBasicForm.Item>
          </XlbBasicForm>
        </>
      ),
      onOkBeforeFunction: async () => {
        const basicFormData = basicForm.getFieldsValue(true);
        const params = {
          stores: basicFormData?.stores,
          fids: selectKeys,
        };
        const res = await XlbFetch.post(
          process.env.BASE_URL + '/scm/hxl.scm.supplierpriceadjust.batchpass',
          { ...params },
        );
        if (res.code == 0) {
          XlbMessage.success('批量处理成功');
          fetchData();
        }
        return true;
      },
    });
  };

  return (
    <>
      <XlbProPageModal
        ref={pageModalRef}
        Content={({ onClose }) => {
          return (
            <Item
              onBack={(flag) => {
                if (flag) pageConatainerRef?.current?.fetchData?.();
                pageModalRef.current?.setOpen(false);
              }}
              record={record}
            />
          );
        }}
      >
        <div className="xlb-table-clickBtn"></div>
      </XlbProPageModal>
      <XlbPageContainer
        ref={pageConatainerRef}
        url={'/scm/hxl.scm.supplierpriceadjust.page'}
        immediatePost={true}
        tableColumn={tableColumn?.map((v: any) => {
          if (v?.code === 'fid') {
            v.render = (value: any) => {
              return (
                <div className="cursors">
                  <span
                    className="link cursors"
                    onClick={(e) => {
                      e.stopPropagation();
                      setRecord({ fid: value });
                      pageModalRef.current?.setOpen(true);
                    }}
                  >
                    {value}
                  </span>
                </div>
              );
            };
          }
          return v;
        })}
        prevPost={prevPost}
      >
        <SearchForm>
          <XlbForm
            isHideDate
            formList={formList}
            form={form}
            initialValues={{
              create_date: [
                dayjs()?.format('YYYY-MM-DD'),
                dayjs()?.format('YYYY-MM-DD'),
              ],
            }}
          />
        </SearchForm>
        <ToolBtn>
          {({
            fetchData,
            dataSource,
            loading,
            selectRowKeys,
            setLoading,
          }: any) => {
            return (
              <XlbButton.Group>
                {hasAuth(['调价管理', '查询']) && (
                  <XlbButton
                    loading={loading}
                    label="查询"
                    type="primary"
                    onClick={() => {
                      fetchData();
                    }}
                    icon={
                      <IconFont name="sousuo" color="currentColor" size={16} />
                    }
                  />
                )}
                {hasAuth(['调价管理', '导出']) && (
                  <XlbButton
                    label="导出"
                    type="primary"
                    loading={loading}
                    disabled={loading || !dataSource?.length}
                    onClick={(e) => {
                      exportItem(setLoading, e);
                    }}
                    icon={
                      <IconFont name="daochu" color="currentColor" size={16} />
                    }
                  />
                )}
                {hasAuth(['调价管理', '导出']) && (
                  <XlbButton
                    label="批量处理"
                    type="primary"
                    loading={loading}
                    disabled={
                      loading || !dataSource?.length || !selectRowKeys?.length
                    }
                    onClick={(e) => {
                      batchChange(selectRowKeys, fetchData);
                    }}
                    icon={
                      <IconFont name="piliang" color="currentColor" size={16} />
                    }
                  />
                )}
              </XlbButton.Group>
            );
          }}
        </ToolBtn>
        <Table selectMode={'multiple'} primaryKey={'fid'} />
      </XlbPageContainer>
    </>
  );
};

export default PriceAdjustmentIndex;

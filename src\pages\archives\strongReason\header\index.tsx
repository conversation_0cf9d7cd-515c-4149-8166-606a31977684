import { tableColumn } from '../data'
import { hasAuth } from '@/utils/kit'
import { ErpFieldKeyMap } from '@/data/common/fieldListConfig'
import { XlbProPageContainer } from '@xlb/components'

const Index: React.FC = () => (
  <XlbProPageContainer
    searchFieldProps={{
      initialValues: { type: 'FROCE_DELIVER' },
      formList: [{ id: ErpFieldKeyMap?.erpItemKeyword }]
    }}
    tableFieldProps={{
      immediatePost: true,
      url: '/erp/hxl.erp.reason.find',
      selectMode: 'single',
      tableColumn: tableColumn
    }}
    deleteFieldProps={{
      name: '删除',
      showField: 'name',
      url: hasAuth(['统配原因', '删除']) ? '/erp/hxl.erp.reason.delete' : ''
    }}
    addFieldProps={{
      name: '新增',
      url: hasAuth(['统配原因', '编辑']) ? '/erp/hxl.erp.reason.save' : ''
    }}
    details={{
      mode: 'modal',
      isCancel: true,
      width: 350,
      title: (item) => {
        return <div>{item?.id ? '编辑' : '新增'}</div>
      },
      hiddenSaveBtn: true,
      primaryKey: 'id',
      initialValues: { type: 'FROCE_DELIVER' },
      formList: [
        {
          componentType: 'form',
          fieldProps: {
            formList: [
              {
                id: ErpFieldKeyMap.erpName,
                itemSpan: 24,
                label: '统配原因',
                rules: [{ required: true, message: '统配原因不能为空' }],
                fieldProps: { maxLength: 10 }
              }
            ]
          }
        }
      ],
      updateFieldProps: {
        url: hasAuth(['统配原因', '编辑']) ? '/erp/hxl.erp.reason.update' : ''
      }
    }}
  />
)

export default Index

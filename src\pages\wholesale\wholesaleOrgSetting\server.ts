import {XlbFetch as ErpRequest } from '@xlb/utils'

export default {
  delete: async (data: any) =>
    await ErpRequest.post('/erp/hxl.erp.wholesalecenterstore.delete', data),
  update: async (data: any) =>
    await ErpRequest.post('/erp/hxl.erp.wholesalecenterstore.update', data),
  detailsExport: async (data: any) =>
    await ErpRequest.post('/erp/hxl.erp.wholesalecenterstore.export', data),
  // 详情
  getDetails: async (data: any) =>
    await ErpRequest.post('/erp/hxl.erp.wholesalecenterstore.find', data),
  // 获取批发共享中心
  getWholeWareCenterStores: async (data: any) =>
    await ErpRequest.post('/erp/hxl.erp.wholesalecenterstore.sharecenterstore.find', data),
  // 获取合作门店
  getCoperateStore: async (data: any) =>
    await ErpRequest.post('/erp/hxl.erp.wholesalecenterstore.cooperatestore.find', data)
}

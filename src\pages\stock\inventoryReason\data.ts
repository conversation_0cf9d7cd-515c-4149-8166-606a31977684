import { XlbTableColumnProps } from '@xlb/components'

export const tableColumn: XlbTableColumnProps<any>[] = [
  {
    name: '序号',
    code: '_index',
    width: 50,
    align: 'center'
  },
  {
    name: '调整原因代码',
    code: 'code',
    width: 190,
    features: { sortable: true }
  },
  {
    name: '库存调整原因名称',
    code: 'name',
    width: 190,
    features: { sortable: true }
  },
  {
    name: '出入库标记',
    code: 'flag',
    width: 120
    // features: { sortable: true }
  },
  {
    name: '应用范围',
    code: 'applicability',
    width: 120
    // features: { sortable: true }
  },
  {
    name: '状态',
    code: 'enabled',
    width: 120
  },
  {
    name: '创建时间',
    code: 'create_time',
    width: 180,
    features: { sortable: true, format: 'TIME' }
  },
  {
    name: '创建人',
    code: 'create_by',
    width: 120,
    features: { sortable: true }
  }
]

export interface StockAdjustmentReasonFindResDTO {
  /**
   * @name 出入库标记
   */
  flag?: boolean

  /**
   * @name id
   */
  id?: number

  /**
   * @name 原因名字
   */
  name?: string
}
export const deveveryPriceValues = [
  { label: '上游配送中心配送价', value: 'UPSTREAM_DELIVERY_PRICE' },
  { label: '中转配送中心配送价', value: 'TRANSIT_DELIVERY_PRICE' },
  { label: '共享配送中心配送价', value: 'SHARE_DELIVERY_PRICE' }
]


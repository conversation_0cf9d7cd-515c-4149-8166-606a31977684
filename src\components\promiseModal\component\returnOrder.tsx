import { columnWidthEnum } from '@/data/common/constant';
import { hasAuth } from '@/utils/kit';
import safeMath from '@/utils/safeMath';
import toFixed from '@/utils/toFixed';
import {
  SearchFormType,
  XlbBasicForm,
  XlbForm,
  XlbTable,
  XlbTableColumnProps,
} from '@xlb/components';
import { Tooltip } from 'antd';
import dayjs from 'dayjs';
import _ from 'lodash';
import { useEffect, useState } from 'react';
import { readReturnOrder } from '../server';

const receiptStatus: any[] = [
  {
    label: '制单',
    value: 'INIT',
    type: 'info',
  },
  {
    label: '审核',
    value: 'AUDIT',
    type: 'warning',
  },
];
const formList: SearchFormType[] = [
  {
    type: 'input',
    disabled: true,
    name: 'supplier_name',
    label: '供应商',
  },
  {
    type: 'input',
    disabled: true,
    name: 'store_name',
    label: '退货门店',
  },
  {
    type: 'select',
    disabled: true,
    name: 'storehouse_name',
    label: '退货仓库',
    options: [],
  },
  {
    type: 'input',
    disabled: true,
    name: 'fid',
    label: '单据号',
  },
  {
    type: 'input',
    disabled: true,
    name: 'state',
    label: '单据状态',
  },
  {
    type: 'input',
    disabled: true,
    name: 'item_dept_names',
    label: '商品部门',
  },
  {
    type: 'datePicker',
    disabled: true,
    name: 'operate_date',
    label: '退货日期',
  },
  {
    type: 'datePicker',
    disabled: true,
    name: 'payment_date',
    label: '付款日期',
  },
  {
    type: 'input',
    disabled: true,
    name: 'purchase_order_fid',
    label: '采购收货单',
  },
  {
    type: 'input',
    disabled: true,
    name: 'memo',
    label: '留言备注',
    width: 648,
  },
];
const columnsList: XlbTableColumnProps<any>[] = [
  {
    name: '序号',
    code: '_index',
    width: columnWidthEnum.INDEX,
    align: 'center',
  },
  {
    name: '操作',
    code: 'operation',
    align: 'center',
    width: 60,
  },
  {
    name: '商品代码',
    code: 'item_code',
    width: columnWidthEnum.ITEM_CODE,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '商品条码',
    code: 'item_bar_code',
    width: columnWidthEnum.ITEM_BAR_CODE,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '商品名称',
    code: 'item_name',
    width: 280,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '采购规格',
    code: 'item_spec',
    width: columnWidthEnum.ITEM_SPEC,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '单位',
    code: 'unit',
    width: 80,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '数量',
    code: 'quantity',
    width: 110,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '单价',
    code: 'price',
    width: 110,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '金额',
    code: 'money',
    width: 120,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '进项税率(%)',
    code: 'tax_rate',
    width: 120,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '单价(去税)',
    code: 'no_tax_price',
    width: 110,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '金额(去税)',
    code: 'no_tax_money',
    width: 110,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '基本单位',
    code: 'basic_unit',
    width: 110,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '换算率',
    code: 'ratio',
    width: 90,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '基本数量',
    code: 'basic_quantity',
    width: 110,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '基本单价',
    code: 'basic_price',
    width: 134,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '基本单价(去税)',
    code: 'no_tax_basic_price',
    width: 134,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '赠品单位',
    code: 'present_unit',
    width: 110,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '赠品数量',
    code: 'present_quantity',
    width: 110,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '生产日期',
    code: 'producing_date',
    width: columnWidthEnum.DATE,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '到期日期',
    code: 'expire_date',
    width: columnWidthEnum.DATE,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '批次号',
    code: 'batch_number',
    width: 140,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '保质期',
    code: 'period',
    width: 90,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '最近进价',
    code: 'latest_basic_price',
    width: 110,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '库存数量',
    code: 'stock_number',
    width: 110,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '税费',
    code: 'tax_money',
    width: 110,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '备注',
    code: 'memo',
    width: columnWidthEnum.MEMO,
    features: { sortable: true },
    align: 'left',
  },
];

const ReturnOrder = (props: any) => {
  const { fid } = props;
  const [formModel] = XlbBasicForm.useForm<any>();
  const [tableLoading, setTableLoading] = useState<any>(false);
  // 列表数据
  const [fidDataList, setFidDataList] = useState<any>([]);
  const [footerData, setFooterData] = useState<any[]>([]);
  const [itemArrdetail] = useState<XlbTableColumnProps<any>[]>(
    columnsList.map((i: any) => ({
      ...i,
      code: i.code == 'index' ? '_index' : i.code,
      hidden: i.code == 'operation' ? true : i.hidden,
    })) as any[],
  );

  const tableRender = (item: any) => {
    switch (item.code) {
      case 'stock_number':
        item.render = (value: any, record: any) => {
          return record.index === '合计' ? null : (
            <div>
              {' '}
              {toFixed(
                safeMath.divide(record.basic_stock_quantity, record.ratio),
                'QUANTITY',
                true,
              )}
            </div>
          );
        };
        break;
      case 'latest_basic_price':
        item.render = (value: any, record: any) => {
          return record.index === '合计' ? null : (
            <div>
              {hasAuth(['采购退货单/采购价', '查询'])
                ? (record.latest_basic_price / record.ratio).toFixed(4) != 'NaN'
                  ? toFixed(
                      record.latest_basic_price / record.ratio,
                      'QUANTITY',
                    )
                  : ''
                : '****'}
            </div>
          );
        };
        break;
      case 'no_tax_money':
        item.render = (value: any) => (
          <div>
            {hasAuth(['采购退货单/采购价', '查询'])
              ? toFixed(value, 'MONEY')
              : '****'}
          </div>
        );
        break;
      case 'no_tax_price':
      case 'no_tax_basic_price':
        item.render = (value: any, record: any) => (
          <div>
            {record.index === '合计'
              ? ''
              : hasAuth(['采购退货单/采购价', '查询'])
                ? toFixed(value, 'PRICE')
                : '****'}
          </div>
        );
        break;
      case 'money':
        item.render = (value: any) => (
          <div>
            {hasAuth(['采购退货单/采购价', '查询'])
              ? toFixed(value, 'MONEY')
              : '****'}
          </div>
        );
        break;
      case 'price':
      case 'basic_price':
        item.render = (value: any, record: any) => (
          <div>
            {record.index === '合计'
              ? ''
              : hasAuth(['采购退货单/采购价', '查询'])
                ? toFixed(value, 'PRICE')
                : '****'}
          </div>
        );
        break;
      case 'quantity':
      case 'basic_quantity':
      case 'present_quantity':
        item.render = (value: any) => (
          <div className="info overwidth">
            {value ? toFixed(value, 'QUANTITY') : '0.00'}
          </div>
        );
        break;
      case 'memo':
        item.render = (value: any) => {
          return (
            <Tooltip placement="topLeft" autoAdjustOverflow title={value}>
              <div>{value}</div>
            </Tooltip>
          );
        };
        break;
    }
    return item;
  };

  const openRefOrder = _.debounce(async (summary: boolean = false) => {
    setTableLoading(true);
    formModel.setFieldsValue({});
    const res = await readReturnOrder({ fid: fid, summary: summary });
    if (res?.code == 0) {
      // 表单的值
      let obj: any = receiptStatus.find((v) => v.value === res.data.state);
      formModel.setFieldsValue({
        ...res?.data,
        state: obj.label,
        operate_date: res.data.operate_date
          ? dayjs(res.data.operate_date)
          : null,
        payment_date: res.data.payment_date
          ? dayjs(res.data.payment_date)
          : null,
      });
      // 列表的值
      let list = res.data.details.map((v: any) => {
        let unit = { name: v.unit, ratio: v.ratio };
        let basic_unit = {
          name: v.basic_unit,
          ratio: v.basic_unit === v.unit ? unit.ratio : 1,
        };
        let delivery_unit = {
          name: v.delivery_unit,
          ratio: v.delivery_unit === v.unit ? unit.ratio : v.delivery_ratio,
        };
        const purchase_unit = {
          name: v.purchase_unit,
          ratio: v.purchase_unit === v.unit ? unit.ratio : v.purchase_ratio,
        };
        const stock_unit = {
          name: v.stock_unit,
          ratio: v.stock_unit === v.unit ? unit.ratio : v.stock_ratio,
        };
        const wholesale_unit = {
          name: v.wholesale_unit,
          ratio: v.wholesale_unit === v.unit ? unit.ratio : v.wholesale_ratio,
        };
        v.units = [
          ...new Set([
            JSON.stringify(basic_unit),
            JSON.stringify(delivery_unit),
            JSON.stringify(purchase_unit),
            JSON.stringify(stock_unit),
            JSON.stringify(wholesale_unit),
            JSON.stringify(unit),
          ]),
        ];
        return v;
      });
      setFidDataList(list);
      // 计算合计
      footerData[0] = {
        _index: '合计',
        money: hasAuth(['采购退货单/采购价', '查询'])
          ? res.data.details
              .reduce((sum: any, v: any) => sum + Number(v.money), 0)
              .toFixed(2)
          : '****',
        quantity: res.data.details
          .reduce((sum: any, v: any) => sum + Number(v.quantity), 0)
          .toFixed(3),
        no_tax_money: hasAuth(['采购退货单/采购价', '查询'])
          ? res.data.details
              .reduce((sum: any, v: any) => sum + Number(v.no_tax_money), 0)
              .toFixed(2)
          : '****',
        basic_quantity: res.data.details
          .reduce((sum: any, v: any) => sum + Number(v.basic_quantity), 0)
          .toFixed(3),
        present_quantity: res.data.details
          .reduce((sum: any, v: any) => sum + Number(v.present_quantity), 0)
          .toFixed(3),
      };
      setFooterData([...footerData]);
    }
    setTableLoading(false);
  }, 50);

  useEffect(() => {
    openRefOrder();
    itemArrdetail.map((v) => tableRender(v));
  }, []);

  return (
    <>
      <XlbForm
        style={{ marginTop: 15 }}
        formList={formList}
        form={formModel}
        isHideDate={true}
      />
      <XlbTable
        isLoading={tableLoading}
        style={{ height: 400, maxHeight: 400, overflowY: 'scroll' }}
        hideOnSinglePage={false}
        showSearch={true}
        columns={itemArrdetail}
        total={fidDataList?.length}
        dataSource={fidDataList}
        key={fidDataList?.length}
        footerDataSource={footerData}
        selectMode="single"
      ></XlbTable>
    </>
  );
};

export default ReturnOrder;

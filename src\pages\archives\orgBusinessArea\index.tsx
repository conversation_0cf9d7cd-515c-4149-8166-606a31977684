import { ErpFieldKeyMap } from '@/data/common/fieldListConfig'
import { hasAuth } from '@/utils'
import {
  XlbBasicData,
  XlbBasicForm,
  XlbButton,
  XlbProPageContainer,
  XlbTipsModal
} from '@xlb/components'
import { useState } from 'react'
import { goodsType, tableColumn } from './data'
import { useBaseParams } from '@/hooks/useBaseParams'
import { DataType } from '@xlb/components/dist/components/XlbTree/type'
import { message } from 'antd'
import { addGoods } from './server'

const OrgBusinessAreaIndex = () => {
  const [form] = XlbBasicForm.useForm()
  const [treeDataSource, setTreeDataSource] = useState<any[]>([])
  const { enable_organization } = useBaseParams((state) => state)
  const [treeKey, setTreeKey] = useState<any>([])
  let treeConfig: any = {}

  const handleAdd = async (context: any) => {
    const { fetchData, form, requestForm, setLoading } = context
    const orgId = requestForm?.org_id
    if (!orgId) {
      return XlbTipsModal({
        tips: '请先选择组织！'
      })
    }
    const list = await XlbBasicData({
      isMultiple: true,
      dataType: 'tree',
      isLeftColumn: true,
      nullable: false,
      type: 'goods',
      fieldNames: {
        idKey: 'item_id',
        nameKey: 'name'
      }
    })
    if (!list) return
    const _goodsList = list?.map((v: any) => v.id)
    console.log('00-:', _goodsList)
    setLoading(true)
    const data = {
      org_id: requestForm?.org_id || undefined,
      item_ids: _goodsList
    }
    const res = await addGoods(data)
    setLoading(false)
    if (res.code == 0) {
      message.success('添加成功')
      fetchData()
    }
  }
  return (
    <XlbProPageContainer
      treeFieldProps={
        enable_organization
          ? {
              leftUrl: '/erp-mdm/hxl.erp.org.find',
              dataType: DataType.LISTS,
              leftKey: 'org_id',
              requestParams: {
                level: 2
              },
              topLevelTreeDisabled: true,
              handleDefaultValue: (data) => {
                if (data?.length) {
                  return [data?.[0]]
                } else {
                  return []
                }
              }
            }
          : undefined
      }
      searchFieldProps={{
        order: 0,
        formList: () => [
          'keyword',
          {
            id: ErpFieldKeyMap.erpItemType,
            name: 'item_types',
            fieldProps: {
              options: goodsType,
              mode: 'multiple'
            }
          }
          // { id: ErpFieldKeyMap?.erpinputPanel }
        ]
      }}
      extra={(context) => {
        return hasAuth(['组织经营范围', '编辑']) ? (
          <div style={{ order: 10 }}>
            <XlbButton
              type="primary"
              label="添加"
              loading={false}
              onClick={() => {
                // console.log()
                handleAdd(context)
              }}
              icon={<span className="iconfont icon-jia" />}
            />
          </div>
        ) : null
      }}
      addFieldProps={{
        name: '新增',
        url: hasAuth(['组织经营范围', '编辑']) ? '' : ''
      }}
      deleteFieldProps={{
        order: 20,
        name: '删除',
        url: hasAuth(['组织经营范围', '删除'])
          ? '/erp-mdm/hxl.erp.org.business.scope.item.batchdelete'
          : '',
        params: (data: any, v: any) => {
          console.log(data, v, 'delete')
          return { ids: v?.map((j) => j?.id) }
        }
      }}
      exportFieldProps={{
        order: 40,
        url: hasAuth(['组织经营范围', '导出'])
          ? '/erp-mdm/hxl.erp.org.business.scope.item.export '
          : '',
        fileName: '组织经营范围.xlsx'
      }}
      uploadFieldProps={{
        order: 30,
        url: hasAuth(['组织经营范围', '导入'])
          ? '/erp-mdm/hxl.erp.org.business.scope.item.import'
          : '',
        templateUrl: '/erp-mdm/hxl.erp.org.business.scope.item.template.download'
      }}
      tableFieldProps={{
        keepDataSource: false,
        url: '/erp-mdm/hxl.erp.org.business.scope.item.page',
        selectMode: 'multiple',
        showColumnsSetting: false,
        tableColumn: tableColumn,
        immediatePost: true
        //     prevPost: (values) => {
        //       console.log('vvv:', values)
        //       return {}
        //     }
      }}
    />
  )
}

export default OrgBusinessAreaIndex
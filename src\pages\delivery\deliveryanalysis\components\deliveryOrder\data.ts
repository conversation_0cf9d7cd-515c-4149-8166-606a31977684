import { columnWidthEnum } from "@/constants";
import { XlbTableColumnProps } from "@xlb/components";


//商品明细 已公用禁止修改
export const itemTableList: XlbTableColumnProps<any>[] = [
  {
    name: '序号',
    code: 'index',
    width: columnWidthEnum.INDEX,
    align: 'center'
  },
  {
    name: '操作',
    code: 'operation',
    align: 'center',
    width: 60
  },
  {
    name: '缺货状态',
    code: 'less_status',
    align: 'center',
    features: { sortable: true },
    width: 100
  },
  {
    name: '商品代码',
    code: 'item_code',
    width: columnWidthEnum.ITEM_CODE,
    features: { sortable: true },
    align: 'left'
  },
  {
    name: '商品条码',
    code: 'item_bar_code',
    width: columnWidthEnum.ITEM_BAR_CODE,
    features: { sortable: true },
    align: 'left'
  },
  {
    name: '商品名称',
    code: 'item_name',
    width: 280,
    features: { sortable: true },
    align: 'left'
  },
  {
    name: '采购规格',
    code: 'item_spec',
    width: columnWidthEnum.ITEM_SPEC,
    features: { sortable: true },
    align: 'left'
  },
  {
    name: '商品类别',
    code: 'item_category_name',
    width: 160,
    features: { sortable: true },
    align: 'left'
  },
  {
    name: '单位',
    code: 'unit',
    width: 80,
    features: { sortable: true },
    align: 'left'
  },
  {
    name: '要货数量',
    code: 'quantity',
    width: 110,
    features: { sortable: true },
    align: 'right'
  },
  {
    name: '缺货数量',
    code: 'less_quantity',
    width: 110,
    features: { sortable: true },
    align: 'right'
  },
  {
    name: '预发数量',
    code: 'actual_quantity',
    width: 110,
    features: { sortable: true },
    align: 'right'
  },
  {
    name: '实发数量',
    code: 'delivery_quantity',
    width: 120,
    features: { sortable: true },
    align: 'right'
  },
  {
    name: '实发率',
    code: 'delivery_rate',
    width: 120,
    features: { sortable: true },
    align: 'right'
  },
  {
    name: '单价',
    code: 'price',
    width: 110,
    features: { sortable: true },
    align: 'right'
  },
  {
    name: '金额',
    code: 'money',
    width: 120,
    features: { sortable: true },
    align: 'right'
  },
  {
    name: '基本单位',
    code: 'basic_unit',
    width: 110,
    features: { sortable: true },
    align: 'left'
  },

  {
    name: '预发基本数量',
    code: 'actual_basic_quantity',
    width: 120,
    features: { sortable: true },
    align: 'right'
  },
  {
    name: '基本单价',
    code: 'basic_price',
    width: 110,
    features: { sortable: true },
    align: 'right'
  },
  {
    name: '零售价',
    code: 'sale_price',
    width: 110,
    features: { sortable: true },
    align: 'right'
  },
  {
    name: '保质期',
    code: 'period',
    width: 90,
    features: { sortable: true },
    align: 'right'
  },
  {
    name: '体积(m³)',
    code: 'volume',
    width: 120,
    features: { sortable: true },
    align: 'right'
  },
  {
    name: '补货门店库存',
    code: 'stock_quantity',
    width: 160,
    features: { sortable: true },
    align: 'right'
  },
  {
    name: '中心库存',
    code: 'center_stock_quantity',
    width: 120,
    features: { sortable: true },
    align: 'right'
  },
  {
    name: '调出数量',
    code: 'basic_out_quantity',
    width: 120,
    features: { sortable: true },
    align: 'right'
  },
  {
    name: '原配送价',
    code: 'original_price',
    width: 100,
    features: { sortable: true },
    align: 'right'
  },
  {
    name: '配送特价单据号',
    code: 'special_fid',
    width: 210,
    features: { sortable: true },
    align: 'right'
  },
  {
    name: '备注',
    code: 'memo',
    width: columnWidthEnum.MEMO,
    features: { sortable: true },
    align: 'left'
  }
]
//单据状态
export const Options1 = [
  {
    label: '制单',
    value: 'INIT',
    type: 'info',
  },
  {
    label: '审核',
    value: 'AUDIT',
    type: 'warning',
  },
];

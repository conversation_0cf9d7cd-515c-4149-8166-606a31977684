import { ErpFieldKeyMap } from '@/data/common/fieldListConfig';
import { useBaseParams } from '@/hooks/useBaseParams';
import { exportPage } from '@/services/system';
import Download from '@/utils/downloadBlobFile';
import { hasAuth } from '@/utils/kit';
import { LStorage } from '@/utils/storage';
import type { XlbTableColumnProps } from '@xlb/components';
import {
  XlbBasicData,
  XlbBasicForm,
  XlbBlueBar,
  XlbButton,
  XlbIcon,
  XlbImportModal,
  XlbInput,
  XlbPageContainer,
  XlbProForm,
  XlbSelect,
  XlbTable,
  XlbTipsModal,
} from '@xlb/components';
import { XlbFetch as ErpRequest, XlbFetch } from '@xlb/utils';
import { InputNumber, Tabs, message } from 'antd';
import { FormInstance } from 'antd/es/form';
import React, { useEffect, useRef, useState } from 'react';
import { ShopArr, checkOptions, goodsType } from '../data';
import BatchChange from '../header/component/batchChange';

const { TabPane } = Tabs;
interface EditableCellProps {
  value: any;
  record: any;
  index: any;
  code: string;
  centerCode: string;
}
const Index = (props) => {
  const { onBack, record } = props;

  const userInfo = LStorage.get('userInfo');
  const [orgList, setOrgList] = useState<any[]>([]);
  const { enable_organization } = useBaseParams();
  const [form] = XlbBasicForm.useForm<any>();
  const [columns, setColumns] = useState<any[]>(ShopArr);
  const [rowData, setRowData] = useState<any[]>([]);
  const [isLoading, setisLoading] = useState<boolean>(false);
  const [storeIds, setStoreIds] = useState<number[]>([]);
  const [tabsKey, setTabsKey] = useState('STORE');
  const [selectedList, setSelectedList] = useState<any>([]);
  const [preId, setId] = useState(-1);
  const [batchVisible, setBatchVisible] = useState<boolean>(false);
  const ref = useRef<FormInstance>();
  const fetchDataRef = useRef<any>();
  const formValuesRef = useRef<any>();
  const dataSourceRef = useRef<any>();
  const setDataSourceRef = useRef<any>();
  const changeColor = (centerPrice, price) => {
    if (centerPrice == null || centerPrice == void 0) {
      centerPrice = 0.0;
    }
    if (centerPrice == price) {
      return '';
    }
    if (centerPrice < price) {
      return 'danger';
    }
    if (centerPrice > price) {
      return 'success';
    }
    return '';
  };
  const EditableCell: React.FC<EditableCellProps> = (props) => {
    const { value, record, index, code, centerCode } = props;
    return hasAuth(['零售价模板/零售价', '编辑']) &&
      hasAuth(['零售价模板/零售价', '查询']) &&
      record?._click ? (
      <InputNumber
        className="full-box"
        max={999999999}
        step={0.01}
        min={0}
        size="small"
        controls={false}
        id={code + '-' + index['index'].toString()}
        defaultValue={value}
        onFocus={(e) => e.target.select()}
        onBlur={(e) => inputBlur(e.target.value, index['index'], code)}
        onChange={(e) => inputChange(e, index['index'], code)}
        onPressEnter={() => onPressEnter(code, index['index'])}
      />
    ) : (
      <div className="info overwidth">
        <span className={`${changeColor(record[centerCode], record[code])}`}>
          {hasAuth(['零售价模板/零售价', '查询'])
            ? /^[+-0]?(0|([1-9]\d*))(\.\d+)?$/.test(value)
              ? value?.toFixed(2)
              : value
            : '****'}
        </span>
      </div>
    );
  };

  const categoryArr: XlbTableColumnProps<any>[] = [
    {
      name: '序号',
      code: '_index',
      width: 60,
      align: 'center',
      lock: true,
    },
    {
      name: '商品代码',
      code: 'code',
      width: 110,
      features: { sortable: true },
    },
    {
      name: '商品条码',
      code: 'bar_code',
      width: 140,
      features: { sortable: true },
    },
    {
      name: '商品名称',
      code: 'name',
      width: 280,
      features: { sortable: true },
    },
    {
      name: '零售规格',
      code: 'retail_spec',
      width: 160,
      features: { sortable: true },
    },
    {
      name: '商品类型',
      code: 'type',
      width: 100,
      features: { sortable: true },
      render: (value, record, index) => {
        return (
          <div className="info overwidth">
            {' '}
            {goodsType.find((e) => e.value === value)?.label}{' '}
          </div>
        );
      },
    },
    {
      name: '商品类别',
      code: 'category_name',
      width: 100,
      features: { sortable: true },
    },
    {
      name: '零售单位',
      code: 'basic_unit',
      width: 100,
      features: { sortable: true },
    },
    {
      name: '中心指导价',
      features: { sortable: true },
      code: 'center_guide_price',
      align: 'right',
      width: 120,
      render: (value, record, index) => {
        return (
          <div className="info overwidth">
            {hasAuth(['零售价模板/档案零售价', '查询'])
              ? (value ?? '0.00')
              : '****'}
          </div>
        );
      },
    },
    {
      name: '标准售价',
      code: 'sale_price',
      align: 'right',
      width: 110,
      features: { sortable: true },
      render: (value: any, record: any, index: any) => {
        return (
          <EditableCell
            value={value}
            index={index}
            record={record}
            centerCode={'center_guide_price'}
            code={'sale_price'}
          ></EditableCell>
        );
      },
    },
    {
      name: '中心指导价2',
      features: { sortable: true },
      code: 'center_guide_price_s',
      align: 'right',
      width: 120,
      render: (value, record, index) => {
        return (
          <div className="info overwidth">
            {hasAuth(['零售价模板/档案零售价', '查询'])
              ? (value ?? '0.00')
              : '****'}
          </div>
        );
      },
    },
    {
      name: '售价2',
      code: 'sale_price_s',
      align: 'right',
      width: 110,
      features: { sortable: true },
      render: (value: any, record: any, index: any) => {
        return (
          <EditableCell
            value={value}
            record={record}
            index={index}
            code={'sale_price_s'}
            centerCode={'center_guide_price_s'}
          ></EditableCell>
        );
      },
    },
    {
      name: '中心指导价3',
      features: { sortable: true },
      code: 'center_guide_price_t',
      align: 'right',
      width: 120,
      render: (value, record, index) => {
        return (
          <div className="info overwidth">
            {hasAuth(['零售价模板/档案零售价', '查询'])
              ? (value ?? '0.00')
              : '****'}
          </div>
        );
      },
    },
    {
      name: '售价3',
      code: 'sale_price_t',
      align: 'right',
      width: 110,
      features: { sortable: true },
      render: (value: any, record: any, index: any) => {
        return (
          <EditableCell
            value={value}
            record={record}
            index={index}
            code={'sale_price_t'}
            centerCode={'center_guide_price_t'}
          ></EditableCell>
        );
      },
    },
    {
      name: '中心指导价4',
      features: { sortable: true },
      code: 'center_guide_price_f',
      align: 'right',
      width: 120,
      render: (value, record, index) => {
        return (
          <div className="info overwidth">
            {hasAuth(['零售价模板/档案零售价', '查询'])
              ? (value ?? '0.00')
              : '****'}
          </div>
        );
      },
    },
    {
      name: '售价4',
      code: 'sale_price_f',
      align: 'right',
      width: 110,
      features: { sortable: true },
      render: (value: any, record: any, index: any) => {
        return (
          <EditableCell
            value={value}
            record={record}
            index={index}
            centerCode={'center_guide_price_f'}
            code={'sale_price_f'}
          ></EditableCell>
        );
      },
    },
    {
      name: '中心最低售价',
      code: 'center_price_min_price',
      features: { sortable: true },
      align: 'right',
      width: 120,
      render: (value, record, index) => {
        return (
          <div className="info overwidth">
            {hasAuth(['零售价模板/档案零售价', '查询'])
              ? (value ?? '0.00')
              : '****'}
          </div>
        );
      },
    },
    {
      name: '最低售价',
      code: 'sale_min_price',
      align: 'right',
      width: 110,
      features: { sortable: true },
      render: (value: any, record: any, index: any) => {
        return (
          <EditableCell
            value={value}
            record={record}
            index={index}
            centerCode={'center_price_min_price'}
            code={'sale_min_price'}
          ></EditableCell>
        );
      },
    },
    {
      name: '中心最高售价',
      code: 'center_price_max_price',
      align: 'right',
      width: 120,
      features: { sortable: true },
      render: (value, record, index) => {
        return (
          <div className="info overwidth">
            {hasAuth(['零售价模板/档案零售价', '查询'])
              ? (value ?? '0.00')
              : '****'}
          </div>
        );
      },
    },
    {
      name: '最高售价',
      code: 'sale_max_price',
      align: 'right',
      width: 110,
      features: { sortable: true },
      render: (value: any, record: any, index: any) => {
        return (
          <EditableCell
            value={value}
            record={record}
            index={index}
            centerCode={'center_price_max_price'}
            code={'sale_max_price'}
          ></EditableCell>
        );
      },
    },
    {
      name: '停购',
      code: 'stop_purchase',
      width: 70,
      features: { sortable: true },
      render: (value, record, index) => {
        const showColor = value ? 'success' : 'danger';
        return (
          <div className={`overwidth ${showColor}`}>{value ? '是' : '否'}</div>
        );
      },
    },
    {
      name: '停售',
      code: 'stop_sale',
      width: 70,
      features: { sortable: true },
      render: (value, record, index) => {
        const showColor = value ? 'success' : 'danger';
        return (
          <div className={`overwidth ${showColor}`}>{value ? '是' : '否'}</div>
        );
      },
    },
    {
      name: '停止要货',
      code: 'stop_request',
      width: 100,
      features: { sortable: true },
      render: (value, record, index) => {
        const showColor = value ? 'success' : 'danger';
        return (
          <div className={`overwidth ${showColor}`}>{value ? '是' : '否'}</div>
        );
      },
    },
    {
      name: enable_organization ? '组织配送价' : '档案配送价',
      code: 'delivery_price',
      align: 'right',
      width: 100,
      features: { sortable: true },
      render: (value: any, record: any, index: any) => {
        return (
          <div className="info overwidth">
            {hasAuth(['零售价模板/档案配送价', '查询'])
              ? /^[+-0]?(0|([1-9]\d*))(\.\d+)?$/.test(value)
                ? value?.toFixed(4)
                : value
              : value}
          </div>
        );
      },
    },
  ];
  const inputBlur = async (e: any, index: number, key: any) => {
    let regPos = /^[+-0]?(0|([1-9]\d*))(\.\d+)?$/;
    if (!regPos.test(e) || e < 0) {
      await XlbTipsModal({
        tips: `请输入>=0的数字`,
        isCancel: true,
        title: '提示',
      });
      return false;
    }
    const sale_max_price = dataSourceRef?.current?.[index]['sale_max_price'];
    const sale_min_price = dataSourceRef?.current?.[index]['sale_min_price'];
    const sale_price = dataSourceRef?.current?.[index]['sale_price'];
    const sale_price_s = dataSourceRef?.current?.[index]['sale_price_s'];
    const sale_price_t = dataSourceRef?.current?.[index]['sale_price_t'];
    const sale_price_f = dataSourceRef?.current?.[index]['sale_price_f'];
    const arr = [sale_price, sale_price_s, sale_price_t, sale_price_f];
    const maxLimit = arr.some(
      (v, i) => ((i !== 0 && v != 0) || i === 0) && v > sale_max_price,
    );
    const minLimit = arr.some(
      (v, i) => ((i !== 0 && v != 0) || i === 0) && v < sale_min_price,
    );
    if (
      (sale_max_price != 0 && maxLimit) ||
      (sale_min_price != 0 && minLimit) ||
      (sale_min_price != 0 && sale_max_price != 0 && (minLimit || maxLimit))
    ) {
      //最高价和最低价都不为0
      await XlbTipsModal({
        isCancel: true,
        tips: (
          <span style={{ color: '#f53f3f' }}>
            零售价设置与最高售价、最低售价存在冲突，请检查！
          </span>
        ),
      });
      return false;
    }
    const data = {
      ...dataSourceRef?.current?.[index],
      id: record.id,
      modify_price: true,
    };
    const res = await ErpRequest.post(
      '/erp/hxl.erp.retailpricetemplate.retailprice.update',
      data,
    );
    if (res.data?.results?.length) {
      await XlbTipsModal({
        isCancel: true,
        tips: `以下商品零售价设置与最低售价、最高售价存在冲突，请检查！`,
        tipsList: res.data.results,
      });
      return false;
    }
    if (res.code === 0) {
      message.success('修改成功');
    }
  };
  const inputChange = (e: any, index: any, key: any) => {
    dataSourceRef.current[index][key] = Number(e);
  };
  //回车事件
  const onPressEnter = (code: any, index: any) => {
    Promise.resolve()
      .then(() => {
        dataSourceRef.current[index]['edit'] = false;
        index + 1 == dataSourceRef?.current.length
          ? (dataSourceRef.current[0]['edit'] = true)
          : (dataSourceRef.current[index + 1]['edit'] = true);
        setDataSourceRef.current(
          JSON.parse(JSON.stringify(dataSourceRef.current)),
        );
      })
      .then(() => {
        let inputBox =
          index + 1 == dataSourceRef.current.length
            ? document.getElementById(code + '-' + (0).toString())
            : document.getElementById(code + '-' + (index + 1).toString());
        inputBox?.focus();
      });
  };
  const handleColumns = async () => {
    columns.find((item) => item.code === 'org_name')!.hidden =
      !enable_organization;
    setColumns([...columns]);
    if (enable_organization) {
      const data = { level: 2 };
      const res = await ErpRequest.post('/erp-mdm/hxl.erp.org.find', data);
      if (res.code == 0) {
        const org_list = res.data.map((i: any) => ({
          value: i.id,
          label: i.name,
        }));
        setOrgList(org_list);
      }
    }
  };
  // 保存
  const saveOrder = async () => {
    if (!(await form.validateFields())) return; //校验不通过，不保存
    setisLoading(true);
    const data = {
      ...form.getFieldsValue(),
      store_ids: rowData?.map((item: any) => item.id),
      id: preId == -1 ? null : preId,
      details: [],
    };
    const res =
      preId == -1
        ? await XlbFetch.post('/erp/hxl.erp.retailpricetemplate.save', data)
        : await XlbFetch.post('/erp/hxl.erp.retailpricetemplate.update', data);
    setisLoading(false);
    if (res.code === 0) {
      message.success('操作成功');
      onBack();
    }
  };
  // 查询
  const getRecord = async (record: any) => {
    setisLoading(true);
    const data = {
      id: record.id,
    };
    const res = await XlbFetch.post(
      '/erp/hxl.erp.retailpricetemplate.read',
      data,
    );
    if (res.code === 0) {
      const { stores } = res.data;
      const ids = stores.map((item: any) => item.id);
      setStoreIds(ids);
      setRowData(stores);
    }
    setisLoading(false);
  };
  const addStore = async () => {
    const bool = await XlbBasicData({
      isMultiple: true,
      dataType: 'lists',
      type: 'store',
      data: {
        org_ids: form.getFieldValue('org_id')
          ? [form.getFieldValue('org_id')]
          : [],
      },
    });
    if (bool) {
      const ids = rowData.map((v) => v.id);
      const newList = bool
        .filter((v: any) => !ids.includes(v.id) && !storeIds.includes(v.id))
        .map((v: any) => ({
          ...v,
          store_group_name: v.store_group_entity?.name,
        }));
      setRowData([...rowData, ...newList]);
    }
  };
  const uploadRowData = async () => {
    await XlbImportModal({
      templateUrl: `${process.env.BASE_URL}/erp/hxl.erp.retailpricetemplate.detail.template.download`,
      importUrl: `${process.env.BASE_URL}/erp/hxl.erp.retailpricetemplate.detail.import?flag=${record?.flag}&id=${record?.id}`,
      templateName: '零售价模板',
      callback: async (data: any) => {
        if (data.code == 0) {
          fetchDataRef?.current?.();
        }
      },
    });
  };
  const exportItem = async () => {
    let data = {
      ...ref.current?.getFieldsValue(true),
      code: record?.code,
      id: record?.id,
      flag: record?.flag,
      org_id: record?.org_id,
    };
    // form.getFieldValue('checkValue')?.forEach((item: any) => {
    //   data[item] = form.getFieldValue('isShow') === 0 ? true : false
    // })
    setisLoading(true);
    const res = await exportPage(
      '/erp/hxl.erp.retailpricetemplate.detail.export',
      data,
      { responseType: 'blob' },
    );
    const download = new Download();
    download.filename = '零售价格模板.xlsx';
    download.xlsx(res?.data);
    setisLoading(false);
  };
  const deleteStore = async () => {
    await XlbTipsModal({
      tips: `是否删除选中的${selectedList?.length}个门店?`,
      onOkBeforeFunction: async () => {
        selectedList.map((item) => {
          const i =
            typeof item === 'number'
              ? rowData.findIndex((e) => e.id === item)
              : Number(item);
          rowData.splice(i, 1);
        });
        setSelectedList([]);
        return true;
      },
    });
  };
  useEffect(() => {
    handleColumns();
  }, [enable_organization]);
  useEffect(() => {
    setId(record.id);
    if (record.id > 0) {
      form.setFieldsValue({
        org_id: record?.org_id,
        code: record.code,
        name: record.name,
      });
      getRecord(record);
    }
  }, []);
  return (
    <XlbPageContainer>
      <div>
        <div style={{ margin: '0 10px 10px' }}>
          <XlbButton.Group>
            <XlbButton
              disabled={isLoading}
              onClick={saveOrder}
              icon={<XlbIcon name="baocun" />}
              type="primary"
            >
              保存
            </XlbButton>
            <XlbButton
              icon={<XlbIcon name="fanhui" />}
              onClick={() => onBack()}
              type="primary"
            >
              返回
            </XlbButton>
          </XlbButton.Group>
        </div>
        <div>
          <XlbBlueBar title={'零售价模板信息'}></XlbBlueBar>
          <XlbBasicForm
            layout={'horizontal'}
            style={{ marginTop: 20, maxWidth: 1200 }}
            form={form}
          >
            <XlbBasicForm.Item
              label="零售价模板代码"
              name="code"
              rules={[
                { required: true, message: '请输入零售价模板代码' },
                { pattern: /^[A-Za-z0-9]+$/, message: '仅支持数字和字母' },
              ]}
            >
              <XlbInput
                style={{ width: 180 }}
                size="small"
                disabled={record?.flag}
                maxLength={10}
                allowClear
              />
            </XlbBasicForm.Item>
            <XlbBasicForm.Item
              label="零售价模板名称"
              name="name"
              rules={[{ required: true, message: '请输入零售价模板名称' }]}
            >
              <XlbInput
                style={{ width: 180 }}
                size="small"
                disabled={record?.flag}
                maxLength={10}
                allowClear
              />
            </XlbBasicForm.Item>
            {enable_organization ? (
              <XlbBasicForm.Item
                label="组织"
                name="org_id"
                rules={[{ required: true, message: '请选择组织' }]}
              >
                <XlbSelect
                  style={{ width: 180 }}
                  size="small"
                  options={orgList}
                  disabled={!!rowData?.length}
                  allowClear
                />
              </XlbBasicForm.Item>
            ) : null}
          </XlbBasicForm>
          <Tabs
            activeKey={tabsKey}
            defaultActiveKey={'STORE'}
            style={{ paddingLeft: 16 }}
            onChange={(key) => setTabsKey(key)}
          >
            <TabPane tab={'门店信息'} key={'STORE'}></TabPane>
            <TabPane
              disabled={record?.id == -1}
              tab={'商品信息'}
              key={'GOODS'}
            ></TabPane>
          </Tabs>
          {tabsKey == 'STORE' && (
            <>
              <div style={{ margin: '0 16px 10px' }}>
                <XlbButton.Group>
                  <XlbButton
                    label="添加"
                    onClick={addStore}
                    type="primary"
                    icon={<span className="iconfont icon-jia" />}
                  >
                    添加
                  </XlbButton>
                  <XlbButton
                    label="删除"
                    onClick={deleteStore}
                    type="primary"
                    disabled={!selectedList?.length}
                    icon={<span className="iconfont icon-shanchu" />}
                  >
                    删除
                  </XlbButton>
                </XlbButton.Group>
              </div>
              <div></div>
              <XlbTable
                columns={columns}
                dataSource={rowData}
                style={{ height: 'calc(100vh - 475px)', margin: '0 16px' }}
                primaryKey="id"
                selectMode="multiple"
                total={rowData?.length}
                showSearch
                onSelectRow={(e) => setSelectedList(e)}
                selectedRowKeys={selectedList}
              ></XlbTable>
            </>
          )}
          {tabsKey == 'GOODS' && (
            <div>
              <XlbPageContainer
                tableColumn={categoryArr}
                immediatePost={true}
                url="/erp/hxl.erp.retailpricetemplate.detail.page"
                prevPost={() => {
                  const formValues = ref.current?.getFieldsValue(true);
                  const data = {
                    ...formValues,
                    code: record?.code,
                    id: record.id,
                    flag: record?.flag,
                    org_id: record?.org_id,
                  };
                  return {
                    ...data,
                  };
                }}
              >
                <XlbPageContainer.ToolBtnNoStyle>
                  {(context) => {
                    const { fetchData, dataSource, setDataSource } = context;
                    fetchDataRef.current = fetchData;
                    dataSourceRef.current = dataSource;
                    setDataSourceRef.current = setDataSource;
                    return (
                      <>
                        <div style={{ paddingBottom: 12, flexWrap: 'nowrap' }}>
                          <XlbButton.Group>
                            {hasAuth(['零售价模板', '查询']) && (
                              <XlbButton
                                label="查询"
                                onClick={() => fetchData()}
                                type="primary"
                                icon={<span className="iconfont icon-sousuo" />}
                              />
                            )}
                            {hasAuth(['零售价模板', '导入']) && (
                              <XlbButton
                                label="导入"
                                type="primary"
                                disabled={
                                  !dataSourceRef?.current?.length || isLoading
                                }
                                onClick={uploadRowData}
                                icon={<span className="iconfont icon-daoru" />}
                              />
                            )}
                            {hasAuth(['零售价模板', '导出']) && (
                              <XlbButton
                                label="导出"
                                type="primary"
                                disabled={
                                  !dataSourceRef?.current?.length || isLoading
                                }
                                onClick={exportItem}
                                icon={<span className="iconfont icon-daochu" />}
                              />
                            )}
                            {hasAuth(['零售价模板', '编辑']) && (
                              <XlbButton
                                label="批量设置"
                                type="primary"
                                disabled={isLoading}
                                onClick={() => setBatchVisible(true)}
                                icon={
                                  <span className="iconfont icon-xiugai1" />
                                }
                              />
                            )}
                          </XlbButton.Group>
                        </div>
                        <XlbProForm
                          formRef={ref}
                          initialValues={{
                            modify_price: true,
                          }}
                          formList={[
                            {
                              id: ErpFieldKeyMap.erpSaleItemId,
                              itemSpan: 12,
                            },
                            {
                              id: ErpFieldKeyMap.erpSaleproductDeptId,
                              itemSpan: 12,
                            },
                            {
                              id: ErpFieldKeyMap.erpCategoryIds,
                              itemSpan: 12,
                              onChange(ids, data, formValues) {
                                if (
                                  Array.isArray(formValues) &&
                                  formValues.length
                                ) {
                                  data?.setFieldValue(
                                    'category_names',
                                    formValues.map((item: any) => item.name) ||
                                      [],
                                  );
                                } else {
                                  data?.setFieldValue(
                                    'category_names',
                                    undefined,
                                  );
                                }
                              },
                            },
                            {
                              id: 'inputPanel',
                              label: '其他条件',
                              name: 'checkValue',
                              fieldProps: {
                                initialValue: 'false',
                                allowClear: true,
                                options: checkOptions,
                                items: [
                                  {
                                    label: '仅显示',
                                    key: true,
                                  },
                                  {
                                    label: '不显示',
                                    key: false,
                                  },
                                ],
                                style: {
                                  width: 150,
                                },
                                width: 150,
                              },
                              itemSpan: 4,
                            },
                            {
                              id: ErpFieldKeyMap.erpModifyPrice,
                              itemSpan: 8,
                            },
                          ]}
                        ></XlbProForm>
                      </>
                    );
                  }}
                </XlbPageContainer.ToolBtnNoStyle>
                <div
                  style={{ height: 'calc(100vh - 495px)', overflow: 'auto' }}
                >
                  <XlbPageContainer.Table
                    style={{ height: 'calc(100vh - 540px)' }}
                    keepDataSource={true}
                    selectMode="single"
                    // total={dataSourceRef?.current?.length}
                    // pageSize={100}
                  />
                </div>
              </XlbPageContainer>
            </div>
          )}
        </div>
        <BatchChange
          record={record}
          getData={fetchDataRef?.current}
          visible={batchVisible}
          handleCancel={() => setBatchVisible(false)}
        />
      </div>
    </XlbPageContainer>
  );
};

export default Index;
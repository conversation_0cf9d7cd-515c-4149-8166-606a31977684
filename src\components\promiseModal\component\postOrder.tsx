import { columnWidthEnum } from '@/data/common/constant';
import {
  SearchFormType,
  XlbBasicForm,
  XlbForm,
  XlbTable,
  XlbTableColumnProps,
} from '@xlb/components';
import { XlbFetch } from '@xlb/utils';
import { Tabs, Tooltip } from 'antd';
import _ from 'lodash';
import { useEffect, useState } from 'react';
import { PostOrderReadInfo } from '../server';
const { TabPane } = Tabs;

const StateType = [
  {
    label: '销售',
    value: 'SALE',
  },
  {
    label: '退货',
    value: 'RETURN',
  },
  {
    label: '赠送',
    value: 'PRESENT',
  },
];
const formList: SearchFormType[] = [
  {
    type: 'input',
    disabled: true,
    name: 'serial_number',
    label: '单据号',
  },
  {
    type: 'input',
    disabled: true,
    name: 'store_name',
    label: '门店',
  },
  {
    type: 'input',
    disabled: true,
    name: 'bizday',
    label: '营业日',
  },
  {
    type: 'input',
    disabled: true,
    name: 'card_print_num',
    label: '会员卡号',
  },
  {
    type: 'input',
    disabled: true,
    name: 'customer_phone',
    label: '会员手机号',
  },
  {
    type: 'input',
    disabled: true,
    name: 'customer_name',
    label: '持卡人姓名',
  },
  {
    type: 'input',
    disabled: true,
    name: 'card_type_name',
    label: '会员卡类型名称',
  },
  {
    type: 'input',
    disabled: true,
    name: 'discount_money',
    label: '商品折扣',
  },

  {
    type: 'input',
    disabled: true,
    name: 'point',
    label: '积分值',
  },
  {
    type: 'input',
    disabled: true,
    name: 'mgr_discount_money',
    label: '结算抹零',
  },
  {
    type: 'input',
    disabled: true,
    name: 'round_money',
    label: '四舍五入',
  },
  {
    type: 'input',
    disabled: true,
    name: 'operator',
    label: '操作人',
  },
  {
    type: 'datePicker',
    disabled: true,
    name: 'operate_date',
    label: '操作时间',
  },
  {
    type: 'input',
    disabled: true,
    name: 'machine',
    label: '收款机',
  },
  {
    type: 'input',
    disabled: true,
    name: 'client_name',
    label: '客户',
  },
  {
    type: 'input',
    disabled: true,
    name: 'external_no',
    label: '外部流水号',
  },
  {
    type: 'input',
    disabled: true,
    name: 'package_fee',
    label: '打包费',
  },
  {
    type: 'input',
    disabled: true,
    name: 'memo',
    label: '留言备注',
    width: 648,
  },
];
const detailArr: XlbTableColumnProps<any>[] = [
  {
    name: '序号',
    code: '_index',
    width: columnWidthEnum.INDEX,
    align: 'center',
    lock: true,
  },
  {
    name: '商品名称',
    code: 'item_name',
    width: 180,
    features: { sortable: true },
  },
  {
    name: '条码',
    code: 'item_bar_code',
    width: columnWidthEnum.ITEM_BAR_CODE,
    features: { sortable: true },
  },
  {
    name: '规格',
    code: 'item_spec',
    width: columnWidthEnum.ITEM_SPEC,
    features: { sortable: true },
  },
  {
    name: '单位',
    code: 'basic_unit',
    width: 160,
    features: { sortable: true },
  },
  {
    name: '数量',
    code: 'basic_quantity',
    width: 100,
    features: { sortable: true },
  },
  {
    name: '单价',
    code: 'basic_original_price',
    width: 140,
    features: { sortable: true },
    // hidden: !hasAuth(userInfo, ['已结账单查询/金额', '查询'])
  },
  {
    name: '折扣',
    code: 'discount_money',
    width: 130,
    features: { sortable: true },
    // hidden: !hasAuth(userInfo, ['已结账单查询/金额', '查询'])
  },
  {
    name: '金额',
    code: 'money',
    width: 110,
    features: { sortable: true },
    // hidden: !hasAuth(userInfo, ['已结账单查询/金额', '查询'])
  },
  {
    name: '供应商',
    code: 'supplier_name',
    width: 130,
    features: { sortable: true },
  },
  {
    name: '返款比例',
    code: 'rebate_rate',
    width: 130,
    features: { sortable: true },
    render: (text: any) =>
      text && !isNaN(text) ? `${text * 100}%` : text && isNaN(text) ? text : '',
    // hidden: !hasAuth(userInfo, ['已结账单查询/金额', '查询'])
  },
  {
    name: '代销金额',
    code: 'refund_money',
    width: 130,
    features: { sortable: true },
    // hidden: !hasAuth(userInfo, ['已结账单查询/金额', '查询'])
  },
  {
    name: '状态',
    code: 'state',
    width: columnWidthEnum.ORDER_STATE,
    features: { sortable: true },
  },
  {
    name: '销售模式',
    code: 'sale_mode',
    width: 140,
  },
  {
    name: '关联活动单据',
    code: 'policy_fid',
    width: 130,
    features: { sortable: true },
  },
  {
    name: '备注',
    code: 'memo',
    width: columnWidthEnum.MEMO,
    align: 'right',
    features: { sortable: true },
  },
];
const detailArr2: XlbTableColumnProps<any>[] = [
  {
    name: '序号',
    code: '_index',
    width: columnWidthEnum.INDEX,
    align: 'center',
  },
  {
    name: '收款方式',
    code: 'payment_type',
    width: 100,
    features: { sortable: true },
  },
  {
    name: '付款金额',
    code: 'money',
    width: 140,
    features: { sortable: true },
  },
  {
    name: '姓名',
    width: 100,
    code: 'name',
    features: { sortable: true },
  },
  {
    name: '手机号码',
    code: 'phone',
    width: 120,
  },
  {
    name: '卡号',
    code: 'card_print_num',
    width: 180,
  },
];
const detailArr3: XlbTableColumnProps<any>[] = [
  {
    name: '序号',
    code: '_index',
    width: columnWidthEnum.INDEX,
    align: 'center',
  },
  {
    name: '消费券项目',
    code: 'name',
    width: 140,
    features: { sortable: true },
  },
  {
    name: '付款金额',
    code: 'money',
    width: 140,
    features: { sortable: true },
  },
  {
    name: '',
    width: 300,
  },
];

const PostOrder = (props: any) => {
  const { fid, url } = props;
  const [formModel] = XlbBasicForm.useForm<any>();

  const [tabKey, setTabKey] = useState<string>('items');
  const [tableLoading, setTableLoading] = useState<any>(false);
  // 列表数据
  const [fidDataList, setFidDataList] = useState<any>([]);
  const [rowData, setRowData] = useState<any[]>([]);
  const [postData, setPostData] = useState<any[]>([]);
  const [postDataConlon, setPostDataConlon] = useState<any[]>([]);
  // 表头
  const detailArrCopy = JSON.parse(JSON.stringify(detailArr));
  const detailArr2Copy = JSON.parse(JSON.stringify(detailArr2));
  const detailArr3Copy = JSON.parse(JSON.stringify(detailArr3));
  const [itemArrdetail, setItemArrDetail] =
    useState<XlbTableColumnProps<any>[]>(detailArrCopy);

  const tableRender = (item: any) => {
    switch (item.code) {
      case 'item_name':
        return (item.render = (value: any, record: any) => (
          <>
            {record?.state == 'RETURN' ? (
              <img
                style={{ width: 18, height: 18, marginTop: -4 }}
                src={require('./images/return.png')}
              ></img>
            ) : null}
            <span>{value}</span>
            <span
              style={
                Number(record.discount_money) > 0
                  ? {
                      display: 'inline-block',
                      width: '12px',
                      height: '14px',
                      background: '#F53F3F',
                      borderRadius: '3px',
                      textAlign: 'center',
                      lineHeight: '14px',
                      fontSize: 'x-small',
                      color: 'white',
                      marginLeft: '5px',
                    }
                  : { display: 'none' }
              }
            >
              折
            </span>
          </>
        ));
        break;
      case 'basic_original_price':
        item.render = (value: any, record: any) => (
          <div>{isNaN(value) ? value : Number(value).toFixed(4)}</div>
        );
        break;
      case 'basic_quantity':
        item.render = (value: any, record: any) => (
          <div>{isNaN(value) ? value : Number(value).toFixed(3)}</div>
        );
        break;
      case 'money':
      case 'discount_money':
        item.render = (value: any, record: any) => (
          <div>{isNaN(value) ? value : Number(value).toFixed(3)}</div>
        );
        break;
      case 'state':
        item.render = (value: any, record: any) => {
          return (
            <div>{StateType.filter((v) => v.value == value)[0].label}</div>
          );
        };
        break;
      case 'policy_fid':
        item.render = (value: any, record: any) => {
          return <div>{value ? value : ''}</div>;
        };
        break;
      case 'payment_type':
      case 'name':
      case 'memo':
        item.render = (value: any) => {
          return (
            <Tooltip placement="topLeft" autoAdjustOverflow title={value}>
              <div>{value}</div>
            </Tooltip>
          );
        };
        break;
      case 'rebate_rate':
        item.render = (value: any) => {
          return (
            <div>
              {value && !isNaN(value)
                ? `${value * 100}%`
                : value && isNaN(value)
                  ? value
                  : ''}
            </div>
          );
        };
        break;
    }
    return item;
  };

  const openRefOrder = _.debounce(async (fid) => {
    setTableLoading(true);
    setFidDataList([]);
    formModel.setFieldsValue({});
    const res = url
      ? await XlbFetch.post(url, { fid: fid })
      : await PostOrderReadInfo({ fid: fid });
    if (res?.code == 0) {
      // 表单的值
      formModel.setFieldsValue({
        ...res.data,
        package_fee: isNaN(res.data.package_fee)
          ? res.data.package_fee
          : Number(res.data.package_fee || 0).toFixed(2),
        discount_money: isNaN(res.data.discount_money)
          ? res.data.discount_money
          : Number(res.data.discount_money).toFixed(2),
        point: isNaN(res.data.point)
          ? res.data.point
          : Number(res.data.point).toFixed(2),
        mgr_discount_money: isNaN(res.data.mgr_discount_money)
          ? res.data.mgr_discount_money
          : Number(res.data.mgr_discount_money).toFixed(2),
        round_money: isNaN(res.data.round_money)
          ? res.data.round_money
          : Number(res.data.round_money).toFixed(2),
      });
      // 列表的值
      setRowData(res.data.details);
      setPostData(res.data.pos_payments);
      setPostDataConlon(res?.data?.pos_order_coupons);
      if (tabKey === 'items') {
        setFidDataList(res.data.details);
      } else if (tabKey === 'collection_information') {
        setFidDataList(res.data.pos_payments);
      } else {
        setFidDataList(res.data.pos_order_coupons);
      }
    }
    setTableLoading(false);
  }, 50);

  // 切换表格数据
  const onChangeKey = (key: any) => {
    setTabKey(key);
    let tableArr: any = [];
    switch (key) {
      case 'items':
        tableArr = detailArrCopy;
        setFidDataList(rowData);
        break;
      case 'collection_information':
        tableArr = detailArr2Copy;
        setFidDataList(postData);
        break;
      case 'voucher_detail':
        tableArr = detailArr3Copy;
        setFidDataList(postDataConlon);
        break;
    }
    tableArr.map((v: any) => tableRender(v));
    setItemArrDetail(tableArr);
  };

  useEffect(() => {
    openRefOrder(fid);
    itemArrdetail.map((v) => tableRender(v));
  }, []);

  return (
    <>
      <header>
        <XlbForm
          style={{ marginTop: 15 }}
          formList={formList}
          form={formModel}
          isHideDate={true}
        />

        <Tabs defaultActiveKey={'items'} onChange={(key) => onChangeKey(key)}>
          <TabPane tab="商品项目" key={'items'} />
          <TabPane tab="收款信息" key={'collection_information'} />
          <TabPane tab="消费券明细" key={'voucher_detail'} />
        </Tabs>
      </header>

      <XlbTable
        isLoading={tableLoading}
        style={{ height: 400, maxHeight: 400, overflowY: 'scroll' }}
        hideOnSinglePage={false}
        showSearch={true}
        columns={itemArrdetail}
        total={fidDataList?.length}
        dataSource={fidDataList}
        key={fidDataList?.length}
      ></XlbTable>
    </>
  );
};

export default PostOrder;

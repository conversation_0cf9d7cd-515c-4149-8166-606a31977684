export const WholesaleCustomerKeyMap = {
  erpOrgIdsMultiplelevel3: 'erpOrgIdsMultiplelevel3'
}

export const wholesaleCustomerConfig: any[] = [
  {
    tag: 'ERP',
    label: '批发组织',
    name: 'org_ids',
    id: WholesaleCustomerKeyMap?.erpOrgIdsMultiplelevel3,
    dependencies: ['level', 'id'], // level：组织管理、id: 组织管理-组织id
    fieldProps: {
      treeModalConfig: {
        title: '选择组织',
        url: '/erp-mdm/hxl.erp.org.tree',
        dataType: 'trees',
        checkable: true, // 是否多选
        primaryKey: 'id',
        afterPost: (data: any) => {
          return data.filter((item: any) => item.level === 3)
        },
        params: {
          level: 3
        }
      }
    },
    onChange: (_ids: any, form: any) => {
      form?.setFieldValue('store_ids', [])
    },
    formItemProps: {
      getValueFromEvent: (value: any) => {
        if (!Array.isArray(value)) return
        return value
      }
    },
    componentType: 'inputDialog'
  }
]
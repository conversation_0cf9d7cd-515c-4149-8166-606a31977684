import { XlbTableColumnProps } from '@xlb/components';
// 列表数据
export const tableList: XlbTableColumnProps<any>[] = [
  {
    name: '序号',
    code: '_index',
    width: 65,
    align: 'center',
  },
  {
    name: '领用原因',
    code: 'name',
    width: 220,
    features: { sortable: true },
  },
  {
    name: '状态',
    code: 'flag',
    width: 120,
    features: { sortable: true },
  },
  {
    name: '创建时间',
    code: 'create_time',
    width: 120,
    features: { sortable: true, format: 'TIME' },
  },
  {
    name: '创建人',
    code: 'create_by',
    width: 120,
    features: { sortable: true },
  },
  {
    name: '更新时间',
    code: 'update_time',
    width: 120,
    features: { sortable: true, format: 'TIME' },
  },
  {
    name: '更新人',
    code: 'update_by',
    width: 120,
    features: { sortable: true },
  },
];

// 
import {XlbFetch as ErpRequest } from '@xlb/utils'


export default {
  // 删除
  checkInfo: async (data={}) => {
    return await ErpRequest.post('/erp/hxl.erp.server.org.check.default', data )
  },
  update: async (data={}) => {
    return await ErpRequest.post('/erp-mdm/hxl.erp.org.update', data )
  },
  save: async (data={}) => {
    return await ErpRequest.post('/erp-mdm/hxl.erp.org.save', data )
  },
  findOrg:  async (data={}) => {
    return await ErpRequest.post('/erp-mdm/hxl.erp.org.find', data )
  },
  //
}
@import 'tailwindcss/base';

// @import 'tailwindcss/components';

@import 'tailwindcss/utilities';
@import '@/assets/style/margin';

// 通用样式
#root {
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
  overflow: auto;
  font-weight: 400;
  font-size: 13px;
  font-family: 'NUM', 'PingFang SC', helvetica, arial, verdana, sans-serif;
  background-color: @color_fff;
  border-radius: 4px;
}

// flex布局样式
.flex {
  display: flex;
}
.row-flex {
  display: flex;
  flex-direction: row;
}
.v-flex {
  display: flex;
  align-items: center;
}
.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}
.flex-wrap {
  display: flex;
  flex-wrap: wrap;
}
.flex-1 {
  flex: 1;
}
.flex-column {
  display: flex;
  flex-direction: column;
}
.flex-row-reverse {
  display: flex;
  flex-direction: row-reverse;
}

.text-center {
  text-align: center;
}
.text-right {
  text-align: right;
}

.link {
  color: @color_link;
  text-decoration: underline;
  cursor: pointer;
}
.cursors {
  cursor: pointer;
}

.info {
  color: @color_init;
}

.default {
  color: @color_link;
}

.warning {
  color: @color_warning;
}

.success {
  color: @color_success;
}

.danger {
  color: @color_danger;
}
.v-flex {
  display: flex;
  align-items: center;
}
.blue {
  color: #3d66fe;
}
.ml-auto {
  margin-left: auto;
}
.abutten {
  text-decoration: 'none';
  font-size: 13;
  color: #3d66fe;
}
.invalid {
  color: @color_invalid;
}

.purple {
  color: @color_purple;
}

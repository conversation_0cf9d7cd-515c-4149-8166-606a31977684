import NiceModal from '@ebay/nice-modal-react';
import {
  XlbBasicForm,
  XlbButton,
  XlbInputDialog,
  XlbModal,
  XlbSelect,
  XlbTable,
  XlbTipsModal,
} from '@xlb/components';
import { RenderExtra } from '@xlb/components/dist/components/XlbBasicTable';
import { Tooltip } from 'antd';
import { cloneDeep } from 'lodash';
import { useEffect, useState } from 'react';
import { getAllStore, getStore, readDetail } from '../server';
import type { OperateItemProps } from '../types';

const operateItemModal = NiceModal.create((props: OperateItemProps) => {
  const [form] = XlbBasicForm.useForm();
  const { title, type, dataItem, onOk, tab } = props;
  const DEFAULT_ROW_ITEM = {
    store_id: null as number | null,
    share_store_ids: [] as number[],
  };
  const [rowData, setRowData] = useState<any[]>([DEFAULT_ROW_ITEM]);
  const [selectedRowKeys, setSelectedRowKeys] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const modal = NiceModal.useModal();

  const getData = async () => {
    if (dataItem) {
      form.setFieldValue('supplier_id', [dataItem.supplier_id]);
      const res = await readDetail({
        store_id: dataItem.store_id,
        supplier_id: dataItem.supplier_id,
      });
      if (res.code === 0) {
        setRowData(res.data.details);
      }
      return;
    }
    setSelectedRowKeys([]);
    setRowData([DEFAULT_ROW_ITEM]);
  };

  // 操作表格项
  const addItemRow = () =>
    setRowData([...rowData, cloneDeep(DEFAULT_ROW_ITEM)]);
  const removeItemRow = () => {
    setRowData(
      rowData.filter(
        (_item, index) => !selectedRowKeys.includes(String(index)),
      ),
    );
    setSelectedRowKeys([]);
  };

  interface OptionItem {
    label: string;
    value: number;
    org_parent_id: number;
  }
  // 采购共享中心列表
  const [storeList, setStoreList] = useState<OptionItem[]>([]);
  const getStoreList = async () => {
    const res = await getStore({
      center_flag: true,
      enable_organization: false,
    });
    const formatList = (res?.data?.content || []).map((item: any) => ({
      ...item,
      label: item.store_name,
      value: item.id,
    }));
    setStoreList(formatList);
  };

  // 实际门店列表
  const [shareStoreList, setShareStoreList] = useState<OptionItem[]>([]);
  const getShareStoreList = async () => {
    const res = await getAllStore({
      center_flag: true,
      enable_organization: false,
    });
    const formatList = (res?.data?.content || []).map((item: any) => ({
      ...item,
      label: item.store_name,
      value: item.id,
    }));
    setShareStoreList(formatList);
  };
  useEffect(() => {
    if (modal.visible) {
      getData();
      getStoreList();
      getShareStoreList();
    } else {
      form.resetFields();
    }
  }, [modal.visible]);
  return (
    <XlbModal
      width={1050}
      open={modal.visible}
      title={title}
      isCancel={true}
      keyboard={false}
      confirmLoading={loading}
      onOk={async () => {
        if (type === 'view') {
          modal.hide();
        } else {
          form.submit();
        }
      }}
      onCancel={modal.hide}
    >
      <div style={{ paddingTop: 12 }}>
        {(type !== 'view' || tab) && (
          <XlbBasicForm
            form={form}
            labelCol={{ span: 6 }}
            onFinish={async (formValues) => {
              // 必填校验
              const headerMap = {
                store_name: '采购共享中心',
                share_store_names: '实际门店',
              };
              const incompleteFieldsList = rowData
                .map((item, index) => {
                  const missingFields = [];
                  if (!item.store_id) missingFields.push(headerMap.store_name);
                  if (!item.share_store_ids.length)
                    missingFields.push(headerMap.share_store_names);
                  return { ...item, rowIndex: index + 1, missingFields };
                })
                .filter((item) => item?.missingFields?.length);
              if (incompleteFieldsList?.length) {
                XlbTipsModal({
                  tips: (
                    <>
                      <div>以下数据未填写完整，请检查！</div>
                      <div
                        style={{
                          display: 'flex',
                          justifyContent: 'center',
                          marginBottom: 12,
                          flexDirection: 'column',
                        }}
                      >
                        {incompleteFieldsList.map((v: any) => (
                          <div>
                            【第{v.rowIndex}行】缺少：$
                            {v.missingFields.join('、')}
                          </div>
                        ))}
                      </div>
                    </>
                  ),
                });
                return false;
              }
              // 检查采购共享中心是否与实际门店相同
              const repeatList = rowData
                .map((item, index) => ({
                  ...item,
                  line: index + 1,
                  store_name: item.store_name || item.short_store?.store_name,
                }))
                .filter((item) => item.share_store_ids.includes(item.store_id));
              if (repeatList?.length) {
                XlbTipsModal({
                  tips: (
                    <>
                      <div>以下数据重复，请检查！</div>
                      <div
                        style={{
                          display: 'flex',
                          justifyContent: 'center',
                          marginBottom: 12,
                          flexDirection: 'column',
                        }}
                      >
                        {repeatList.map((v: any) => (
                          <div>
                            【第{v.line}行】采购共享中心【{v.store_name}
                            】与实际门店相同
                          </div>
                        ))}
                      </div>
                    </>
                  ),
                });
                return false;
              }
              // 校验采购共享中心与实际门店不能属于同一个二级组织
              const repeatOrgList = rowData
                .map((item, index) => {
                  // 已选采购共享中心二级组织(单选)
                  const storeOrgId = storeList.find(
                    (v) => v.value === item.store_id,
                  )?.org_parent_id;
                  // 已选实际门店二级组织(多选)
                  const shareStoreOrgIds = shareStoreList
                    .filter((v) => item.share_store_ids.includes(v.value))
                    .map((v) => ({
                      org_parent_id: v.org_parent_id,
                      label: v.label,
                    }));
                  // 与采购共享中心重复二级组织的实际门店
                  const repeatOrgNames = shareStoreOrgIds
                    .filter((v) => v.org_parent_id === storeOrgId)
                    ?.map((v) => v.label);
                  return {
                    line: index + 1,
                    store_name: item.store_name || item.short_store?.store_name,
                    share_store_names: repeatOrgNames.length
                      ? repeatOrgNames.join('、')
                      : '',
                  };
                })
                .filter((item) => item.share_store_names);

              if (repeatOrgList.length) {
                XlbTipsModal({
                  tips: (
                    <>
                      <div>
                        采购共享中心与实际门店不能属于同一个二级组织，请检查！
                      </div>
                      <div
                        style={{
                          display: 'flex',
                          justifyContent: 'center',
                          marginBottom: 12,
                          flexDirection: 'column',
                        }}
                      >
                        {repeatOrgList.map(
                          (v: {
                            store_name: any;
                            line: number;
                            share_store_names: string;
                          }) => (
                            <div>
                              【第{v.line}行】采购共享中心【{v.store_name}
                              】与实际门店【{v.share_store_names}
                              】属于同一二级组织
                            </div>
                          ),
                        )}
                      </div>
                    </>
                  ),
                });
                return false;
              }

              // 只有保存成功才关闭弹窗
              setLoading(true);
              const success = await onOk({
                supplier_id: formValues?.supplier_id?.[0],
                details: rowData,
              });
              setLoading(false);
              success && modal.hide();
            }}
          >
            <XlbBasicForm.Item
              name="supplier_id"
              label="供应商"
              rules={[{ required: true, message: '供应商不能为空' }]}
            >
              <XlbInputDialog
                disabled={type === 'edit' || tab === 'summary'}
                width={200}
                placeholder="请选择供应商"
                dialogParams={{
                  type: 'supplier',
                  isMultiple: false,
                }}
              />
            </XlbBasicForm.Item>
          </XlbBasicForm>
        )}
        {type !== 'view' && (
          <div style={{ marginBottom: 12 }}>
            <XlbButton.Group>
              <XlbButton type="primary" onClick={addItemRow}>
                新增
              </XlbButton>
              <XlbButton
                type="primary"
                onClick={removeItemRow}
                disabled={!selectedRowKeys?.length}
              >
                删除
              </XlbButton>
            </XlbButton.Group>
          </div>
        )}
        <XlbTable
          style={{ flex: 1, height: 285, overflowY: 'auto' }}
          columns={[
            { name: '序号', code: '_index', width: 50, align: 'center' },
            {
              name: '采购共享中心',
              code: 'store_id',
              width: 280,
              features: { tips: '采购单内的收、退货门店' },
              align: 'left',
              render: (value: any, _record: any, index: RenderExtra) => {
                return (
                  <div onClick={(e) => e.stopPropagation()}>
                    <XlbSelect
                      width={260}
                      value={value}
                      showSearch
                      filterOption={(input, option) =>
                        `${option?.children ? option.children.toString() : ''}`
                          .toLowerCase()
                          .includes(input.toLowerCase())
                      }
                      disabled={type === 'view'}
                      onChange={(id, item: any) => {
                        rowData[index.index].store_id = id;
                        rowData[index.index].store_name = item.children;
                        setRowData([...rowData]);
                      }}
                    >
                      {storeList.map((item) => (
                        <XlbSelect.Option key={item.value} value={item.value}>
                          {item.label}
                        </XlbSelect.Option>
                      ))}
                    </XlbSelect>
                  </div>
                );
              },
            },
            {
              name: '实际门店',
              code: 'share_store_ids',
              width: 280,
              features: { tips: '采购单内的实际收、退货门店' },
              align: 'left',
              render: (value: any, _record: any, index: RenderExtra) => {
                return (
                  <div onClick={(e) => e.stopPropagation()}>
                    <XlbSelect
                      width={260}
                      value={value}
                      mode="multiple"
                      showSearch
                      filterOption={(input, option) =>
                        `${option?.children ? option.children.toString() : ''}`
                          .toLowerCase()
                          .includes(input.toLowerCase())
                      }
                      disabled={type === 'view'}
                      onChange={(ids, items) => {
                        rowData[index.index].share_store_ids = ids;
                        rowData[index.index].share_store_names = items.map(
                          (v: any) => v.children,
                        );
                        setRowData([...rowData]);
                      }}
                      maxTagPlaceholder={(omittedValues) => {
                        return (
                          <Tooltip
                            title={omittedValues
                              .map(({ label }) => label)
                              .join(', ')}
                          >
                            <span>
                              +
                              {omittedValues?.length}
                            </span>
                          </Tooltip>
                        );
                      }}
                    >
                      {shareStoreList.map((item) => (
                        <XlbSelect.Option key={item.value} value={item.value}>
                          {item.label}
                        </XlbSelect.Option>
                      ))}
                    </XlbSelect>
                  </div>
                );
              },
            },
          ]}
          dataSource={rowData}
          showSizeChanger={false}
          total={rowData?.length ?? 0}
          selectMode="multiple"
          disabled={type === 'view'}
          selectedRowKeys={selectedRowKeys}
          onSelectRow={setSelectedRowKeys}
        />
      </div>
    </XlbModal>
  );
});

export default operateItemModal;

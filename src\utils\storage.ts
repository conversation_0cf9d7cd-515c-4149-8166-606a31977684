/*
 !警告: 改动这个文件的代码，需要通知前端董海洋且进行全局测试，否则不允许上线
*/

enum StorageType {
  l = 'localStorage',
  s = 'sessionStorage'
}
interface StorageItem {
  value: any
  expires?: number
}

export interface KeyValueProps {
  /*
    作用：缓存用户信息，获取用户权限
    设置：登录时
    清除：退出时
   */
  userInfo: any
  access_token: any
  old_access_token: any
  /*
    作用：用户token
    设置：登录时
    清除：退出时
   */
  stockWarning: any
  newStocking: any
  /*
    作用：缓存调出单查询条件
    设置：打开调出单tab页面的时候
    清除：关闭调出单tab页面清除
   */
  shippingOrder: any
  deliveryOrder: any
  /*
    作用：缓存打开的tab的页面 （张福旭）
    设置：每次打开都会进行更新
    清除：刷新页面做了清除
   */
  tabList: any
  /*
    作用：缓存当前的子模块（张福旭）
    设置：切换模块会更新
    清除：刷新页面/退出时，做了清除
      */
  PromotionSpecial: any
  /*
    作用：缓存上次打开的模块信息 scm erp等（董海洋）
    设置：退出账户，选择scm erp等模块的时候
    清除：登录账户没有相关权限的时候进行清除
   */
  '$$cache-menu': any
  /*
    作用：正在使用的模块 erp scm wms等
    设置：选择scm erp等模块的时候
    清除：退出登录的时候
   */
  menuType: any

  /*
    作用：缓存施工项管理查询条件 (韩鑫)
    设置：打开调出单tab页面的时候
    清除：关闭调出单tab页面清除
   */
  buildItem: any
  CommercialQuery: any

  /*
    作用：非配送中心门店是否启用调拨确认模块
    设置：登录系统选择erp模块时
    清除：退出登录时
   */
  enableInCheckOrder: boolean
  storeRetailPrice: any
  /* 作用:促销特价模块缓存 */

  /** 预览客户变动项，用于取消layout utils 路由跳转副作用 */
  isFormClueTemplatePreview: string
}

export type CacheKey = keyof KeyValueProps

/*
  作用：增加缓存，减小频繁的 JSON.parse 带来的性能消耗
  增删改查，都需要同步更改缓存
*/
let cache = new Map()
class MyStorage {
  storage: Storage

  constructor(type: StorageType) {
    this.storage = type === StorageType.l ? window.localStorage : window.sessionStorage
  }

  set(key: any, value: any, expires: number = 7) {
    const source: StorageItem = { value: '' }
    // 增加存储时间 默认缓存 7 天时间
    source.expires = new Date().getTime() + 1000 * 60 * 60 * 24 * expires
    source.value = value
    cache.set(key, source)
    const data = JSON.stringify(source)
    this.storage.setItem(key, data)
  }

  get(key: any) {
    const value = this.storage.getItem(key)
    const cacheValue = cache.get(key)
    let source
    if (value) {
      if (cacheValue) {
        source = cacheValue
      } else {
        const parseValue = JSON.parse(value)
        cache.set(key, parseValue)
        source = parseValue
      }
      const expires = source.expires
      const now = new Date().getTime()
      if (expires && now > expires) {
        this.delete(key)
        return null
      }
      return source.value
    }
  }

  delete(key: CacheKey) {
    cache.delete(key)
    this.storage.removeItem(key)
  }

  clear() {
    cache = new Map()
    this.storage.clear()
  }
}

const LStorage = new MyStorage(StorageType.l)
const SStorage = new MyStorage(StorageType.s)

export { LStorage, SStorage }

import { XlbProgress } from '@/components/common';
import { ErpFieldKeyMap } from '@/data/common/fieldListConfig';
import { useBaseParams } from '@/hooks/useBaseParams';
import { exportPage } from '@/services/system';
import { hasAuth } from '@/utils';
import Download from '@/utils/downloadBlobFile';
import NiceModal from '@ebay/nice-modal-react';
import type { ContextState, XlbTableColumnProps } from '@xlb/components';
import {
  XlbBasicForm,
  XlbButton,
  XlbCheckbox,
  XlbIcon,
  XlbImportModal,
  XlbProPageContainerWithMemo,
  XlbTipsModal,
} from '@xlb/components';
import type { RenderExtra } from '@xlb/components/dist/components/XlbBasicTable';
import { DataType } from '@xlb/components/dist/components/XlbTree/type';
import { message, Tooltip } from 'antd';
import { type FC, useState } from 'react';
import { BatchChangeModal } from './component/batchChange';
import { BatchChangeStoreModal } from './component/batchChangStore';
import { deveveryPriceValues } from './data';
import { Modal } from './modal';
import Api from './server';
const ProForm: FC<{ title: string }> = () => {
  const tableList: XlbTableColumnProps<any>[] = [
    {
      name: '序号',
      code: '_index',
      width: 50,
      lock: true,
      align: 'center',
    },
    {
      name: '组织',
      code: 'org_name',
      width: 140,
      hidden: true,
    },
    {
      name: '门店代码',
      code: 'store_code',
      width: 160,
      features: { sortable: true },
      align: 'left',
      render: (text: any, record: any) => (
        <div
          className="cursor link"
          onClick={(e) => {
            e.stopPropagation();
            batchEdit(record.store_id, record?.store_name);
          }}
        >
          {text}
        </div>
      ),
    },
    {
      name: '门店',
      code: 'store_name',
      width: 280,
      features: { sortable: true },
      align: 'left',
    },
    {
      name: '上游配送中心',
      code: 'center_store_name',
      width: 160,
      features: { sortable: true },
      align: 'left',
    },
    {
      name: '是否默认',
      code: 'default_flag',
      width: 100,
      features: { sortable: true },
      align: 'left',
      render: (text: any, record: any, index: RenderExtra) => {
        return (
          <XlbCheckbox
            checked={text}
            disabled={!hasAuth(['配送组织设置', '编辑'])}
            onChange={(e) => {
              e.stopPropagation();
              handleFlag(record, e.target.checked, index.fetchData);
            }}
          />
        );
      },
    },
    {
      name: '中转配送中心',
      code: 'transit_store_name',
      width: 170,
      features: { sortable: true },
    },
    {
      name: '共享配送中心',
      code: 'share_store_name',
      width: 170,
      features: { sortable: true },
    },
    {
      name: '配送价取值',
      code: 'delivery_price_val',
      width: 170,
      features: { sortable: true },
      render: (text: any, record: any, index: RenderExtra) => {
        return (
          //  根据deveveryPriceValues翻译
          <div>
            {text
              ? deveveryPriceValues.find((item) => item.value === text)?.label
              : ''}
          </div>
        );
      },
    },
  ];

  const summaryColumns: XlbTableColumnProps<any>[] = [
    {
      name: '序号',
      code: '_index',
      width: 50,
      lock: true,
      align: 'center',
    },
    {
      name: '门店代码',
      code: 'store_code',
      width: 160,
      features: { sortable: true },
      render: (text: any, record: any) => (
        <div
          className="cursor link"
          onClick={(e) => {
            e.stopPropagation();
            batchEdit(record.store_id, record?.store_name);
          }}
        >
          {text}
        </div>
      ),
    },
    {
      name: '门店名称',
      code: 'store_name',
      width: 280,
      features: { sortable: true },
    },
    {
      name: '上游配送中心',
      code: 'center_store_names',
      width: 280,
      features: { sortable: true },
      render: (text: any, record: any) => (
        <div style={{ display: 'flex', alignItems: 'center' }}>
          {record.default_center_store_name ? (
            <Tooltip title="默认上游配送中心">
              <span
                style={{
                  display: 'inline-block',
                  width: 4,
                  height: 4,
                  borderRadius: '50%',
                  background: '#F53F3F',
                  marginRight: 4,
                }}
              />
            </Tooltip>
          ) : null}
          <span
            style={{
              flex: 1,
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              whiteSpace: 'nowrap',
            }}
          >
            {record.default_center_store_name ??
              text?.split(',')?.filter(Boolean)[0]}
          </span>
          {text?.split(',')?.filter(Boolean)?.length > 1 ? (
            <span
              style={{
                width: 30,
                cursor: 'pointer',
                color: '#3D66FE',
              }}
              onClick={(e) => {
                e.stopPropagation();
                openModal('view', {}, record.store_id);
              }}
            >
              更多
            </span>
          ) : null}
        </div>
      ),
    },
    {
      name: '中转配送中心',
      code: 'transit_store_names',
      width: 170,
      features: { sortable: true },
      render: (text: any, record: any) => (
        <div style={{ display: 'flex', alignItems: 'center' }}>
          {record.default_transit_store_name ? (
            <Tooltip title="默认中转配送中心">
              <span
                style={{
                  display: 'inline-block',
                  width: 4,
                  height: 4,
                  borderRadius: '50%',
                  background: '#F53F3F',
                  marginRight: 4,
                }}
              />
            </Tooltip>
          ) : null}
          <span
            style={{
              flex: 1,
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              whiteSpace: 'nowrap',
            }}
          >
            {record.default_transit_store_name ??
              text?.split(',')?.filter(Boolean)[0]}
          </span>
          {text?.split(',')?.filter(Boolean)?.length > 1 ? (
            <span
              style={{
                width: 30,
                cursor: 'pointer',
                color: '#3D66FE',
              }}
              onClick={(e) => {
                e.stopPropagation();
                openModal('view', {}, record.store_id);
              }}
            >
              更多
            </span>
          ) : null}
        </div>
      ),
    },
    {
      name: '共享配送中心',
      code: 'share_store_names',
      width: 216,
      features: { sortable: true },
      render: (text: any, record: any) => (
        <div style={{ display: 'flex', alignItems: 'center' }}>
          {record.default_share_store_name ? (
            <Tooltip title="默认共享配送中心">
              <span
                style={{
                  display: 'inline-block',
                  width: 4,
                  height: 4,
                  borderRadius: '50%',
                  background: '#F53F3F',
                  marginRight: 4,
                }}
              />
            </Tooltip>
          ) : null}
          <span
            style={{
              flex: 1,
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              whiteSpace: 'nowrap',
            }}
          >
            {record.default_share_store_name ??
              text?.split(',')?.filter(Boolean)[0]}
          </span>
          {text?.split(',')?.filter(Boolean)?.length > 1 ? (
            <span
              style={{
                width: 30,
                cursor: 'pointer',
                color: '#3D66FE',
              }}
              onClick={(e) => {
                e.stopPropagation();
                openModal('view', {}, record.store_id);
              }}
            >
              更多
            </span>
          ) : null}
        </div>
      ),
    },
  ];

  const { enable_organization } = useBaseParams((state) => state);
  const [form] = XlbBasicForm.useForm();
  const [loading, setIsLoading] = useState(false);
  let ref: ContextState;

  const handleFlag = async (
    record: any,
    checked: boolean,
    callback?: Function,
  ) => {
    const res = await Api.update({
      store_id: record.store_id,
      default_flag: checked,
      center_store_id: record.center_store_id,
      share_store_id: record.share_store_id,
    });
    if (res.code === 0) {
      message.success('操作成功');
    }
    callback!();
  };

  const openModal = (
    type: 'add' | 'edit' | 'view',
    initialValues?: any,
    fid?: string,
    onOk?: (values: any) => Promise<boolean>,
  ) => {
    return NiceModal.show(NiceModal.create(Modal), {
      title:
        type === 'add' ? '新增' : type === 'edit' ? '批量编辑' : '配送中心',
      type,
      initialValues,
      fid,
      onOk,
    });
  };
  const handleBatchModal = () => {
    if (!ref?.selectRow?.length) {
      return NiceModal.show(NiceModal.create(BatchChangeModal), {
        requestForm: ref?.requestForm,
        fetchData: ref?.fetchData,
      });
    } else {
      // 统计 store_id 出现次数
      const storeIdCount: Record<string, number> = {};
      ref?.selectRow?.forEach((item) => {
        storeIdCount[item.store_id] = (storeIdCount?.[item.store_id] || 0) + 1;
      });
      // 找出重复的 store_id
      const duplicateStoreIds = Object.keys(storeIdCount).filter(
        (id) => storeIdCount[id] > 1,
      );
      if (duplicateStoreIds.length > 0) {
        // 获取重复门店的名称（去重，只显示一次）
        const duplicateStoreNames = Array.from(
          new Set(
            ref?.selectRow
              ?.filter((item) =>
                duplicateStoreIds.includes(item.store_id?.toString()),
              )
              ?.map((item) => item.store_name),
          ),
        ).join('、');

        // 弹出提示
        XlbTipsModal({
          tips: `一个门店仅可选择一行数据，${duplicateStoreNames}门店存在重复`,
          isConfirm: true,
          isCancel: true,
        });
        return;
      }

      return NiceModal.show(NiceModal.create(BatchChangeStoreModal), {
        requestForm: ref?.requestForm,
        fetchData: ref?.fetchData,
        selectRow: ref?.selectRow,
      });
    }
  };

  const addItem = async (values: any) => {
    form.resetFields();
    try {
      const modalResult = await openModal(
        'add',
        {
          center_store_id: values?.center_store_id,
        },
        undefined,
        async (modalValues) => {
          ref?.setLoading!(true);
          const res = await Api.save(modalValues);
          ref?.setLoading!(false);

          if (res.code === 0) {
            message.success('操作成功');
            ref?.fetchData();
            return true;
          }
          return false;
        },
      );

      if (!modalResult) return;
    } catch (error) {
      ref?.setLoading!(false);
      return false;
    }
  };
  // 导入
  const setRecreate = async (parameterSaving: any) => {
    if (
      parameterSaving?.upstream_center_update_res_dto?.need_update_store_ids
        ?.length > 0 ||
      parameterSaving?.upstream_center_update_res_dto?.upstream_updates
        ?.length > 0
    ) {
      const { need_update_store_ids, upstream_updates } =
        parameterSaving?.upstream_center_update_res_dto;
      // const { ids, store_names } = parameterSaving

      const updatedIds =
        need_update_store_ids?.length > 0
          ? need_update_store_ids
          : upstream_updates?.length > 0
            ? upstream_updates.map((v) => v.store_id)
            : [];
      const storeCondition = updatedIds.map((id: number, index: number) => {
        const upstreamCenter = upstream_updates?.find((v) => v.store_id === id);
        return {
          ...parameterSaving,
          store_find_condition: {
            ids: [id],
          },
          upstream_center_id: upstreamCenter?.upstream_center_id || null,
        };
      });
      await XlbTipsModal({
        tips: '是否重新生成修改门店今日未调出的补货单',
        isCancel: true,
        onOkBeforeFunction: async () => {
          NiceModal.show(XlbProgress, {
            requestApi: '/erp/hxl.erp.requestorder.recreate',
            items: storeCondition || [],
            titleList: [],
            promptTitle: '正在操作：',
          }).then(() => {
            ref?.fetchData();
          });
          return true;
        },
      });
    }
  };
  // 单个编辑
  const setRecreateUpdate = async (parameterSaving: any) => {
    if (
      parameterSaving?.need_update_store_ids?.length > 0 ||
      parameterSaving?.upstream_updates?.length > 0
    ) {
      const { need_update_store_ids, upstream_updates } = parameterSaving;
      const updatedIds =
        need_update_store_ids?.length > 0
          ? need_update_store_ids
          : upstream_updates?.length > 0
            ? upstream_updates.map((v) => v.store_id)
            : [];
      const storeCondition = updatedIds.map((id: number, index: number) => {
        return {
          ...parameterSaving,
          store_find_condition: {
            ids: [id],
          },
          upstream_center_id: parameterSaving?.upstream_center_id || null,
        };
      });
      await XlbTipsModal({
        tips: '是否重新生成修改门店今日未调出的补货单',
        isCancel: true,
        onOkBeforeFunction: async () => {
          NiceModal.show(XlbProgress, {
            requestApi: '/erp/hxl.erp.requestorder.recreate',
            items: storeCondition || [],
            titleList: [parameterSaving?.store_name],
            promptTitle: '正在操作：',
          }).then(() => {
            ref?.fetchData();
          });
          return true;
        },
      });
    }
  };
  const batchEdit = async (fid: string, store_name: string) => {
    form.resetFields();
    try {
      const modalResult = await openModal(
        'edit',
        {},
        fid,
        async (modalValues) => {
          ref?.setLoading!(true);
          modalValues.store_id = modalValues.store_ids[0];
          const res = await Api.batchupdate(modalValues);
          ref?.setLoading!(false);

          if (res.code === 0) {
            message.success('操作成功');
            setRecreateUpdate({
              ...res.data,
              store_name: store_name,
            });
            ref?.fetchData();
            return true;
          }
          return false;
        },
      );

      if (!modalResult) return;
    } catch (error) {
      ref?.setLoading!(false);
      return false;
    }
  };

  const dropdownItemClick = async (index: number) => {
    const res = await XlbImportModal({
      importUrl: `${process.env.BASE_URL}${
        index === 0 ? '/erp/hxl.erp.deliverycenterstore.import' : ''
      }`,
      templateUrl: `${process.env.BASE_URL}${
        index === 0 ? '/erp/hxl.erp.deliverycenterstore.download' : ''
      }`,
      params: {},
    });
    if (res) {
      setRecreate({
        upstream_center_update_res_dto:
          res?.data?.upstream_center_update_res_dto,
      });
    }
  };

  const deleteItem = async (selectRow?: string[]) => {
    const flag = await XlbTipsModal({
      tips: `已选择${selectRow?.length}条数据，是否确认？`,
    });
    if (!flag) return;
    ref?.setLoading!(true);
    const res = await Api.delete({
      delete_list: selectRow?.map((i: any) => ({
        store_id: i.store_id,
        share_store_id: i.share_store_id,
        center_store_id: i.center_store_id,
      })),
    });
    ref?.setLoading!(false);
    if (res.code === 0) {
      message.success('操作成功');
      ref?.fetchData();
    }
  };

  const exportItem = async (values: any, type: 'details' | 'summary') => {
    values.responseType = 'blob';
    const url =
      type === 'details'
        ? '/erp/hxl.erp.deliverycenterstore.export'
        : '/erp/hxl.erp.deliverycenterstore.storesummary.export';
    const data = values;
    const res = await exportPage(url, data, {
      responseType: 'blob',
    });
    const download = new Download();
    download.filename =
      type === 'details'
        ? '配送组织设置明细导出.xlsx'
        : '配送组织设置汇总导出.xlsx';
    download.xlsx(res?.data);
  };

  return (
    <XlbProPageContainerWithMemo
      treeFieldProps={{
        leftUrl: '/erp-mdm/hxl.erp.org.tree',
        dataType: DataType.LISTS,
        leftKey: 'org_id',
      }}
      tabFieldProps={{
        defaultActiveKey: 'details',
        name: 'tabKey',
        items: [
          {
            label: '明细',
            key: 'details',
            children: {
              searchFieldProps: {
                formList: [
                  { id: 'commonInput', label: '关键字', name: 'keyword' },
                  {
                    id: ErpFieldKeyMap.erpOrderTimeruleStoreIds,
                    label: '门店',
                    name: 'store_ids',
                  },
                  {
                    id: 'commonSelect',
                    label: '是否默认',
                    name: 'default_flag',
                    options: [
                      { label: '是', value: true },
                      { label: '否', value: false },
                    ],
                  },
                  {
                    id: ErpFieldKeyMap.erpCenterStoreId,
                    label: '上游配送中心',
                    name: 'center_store_ids',
                  },
                  {
                    id: ErpFieldKeyMap.erpShareDeliverCenter,
                    label: '共享配送中心',
                    name: 'share_store_ids',
                  },
                  {
                    id: ErpFieldKeyMap.erpTransitDistributionCenter,
                    label: '中转配送中心',
                  },
                ],
              },
              extra: (content: ContextState) => {
                ref = content;
                return (
                  <>
                    {hasAuth(['配送组织设置', '编辑']) ? (
                      <XlbButton
                        type="primary"
                        onClick={() => addItem(content.requestForm)}
                        icon={<XlbIcon name="jia" />}
                      >
                        新增
                      </XlbButton>
                    ) : null}
                    {hasAuth(['配送组织设置', '删除']) ? (
                      <XlbButton
                        type="primary"
                        key={content?.selectRow?.join(',')}
                        disabled={!content?.selectRow?.length}
                        onClick={() =>
                          deleteItem(content?.selectRow)
                        }
                        icon={<XlbIcon name="shanchu" />}
                      >
                        删除
                      </XlbButton>
                    ) : null}
                    {hasAuth(['配送组织设置', '导入']) ? (
                      <XlbButton
                        type="primary"
                        disabled={!content?.dataSource?.length}
                        onClick={() => dropdownItemClick(0)}
                        icon={<XlbIcon name="daoru" />}
                      >
                        导入
                      </XlbButton>
                    ) : null}
                    {/* {hasAuth(['配送组织设置', '编辑']) ? (
                          <XlbDropdownButton
                            // @ts-ignore
                            trigger="click"
                            dropList={[{ label: '导入' }, { label: '导入更新' }]}
                            dropdownItemClick={dropdownItemClick}
                            label={'导入'}
                          />
                        ) : null} */}
                    {hasAuth(['配送组织设置', '导出']) ? (
                      <XlbButton
                        type="primary"
                        disabled={!content?.dataSource?.length}
                        onClick={() =>
                          exportItem(content.requestForm, 'details')
                        }
                        icon={<XlbIcon name="daochu" />}
                      >
                        导出
                      </XlbButton>
                    ) : null}
                    {hasAuth(['配送组织设置', '编辑']) ? (
                      <XlbButton
                        type="primary"
                        disabled={!content?.dataSource?.length}
                        onClick={handleBatchModal}
                        // icon={<XlbIcon name="daochu" />}
                      >
                        批量修改
                      </XlbButton>
                    ) : null}
                  </>
                );
              },
              tableFieldProps: {
                url: '/erp/hxl.erp.deliverycenterstore.find',
                tableColumn: () => {
                  tableList.find((i) => i.code === 'org_name')!.hidden =
                    !enable_organization;
                  return tableList;
                },
                primaryKey: 'fid',
                afterPost: (res: any) => {
                  res?.forEach((item: any) => {
                    item.fid =
                      item?.store_id +
                      '_' +
                      item?.sort +
                      '_' +
                      item?.center_store_id;
                  });
                  return res;
                },
                selectMode: 'multiple',
                showColumnsSetting: false,
                changeColumnAndResetDataSource: false,
                keepDataSource: false,
                immediatePost: true,
              },
            },
          },
          {
            label: '汇总',
            key: 'summary',
            children: {
              searchFieldProps: {
                formList: [
                  { id: 'commonInput', label: '关键字', name: 'keyword' },
                  {
                    id: ErpFieldKeyMap.erpOrderTimeruleStoreIds,
                    label: '门店',
                    name: 'store_ids',
                  },
                  {
                    id: 'commonSelect',
                    label: '是否默认',
                    name: 'default_flag',
                    options: [
                      { label: '是', value: true },
                      { label: '否', value: false },
                    ],
                  },
                  {
                    id: ErpFieldKeyMap.erpCenterStoreId,
                    label: '上游配送中心',
                    name: 'center_store_ids',
                  },
                  {
                    id: ErpFieldKeyMap.erpShareDeliverCenter,
                    label: '共享配送中心',
                    name: 'share_store_ids',
                  },
                  {
                    id: ErpFieldKeyMap.erpTransitDistributionCenter,
                    label: '中转配送中心',
                  },
                ],
              },
              extra: (content: ContextState) => {
                ref = content;
                return (
                  <>
                    {hasAuth(['配送组织设置', '编辑']) ? (
                      <XlbButton
                        type="primary"
                        onClick={() => addItem(content.requestForm)}
                        icon={<XlbIcon name="jia" />}
                      >
                        新增
                      </XlbButton>
                    ) : null}
                    {hasAuth(['配送组织设置', '导入']) ? (
                      <XlbButton
                        type="primary"
                        disabled={!content?.dataSource?.length}
                        onClick={() => dropdownItemClick(0)}
                        icon={<XlbIcon name="daoru" />}
                      >
                        导入
                      </XlbButton>
                    ) : null}
                    {/* {hasAuth(['配送组织设置', '编辑']) ? (
                          <XlbDropdownButton
                            // @ts-ignore
                            trigger="click"
                            dropList={[{ label: '导入' }, { label: '导入更新' }]}
                            dropdownItemClick={dropdownItemClick}
                            label={'导入'}
                          />
                        ) : null} */}
                    {hasAuth(['配送组织设置', '导出']) ? (
                      <XlbButton
                        type="primary"
                        disabled={!content?.dataSource?.length}
                        onClick={() =>
                          exportItem(content.requestForm, 'summary')
                        }
                        icon={<XlbIcon name="daochu" />}
                      >
                        导出
                      </XlbButton>
                    ) : null}
                  </>
                );
              },
              tableFieldProps: {
                primaryKey: 'store_id',
                url: '/erp/hxl.erp.deliverycenterstore.storesummary.find',
                tableColumn: summaryColumns,
                selectMode: 'single',
                showColumnsSetting: false,
                changeColumnAndResetDataSource: false,
                keepDataSource: false,
                immediatePost: true,
              },
            },
          },
        ],
      }}
    />
  );
};
export default ProForm;

import { useState, useEffect } from 'react'
import { message } from 'antd'
import style from './copy.less'
import { XlbProgress } from '@/components/common'
import NiceModal from '@ebay/nice-modal-react'
import { XlbBasicForm, XlbInputDialog, XlbModal, XlbTipsModal } from '@xlb/components'

interface IProp {
  refresh: () => void
}
const Copy = NiceModal.create((props: IProp) => {
  const { refresh } = props
  const { visible, hide } = NiceModal.useModal()
  const [form] = XlbBasicForm.useForm()
  const [loding, setloding] = useState<boolean>(false)

  const handleOk = async () => {
    const { revise_names, revise_ids, reference_ids, reference_names } = form.getFieldsValue(true)
    if (!revise_ids?.length) {
      await XlbTipsModal({ tips: '请先选择修改门店' })
      return
    }

    if (!reference_ids?.length) {
      await XlbTipsModal({ tips: '请先选择参照门店' })
      return
    }

    if (revise_ids?.join(',') === reference_ids?.join(',')) {
      await XlbTipsModal({ tips: '修改门店和参照门店不能相同！' })
      return
    }
    setloding(true)
    const storeCondition = revise_ids?.map((id: number, index: number) => ({
      target_store_ids: [id],
      revise_names: revise_names?.[index],
      source_store_id: reference_ids?.[0],
      reference_names: reference_names?.[0]
    }))
    NiceModal.show(XlbProgress, {
      requestApi: '/erp/hxl.erp.storeitemsupplier.copy',
      items: storeCondition || [],
      titleList: revise_names,
      promptTitle: '正在操作：'
    }).then(() => {
      message.success('操作成功')
      refresh()
      hide()
    })
    setloding(false)
  }

  useEffect(() => {
    if (!visible) {
      form.resetFields()
      return
    }
  }, [visible])
  return (
    <XlbModal
      title="复制"
      style={{ top: -100 }}
      centered
      keyboard={false}
      open={visible}
      maskClosable={false}
      onOk={handleOk}
      isCancel
      onCancel={hide}
      width={400}
      zIndex={999}
      confirmLoading={loding}
    >
      <XlbBasicForm form={form} style={{ margin: '16px 0' }}>
        <div className={style.box}>
          <p className={style.title}>复制门店</p>
          <XlbBasicForm.Item name="revise_ids" label="修改门店">
            <XlbInputDialog
              width={200}
              dialogParams={{
                type: 'store',
                isMultiple: true,
                data: { status: true }
              }}
              fieldNames={{ idKey: 'id', nameKey: 'store_name' }}
              onChange={(_e: number[], chooseList: any[]) =>
                form.setFieldsValue({
                  revise_names: chooseList.map((item: any) => item.store_name)
                })
              }
            />
          </XlbBasicForm.Item>
          <XlbBasicForm.Item name="reference_ids" label="参照门店">
            <XlbInputDialog
              width={200}
              dialogParams={{
                type: 'store',
                isMultiple: false,
                data: { status: true }
              }}
              fieldNames={{ idKey: 'id', nameKey: 'store_name' }}
              onChange={(_e: number[], chooseList: any[]) =>
                form.setFieldsValue({
                  reference_names: chooseList.map((item: any) => item.store_name)
                })
              }
            />
          </XlbBasicForm.Item>
        </div>
      </XlbBasicForm>
    </XlbModal>
  )
})
export default Copy

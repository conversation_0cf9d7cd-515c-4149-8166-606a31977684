import { ECharts, EChartsOption, XlbChart, XlbChartProps } from '@xlb/datav';
import { Skeleton } from 'antd';
import dayjs from 'dayjs';
import { isNumber } from 'lodash-es';
import { FC, useCallback, useEffect, useRef, useState } from 'react';
import forecastIcon from '../images/legend-icon-forecast.svg';
import orderIcon from '../images/legend-icon-order.svg';
import './analysisCharts.less';
import { CHARTS_UNITS, ChartsTypes, ChartsUnits } from './data';
import { getEndDataZoom, isShowDataZoom } from './utils';

export interface IProps extends XlbChartProps {
  data: any;
  getCharsData: (unit: ChartsUnits) => void;
  loading: boolean;
}
const AnalysisCharts: FC<IProps> = ({
  height,
  data,
  getCharsData,
  loading,
}) => {
  const [activeUnit, setActiveUnit] = useState<ChartsUnits>(ChartsUnits.WEEK);
  const instanceRef = useRef<ECharts>();
  const getInstance = useCallback((instance: ECharts) => {
    instanceRef.current = instance;
  }, []);

  const COLORS = ['#1A6AFF', '#41D985', '#FF9340'];
  // 渲染图表
  function renderChart(unit: ChartsUnits) {
    const allList = (data?.previous_weeks || []).concat(
      data?.future_weeks || [],
    );
    const option: EChartsOption = {
      color: COLORS,
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow',
          shadowStyle: {
            color: 'rgba(76,114,255,0.1)',
          },
        },
        formatter: (values: any) => {
          const store = values?.find(
            (item: any) => item.seriesName === ChartsTypes.store,
          );
          const order = values?.find(
            (item: any) => item.seriesName === ChartsTypes.order,
          );
          const forecast = values?.find(
            (item: any) => item.seriesName === ChartsTypes.forecast,
          );
          const valueFormatter = (value: string) =>
            value
              .split('-')
              .map((item: string) => dayjs(item)?.format('YYYY/MM/DD'))
              .join('-');

          const storeItem = `<div class="tooltip-item">
                <div class="tooltip-item__icon is-store"></div>
                <div class="tooltip-item__value">门店数：${store?.data ?? 0}</div>
            </div>`;
          const orderItem = `<div class="tooltip-item">
                <div class="tooltip-item__icon is-order"></div>
                <div class="tooltip-item__value">实际出库量：${order?.data ?? 0}</div>
            </div>`;
          const forecastItem = `<div class="tooltip-item">
                <div class="tooltip-item__icon is-order is-forecast"></div>
                <div class="tooltip-item__value">预测出库量：${forecast?.data ?? 0}</div>
            </div>`;

          return `<div class="tooltip-container">
            <div class="tooltip-title">${valueFormatter(store?.axisValue || order?.axisValue || forecast?.axisValue)}</div>
            ${isNumber(store?.data) ? storeItem : ''}
            ${isNumber(order?.data) ? orderItem : ''}
            ${isNumber(forecast?.data) ? forecastItem : ''}
        </div>`;
        },
      },
      legend: {
        itemWidth: 14,
        itemHeight: 8,
        itemGap: 12,
        left: 10,
        top: -3,
        textStyle: {
          color: '#4E5969',
          lineHeight: 17,
        },
        data: [
          {
            name: ChartsTypes.store,
            icon: 'roundRect',
          },
          {
            name: ChartsTypes.order,
            icon: `image://${orderIcon}`,
          },
          {
            name: ChartsTypes.forecast,
            icon: `image://${forecastIcon}`,
          },
        ],
      },
      xAxis: {
        type: 'category',
        axisTick: { show: false },
        axisLine: { lineStyle: { color: '#C9CDD4' } },
        axisLabel: {
          color: '#4E5969',
          lineHeight: 14,
          align: 'center',
          fontSize: 10,
          formatter: (value: any) => {
            if (value && value.split('-')?.length) {
              return value
                .split('-')
                .map((item: string) => dayjs(item)?.format('MM/DD'))
                .join('-');
            }
            return value;
          },
        },
        data: allList?.map((item: any) => item.sdt) || [],
      },
      yAxis: [
        {
          type: 'value',
          name: '门店数',
          position: 'left',
          alignTicks: true,
          nameTextStyle: {
            color: '#86909C',
            fontSize: 12,
            align: 'right',
            padding: [0, 8, 0, 0],
          },
          axisLine: { show: false },
          splitLine: {
            show: true,
            lineStyle: {
              color: '#C9CDD4',
              width: 1,
              type: 'dashed',
            },
          },
          axisLabel: {
            color: '#4E5969',
            fontSize: 12,
          },
        },
        {
          type: 'value',
          name: '出库量',
          position: 'right',
          alignTicks: true,
          nameTextStyle: {
            color: '#86909C',
            fontSize: 12,
            align: 'left',
            padding: [0, 0, 0, 8],
          },
          axisLine: { show: false },
          axisLabel: {
            color: '#4E5969',
            fontSize: 12,
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: '#C9CDD4',
              width: 1,
              type: 'dashed',
            },
          },
        },
      ],
      dataZoom: [
        {
          type: 'slider',
          show: unit === ChartsUnits.DAY && isShowDataZoom(allList || []),
          brushSelect: false,
          height: 10,
          xAxisIndex: [0],
          left: 40,
          right: 30,
          bottom: 20,
          start: 0,
          end: getEndDataZoom(allList || []),
          handleSize: '0px',
          handleStyle: {
            color: '#BEBEBE',
            borderColor: '#BEBEBE',
          },
          showDetail: true,
          textStyle: false,
        },
      ],
      series: [
        {
          name: ChartsTypes.store,
          type: 'bar',
          data: allList?.map((item: any) => item.cover_store_num) || [],
          barWidth: 20,
        },
        {
          name: ChartsTypes.order,
          type: 'line',
          yAxisIndex: 1,
          data: allList?.map(
            (item: any) =>
              data?.previous_weeks?.find(
                (actual: any) => actual.sdt === item.sdt,
              )?.actual_out_quantity ?? null,
          ),
          lineStyle: { width: 2 },
          symbolSize: 6,
        },
        {
          name: ChartsTypes.forecast,
          type: 'line',
          yAxisIndex: 1,
          data: allList?.map(
            (item: any) =>
              data?.future_weeks?.find((actual: any) => actual.sdt === item.sdt)
                ?.actual_out_quantity ?? null,
          ),
          lineStyle: { width: 2 },
          symbolSize: 6,
        },
      ],
    };
    instanceRef.current?.setOption(option);
  }

  useEffect(() => {
    renderChart(activeUnit);
  }, [data]);

  return (
    <div className="chart-container">
      <div className="chart-tabs">
        {CHARTS_UNITS.map((item) => (
          <div
            key={item.value}
            className={`chart-tabs__item ${activeUnit === item.value ? 'active' : ''}`}
            onClick={() => {
              if (item.value !== activeUnit) {
                setActiveUnit(item.value);
                getCharsData(item.value);
              }
            }}
          >
            {item.label}
          </div>
        ))}
      </div>
      <Skeleton
        style={{ height }}
        active
        paragraph={{ rows: 9 }}
        loading={loading}
      >
        <div className="chart-container__chart">
          <XlbChart height={height} getInstance={getInstance} />
        </div>
      </Skeleton>
    </div>
  );
};
export default AnalysisCharts;

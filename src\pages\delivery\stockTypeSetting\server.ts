import { XlbFetch } from '@xlb/utils';

// 获取备货类型
export const getPrepareType = async (data: any) => {
  return await XlbFetch.post('/erp/hxl.erp.preparetype.find', data);
};

// 新增备货类型
export const addPrepareType = async (data: any) => {
  return await XlbFetch.post('/erp/hxl.erp.preparetype.save', data);
};

// 修改备货类型
export const editPrepareType = async (data: any) => {
  return await XlbFetch.post('/erp/hxl.erp.preparetype.update', data);
};

// 获取组织在销量取值范围内的PSD
export const getRangePsd = async (data: any) => {
  return await XlbFetch.post('/bi/hxl.bi.org.psd.find', data);
};

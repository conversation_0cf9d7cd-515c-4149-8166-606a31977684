import { columnWidthEnum } from '@/data/common/constant';
import { SearchFormType, XlbTableColumnProps } from '@xlb/components';

//商品类型
export const goodsType = [
  {
    label: '主规格商品',
    value: 'MAINSPEC',
  },
  {
    label: '多规格商品',
    value: 'MULTIPLESPEC',
  },
  {
    label: '标准商品',
    value: 'STANDARD',
  },
  {
    label: '组合商品',
    value: 'COMBINATION',
  },
  {
    label: '成分商品',
    value: 'COMPONENT',
  },
  {
    label: '制单组合',
    value: 'MAKEBILL',
  },
  {
    label: '分级商品',
    value: 'GRADING',
  },
];
export const goodsType2 = [
  {
    label: '主规格商品',
    value: 'MAINSPEC',
  },
  {
    label: '多规格商品',
    value: 'MULTIPLESPEC',
  },
  {
    label: '标准商品',
    value: 'STANDARD',
  },
  {
    label: '成分商品',
    value: 'COMPONENT',
  },
  {
    label: '组合商品',
    value: 'COMBINATION',
  },
  {
    label: '制单组合',
    value: 'MAKEBILL',
  },
]; //查询单位query_unit
export const queryUnit = [
  {
    label: '批发单位',
    value: 'WHOLESALE',
  },
  {
    label: '基本单位',
    value: 'BASIC',
  },
];
// PURCHASE——采购单位,
// STOCK——库存单位,
// DELIVERY——配送单位,
// WHOLESALE——批发单位,
// BASIC——基本单位

export const types = [
  {
    label: '　',
    value: 'NULL',
  },
  {
    label: '按比例(%)',
    value: 'RATIO',
  },
  {
    label: '按金额',
    value: 'MONEY',
  },
  {
    label: '固定金额',
    value: 'FIXED_MONEY',
  },
];
export const formListData: SearchFormType[] = [
  {
    label: '组织',
    name: 'org_ids',
    type: 'inputDialog',
    treeModalConfig: {
      // @ts-ignore
      title: '选择组织',
      url: '/erp-mdm/hxl.erp.org.tree',
      dataType: 'lists',
      checkable: true, // 是否多选
      primaryKey: 'id',
    },
    hiddenInXlbColumns: true,
    // @ts-ignore
    onChange: (_e: any, option: any, form: any) => {
      // console.log('form', form, _e, option);
      form?.setFieldsValue({ store_ids: [] });
    },
  },
  {
    label: '门店',
    name: 'store_ids',
    type: 'inputDialog',
    dependencies: ['org_ids'],
    disabledChecked: true,
    rules: [{ required: true, message: '请选择门店' }],
    dialogParams: (params: any) => {
      return {
        type: 'store',
        dataType: 'lists',
        isLeftColumn: true,
        isMultiple: true,
        primaryKey: 'id',
        data: {
          enable_organization: false,
          status: true,
          org_ids: params?.org_ids || [],
        },
      };
    },
    fieldNames: {
      idKey: 'id',
      nameKey: 'store_name',
    } as any,
  },
  {
    rules: [{ required: true, message: '请选择批发客户' }],
    disabledChecked: true,
    required: true,
    label: '批发客户',
    name: 'client_id',
    type: 'inputDialog',
    check: true,
    dialogParams: {
      type: 'wholesaler',
      dataType: 'lists',
      isLeftColumn: true,
      isMultiple: false,
      primaryKey: 'id',
      data: {
        status: 1,
      },
    },
  },
  {
    label: '商品档案',
    name: 'item_ids',
    type: 'inputDialog',
    dialogParams: {
      type: 'goods',
      dataType: 'lists',
      isLeftColumn: true,
      isMultiple: true,
      primaryKey: 'id',
      data: {
        enabled: true,
      },
    },
  },
  {
    label: '商品类别',
    name: 'item_category_ids',
    type: 'inputDialog',
    treeModalConfig: {
      // @ts-ignore
      title: '选择商品分类', // 标题
      topLevelTreeCheckable: true,
      url: '/erp/hxl.erp.category.find', // 请求地址
      dataType: 'lists',
      checkable: true, // 是否多选
      primaryKey: 'id',
      data: {
        enabled: true,
      },
    },
  },
  {
    label: '商品类型',
    name: 'item_types',
    type: 'select',
    multiple: true,
    options: goodsType2,
  },
  {
    label: '批发价类型',
    name: 'type',
    type: 'select',
    options: types,
  },
  {
    label: '单位',
    name: 'unit_type',
    type: 'select',
    allowClear: false,
    check: false,
    disabledChecked: true,
    options: queryUnit,
  },
  {
    label: '经营范围',
    name: 'business_scope_ids',
    type: 'inputDialog',
    dialogParams: {
      type: 'businessRangeOrg',
      dataType: 'lists',
      isMultiple: true,
      // isLeftColumn: true,
      data: { business_type: 1 },
    },
  },
  {
    label: '其他条件',
    className: 'inputPanelValue',
    type: 'inputPanel',
    name: 'panelValue',
    placeholder: '停购/停止批发',
    allowClear: true,
    items: [
      {
        label: '仅显示',
        // @ts-ignore
        key: true,
      },
      {
        label: '不显示',
        // @ts-ignore
        key: false,
      },
    ],
    options: [
      {
        label: '停购',
        value: 'stop_purchase',
      },
      {
        label: '停止批发',
        value: 'stop_wholesale',
      },
    ],
    width: 120,
  },
];

export const StorePriceTable: XlbTableColumnProps<any>[] = [
  {
    name: '序号',
    code: '_index',
    lock: true,
    width: columnWidthEnum.INDEX,
    align: 'center',
  },
  {
    name: '组织',
    code: 'org_name',
    width: 140,
    hiddenInXlbColumns: true,
  },
  {
    name: '门店',
    code: 'store_name',
    width: 220,
    features: { sortable: false },
  },
  {
    name: '商品代码',
    code: 'code',
    width: 160,
    features: { sortable: true },
  },
  {
    name: '商品条码',
    code: 'bar_code',
    width: 160,
    features: { sortable: true },
  },
  {
    name: '商品名称',
    code: 'name',
    width: 280,
    features: { sortable: true },
  },
  {
    name: '采购规格',
    code: 'purchase_spec',
    width: 110,
    features: { sortable: true },
  },
  {
    name: '商品类型',
    code: 'item_type',
    width: 100,
    features: { sortable: true },
  },
  {
    name: '商品类别',
    code: 'category_name',
    width: 140,
    features: { sortable: false },
  },
  {
    name: '基本单位', // 6178370443
    width: 100,
    code: 'basic_unit',
    features: { sortable: true },
  },
  {
    name: '单位',
    code: 'unit',
    width: 80,
    features: { sortable: true },
  },
  {
    name: '批发换算率', // 6178370443
    width: 140,
    code: 'wholesale_ratio',
    features: { sortable: true },
  },
  {
    name: '组织采购价',
    code: 'purchase_price',
    width: 120,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '组织批发价1',
    code: 'item_price',
    width: 120,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '批发价类型1',
    code: 'type',
    width: 130,
    features: { sortable: true },
  },
  {
    name: '数值1',
    code: 'value',
    width: 100,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '门店批发价1',
    code: 'price',
    width: 120,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '组织批发价2',
    code: 'item_price2',
    width: 120,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '批发价类型2',
    code: 'type2',
    width: 130,
    features: { sortable: true },
  },
  {
    name: '数值2',
    code: 'value2',
    width: 100,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '门店批发价2',
    code: 'price2',
    width: 120,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '组织批发价3',
    code: 'item_price3',
    width: 120,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '批发价类型3',
    code: 'type3',
    width: 130,
    features: { sortable: true },
  },
  {
    name: '数值3',
    code: 'value3',
    width: 100,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '门店批发价3',
    code: 'price3',
    width: 120,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '组织批发价4',
    code: 'item_price4',
    width: 120,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '批发价类型4',
    code: 'type4',
    width: 130,
    features: { sortable: true },
  },
  {
    name: '数值4',
    code: 'value4',
    width: 100,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '门店批发价4',
    code: 'price4',
    width: 120,
    align: 'right',
    features: { sortable: true },
  },
];

export const CustumerPriceTable: XlbTableColumnProps<any>[] = [
  {
    name: '序号',
    code: '_index',
    width: columnWidthEnum.INDEX,
    lock: true,
    align: 'center',
  },
  {
    name: '组织',
    code: 'org_name',
    width: 140,
    hiddenInXlbColumns: true,
  },
  {
    name: '批发客户',
    code: 'client_name',
    width: 160,
    features: { sortable: false },
  },
  {
    name: '门店',
    code: 'store_name',
    width: 220,
    features: { sortable: false },
  },
  {
    name: '商品代码',
    code: 'code',
    width: 160,
    features: { sortable: true },
  },
  {
    name: '商品条码',
    code: 'bar_code',
    width: 160,
    features: { sortable: true },
  },
  {
    name: '商品名称',
    code: 'name',
    width: 280,
    features: { sortable: true },
  },
  {
    name: '采购规格',
    code: 'purchase_spec',
    width: 160,
    features: { sortable: true },
  },
  {
    name: '商品类型',
    code: 'item_type',
    width: 100,
    features: { sortable: true },
  },
  {
    name: '商品类别',
    code: 'category_name',
    width: 140,
    features: { sortable: false },
  },
  {
    name: '基本单位', // 6178370443
    width: 100,
    code: 'basic_unit',
    features: { sortable: true },
  },
  {
    name: '单位',
    code: 'unit',
    width: 80,
    features: { sortable: true },
  },
  {
    name: '批发换算率', // 6178370443
    width: 140,
    code: 'wholesale_ratio',
    features: { sortable: true },
  },
  {
    name: '组织采购价',
    code: 'purchase_price',
    width: 120,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '组织批发价',
    code: 'item_price',
    width: 120,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '批发价类型',
    code: 'type',
    width: 130,
    features: { sortable: true },
  },
  {
    name: '数值',
    code: 'value',
    width: 100,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '客户批发价',
    code: 'price',
    width: 120,
    align: 'right',
    features: { sortable: true },
  },
];

.badge {
  z-index: 10;
}
// .itemConatainer {
//   :global .xlb-table-pagination {
//     margin: 0 12px !important;
//   }
// }

.popoverWrap {
  :global .ant-popover-inner-content {
    max-height: 260px !important;
    overflow-y: auto !important;
  }
}

.box {
  position: relative;
  // width: 860px;
  margin: 25px auto;
  padding: 10px;
  border: 1px solid #D8D8D8;
  border-radius: 5px;
  .title {
    position: absolute;
    top: -20px;
    left: 20px;
    padding: 0 13px;
    background: white;
  }
  :global .ant-form-item {
    display: inline-block;
    margin: 0 30px 0 10px;
  }
  :global .ant-space-item {
    display: inline-block;
  }
  :global label.ant-checkbox-wrapper.ant-checkbox-wrapper-in-form-item{
    width: 154px;
  }
  :global .ant-radio-wrapper {
    line-height: 26px;
  }
  :global .ant-input-affix-wrapper > .ant-input {
    height: 26px;
  }
  // :global .ant-input-affix-wrapper {
  //   margin: 0 10px;
  // }
  :global .ant-select {
    display: inline-block;
    width: 140px;
  }
  :global .ant-input-suffix{
    height: 26px;
  }
  // :global .ant-checkbox-wrapper {
  //   width: 120px;
  // }
}
import { XlbIcon, XlbInput, XlbProForm } from '@xlb/components'
import { formList, tableList, checkOptions, checkOptions1, checkOptions2, batch } from '../data'
import {
  update,
  batchUpdateDetail,
  getExport
} from '../server'
import BatchChange from './component/batchChange/batchChange'
import React, { useEffect, useState, useRef } from 'react'
import { LStorage } from '@/utils/storage'
import Copy from './component/copy/copy'
import Rule from './component/rule/rule'
import { hasAuth } from '@/utils/kit'
import styles from './index.less'
import toFixed from '@/utils/toFixed'
import Download from '@/utils/downloadBlobFile'
import { useBaseParams } from '@/hooks/useBaseParams'
// @ts-ignore

import { XlbPageContainerRef } from '@xlb/components-stage/dist/lowcodes/XlbPageContainer'
import {
  XlbConfigProvider,
  ContextState,
  XlbPageContainer,
  XlbTableColumnProps,
  XlbButton,
  XlbBasicForm,
  XlbForm,
  XlbTable,
  XlbTipsModal,
  XlbImportModal,
  XlbInputDialog,
  XlbInputNumber,
  XlbMessage
} from '@xlb/components'
import { XlbFetch } from '@xlb/utils'
import { ErpFieldKeyMap } from '@/data/common/fieldListConfig'
import { config } from '@/constants/baseDataConfig'
import { fieldListConfig } from '@/data/common/fieldListConfig'
import { exportPage } from '@/services/system'
const StoreItemReplenish = () => {
  const { ToolBtn, SearchForm, ToolBtnNoStyle } = XlbPageContainer
  const pageRef = useRef<XlbPageContainerRef>(null)
  const [_form] = XlbBasicForm.useForm()

  const [itemArr, setItemArr] = useState<XlbTableColumnProps<any>[]>(
    JSON.parse(JSON.stringify(tableList))
  )
  const [batchVisible, setBatchVisible] = useState<boolean>(false) //批量设置弹框
  const [copyVisible, setCopyVisible] = useState<boolean>(false) //复制弹框
  const [ruleVisible, setRuleVisible] = useState<boolean>(false) //复制弹框
  const [ruleLoding, setruleLoding] = useState<boolean>(true)
  const [details, setdetails] = useState<any[]>([])
  const [form] = XlbBasicForm.useForm()
  const { enable_organization } = useBaseParams((state) => state)

  const Purchase = (
    value: any,
    record: any,
    index: any,
    item: any,
    unit_type: any,
    setLoading: any,
    setSelectRow: any
  ) => {
    _form.setFieldValue('purchase_by', value)
    return (
      <XlbBasicForm.Item label="" name={'purchase_by'}>
        <XlbInputDialog
          style={{ width: '100%' }}
          allowClear
          dialogParams={{
            type: 'user',
            isMultiple: false,
            immediatePost: false,
            isLeftColumn: false,
            data: {
              business_dept: 'SUPPLY_CENTER'
            }
          }}
          fieldNames={{
            idKey: 'name'
          }}
          onChange={(e: any) =>
            inputBlur(e, record, item.code, index, unit_type, setLoading, setSelectRow)
          }
        />
      </XlbBasicForm.Item>
    )
  }
  const tableRender = (item: any, unit_type: any, setLoading: any, setSelectRow: any) => {
    switch (item.code) {
      case 'store_id':
        item.render = (value: any, record: any, index: number) => {
          return record?.store_name
        }
        break
      case 'point':
      case 'upper_limit':
      case 'quantity':
      case 'base_stock_quantity':
      case 'base_stock_quantity':
        item.render = (value: any, record: any, index: number) => {
          return record._click && hasAuth(['补货参考值', '编辑']) ? (
            <XlbInputNumber
              step={0.001}
              min={0}
              max={999999999.999}
              controls={false}
              autoComplete={'off'}
              onBlur={(e: any) =>
                inputBlur(e, record, item.code, index, unit_type, setLoading, setSelectRow)
              }
              onFocus={(e) => e.target.select()}
              size="small"
              style={{ textAlign: 'right', display: 'block' }}
              defaultValue={toFixed(Number(value), 'QUANTITY')}
            />
          ) : (
            <div>{toFixed(Number(value), 'QUANTITY')}</div>
          )
        }
        break
      case 'warn_day':
        item.render = (value: any, record: any, index: number) => {
          return record._click && hasAuth(['补货参考值', '编辑']) && record.center_flag ? (
            <XlbInputNumber
              precision={0}
              min={0}
              max={999999999}
              controls={false}
              autoComplete={'off'}
              onBlur={(e: any) =>
                inputBlur(e, record, item.code, index, unit_type, setLoading, setSelectRow)
              }
              onFocus={(e) => e.target.select()}
              size="small"
              style={{ textAlign: 'right', display: 'block' }}
              defaultValue={value}
            />
          ) : (
            <div>{value}</div>
          )
        }
        break
      case 'purchase_by':
        item.render = (value: any, record: any, index: number) => {
          return record._click && hasAuth(['补货参考值', '编辑']) && record.center_flag ? (
            Purchase(value, record, index, item, unit_type, setLoading, setSelectRow)
          ) : (
            <div>{value}</div>
          )
        }
        break
      case 'stop_request':
      case 'stop_purchase':
      case 'stop_sale':
        item.render = (value: any) => {
          const showColor = value ? 'success' : 'danger'
          return <div className={`${showColor}`}>{value ? '是' : '否'}</div>
        }
        break
      case 'avg_sale_quantity':
      case 'sale_quantity':
        item.render = (value: any) => {
          return <div>{toFixed(Number(value), 'QUANTITY')}</div>
        }
        break
    }
    return item
  }
  //updateData 仅用作采购员清空时更新的传参 ST-7968
  const inputBlur = async (
    e: any,
    record: any,
    key: any,
    index: any,
    unit_type: any,
    setLoading: any,
    setSelectRow: any
  ) => {
    let value
    if (key === 'purchase_by') {
      value = e?.length > 0 ? e[0] : '空'
    } else if (key === 'warn_day') {
      value = e.target.value
    } else {
      value = toFixed(Number(e.target.value), 'QUANTITY')
    }
    console.log('🚀 ~ inputBlur ~ value:', pageRef)

    if (value == record[key]) return
    const _data = {
      [key]: value,
      item_id: record?.item_id,
      store_id: record?.store_id,
      unit_type: unit_type
    }
    setLoading(true)
    const res = await update(_data)
    setLoading(false)
    if (res.code === 0 && pageRef.current) {
      XlbMessage.success('更新成功')
      pageRef.current.dataSource[index?.index]._click = false
      pageRef.current.dataSource[index?.index][key] = value
      pageRef.current.dataSource[index?.index]['update_by'] = res.data.update_by
      pageRef.current.dataSource[index?.index]['update_time'] = res.data.update_time
      pageRef.current?.setDataSource([...pageRef.current.dataSource])
    }
  }
  // @ts-ignore
  const clickGetData = (ruleLoding: boolean) => {
    if (!ruleLoding) {
      XlbTipsModal({
        tips: `调整规则未保存，查询后将重置调整数据，是否确定？`,
        isConfirm: true,
        isCancel: true,
        onOk: () => {
          pageRef.current?.fetchData()
        }
      })
    } else {
      pageRef.current?.fetchData()
    }
  }
  const afterPost = (data: any) => {
    setruleLoding(true)
  }
  const prevPost = async () => {
    try {
      await form.validateFields()
    } catch (err: any) {
      throw err
    }
    LStorage.set('storeItemReplenish', {
      ...form.getFieldsValue()
    })
    return { ...form.getFieldsValue(true) }
  }

  // 导入
  const importItem = async () => {
    const res = await XlbImportModal({
      importUrl: `${process.env.BASE_URL}/erp/hxl.erp.storeitemreplenish.import`,
      templateUrl: `${process.env.BASE_URL}/erp/hxl.erp.storeitemreplenish.template.download`,
      title: `补货参考值导入`,
      callback: (res: any) => {
        if (res?.data?.state) {
          pageRef.current?.fetchData()
        }
      }
    })
  }
  const exportItem = async (setLoading: any) => {
    setLoading(true)
    const data = await prevPost()
    const res = await exportPage(
      '/erp/hxl.erp.storeitemreplenish.export',
      data,
      { responseType: 'blob' },
    );
    setLoading(false)
    const download = new Download()
    download.filename = '补货参考值.xlsx'
    download.xlsx(res?.data)
  }
  const saveRule = (setLoading: any) => {
    Promise.resolve()
      .then(() => {
        setdetails([])
      })
      .then(async () => {
        pageRef.current?.dataSource?.forEach((item, i) => {
          details.push({
            base_stock_quantity: item.base_stock_quantity,
            item_id: item.item_id,
            point: item.point,
            quantity: item.quantity,
            store_id: item.store_id,
            upper_limit: item.upper_limit
          })
        })
        setLoading(true)
        const res = await batchUpdateDetail({
          unit_type: form.getFieldValue('unit_type'),
          details: details
        })
        setLoading(false)
        if (res.code === 0) {
          XlbMessage.success('保存成功')
          pageRef.current?.fetchData()
        }
      })
  }
  useEffect(() => {
    itemArr.find((i) => i.code === 'org_name')!.hidden = !enable_organization
    setItemArr([...itemArr])
  }, [enable_organization])
  useEffect(() => {
    if (ruleLoding) {
      itemArr.map((v: any) => {
        v.name === '序号' || v.name === '组织'
          ? (v.features.sortable = false)
          : (v.features.sortable = true)
      })
    } else {
      itemArr.map((v: any) => {
        v.features.sortable = false
      })
    }
    setItemArr(itemArr)
    // itemArr.map((v) => tableRender(v))
  }, [ruleLoding])
  useEffect(() => {
    const storage = LStorage.get('storeItemReplenish')
    if (storage) form.setFieldsValue({ ...storage })
    pageRef.current?.fetchData()
    itemArr.map((v: any) => {
      v.name === '序号' || v.name === '组织'
        ? (v.features.sortable = false)
        : (v.features.sortable = true)
    })
    // itemArr.map((v) => tableRender(v))
  }, [])
  /**
   * 转换数据类型
   */
  const coverSortItemToOrder = (sortItem: any) => {
    if (sortItem.order == 'none') {
      return
    }

    let obj = {
      property: sortItem.code,
      direction: sortItem.order.toUpperCase()
    }
    return obj
  }
  return (
    <XlbConfigProvider.Provider
      value={{
        baseURL: process.env.BASE_URL as string,
        config: config,
        globalFetch: XlbFetch,
        isOldBtn: true,
        fieldList: fieldListConfig
      }}
    >
      {/* <div
        className={styles.storeGroupContainer}
        style={{ height: 'calc(100vh - 78px)', padding: '12px 12px', background: '#f2f3f5' }}
      > */}
      <XlbBasicForm
        form={_form}
        className={styles.storeItemReplenishContainer}
        style={{
          height: 'calc(100vh - 78px)',
          width: '100%',
          padding: '12px 12px',
          background: '#f2f3f5'
        }}
      >
        <div
          style={{
            backgroundColor: '#fff',
            height: '100%',
            width: '100%',
            borderRadius: '4px',
            overflow: 'hidden'
          }}
        >
          <XlbPageContainer
            url={'/erp/hxl.erp.storeitemreplenish.page'}
            tableColumn={itemArr}
            prevPost={prevPost}
            afterPost={afterPost}
            ref={pageRef}
            immediatePost
            changeColumnAndResetDataSource={false}
          >
            <SearchForm>
              <XlbProForm
                formList={[
                  {
                    id: ErpFieldKeyMap.billOrgIds,
                    label: '组织',
                    onChange: (e: string[], form: any, _: any) => {
                      if (e.length > 0) {
                        form.setFieldsValue({
                          store_ids: null
                        })
                      }
                    },
                    hidden: !enable_organization
                  },
                  {
                    id: ErpFieldKeyMap.erpStoreIdsMultipletrue,
                    label: '门店'
                  },
                  {
                    id: ErpFieldKeyMap.erpRateStatisticsItemCategory,
                    label: '商品类别'
                  },
                  {
                    id: ErpFieldKeyMap.erpitemIds,
                    label: '商品档案'
                  },
                  {
                    id: ErpFieldKeyMap.erpUnitType,
                    label: '显示单位'
                  },
                  {
                    id: ErpFieldKeyMap.erpSupplierIdsMain
                  },
                  {
                    id: ErpFieldKeyMap.erpIssaleDays
                  },
                  {
                    id: 'inputPanel',
                    label: '其他条件',
                    name: 'checkValue',
                    fieldProps: {
                      initialValue: 'false',
                      allowClear: true,
                      width: 180,
                      options: [
                        {
                          label: '停购',
                          value: 'stop_purchase'
                        },
                        {
                          label: '停售',
                          value: 'stop_sale'
                        },
                        {
                          label: '停止要货',
                          value: 'stop_request'
                        }
                      ],
                      items: [
                        {
                          label: '仅显示',
                          key: 'true'
                        },
                        {
                          label: '不显示',
                          key: 'false'
                        }
                      ]
                    }
                  }
                ]}
                form={form}
                initialValues={{
                  unit_type: 'PURCHASE',
                  isShow: 1,
                  sale_day: 0,
                  store_ids: [JSON.parse(localStorage.userInfo).value.store_id],
                  ...(LStorage.get('storeItemReplenish') ? LStorage.get('storeItemReplenish') : {})
                }}
              />
            </SearchForm>
            <ToolBtn>
              {({ dataSource, fetchData, selectRow, loading, setLoading, setPagin }) => {
                return (
                  <XlbButton.Group>
                    {hasAuth(['补货参考值', '查询']) ? (
                      <XlbButton
                        label="查询"
                        type="primary"
                        loading={loading}
                        onClick={() => clickGetData(ruleLoding)}
                        icon={<XlbIcon size={16} name="sousuo" />}
                      />
                    ) : null}
                    {hasAuth(['补货参考值', '导入']) ? (
                      <XlbButton
                        label="导入"
                        type="primary"
                        disabled={loading}
                        onClick={() => importItem()}
                        icon={<XlbIcon size={16} name="daoru" />}
                      />
                    ) : null}
                    {hasAuth(['补货参考值', '导出']) ? (
                      <XlbButton
                        label="导出"
                        type="primary"
                        disabled={!dataSource?.length || loading}
                        onClick={() => exportItem(setLoading)}
                        icon={<XlbIcon size={16} name="daochu" />}
                      />
                    ) : null}
                    {hasAuth(['补货参考值', '编辑']) ? (
                      <XlbButton
                        label="复制"
                        disabled={loading}
                        type="primary"
                        onClick={() => {
                          setCopyVisible(true)
                        }}
                        icon={<XlbIcon size={16} name="fuzhi" />}
                      />
                    ) : null}
                    {hasAuth(['补货参考值', '编辑']) ? (
                      <XlbButton
                        label="批量修改"
                        disabled={loading}
                        type="primary"
                        onClick={() => {
                          setBatchVisible(true)
                        }}
                        icon={<span className="iconfont icon-xiugai1" />}
                      />
                    ) : null}
                    {hasAuth(['补货参考值', '编辑']) ? (
                      <XlbButton
                        label="调整规则"
                        disabled={loading}
                        type="primary"
                        onClick={() => {
                          setRuleVisible(true)
                        }}
                        icon={<XlbIcon size={16} name="shezhi" />}
                      />
                    ) : null}
                    {hasAuth(['补货参考值', '编辑']) ? (
                      <XlbButton
                        label="保存调整"
                        type="primary"
                        disabled={loading || ruleLoding}
                        onClick={() => saveRule(setLoading)}
                        icon={<XlbIcon size={16} name="baocun" />}
                      />
                    ) : null}
                    <Rule
                      visible={ruleVisible}
                      handleCancel={() => setRuleVisible(false)}
                      setRowData={pageRef.current?.setDataSource}
                      requestData={prevPost}
                      setPagin={setPagin}
                      setIsLoading={setLoading}
                      setruleLoding={setruleLoding}
                    />
                    <Copy
                      visible={copyVisible}
                      handleCancel={() => setCopyVisible(false)}
                      getData={pageRef.current?.fetchData}
                    />
                    <BatchChange
                      visible={batchVisible}
                      handleCancel={() => setBatchVisible(false)}
                      getData={pageRef.current?.fetchData}
                      enableOrganization={enable_organization}
                    />
                  </XlbButton.Group>
                )
              }}
            </ToolBtn>
            <ToolBtnNoStyle noDom={true}>
              {(context) => {
                return (
                  <XlbTable
                    style={{ flex: 1, width: '100%', marginLeft: '16px' }}
                    columns={context?.columns?.map((v) => {
                      return tableRender(
                        v,
                        form.getFieldValue('unit_type'),
                        context?.setLoading,
                        context?.setSelectRow
                      )
                    })}
                    dataSource={context?.dataSource}
                    keyboard={false}
                    isLoading={context?.loading}
                    total={!ruleLoding ? context?.dataSource?.length : context?.pagin?.total}
                    selectMode={'single'}
                    pageSize={context?.pagin?.pageSize}
                    keepDataSource={true}
                    pageNum={context?.pagin?.pageNum}
                    onChangeSorts={(sortItem) => {
                      const orders = coverSortItemToOrder(sortItem)
                      const arr = [
                        'supplier_names',
                        'sale_quantity',
                        'avg_sale_quantity',
                        'purchase_period'
                      ]
                      !arr.includes(orders?.property) &&
                        context?.setRequestForm({
                          ...context?.requestForm,
                          orders: orders ? [orders] : undefined
                        })
                      // 前端排序的话不请求 接口
                      if (ruleLoding) {
                        setTimeout(() => {
                          pageRef.current?.fetchData(context?.pagin?.pageNum)
                        }, 500)
                      }
                    }}
                    onPaginChange={(pageNum, pageSize) => {
                      context?.setRequestForm({
                        ...context?.requestForm,
                        page_num: pageNum,
                        page_size: pageSize
                      })
                      context?.setPagin({
                        pageNum: pageNum,
                        pageSize: pageSize,
                        total: context?.pagin?.total
                      })
                      if (ruleLoding) {
                        setTimeout(() => {
                          pageRef.current?.fetchData(pageNum, true)
                        }, 500)
                      }
                    }}
                    onSelectRow={(value, data) => {
                      context?.setSelectRow(data)
                    }}
                  />
                )
              }}
            </ToolBtnNoStyle>
          </XlbPageContainer>
        </div>
      </XlbBasicForm>
    </XlbConfigProvider.Provider>
  )
}

export default StoreItemReplenish

# README

`@umijs/max` 模板项目，更多功能参考 [Umi Max 简介](https://umijs.org/docs/max/introduce)

## 子应用名称及权限

#### 名称

> 请先修改 package.json 里面的 name 字段改成符合自己项目的简称

#### 权限

> 修改 wujie/utils.ts 文件中的 auth。 如：ERP

##

## 开发

- 安装依赖
  ```
  npm install
  ```
- 开发环境启动
  ```
  npm run dev
  ```
- 测试环境构建
  ```
  npm run build:dev
  ```
- 生产环境构建
  ```
  npm run build
  ```

## 本地单独启动子应用 token 获取

1. 登录新零帮 https://react-web.react-web.ali-test.xlbsoft.com/ 找同事创建账号。
   - <img src="./src/assets/images/docImg/001.png" />
   - 备注：登录账号需要有 tms 模块的权限，可以到以下模块添加。
   - <img src="./src/assets/images/docImg/002.png" />
2. 点击 TMS 物流系统模块，此时 cookie 中会多出以下 key:value。
   - <img src="./src/assets/images/docImg/003.png" />
3. 获取 cookie 中的 development_qiankun_token key 的值放入 本地开发域名下，刷新页面即可。

## 自动生成 service 文件,使用 openAPI 插件

1. 后端接口更新需要通过 npm run openapi 更新本地接口数据(数据在 service/xlb_tms)
2. 如果有其他非本项目的自动化接口可以放到 service/system 文件夹下

- 更新 openApi
  ```
  npm run openapi
  ```

## 接口转发设置

1. 本地开发 需要再 .umirc.ts proxy 中配置对应的 调用地址
2. 上测试和正式环境需要运维配置相关的 nginx

## 菜单路由添加

1. 本地开发 router 中添加，注意还要保证当前用户权限是有访问权限的

```
// router文件夹中的data.ts文件,用来配置左侧菜单，可以设置一级菜单及二级菜单
export const MenuList = {
  board: {
    label: '看板',
    key: 'board',
    iconUrl: 'iconfont icon-kanban',
  },
  archives: {
    label: '档案',
    key: 'archives',
    iconUrl: 'iconfont icon-dangan',
    list: [
      { label: '基础资料', children: [] },
      { label: '基础项目', children: [] },
      { label: '业务参数', children: [] },
    ],
  },
};


// router文件夹中index.ts文件，用来配置基础路由文件,通过subMenu配置对应的一级菜单，subTitle配置对应的二级菜单。
export const routeList= [
  {
    path: '/home',
    component: '@/pages/Home/index',
    title: 'KMS看板',
    subTitle: '数据看板',
    subMenu: 'board',
  },
  {
    path: '/archives/home',
    component: '@/pages/Home/index',
    title: 'KMS看板',
    subTitle: '基础资料',
    subMenu: 'archives',
  }
]
```

2. 测试正式环境需要再 xlb_main_web 项目中同步添加路由, 并且路由受权限控制,权限配置需要后端添加, 可在 ERP 权限页面看到

## 主子应用交互方式

```
 // 通过umi + qiankun提供的useModel取到主应用传递的函数
 const masterProps = useModel?.('@@qiankunStateFromMaster');
```

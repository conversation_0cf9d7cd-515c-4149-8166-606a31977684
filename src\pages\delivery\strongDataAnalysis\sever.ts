import { default as XlbFetch } from '@/utils/XlbFetch';
import { XlbFetch as ErpRequest } from '@xlb/utils';
// export default {
//   save: async (data: any) => {
//     return await XlbFetch('/ems/hxl.ems.maintenancecompany.save', { ...data });
//   },
//   update: async (data: any) => {
//     return await XlbFetch('/ems/hxl.ems.maintenancecompany.update', {
//       ...data,
//     });
//   },
//   delete: async (data: any) => {
//     return await XlbFetch('/ems/hxl.ems.maintenancecompany.delete', {
//       ...data,
//     });
//   },
// };

//  查询
export const getStrongDataAnalysis = async (data: any) => {
  return await ErpRequest.post('/erp/hxl.erp.deliveryreport.forcedelivery.page', { data })
}

import type { HttpResponse } from '@xlb/components/dist/utils/xlbFetch';
import { LStorage, XlbFetch } from '@xlb/utils';

const fetchData = async <T = any, K = any>(url: string, data?: any) => {
  const requestData = {
    company_id: LStorage.get('userInfo')?.company_id,
    operator_store_id: LStorage.get('userInfo').store_id,
    ...data,
  };

  const filteredObj = Object.fromEntries(
    Object.entries({ ...requestData }).reduce((acc, [key, value]) => {
      if (value !== null && value !== undefined && value !== '') {
        acc.push([key, value]);
      }
      return acc;
    }, []),
  );

  return await XlbFetch.post<T, HttpResponse<K>>(
    `${process.env.BASE_URL}${url}`,
    filteredObj,
  );
};
export default fetchData;

import { columnWidthEnum } from '@/constants';
import { hasAuth } from '@/utils/kit';
import { SearchFormType, XlbTableColumnProps } from '@xlb/components';
import { useNavigation } from '@xlb/max';
import { message } from 'antd';

export const tableColumn: XlbTableColumnProps<any>[] = [
  {
    name: '序号',
    code: '_index',
    width: columnWidthEnum.INDEX,
    align: 'center',
  },
  {
    name: '配送中心代码',
    code: 'store_code',
    width: 140,
  },
  {
    name: '配送中心名称',
    code: 'store_name',
    width: columnWidthEnum.STORE_NAME,
  },
  {
    name: '门店代码',
    code: 'source_store_code',
    width: 160,
  },
  {
    name: '门店名称',
    code: 'source_store_name',
    width: columnWidthEnum.STORE_NAME,
  },
  {
    name: '商品代码',
    code: 'item_code',
    width: 160,
  },
  {
    name: '商品名称',
    code: 'item_name',
    width: 160,
  },
  {
    name: '要货数量',
    code: 'quantity',
    width: 160,
  },
  {
    name: '关联采购订单',
    code: 'purchase_fid',
    width: 200,
    render: (text) =>
      text ? (
        <div
          className="xlb-table-clickBtn"
          onClick={(e) => {
            e.stopPropagation();
            if (!hasAuth(['采购订单', '查询'], 'ERP')) {
              return message.info('该单据无权限！');
            }
            const { navigate } = useNavigation();
            navigate(
              '/xlb_erp/purchaseOrder/item',
              { fid: text },
              'xlb_erp',
              true,
            );
          }}
        >
          {text}
        </div>
      ) : (
        '-'
      ),
  },
  {
    name: '创建时间',
    code: 'create_time',
    width: columnWidthEnum.TIME,
    features: { sortable: true, format: 'TIME' },
  },
];

type GetForm = (customData: any) => SearchFormType[];
export const getFormList: GetForm = ({ storeList }) => [
  {
    type: 'compactDatePicker',
    label: '创建日期',
    name: 'create_date',
    allowClear: false,
  },
  {
    label: '配送中心',
    name: 'store_ids',
    type: 'select',
    clear: true,
    multiple: true,
    check: true,
    options: storeList,
  },
  {
    label: '收货门店',
    name: 'source_store_ids',
    type: 'inputDialog',
    dialogParams: {
      type: 'store',
      dataType: 'lists',
      isLeftColumn: true,
      isMultiple: true,
    },
  },
  {
    label: '商品档案',
    name: 'item_ids',
    type: 'inputDialog',
    dialogParams: {
      type: 'goods',
      dataType: 'lists',
      isMultiple: true,
    },
  },
  {
    label: '关联单据',
    name: 'purchase_fid',
    type: 'input',
    clear: true,
    check: true,
  },
  {
    label: ' ',
    name: 'checkbox',
    type: 'checkbox',
    initialValue: ['flag'],
    colon: false,
    options: [{ label: '未生成采购订单', value: 'flag' }],
  },
];

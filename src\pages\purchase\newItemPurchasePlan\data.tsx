import { hasAuth } from '@/utils/kit';
import { LStorage } from '@/utils/storage';
import { useIRouter } from '@/wujie/utils';
import {
  SearchFormType,
  XlbInputNumber,
  XlbTableColumnProps,
} from '@xlb/components';
import { useNavigation } from '@xlb/max';
import { message } from 'antd';
import { updatePurchaseOrder } from './server';

export enum PurchasePlanState {
  INIT = 'INIT', // 制单
  AUDIT = 'AUDIT', // 审核通过
  REJECT = 'REJECT', // 审核拒绝
}
export const STATE_LIST = [
  {
    label: '制单',
    value: PurchasePlanState.INIT,
  },
  {
    label: '审核通过',
    value: PurchasePlanState.AUDIT,
  },
  {
    label: '审核拒绝',
    value: PurchasePlanState.REJECT,
  },
];

const updateItem = async (
  label: string,
  value: string | number,
  record: any,
  fetchData: any,
) => {
  record[label] = value;
  const res = await updatePurchaseOrder({ ...record });
  if (res?.code === 0) {
    fetchData?.();
  }
};
export const tableColumn: XlbTableColumnProps<any>[] = [
  {
    name: '序号',
    code: '_index',
    width: 50,
    align: 'center',
  },
  {
    name: '计划单号',
    code: 'fid',
    width: 200,
  },
  {
    name: '组织',
    code: 'org_name',
    width: 140,
    features: { sortable: true },
  },
  {
    name: '供应商',
    code: 'supplier_name',
    width: 200,
    features: { sortable: true },
  },
  {
    name: '门店',
    code: 'store_name',
    width: 140,
    features: { sortable: true },
  },
  {
    name: '商品代码',
    code: 'item_code',
    width: 160,
    render: (text, record: any) =>
      text ? (
        <span
          className="xlb-table-clickBtn"
          onClick={(e) => {
            e.stopPropagation();
            const { navigate } = useIRouter();
            navigate('/xlb_erp/newItemPurchasePlan/item/index', {
              item_id: record?.item_id,
              store_id: record?.store_id,
            });
          }}
        >
          {text}
        </span>
      ) : (
        '-'
      ),
  },
  {
    name: '商品名称',
    code: 'item_name',
    width: 160,
  },
  {
    name: '店均PSD(数量)',
    code: 'psd',
    width: 160,
    render: (text: string, record: any, operate: any) => {
      if (record._click && record.state === PurchasePlanState.INIT) {
        return (
          <XlbInputNumber
            onClick={(e) => e.stopPropagation()}
            defaultValue={text}
            precision={2}
            onBlur={(e) =>
              updateItem('psd', e.target.value, record, operate?.fetchData)
            }
          />
        );
      }
      return text;
    },
  },
  {
    name: '店均统配量',
    code: 'allocated_quantity',
    width: 160,
  },
  {
    name: '进店率',
    code: 'store_entry_rate',
    width: 160,
    render: (text: string) => {
      return text ? `${text}%` : '';
    },
  },
  {
    name: '原店均PSD(数量)',
    code: 'org_psd',
    width: 160,
  },
  {
    name: '原计划采购量',
    code: 'org_quantity',
    width: 160,
  },
  {
    name: '计划采购量',
    code: 'quantity',
    width: 160,
    render: (text: string, record: any, operate: any) => {
      if (record._click && record.state === PurchasePlanState.INIT) {
        return (
          <XlbInputNumber
            onClick={(e) => e.stopPropagation()}
            defaultValue={text}
            precision={2}
            onBlur={(e) =>
              updateItem('quantity', e.target.value, record, operate?.fetchData)
            }
          />
        );
      }
      return text;
    },
  },
  {
    name: '最早到货日期',
    code: 'earliest_arrival_date',
    width: 160,
    features: { sortable: true, format: 'TIME' },
  },
  {
    name: '状态',
    code: 'state',
    width: 160,
    render: (text: string) => {
      return STATE_LIST.find((item) => item.value === text)?.label || '';
    },
  },
  {
    name: '制单时间',
    code: 'create_time',
    width: 160,
    features: { sortable: true, format: 'TIME' },
  },
  {
    name: '审核人',
    code: 'audit_by',
    width: 160,
  },
  {
    name: '审核时间',
    code: 'audit_time',
    width: 160,
    features: { sortable: true, format: 'TIME' },
  },
  {
    name: '关联采购订单',
    code: 'purchase_order_fid',
    width: 160,
    render: (text) =>
      text ? (
        <div
          className="xlb-table-clickBtn"
          onClick={(e) => {
            e.stopPropagation();
            if (!hasAuth(['采购订单', '查询'])) {
              return message.info('该单据无权限！');
            }
            const { navigate } = useNavigation();
            navigate(
              '/xlb_erp/purchaseOrder/item',
              { fid: text },
              'xlb_erp',
              true,
            );
          }}
        >
          {text}
        </div>
      ) : (
        '-'
      ),
  },
];

const userInfo = LStorage.get('userInfo');
export const FORM_LIST: SearchFormType[] = [
  {
    type: 'compactDatePicker',
    label: '创建日期',
    name: 'date',
    allowClear: false,
  },
  {
    label: '所属组织',
    name: 'org_ids',
    type: 'select',
    clear: true,
    multiple: true,
    check: true,
    options: [],
    selectRequestParams: {
      url: '/erp-mdm/hxl.erp.org.find',
      responseTrans(response: any) {
        return (
          response
            ?.filter((v: any) => v.level === 2)
            ?.map((item: any) => ({
              ...item,
              label: item.name,
              value: item.id,
            })) || []
        );
      },
    },
  },
  {
    label: '门店',
    name: 'store_ids',
    type: 'inputDialog',
    allowClear: false,
    dialogParams: {
      type: 'store',
      dataType: 'lists',
      isLeftColumn: true,
      isMultiple: true,
      data: {
        enable_organization: false,
        status: true,
      },
    },
    fieldNames: {
      idKey: 'id',
      nameKey: 'store_name',
    } as any,
  },
  {
    label: '供应商',
    name: 'supplier_ids',
    type: 'inputDialog',
    dialogParams: {
      type: 'supplier',
      dataType: 'lists',
      isMultiple: true,
    },
  },
  {
    label: '商品档案',
    name: 'item_ids',
    type: 'inputDialog',
    dialogParams: {
      type: 'goods',
      dataType: 'lists',
      isMultiple: true,
    },
  },
  {
    label: '计划单号',
    name: 'fid',
    type: 'input',
    clear: true,
    check: true,
  },
  {
    label: '采购单号',
    name: 'purchase_order_fid',
    type: 'input',
    clear: true,
    check: true,
  },
  {
    label: '状态',
    name: 'state',
    type: 'select',
    clear: true,
    check: true,
    options: STATE_LIST,
  },
];
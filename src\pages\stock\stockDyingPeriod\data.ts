import { SearchFormType, XlbTableColumnProps } from '@xlb/components';
import { toFixed } from '@xlb/utils';

export const summaryTableColumn: XlbTableColumnProps<any>[] = [
  {
    name: '序号',
    code: '_index',
    width: 72,
    align: 'center',
  },
  {
    name: '门店',
    code: 'store_name',
    width: 172,
    features: { sortable: true },
  },
  {
    name: '仓库',
    code: 'storehouse_name',
    width: 172,
    features: { sortable: true },
  },
  {
    name: '货主',
    code: 'cargo_owner_name',
    hiddenInXlbColumns: true,
    width: 160,
    features: { sortable: true },
  },
  {
    name: '商品代码',
    code: 'item_code',
    width: 117,
    features: { sortable: true },
  },
  {
    name: '商品条码',
    code: 'item_bar_code',
    width: 142,
    features: { sortable: true },
  },
  {
    name: '商品名称',
    code: 'item_name',
    width: 312,
    features: { sortable: true },
  },
  {
    name: '一级分类',
    code: 'one_category_name',
    width: 144,
    features: { sortable: true },
  },
  {
    name: '二级分类',
    code: 'two_category_name',
    width: 144,
    features: { sortable: true },
  },
  {
    name: '三级分类',
    code: 'three_category_name',
    width: 144,
    features: { sortable: true },
  },
  {
    name: '规格',
    code: 'item_spec',
    width: 121,
    features: { sortable: true },
  },
  {
    name: '采购单位',
    code: 'unit',
    width: 112,
    features: { sortable: true },
  },
  {
    name: '库存数量',
    code: 'stock_quantity',
    width: 112,
    align: 'right',
    render: (value) => toFixed(value, 'QUANTITY'),
    features: { sortable: true },
  },
  {
    name: '库存单价',
    code: 'stock_price',
    width: 102,
    align: 'right',
    render: (value) => toFixed(value, 'PRICE'),
    features: { sortable: true },
  },
  {
    name: '库存金额',
    code: 'stock_money',
    width: 137,
    align: 'right',
    render: (value) => toFixed(value, 'MONEY'),
    features: { sortable: true },
  },
  {
    name: '最近到货日期',
    code: 'latest_in_date',
    width: 132,
    features: { sortable: true },
  },
  {
    name: '临期数量',
    code: 'expired_num',
    width: 102,
    align: 'right',
    render: (value) => toFixed(value, 'QUANTITY'),
    features: { sortable: true },
  },
  {
    name: '临期占比',
    code: 'expired_rate_str',
    width: 102,
    align: 'right',
    features: { sortable: true },
  },
];

export const detailTableColumn: XlbTableColumnProps<any>[] = [
  {
    name: '序号',
    code: '_index',
    width: 72,
    align: 'center',
  },
  {
    name: '门店',
    code: 'store_name',
    width: 172,
  },
  {
    name: '仓库',
    code: 'storehouse_name',
    width: 172,
  },
  {
    name: '货主',
    code: 'cargo_owner_name',
    hiddenInXlbColumns: true,
    width: 160,
    features: { sortable: true },
  },
  {
    name: '商品代码',
    code: 'item_code',
    width: 117,
  },
  {
    name: '商品条码',
    code: 'item_bar_code',
    width: 142,
  },
  {
    name: '商品名称',
    code: 'item_name',
    width: 312,
  },
  {
    name: '一级分类',
    code: 'one_category_name',
    width: 144,
  },
  {
    name: '二级分类',
    code: 'two_category_name',
    width: 144,
  },
  {
    name: '三级分类',
    code: 'three_category_name',
    width: 144,
  },
  {
    name: '规格',
    code: 'item_spec',
    width: 121,
  },
  {
    name: '采购单位',
    code: 'unit',
    width: 112,
  },
  {
    name: '库存数量',
    code: 'stock_quantity',
    width: 112,
    align: 'right',
    render: (value) => toFixed(value, 'QUANTITY'),
  },
  {
    name: '库存单价',
    code: 'stock_price',
    width: 102,
    align: 'right',
    render: (value) => toFixed(value, 'PRICE'),
  },
  {
    name: '库存金额',
    code: 'stock_money',
    width: 137,
    align: 'right',
    render: (value) => toFixed(value, 'MONEY'),
  },
  {
    name: '生产日期',
    code: 'producing_date',
    width: 132,
  },
  {
    name: '到期日期',
    code: 'expire_date',
    width: 132,
  },
  {
    name: '最近到货日期',
    code: 'latest_in_date',
    width: 132,
  },
  {
    name: '临期数量',
    code: 'expired_num',
    width: 102,
    align: 'right',
    render: (value) => toFixed(value, 'QUANTITY'),
  },
];

export const searchFormList: SearchFormType[] = [
  {
    type: 'select',
    name: 'mode',
    label: '查询模式',
    options: [
      { label: '汇总', value: 'summary' },
      { label: '明细', value: 'detail' },
    ],
    allowClear: false,
    width: 200,
  },
  {
    label: '门店',
    name: 'store_ids',
    type: 'inputDialog',
    dialogParams: {
      type: 'store',
      dataType: 'lists',
      isLeftColumn: true,
      isMultiple: true,
      primaryKey: 'id',
      data: {
        enabled: true,
        center_flag: true,
      },
      nullable: false,
    },
    allowClear: true,
    fieldNames: {
      // @ts-ignore
      idKey: 'id',
      nameKey: 'store_name',
    },
    removeIcon: null,
    width: 200,
  },
  {
    label: '仓库',
    name: 'storehouse_ids',
    type: 'select',
    options: [],
    linkId: 'storehouse_ids_options',
    width: 200,
  },
  {
    label: '商品分类',
    name: 'item_category_ids',
    type: 'inputDialog',
    treeModalConfig: {
      title: '选择商品分类', // 标题
      url: '/erp-mdm/hxl.erp.category.find', // 请求地址
      dataType: 'lists',
      checkable: true, // 是否多选
      primaryKey: 'id',
      data: {
        enabled: true,
      },
      width: 360, // 模态框宽度
    } as any,
    width: 200,
  },
  {
    label: '商品档案',
    name: 'item_ids',
    type: 'inputDialog',
    dialogParams: {
      type: 'goods',
      dataType: 'lists',
      isLeftColumn: true,
      isMultiple: true,
      primaryKey: 'id',
      data: {
        enabled: true,
      },
    },
    width: 200,
  },
  {
    type: 'select',
    name: 'category_level',
    label: '类别等级',
    options: [
      { label: '当前分类', value: 'empty' },
      { label: '一级类别', value: 1 },
      { label: '二级类别', value: 2 },
      { label: '三级类别', value: 3 },
    ],
    width: 200,
  },
  {
    type: 'select',
    name: 'unit_type',
    label: '查询单位',
    allowClear: false,
    options: [
      { label: '基本单位', value: 'BASIC' },
      { label: '采购单位', value: 'PURCHASE' },
      { label: '配送单位', value: 'DELIVERY' },
      { label: '库存单位', value: 'STOCK' },
      { label: '批发单位', value: 'WHOLESALE' },
    ],
    width: 200,
  },
  {
    label: '货主',
    name: 'cargo_owner_ids',
    type: 'inputDialog',
    clear: true,
    check: true,
    dialogParams: {
      type: 'cargoOwner',
      dataType: 'lists',
      isLeftColumn: false,
      isMultiple: true,
      primaryKey: 'id',
      data: {
        enabled: true,
      },
    },
    allowClear: false,
    fieldNames: {
      // @ts-ignore
      idKey: 'id',
      nameKey: 'source_name',
    },
    width: 200,
  },
  {
    type: 'group',
    label: '临期条件',
    formList: [
      {
        type: 'select',
        name: 'expire_type',
        width: 110,
        options: [
          { label: '按比例', value: '1' },
          { label: '按天数', value: '0' },
        ],
        allowClear: false,
        onChange(value: any, form: any) {
          if (value === '1') {
            form.setFieldValue('expire_value', 33);
          } else {
            form.setFieldValue('expire_value', undefined);
          }
        },
      },
      {
        type: 'inputNumber',
        name: 'expire_value',
        width: 80,
        max: 100,
        min: 1,
        precision: 0,
        onDependencies(form: any) {
          if (form.getFieldValue('expire_type') == '1') {
            return false;
          }
          return true;
        },
      },
      {
        type: 'inputNumber',
        name: 'expire_value',
        width: 80,
        max: 10000,
        min: 1,
        precision: 0,
        onDependencies(form: any) {
          if (form.getFieldValue('expire_type') == '0') {
            return false;
          }
          return true;
        },
      },
    ],
  },
  {
    type: 'checkbox',
    name: 'query_expired_item',
    options: [{ label: '仅查询过期商品', value: 1 }],
    onDependencies(form: any) {
      if (form.getFieldValue('mode') == 'detail') {
        return false;
      }
      return true;
    },
  },
];

export const summaryUrl = '/erp/hxl.erp.stockexpiredchecksummary.find';

export const detailUrl = '/erp/hxl.erp.stockexpiredcheck.find';

import { hasAuth } from '@/utils/kit';
import { LStorage } from '@/utils/storage';
import toFixed from '@/utils/toFixed';
import {
  XlbBasicForm,
  XlbButton,
  XlbColumns,
  XlbForm,
  XlbIcon,
  XlbPageContainer,
  XlbTable,
  XlbTooltip,
} from '@xlb/components';
import { message } from 'antd';
import classnames from 'classnames';
import dayjs from 'dayjs';
import { isArray } from 'lodash-es';
import { useState } from 'react';
import { searchFormList, tableColumn } from './data';
import './index.less';
import style from './index.less';
import { getExport, getList } from './server';
const { SearchForm } = XlbPageContainer;

const UnsalabelItem = () => {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [rowData, setRowData] = useState<any[]>([]);
  const [pagin, setPagin] = useState({
    pageSize: 100,
    pageNum: 1,
    total: 0,
  });
  const [isFold, setIsFold] = useState<boolean>(false);
  const [tableColumns, setTableColumns] = useState<any[]>(
    JSON.parse(JSON.stringify(tableColumn)),
  );
  const [searchFormLists, setSearchFormLists] = useState(searchFormList);
  const [form] = XlbBasicForm.useForm();

  const requestData = () => {
    const filter1 = form.getFieldValue('filter1')
      ? form.getFieldValue('filter1')
      : null;
    const filter2 = form.getFieldValue('filter2')
      ? form.getFieldValue('filter2')
      : null;
    return {
      ...form.getFieldsValue(),
      storehouse_id: form.getFieldValue('storehouse_id'),
      latest_in_date: form.getFieldValue('latest_in_date') || null,
      unit_type: form.getFieldValue('unit_type') || 0,
      day: form.getFieldValue('day') || 0,
      total_out_quantity: form.getFieldValue('total_out_quantity') || 0,

      out_quantity_less_than_stock_quantity:
        filter1 != null
          ? filter1.includes('out_quantity_less_than_stock_quantity')
          : false,
      filter_no_stock_item:
        filter2 != null ? filter2.includes('filter_no_stock_item') : false,
      filter_stop_purchase:
        filter2 != null ? filter2.includes('filter_stop_purchase') : false,
      filter_stop_sale:
        filter2 != null ? filter2.includes('filter_stop_sale') : false,
      open_news_cycle:
        filter2 != null ? filter2.includes('open_news_cycle') : false,
      filter_stop_request:
        filter2 != null ? filter2.includes('stop_request') : false,
      filter_zero_stock_item:
        filter2 != null ? filter2.includes('filter_zero_stock_item') : false,
    };
  };

  const getData = async (page_number: number, page_size: number) => {
    const data = requestData();
    data.page_number = page_number;
    data.page_size = page_size;
    setIsLoading(true);
    const res = await getList({
      ...data,
    });
    setIsLoading(false);
    if (res?.code === 0) {
      setRowData(res.data || []);
      setPagin({
        pageNum: page_number,
        pageSize: page_size,
        total: res.data?.length,
      });
    }
  };

  const exportItem = async () => {
    setIsLoading(true);
    const data = requestData();
    const res = await getExport({
      ...data,
    });

    if (res?.code == 0) {
      message.success('导出受理成功，请前往下载中心查看');
    }
    setIsLoading(false);
  };

  const tableRender = (item: any) => {
    switch (item.code) {
      case '_index':
        item.render = (t: any, r: any, i: number) => {
          return <span>{i + 1}</span>;
        };
        break;
      case 'stop_purchase':
      case 'stop_sale':
      case 'open_news_cycle':
      case 'stop_request':
        item.render = (value: any, record: any, index: number) => {
          const showColor = value ? 'success' : 'danger';
          return (
            <div className={`overwidth ${showColor}`}>
              {value ? '是' : '否'}
            </div>
          );
        };
        break;
      case 'out_quantity':
      case 'wholesale_quantity':
      case 'pos_sale_quantity':
      case 'total_out_quantity':
      case 'receive_quantity':
      case 'in_quantity':
      case 'stock_quantity':
      case 'total_in_quantity':
        item.render = (value: any, record: any, index: number) => (
          <div style={{ textAlign: 'right' }}>
            {toFixed(Number(value), 'QUANTITY', item.code === 'stock_quantity')}
          </div>
        );
        break;
    }
    return item;
  };

  const pageChange = (number: number, size: number) => {
    if (size != pagin.pageSize) {
      // getData(1, size);

      setPagin({
        ...pagin,
        pageSize: size,
        pageNum: 1,
      });
    } else {
      // getData(number, size);

      setPagin({
        ...pagin,
        pageSize: size,
        pageNum: number,
      });
    }
  };

  const isInteger = (obj: any) => {
    return typeof obj === 'number' && obj % 1 === 0;
  };

  const onValuesChange = (e: Object) => {
    const changeKey = Object.keys(e)[0];
    if (changeKey === 'store_ids') {
      console.log('?????????????');
      form.setFieldsValue({
        storehouse_id: null,
      });
      searchFormLists.find((i) => i.name === 'storehouse_id')!.options = [];
      if (isArray(e.store_ids)) {
        searchFormLists.find((i) => i.name === 'storehouse_id')!.disabled =
          e.store_ids.length != 1;
        setSearchFormLists([...searchFormLists]);
      } else {
        searchFormLists.find((i) => i.name === 'storehouse_id')!.disabled =
          true;
        setSearchFormLists([...searchFormLists]);
      }
    }
  };

  const cloneList = JSON.parse(JSON.stringify(tableColumns)).map((e: any) => {
    e.children = e.children.map((i: any) => {
      return tableRender(i);
    });
    return e;
  });

  // @ts-ignore
  return (
    <XlbPageContainer className={style.pageContainer}>
      <div className={'main-container'}>
        <div className={'button_box row-flex option-container'}>
          <div style={{ width: '90%' }} className="row-flex">
            <XlbButton.Group>
              {hasAuth(['滞销商品', '查询']) ? (
                <XlbButton
                  label="查询"
                  type="primary"
                  loading={isLoading}
                  onClick={() => {
                    setPagin({
                      ...pagin,
                      pageNum: 1,
                    });
                    getData(1, pagin.pageSize);
                  }}
                  icon={
                    <XlbIcon name="sousuo" color="currentColor" size={16} />
                  }
                />
              ) : null}
              {hasAuth(['滞销商品', '导出']) ? (
                <XlbButton
                  label="导出"
                  type="primary"
                  loading={isLoading}
                  disabled={isLoading || !rowData?.length}
                  onClick={() => exportItem()}
                  icon={<XlbIcon size={16} name="daochu" />}
                />
              ) : null}
            </XlbButton.Group>
          </div>
          <div
            style={{
              width: '10%',
              height: '28px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'flex-end',
            }}
          >
            <XlbTooltip title={isFold ? '收起' : '展开'}>
              <XlbIcon
                data-type={'顶层展开收起'}
                onClick={() => setIsFold(!isFold)}
                name="shouqi"
                size={20}
                className={classnames('xlb-columns-main-btn', {
                  'xlb-columns-fold': isFold,
                  'xlb-columns-expand-btn-dev': true,
                })}
              />
            </XlbTooltip>
            <XlbColumns
              isFold={isFold}
              isFoldChange={setIsFold}
              url={'/hxl.erp.unsalableitem.find-columns'}
              originColumns={tableColumn}
              value={tableColumns}
              onChange={setTableColumns}
              name={'/hxl.erp.unsalableitem.find-columns-name'}
            />
          </div>
        </div>
        {isFold ? null : (
          <div className={'form_header_box ant-form'}>
            <div className="form-container">
              <SearchForm>
                <XlbForm
                  formList={searchFormLists}
                  form={form}
                  onValuesChange={(value: any) => onValuesChange(value)}
                  isHideDate
                  initialValues={{
                    store_ids: [LStorage.get('userInfo').store_id],
                    latest_in_date: dayjs().add(+1, 'day').format('YYYY-MM-DD'),
                    day: 30,
                    total_out_quantity: 0,
                    filter2: ['filter_zero_stock_item'],
                    stock_day: [0, 28],
                  }}
                />
              </SearchForm>
            </div>
          </div>
        )}
        <XlbTable
          style={{
            marginLeft: '16px',
            height: 'calc(100% - 200px)',
          }}
          columns={cloneList}
          isLoading={isLoading}
          pageSize={pagin?.pageSize}
          pageNum={pagin?.pageNum}
          total={pagin.total}
          dataSource={rowData}
          isFold={isFold}
          onPaginChange={(page: number, size: number) => {
            pageChange(page, size);
          }}
          showSearch={true}
        />
      </div>
    </XlbPageContainer>
  );
};

export default UnsalabelItem;

import { appName } from '@/wujie/utils';
import { useAliveController } from '@@/exports';
import { Outlet } from '@umijs/max';
import { ProComponentContext } from '@xlb/components';
import { useEffect, useRef } from 'react';
export default () => {
  const detailDomRef = useRef<HTMLDivElement>(null);
  const { dropScope } = useAliveController();
  // 清除tab时候刷新keepalive scope
  const refreshKeepAlive = (currentTabItem: any) => {
    const { wujiePath, aliveName } = currentTabItem ?? {};
    const pathRouter = aliveName ?? `/${wujiePath.replace(/\+/g, '/')}`;
    dropScope(pathRouter);
  };

  useEffect(() => {
    window.$wujie?.bus.$on(`${appName}-UnmountKeepAlive`, refreshKeepAlive);
    return () => {
      window.$wujie?.bus.$off(`${appName}-UnmountKeepAlive`, refreshKeepAlive);
    };
  }, []);
  return (
    <ProComponentContext.Provider
      value={{
        detailDomRef,
      }}
    >
      <div
        datatype={`挂载空投节点`}
        style={{ position: 'relative', height: '100%' }}
        // 当前挂载的空投节点
        ref={detailDomRef}
      >
        <Outlet />
      </div>
    </ProComponentContext.Provider>
  );
};

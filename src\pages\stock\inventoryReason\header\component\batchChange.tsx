import NiceModal from '@ebay/nice-modal-react';
import type { BaseModalProps } from '@xlb/components';
import {
  XlbBasicForm,
  XlbModal,
  XlbRadio,
  XlbSelect,
  XlbTipsModal,
} from '@xlb/components';
import { Checkbox, message } from 'antd';
import type { FC } from 'react';
import { useEffect, useState } from 'react';
import { editstockadjustmentreasonBatch, getReason } from '../../server';
import style from './batchChange.less';

interface Props extends Pick<BaseModalProps, 'title'> {
  requestForm?: any;
  fetchData?: any;
  nameList?: any;
}

export const BatchChangeModal: FC<Props> = ({
  requestForm,
  fetchData,
  nameList,
}) => {
  const [reasonList, setReasonList] = useState<any>([]);

  const [form] = XlbBasicForm.useForm();
  const modal = NiceModal.useModal();
  const batch = [
    { label: '出入库标记', value: 'flag' },
    { label: '应用范围', value: 'applicability' },
    { label: '状态', value: 'enabled' },
  ];

  const getReasons = async () => {
    const res = await getReason({
      applicabilities: [0, 1, 2],
    });
    if (res.code === 0) {
      const reasonArr = res.data.map((item: any) => ({
        label: item.name,
        value: item.id,
        flag: item.flag,
        code: item.code,
      }));
      setReasonList(reasonArr);
    }
  };
  useEffect(() => {
    getReasons();
  }, []);
  const renderCheckBox = (item: string, checked: any[]) => {
    switch (item) {
      case '出入库标记':
        return (
          <XlbRadio.Group>
            <XlbRadio value={1}>出库</XlbRadio>
            <XlbRadio value={0}>入库</XlbRadio>
          </XlbRadio.Group>
        );
      case '应用范围':
        return (
          <XlbSelect
            style={{ width: 156 }}
            options={[
              {
                label: '不限',
                value: 0,
              },
              {
                label: '配送中心',
                value: 1,
              },
              {
                label: '非配送中心',
                value: 2,
              },
            ]}
          ></XlbSelect>
        );
      case '状态':
        return (
          <XlbRadio.Group>
            <XlbRadio value={true} style={{ width: 100 }}>
              启用
            </XlbRadio>
            <XlbRadio value={false}>禁用</XlbRadio>
          </XlbRadio.Group>
        );
      default:
        return null;
    }
  };

  const handleOk = async (values: any) => {
    const data = {
      ...values,
      ids: form.getFieldValue('reason_ids'),
    };
    const checkValue = form.getFieldValue('check_value');
    batch.forEach((i) => {
      if (!checkValue?.includes(i?.value)) {
        delete data[i?.value];
      }
    });
    if (
      Array.isArray(data.reason_ids) &&
      form.getFieldValue('reason_ids')?.length == 0
    ) {
      XlbTipsModal({
        tips: '请选择库存调整原因',
      });
      return false;
    }
    const res = await editstockadjustmentreasonBatch(data);
    if (res.code == 0) {
      message.success('操作成功');
      modal.resolve(false);
      modal.hide();
      fetchData();
    }
  };
  return (
    <XlbModal
      width={450}
      open={modal.visible}
      title="批量修改"
      isCancel={true}
      onOk={async () => {
        form.submit();
      }}
      onCancel={() => {
        modal.resolve(false);
        modal.hide();
      }}
    >
      <div>
        <XlbBasicForm
          form={form}
          style={{ margin: '20px 0' }}
          initialValues={{
            applicability: 0,
          }}
          onFinish={() => {
            const values = form.getFieldsValue(true);
            handleOk(values);
          }}
        >
          <div className={style.box}>
            <p className={style.title}>修改范围</p>
            <XlbBasicForm.Item
              label="库存调整原因名称"
              labelCol={{ span: 12 }}
              name="reason_ids"
            >
              <XlbSelect
                style={{ width: 156 }}
                mode="multiple"
                allowClear
                options={reasonList}
              ></XlbSelect>
            </XlbBasicForm.Item>
          </div>
          <div className={style.box} style={{ marginBottom: 0 }}>
            <p className={style.title}>修改内容</p>
            <XlbBasicForm.Item name="check_value" style={{ marginTop: 15 }}>
              <Checkbox.Group style={{ width: '100%' }}>
                {batch.map((item) => {
                  return (
                    <div
                      style={{ display: 'flex', alignItems: 'center' }}
                      key={item.value}
                    >
                      <Checkbox value={item.value}>{item.label}</Checkbox>
                      <XlbBasicForm.Item dependencies={['check_value']} noStyle>
                        {({ getFieldValue }) => {
                          return (
                            <XlbBasicForm.Item
                              name={item.value}
                              key={item.value}
                            >
                              {renderCheckBox(
                                item.label,
                                getFieldValue('check_value'),
                              )}
                            </XlbBasicForm.Item>
                          );
                        }}
                      </XlbBasicForm.Item>
                    </div>
                  );
                })}
              </Checkbox.Group>
            </XlbBasicForm.Item>
          </div>
        </XlbBasicForm>
      </div>
    </XlbModal>
  );
};

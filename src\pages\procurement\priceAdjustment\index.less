.priceAdjustmentItemBox {
  padding: 12px;
  // background-color: #f2f3f5;
  .priceAdjustmentItem {
    // padding: 16px;
    overflow-y: auto;
    background-color: #ffffff;
    border-radius: 4px;
  }

  .priceAdjustmentInfo {
    display: flex;
    .priceAdjustmentTextInfo {
      display: flex;
      flex: 3;
      flex-wrap: wrap;
    }
    .priceAdjustmentTextInfo > .item {
      width: 296px;
      height: 32px;
      color: #86909c;
      font-size: 14px;
      line-height: 32px;
      span {
        overflow: hidden;
        color: #1d2129;
        white-space: nowrap;
        text-overflow: ellipsis;
        word-break: keep-all;
      }
    }

    .priceAdjustmentImageInfo {
      flex: 1;
    }
  }
}

.priceAdjustmentCopy {
  color: #86909c !important;
}

.priceAdjustmentCopy:hover {
  color: #3d66fe !important;
}

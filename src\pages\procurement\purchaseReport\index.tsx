import PromiseModal from '@/components/promiseModal/PromiseModal';
import { useBaseParams } from '@/hooks/useBaseParams';
import { exportPage } from '@/services/system';
import Download from '@/utils/downloadBlobFile';
import {
  formatWithCommas,
  hasAuth,
  normalizeToCommaSeparated,
} from '@/utils/kit';
import { LStorage } from '@/utils/storage';
import toFixed from '@/utils/toFixed';
import NiceModal from '@ebay/nice-modal-react';
import {
  XlbBasicForm,
  XlbButton,
  XlbColumns,
  XlbForm,
  XlbIcon,
  XlbPageContainer,
  XlbTable,
  XlbTabs,
  XlbTooltip,
} from '@xlb/components';
import { message } from 'antd';
import classnames from 'classnames';
import dayjs from 'dayjs';
import { cloneDeep, isArray } from 'lodash-es';
import { useEffect, useState, type FC } from 'react';
import {
  categoryLevels,
  categoryTableColumn,
  formList,
  PURCHASE_TYPE_LIST,
  receiveTableColumn,
  returnTableColumn,
  storeItemTableColumn,
  supplierItemTableColumn,
  supplierStoreItemTableColumn,
  supplierStoreTableColumn,
  supplierTableColumn,
  TableEnumBySummary,
  UnitType,
} from './data';
import './index.less';
import {
  queryFinanceCode,
  receiveOrderDetail,
  returnOrderDetail,
  storeCategoryDetail,
  storeItemDetail,
  supplierDetail,
  supplierItemDetail,
  supplierStoreDetail,
  supplierStoreItemDetail,
} from './server';

const { SearchForm } = XlbPageContainer;
const PurchaseLatestPrice: FC = () => {
  const { enable_organization, enable_cargo_owner } = useBaseParams(
    (state) => state,
  );
  const [tabKey, setTabKey] = useState('receive');
  const [form] = XlbBasicForm.useForm();
  const [exportParams, setExportParams] = useState({});
  const [rowData, setRowData] = useState<any[]>([]);
  const [tabList, setTabList] = useState<any[]>(receiveTableColumn);
  const [oldArr, setOldArr] = useState<any[]>([...receiveTableColumn]);

  const [isFold, setIsFold] = useState<boolean>(false);
  const [formLists, setSearchFormLists] = useState(
    formList(enable_organization, enable_cargo_owner),
  );
  const [selectedObj, setSelectedObj] = useState<object>([]);
  const [selectedAreaObj, setSelectedAreaObj] = useState<object>([]); // 已选择的

  const [useReceiveTableRowData, setUseReceiveTableRowData] = useState<[]>([]);
  const [useReturnTableRowData, setUseReturnTableRowData] = useState<[]>([]);
  const [useStoreItemTableRowData, setUseStoreItemTableRowData] = useState<[]>(
    [],
  );
  const [useSupplierTableRowData, setUseSupplierTableRowData] = useState<[]>(
    [],
  );
  const [useCategoryTableRowData, setUseCategoryTableRowData] = useState<[]>(
    [],
  );
  const [useSupplierStoreTableRowData, setUseSupplierStoreTableRowData] =
    useState<[]>([]);
  const [useSupplierItemTableRowData, setUseSupplierItemTableRowData] =
    useState<[]>([]);
  const [
    useSupplierStoreItemTableRowData,
    setUseSupplierStoreItemTableRowData,
  ] = useState<[]>([]);

  const [useReceiveTableFooterData, setUseReceiveTableFooterData] = useState<
    []
  >([]);
  const [useReturnTableFooterData, setUseReturnTableFooterData] = useState<[]>(
    [],
  );
  const [useStoreItemTableFooterData, setUseStoreItemTableFooterData] =
    useState<[]>([]);
  const [useSupplierTableFooterData, setUseSupplierTableFooterData] = useState<
    []
  >([]);
  const [useCategoryTableFooterData, setUseCategoryTableFooterData] = useState<
    []
  >([]);
  const [useSupplierStoreTableFooterData, setUseSupplierStoreTableFooterData] =
    useState<[]>([]);
  const [useSupplierItemTableFooterData, setUseSupplierItemTableFooterData] =
    useState<[]>([]);
  const [
    useSupplierStoreItemTableFooterData,
    setUseSupplierStoreItemTableFooterData,
  ] = useState<[]>([]);

  const [footerData, setFooterData] = useState<any[]>([]);

  const [uidUrl, setUidUrl] = useState('');

  const filterTab = [
    'storeItem',
    'category',
    'supplierStore',
    'supplierStoreItem',
  ];

  const [useReceiveTablePagin, setUseReceiveTablePagin] = useState({
    pageSize: 200,
    pageNum: 1,
    total: 0,
  });
  const [useReturnTablePagin, setUseReturnTablePagin] = useState({
    pageSize: 200,
    pageNum: 1,
    total: 0,
  });
  const [useStoreItemTablePagin, setUseStoreItemTablePagin] = useState({
    pageSize: 200,
    pageNum: 1,
    total: 0,
  });
  const [useSupplierTablePagin, setUseSupplierTablePagin] = useState({
    pageSize: 200,
    pageNum: 1,
    total: 0,
  });
  const [useCategoryTablePagin, setUseCategoryTablePagin] = useState({
    pageSize: 200,
    pageNum: 1,
    total: 0,
  });
  const [useSupplierStoreTablePagin, setUseSupplierStoreTablePagin] = useState({
    pageSize: 200,
    pageNum: 1,
    total: 0,
  });
  const [useSupplierItemTablePagin, setUseSupplierItemTablePagin] = useState({
    pageSize: 200,
    pageNum: 1,
    total: 0,
  });
  const [useSupplierStoreItemTablePagin, setUseSupplierStoreItemTablePagin] =
    useState({
      pageSize: 200,
      pageNum: 1,
      total: 0,
    });

  const [pagin, setPagin] = useState({
    pageSize: 200,
    pageNum: 1,
    total: 0,
  });

  const [isLoading, setIsLoading] = useState(false);
  const [sortType, setSortType] = useState<{ order: string; code: string }>({
    order: '',
    code: '',
  });

  let refresh = () => {};

  const onValuesChange = (e: any) => {
    const keys = Object.keys(e);
    //组织变动，清空门店值
    if (keys.includes('org_ids')) {
      formLists.find(
        (i) => i.name === 'store_ids',
      )!.dialogParams!.data!.org_ids = e?.out_org_ids ?? [];
      formLists.find((i) => i.name === 'storehouse_ids')!.options = [];
      formLists.find((i) => i.name === 'storehouse_ids')!.disabled = true;
      setSearchFormLists([...formLists]);
      form.setFieldsValue({
        store_ids: [],
        storehouse_ids: null,
      });
    }

    if (keys.includes('store_ids')) {
      form.setFieldsValue({
        storehouse_ids: null,
      });
      formLists.find((i) => i.name === 'storehouse_ids')!.options = [];
      if (isArray(e.store_ids)) {
        formLists.find((i) => i.name === 'storehouse_ids')!.disabled =
          e.store_ids.length != 1;
        setSearchFormLists([...formLists]);
      } else {
        formLists.find((i) => i.name === 'storehouse_ids')!.disabled = true;
        setSearchFormLists([...formLists]);
      }
    }

    // 查询条件或汇总条件更新时，更新表单和表格
    if (['condition', 'summary_conditions'].includes(keys[0])) {
      const summary_conditions = form.getFieldValue('summary_conditions');

      if (!summary_conditions?.includes('STORE')) {
        form.setFieldsValue({
          store_ids: [],
        });
      }

      if (!summary_conditions?.includes('BUSINESS_AREA')) {
        form.setFieldsValue({
          business_area_id: [],
        });
      }

      if (
        !summary_conditions?.includes('PROVINCE') &&
        !summary_conditions?.includes('CITY') &&
        !summary_conditions?.includes('DISTRICT')
      ) {
        form.setFieldsValue({
          city_codes: [],
        });
      }

      changeKey(tabKey);
    }
  };

  // const preFormData = async (store_id: string) => {
  //   let res = null
  //   res = await getStoreHouse({ store_id: store_id })
  //   if (res.code == 0) {
  //     const houseList = res.data.map((item: any) => {
  //       return { label: item.name, value: item.id }
  //     })
  //     const formItem = formLists.find((item) => item.label === '仓库')
  //     formItem!.disabled = false
  //     formItem!.options = houseList
  //     formLists.find((item) => item.value === 'org_ids')!.hidden = !enable_organization
  //     updateSearchFormLists(formLists)
  //   }
  // }

  const tableRender = (item: any) => {
    switch (item.code) {
      // case '_index':
      //   item.render = (value: any, record: any, index: number) => {
      //     return value === '合计' ? (
      //       <div className="info overwidth">{value}</div>
      //     ) : (
      //       <div className="info overwidth">
      //         {(pagin.pageNum - 1) * pagin.pageSize + 1 + index}
      //       </div>
      //     );
      //   };
      //   break;
      case 'basic_quantity':
      case 'present_quantity':
        item.name = <span style={{ marginLeft: 'auto' }}>{item.name}</span>;
        item.render = (value: any, record: any, index: number) => {
          return (
            <div style={{ textAlign: 'right' }}>
              {toFixed(value, 'QUANTITY')}
            </div>
          );
        };
        break;
      case 'present_money':
      case 'no_tax_present_money':
        item.name = <span style={{ marginLeft: 'auto' }}>{item.name}</span>;
        item.render = (value: any, record: any, index: number) => {
          return hasAuth(['采购汇总明细/采购价', '查询']) ? (
            <div style={{ textAlign: 'right' }}>
              {formatWithCommas(toFixed(value, 'MONEY'))}
            </div>
          ) : (
            <div style={{ textAlign: 'right' }}>***</div>
          );
        };
        break;
      case 'fid':
        item.render = (value: any, record: any, index: number) => {
          return (
            <div
              className="link overwidth"
              onClick={(e) => {
                e.stopPropagation();
                NiceModal.show(PromiseModal, {
                  order_type: tabKey === 'receive' ? '收货单' : '退货单',
                  order_fid: value,
                });
              }}
            >
              {value}
            </div>
          );
        };
        break;
      case 'receive_quantity':
      case 'receive_basic_quantity':
      case 'basic_present_quantity':
      case 'receive_present_quantity':
      case 'receive_basic_present_quantity':
      case 'return_quantity':
      case 'return_basic_quantity':
      case 'quantity':
        item.name = <span style={{ marginLeft: 'auto' }}>{item.name}</span>;
        item.render = (value: any, record: any, index: number) => {
          return (
            <div style={{ textAlign: 'right' }}>
              {toFixed(value, 'QUANTITY')}
            </div>
          );
        };
        break;
      case 'price':
      case 'no_tax_price':
        item.name = <span style={{ marginLeft: 'auto' }}>{item.name}</span>;
        item.render = (value: any, record: any, index: number) => {
          return hasAuth(['采购汇总明细/采购价', '查询']) ? (
            <div style={{ textAlign: 'right' }}>{toFixed(value, 'PRICE')}</div>
          ) : (
            <div style={{ textAlign: 'right' }}>***</div>
          );
        };
        break;
      case 'return_cost_money':
      case 'return_no_tax_cost_money':
        item.name = <span style={{ marginLeft: 'auto' }}>{item.name}</span>;
        item.render = (value: any, record: any, index: number) => {
          return hasAuth(['采购汇总明细/成本价', '查询']) ? (
            <div style={{ textAlign: 'right' }}>
              {formatWithCommas(toFixed(value, 'MONEY'))}
            </div>
          ) : (
            <div style={{ textAlign: 'right' }}>***</div>
          );
        };
        break;
      case 'receive_money':
      case 'receive_present_money':
      case 'return_money':
      case 'money':
      case 'no_tax_money':
      case 'supplier_money':
      case 'receive_no_tax_money':
      case 'return_no_tax_money':
      case 'receive_no_tax_present_money':
        item.name = <span style={{ marginLeft: 'auto' }}>{item.name}</span>;
        item.render = (value: any, record: any, index: number) => {
          return hasAuth(['采购汇总明细/采购价', '查询']) ? (
            <div style={{ textAlign: 'right' }}>
              {formatWithCommas(toFixed(value, 'MONEY'))}
            </div>
          ) : (
            <div style={{ textAlign: 'right' }}>***</div>
          );
        };
        break;
      case 'purchase_type':
        item.render = (value: any) => (
          <div>
            {PURCHASE_TYPE_LIST.find((item) => item.value == value)?.label ||
              ''}
          </div>
        );
        break;
      case 'category_level':
        item.render = (value: any, record: any, index: number) => {
          const curCategoryName = categoryLevels?.find(
            (i) => i.value == value,
          )?.label;
          return <div>{curCategoryName}</div>;
        };
      case 'cargo_owner_id':
        item.render = (_value: any, record: any) => (
          <span>{record?.cargo_owner_name}</span>
        );
        break;
      default:
        item.render = (value: any, record: any, index: number) => {
          return <div>{value}</div>;
        };
    }

    return item;
  };

  const checkData = (page_number: number = 1) => {
    const start_time = form.getFieldValue('compactDatePicker')[0];
    const end_time = form.getFieldValue('compactDatePicker')[1];
    const time_type = form.getFieldValue('timeClass');
    let data: any = {
      ...form.getFieldsValue(),
      fid: normalizeToCommaSeparated(form.getFieldValue('fid')),
      item_category_ids: form.getFieldValue('category_ids'),
      purchase_types: form.getFieldValue('purchase_types') || [],
      page_number: page_number - 1,
      page_size: pagin.pageSize,
      company_id: LStorage.get('userInfo').company_id,
      operator_store_id: LStorage.get('userInfo').store_id,
    };

    data[time_type] = [
      dayjs(start_time).format('YYYY-MM-DD'),
      dayjs(end_time).format('YYYY-MM-DD'),
    ];

    delete data.Data_Compact_RangeType_compactDatePicker;
    delete data.compactDatePicker;
    delete data.compactDatePicker;

    if (filterTab.includes(tabKey)) {
      data = {
        ...data,
        one_level_business_area_ids: form.getFieldValue('business_area_id'),
      };
    }

    if (sortType.code) {
      data.orders = sortType.code
        ? [
            {
              direction: sortType.order.toUpperCase(),
              property:
                sortType.code === 'supplier_ids'
                  ? 'supplier_ids'
                  : sortType.code,
            },
          ]
        : null;
    }

    return data;
  };

  const getData = async (page_number: number = 1) => {
    // 汇总条件必选
    const { summary_conditions } = form.getFieldsValue(true);

    if (summary_conditions.length === 0) {
      message.error(`最少选择一项汇总条件`);
      return;
    }

    // 采购退货明细 、采购收货明细，需要按选择条件显示对应的单位
    if (['receive', 'return']?.includes(tabKey)) {
      const currentUnitType = form.getFieldValue('unit_type');
      tabList.find((v: any) => v?.code === 'unit')!.name = UnitType?.filter(
        (i: any) => i.value == currentUnitType,
      )[0]?.label;
    }
    if (
      form.getFieldValue('unit_type') === 'ORDER' &&
      (tabKey === 'storeItem' ||
        tabKey === 'supplierItem' ||
        tabKey === 'supplierStoreItem')
    ) {
      tabList.find((v: any) => v.code === 'unit')!.hidden = true;
    } else if (
      form.getFieldValue('unit_type') !== 'ORDER' &&
      (tabKey === 'storeItem' ||
        tabKey === 'supplierItem' ||
        tabKey === 'supplierStoreItem')
    ) {
      tabList.find((v: any) => v.code === 'unit')!.hidden = false;
    }
    const data = checkData(page_number);
    setIsLoading(true);
    let res = null;
    switch (tabKey) {
      case 'receive':
        res = await receiveOrderDetail({ ...data });
        break;
      case 'return':
        res = await returnOrderDetail({ ...data });
        break;
      case 'storeItem':
        res = await storeItemDetail({ ...data });
        break;
      case 'supplier':
        data.condition = form.getFieldsValue(true)?.condition;
        res = await supplierDetail({ ...data });
        break;
      case 'category':
        res = await storeCategoryDetail({ ...data });
        break;
      case 'supplierStore':
        res = await supplierStoreDetail({ ...data });
        break;
      case 'supplierItem':
        res = await supplierItemDetail({
          ...data,
          supplierMergerFlag: form.getFieldValue('supplier_ids') ? true : false,
        });
        break;
      case 'supplierStoreItem':
        res = await supplierStoreItemDetail({ ...data });
        break;
    }
    setIsLoading(false);
    if (res?.code === 0) {
      setRowData(res.data.content || []);
      //合计行
      const footDatas: any = res.data;
      const data: any = [
        {
          index: '合计',
          receive_basic_present_quantity: toFixed(
            footDatas.receive_basic_present_quantity,
            'QUANTITY',
          ),
          receive_present_quantity: toFixed(
            footDatas.receive_present_quantity,
            'QUANTITY',
          ),
          basic_present_quantity: toFixed(
            footDatas.basic_present_quantity,
            'QUANTITY',
          ),
          receive_basic_quantity: toFixed(
            footDatas.receive_basic_quantity,
            'QUANTITY',
          ),
          return_basic_quantity: toFixed(
            footDatas.return_basic_quantity,
            'QUANTITY',
          ),
          receive_present_money: toFixed(
            footDatas.receive_present_money,
            'MONEY',
          ),
          receive_no_tax_present_money: toFixed(
            footDatas.receive_no_tax_present_money,
            'MONEY',
          ),
          present_quantity: toFixed(footDatas.present_quantity, 'QUANTITY'),
          receive_quantity: toFixed(footDatas.receive_quantity, 'QUANTITY'),
          return_cost_money: hasAuth(['采购汇总明细/成本价', '查询'])
            ? toFixed(footDatas.return_cost_money, 'MONEY')
            : '****',
          return_no_tax_cost_money: hasAuth(['采购汇总明细/成本价', '查询'])
            ? toFixed(footDatas.return_no_tax_cost_money, 'MONEY')
            : '****',
          return_quantity: toFixed(footDatas.return_quantity, 'QUANTITY'),
          basic_quantity: toFixed(footDatas.basic_quantity, 'QUANTITY'),
          receive_money: toFixed(footDatas.receive_money, 'MONEY'),
          receive_no_tax_money: toFixed(
            footDatas.receive_no_tax_money,
            'MONEY',
          ),
          present_money: hasAuth(['采购汇总明细/采购价', '查询'])
            ? toFixed(footDatas.present_money, 'MONEY')
            : '****',
          no_tax_present_money: hasAuth(['采购汇总明细/采购价', '查询'])
            ? toFixed(footDatas.no_tax_present_money, 'MONEY')
            : '****',
          supplier_money: hasAuth(['采购汇总明细/采购价', '查询'])
            ? toFixed(footDatas.supplier_money, 'MONEY')
            : '****',
          return_money: toFixed(footDatas.return_money, 'MONEY'),
          return_no_tax_money: toFixed(footDatas.return_no_tax_money, 'MONEY'),
          quantity: toFixed(footDatas.quantity, 'QUANTITY'),
          money: hasAuth(['采购汇总明细/采购价', '查询'])
            ? toFixed(footDatas.money, 'MONEY')
            : '****',
          no_tax_money: hasAuth(['采购汇总明细/采购价', '查询'])
            ? toFixed(footDatas.no_tax_money, 'MONEY')
            : '****',
        },
      ];
      setFooterData(data);
      const paginData = {
        ...pagin,
        pageNum: page_number,
        total: res.data.total_elements,
      };
      setPagin(paginData);
      switch (tabKey) {
        case 'receive':
          setUseReceiveTableRowData(res.data.content || []);
          setUseReceiveTableFooterData(data);
          setUseReceiveTablePagin(paginData);
          break;
        case 'return':
          setUseReturnTableRowData(res.data.content || []);
          setUseReturnTableFooterData(data);
          setUseReturnTablePagin(paginData);
          break;
        case 'storeItem':
          setUseStoreItemTableRowData(res.data.content || []);
          setUseStoreItemTableFooterData(data);
          setUseStoreItemTablePagin(paginData);
          break;
        case 'supplier':
          setUseSupplierTableRowData(res.data.content || []);
          setUseSupplierTableFooterData(data);
          setUseSupplierTablePagin(paginData);
          break;
        case 'category':
          setUseCategoryTableRowData(res.data.content || []);
          setUseCategoryTableFooterData(data);
          setUseCategoryTablePagin(paginData);
          break;
        case 'supplierStore':
          setUseSupplierStoreTableRowData(res.data.content || []);
          setUseSupplierStoreTableFooterData(data);
          setUseSupplierStoreTablePagin(paginData);
          break;
        case 'supplierItem':
          setUseSupplierItemTableRowData(res.data.content || []);
          setUseSupplierItemTableFooterData(data);
          setUseSupplierItemTablePagin(paginData);
          break;
        case 'supplierStoreItem':
          setUseSupplierStoreItemTableRowData(res.data.content || []);
          setUseSupplierStoreItemTableFooterData(data);
          setUseSupplierStoreItemTablePagin(paginData);
          break;
      }
    }
  };

  //换页事件
  const pageChange = (pageNum: number, pageSize: number) => {
    setPagin({
      ...pagin,
      pageNum: pageNum,
      pageSize: pageSize,
    });
    getData(pageNum);
  };

  const getFilterList = (tableColumn: any) => {
    const list = [...tableColumn];
    const formValues = form.getFieldsValue(true);

    return list.map((i) => {
      const obj = TableEnumBySummary.find((j) => j.label === i.code);
      if (!obj) return i;
      if (i.code === 'administrative') {
        return {
          ...i,
          hidden:
            !formValues?.summary_conditions?.includes('PROVINCE') &&
            !formValues?.summary_conditions?.includes('CITY') &&
            !formValues?.summary_conditions?.includes('DISTRICT'),
        };
      } else if (i.code === 'cargo_owner_id') {
        return {
          ...i,
          hidden:
            !formValues?.summary_conditions?.includes('STORE') ||
            !enable_cargo_owner,
        };
      } else {
        return {
          ...i,
          hidden: formValues?.summary_conditions?.indexOf(obj.value) === -1,
        };
      }
    });
  };

  const changeKey = (key: string) => {
    setSortType({ order: '', code: '' });
    let tableArr: any = [];
    switch (key) {
      case 'receive':
        tableArr = receiveTableColumn;
        setRowData([...useReceiveTableRowData]);
        setFooterData([...useReceiveTableFooterData]);
        setPagin(useReceiveTablePagin);
        setUidUrl(
          'hxl.erp.purchasereport.purchasesummary.receiveorderdetail.page',
        );
        setOldArr(cloneDeep(tableArr));
        break;
      case 'return':
        tableArr = returnTableColumn;
        setRowData([...useReturnTableRowData]);
        setFooterData([...useReturnTableFooterData]);
        setPagin(useReturnTablePagin);
        setUidUrl(
          'hxl.erp.purchasereport.purchasesummary.returnorderdetail.page',
        );
        setOldArr(cloneDeep(returnTableColumn));
        break;
      case 'storeItem':
        tableArr = getFilterList(storeItemTableColumn);
        setRowData([...useStoreItemTableRowData]);
        setFooterData([...useStoreItemTableFooterData]);
        setPagin(useStoreItemTablePagin);
        setUidUrl('hxl.erp.purchasereport.purchasesummary.storeitem.page');
        setOldArr(cloneDeep(tableArr));
        break;
      case 'supplier':
        setRowData([...useSupplierTableRowData]);
        setFooterData([...useSupplierTableFooterData]);
        setPagin(useSupplierTablePagin);
        setUidUrl('hxl.erp.purchasereport.purchasesummary.supplier.page');
        const { condition } = form.getFieldsValue();
        tableArr = supplierTableColumn.map((item) => {
          if (item.code === 'month' && condition === 'MONTHLY') {
            return {
              ...item,
              hidden: false,
            };
          } else {
            return item;
          }
        });

        setOldArr(cloneDeep(tableArr));

        break;
      case 'category':
        tableArr = getFilterList(categoryTableColumn);
        setRowData([...useCategoryTableRowData]);
        setFooterData([...useCategoryTableFooterData]);
        setPagin(useCategoryTablePagin);
        setUidUrl('hxl.erp.purchasereport.purchasesummary.storecategory.page');
        setOldArr(cloneDeep(tableArr));
        break;
      case 'supplierStore':
        tableArr = getFilterList(supplierStoreTableColumn);
        setRowData([...useSupplierStoreTableRowData]);
        setFooterData([...useSupplierStoreTableFooterData]);
        setPagin(useSupplierStoreTablePagin);
        setUidUrl('hxl.erp.purchasereport.purchasesummary.supplierstore.page');
        setOldArr(cloneDeep(tableArr));
        break;
      case 'supplierItem':
        tableArr = supplierItemTableColumn;
        setRowData([...useSupplierItemTableRowData]);
        setFooterData([...useSupplierItemTableFooterData]);
        setPagin(useSupplierItemTablePagin);
        setUidUrl('hxl.erp.purchasereport.purchasesummary.supplieritem.page');
        setOldArr(cloneDeep(tableArr));
        break;
      case 'supplierStoreItem':
        tableArr = getFilterList(supplierStoreItemTableColumn);
        setRowData([...useSupplierStoreItemTableRowData]);
        setFooterData([...useSupplierStoreItemTableFooterData]);
        setPagin(useSupplierStoreItemTablePagin);
        setUidUrl(
          'hxl.erp.purchasereport.purchasesummary.supplierstoreitem.page',
        );
        setOldArr(cloneDeep(tableArr));

        break;
    }
    const item = tableArr?.find((v: any) => v.code === 'org_name');
    if (item) {
      item.hidden = !enable_organization;
    }
    if (
      form.getFieldValue('unit_type') === 'ORDER' &&
      (key === 'storeItem' ||
        key === 'supplierItem' ||
        key === 'supplierStoreItem')
    ) {
      tableArr.find((v: any) => v.code === 'unit')!.hidden = true;
    } else if (
      form.getFieldValue('unit_type') !== 'ORDER' &&
      (key === 'storeItem' ||
        key === 'supplierItem' ||
        key === 'supplierStoreItem')
    ) {
      tableArr.find((v: any) => v.code === 'unit')!.hidden = false;
    }
    if (tableArr.find((v: any) => v.name === '门店')) {
      tableArr.find((v: any) => v.name === '门店')!.hidden = [
        'supplier',
        'supplierItem',
      ].includes(key);
    }

    setTabList([...tableArr]);
    // if (key === 'category') {
    if (
      [
        'receive',
        'return',
        'storeItem',
        'category',
        'supplierItem',
        'supplierStoreItem',
      ]?.includes(key)
    ) {
      formLists.find((v) => v.label === '类别等级')!.hidden = false;
    } else {
      formLists.find((v) => v.label === '类别等级')!.hidden = true;
    }
    if (['receive', 'return', 'supplier', 'supplierItem']?.includes(key)) {
      formLists.find((v) => v.label === '货主')!.hidden = false;
    } else {
      formLists.find((v) => v.label === '货主')!.hidden = true;
    }

    // 切换tab对表单项隐藏

    if (filterTab.includes(key)) {
      formLists.find((v) =>
        ['summary_conditions', 'business_area_id', 'city_codes'].includes(
          v.name,
        ),
      )!.hidden = false;

      // 切换汇总条件对表单项隐藏
      const summary_conditions = form.getFieldValue('summary_conditions');
      formLists.find((i) => i.name === 'org_ids')!.hidden =
        !summary_conditions?.includes('STORE');
      formLists.find((i) => i.name === 'store_ids')!.hidden =
        !summary_conditions?.includes('STORE');

      formLists.find((i) => i.name === 'business_area_id')!.hidden =
        !summary_conditions?.includes('BUSINESS_AREA');

      formLists.find((i) => i.name === 'city_codes')!.hidden =
        !summary_conditions?.includes('PROVINCE') &&
        !summary_conditions?.includes('CITY') &&
        !summary_conditions?.includes('DISTRICT');
      // 货主选择展示---【商品、类别、供应商-门店、供应商-门店-商品】4个tab在汇总条件包含store时显示
      formLists.find((i) => i.name === 'cargo_owner_ids')!.hidden = !(
        enable_cargo_owner && summary_conditions?.includes('STORE')
      );
    } else {
      // 其他几个tab展示汇总条件
      formLists.find((v) =>
        ['summary_conditions', 'business_area_id', 'city_codes'].includes(
          v.name,
        ),
      )!.hidden = true;

      // formLists.find((i) => i.name === 'org_ids')!.hidden =  true

      formLists.find((i) => i.name === 'store_ids')!.hidden = false;

      formLists.find((i) => i.name === 'business_area_id')!.hidden = true;

      formLists.find((i) => i.name === 'city_codes')!.hidden = true;
    }
    formLists.find((v) => v.label === '查询条件')!.hidden = key !== 'supplier';

    setSearchFormLists([...formLists]);
    // else {
    //   clearHouseSeting()
    // }
  };

  const exportItem = async () => {
    const data = checkData(pagin.pageNum);
    data.responseType = 'blob';
    setIsLoading(true);
    let res = null;
    const download = new Download();
    switch (tabKey) {
      case 'receive':
        res = await exportPage(
          '/erp/hxl.erp.purchasereport.purchasesummary.receiveorderdetail.export',
          data,
          { responseType: 'blob' },
        );
        download.filename = '采购汇总明细-采购收货明细导出.xlsx';
        break;
      case 'return':
        res = await exportPage(
          '/erp/hxl.erp.purchasereport.purchasesummary.returnorderdetail.export',
          data,
          { responseType: 'blob' },
        );
        download.filename = '采购汇总明细-采购退货明细导出.xlsx';
        break;
      case 'storeItem':
        res = await exportPage(
          '/erp/hxl.erp.purchasereport.purchasesummary.storeitem.export',
          data,
          { responseType: 'blob' },
        );
        download.filename = '采购汇总明细-采购商品汇总导出.xlsx';
        break;
      case 'supplier':
        data.condition = form.getFieldsValue(true)?.condition;
        res = await exportPage(
          '/erp/hxl.erp.purchasereport.purchasesummary.supplier.export',
          data,
          { responseType: 'blob' },
        );
        download.filename = '采购汇总明细-采购供应商汇总导出.xlsx';
        break;
      case 'category':
        res = await exportPage(
          '/erp/hxl.erp.purchasereport.purchasesummary.storecategory.export',
          data,
          { responseType: 'blob' },
        );
        download.filename = '采购汇总明细-采购类别汇总导出.xlsx';
        break;
      case 'supplierStore':
        res = await exportPage(
          '/erp/hxl.erp.purchasereport.purchasesummary.supplierstore.export',
          data,
          { responseType: 'blob' },
        );
        download.filename = '采购汇总明细-供应商-门店汇总导出.xlsx';
        break;
      case 'supplierItem':
        res = await exportPage(
          '/erp/hxl.erp.purchasereport.purchasesummary.supplieritem.export',
          {
            ...data,
            supplierMergerFlag: form.getFieldValue('supplier_ids')
              ? true
              : false,
          },
          { responseType: 'blob' },
        );
        download.filename = '采购汇总明细-供应商-商品汇总导出.xlsx';
        break;
      case 'supplierStoreItem':
        res = await exportPage(
          '/erp/hxl.erp.purchasereport.purchasesummary.supplierstoreitem.export',
          data,
          { responseType: 'blob' },
        );
        download.filename = '采购汇总明细-供应商-门店-商品汇总导出.xlsx';
        break;
    }
    download.xlsx(res.data);
    setIsLoading(false);
  };

  /**
   * @description: 查询业财核算分类枚举
   */
  const getFinanceCode = async () => {
    const res = await queryFinanceCode({});
    if (res.code === 0) {
      const formItem = (formLists || []).find(
        (v: any) => v.label === '业财核算分类',
      );
      formItem!.options = (res?.data || []).map((item: any) => {
        return { label: item.category_name, value: item.code };
      });
      setSearchFormLists([...formLists]);
    }
  };

  const initFormState = () => {
    form.setFieldsValue({
      timeClass: 'audit_date',
      time_desc: 0,
      compactDatePicker: [dayjs(), dayjs()],
      condition: 'SUMMARY',
      summary_conditions: ['STORE'],
    });
  };

  useEffect(() => {
    // 查询业财核算分类枚举
    getFinanceCode();
    initFormState();
    setUidUrl('hxl.erp.purchasereport.purchasesummary.receiveorderdetail.page');
  }, []);

  useEffect(() => {
    getData(1);
  }, [sortType]);

  //处理查询参数

  const tabKeyChange = (e: string) => {
    setTabKey(e);
    changeKey(e);
  };

  const cloneArr = JSON.parse(JSON.stringify(tabList));

  return (
    <XlbPageContainer>
      <div className={'button_box row-flex'} style={{ padding: '0 16px' }}>
        <div style={{ width: '90%' }} className="row-flex">
          <XlbButton.Group>
            {hasAuth(['采购汇总明细', '查询']) ? (
              <XlbButton
                label="查询"
                type="primary"
                loading={isLoading}
                onClick={() => {
                  setPagin({
                    ...pagin,
                    pageNum: 1,
                  });
                  getData(1);
                }}
                icon={<XlbIcon name="sousuo" color="currentColor" size={16} />}
              />
            ) : null}
            {hasAuth(['采购汇总明细', '导出']) ? (
              <XlbButton
                label="导出"
                type="primary"
                loading={isLoading}
                disabled={isLoading || !rowData?.length}
                onClick={(e) => exportItem()}
                icon={<XlbIcon size={16} name="daochu" />}
              />
            ) : null}
          </XlbButton.Group>
        </div>

        <div
          style={{
            width: '10%',
            height: '28px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'flex-end',
            columnGap: 8,
          }}
        >
          <XlbTooltip title={isFold ? '收起' : '展开'}>
            <XlbIcon
              data-type={'顶层展开收起'}
              onClick={() => setIsFold(!isFold)}
              name="shouqi"
              size={20}
              className={classnames('xlb-columns-main-btn', {
                'xlb-columns-fold': isFold,
                'xlb-columns-expand-btn-dev': true,
              })}
            />
          </XlbTooltip>
          <XlbColumns
            isFold={isFold}
            isFoldChange={setIsFold}
            url={uidUrl}
            originColumns={oldArr}
            value={tabList}
            onChange={setTabList}
            name={uidUrl + '-name'}
          />
        </div>
      </div>
      <div
        style={{
          display: !isFold ? 'block' : 'none',
        }}
      >
        <SearchForm>
          <XlbForm
            isHideDate
            formList={formLists}
            form={form}
            onValuesChange={onValuesChange}
            getFormRecord={refresh}
            // initialValues={{
            //   timeClass: 'audit_date',
            //   store_ids: [LStorage.get('userInfo').store_id],
            //   time_desc: 0,
            //   compactDatePicker: [dayjs(), dayjs()],
            //   condition: 'SUMMARY',
            //   summary_conditions: ['STORE'],
            // }}
          />
        </SearchForm>
      </div>

      <div>
        <XlbTabs
          style={{ marginLeft: '16px' }}
          activeKey={tabKey}
          onTabClick={(e) => {
            tabKeyChange(e);
          }}
          items={[
            {
              label: '采购收货明细',
              key: 'receive',
            },
            {
              label: '采购退货明细',
              key: 'return',
            },
            {
              label: '采购商品汇总',
              key: 'storeItem',
            },
            {
              label: '采购供应商汇总',
              key: 'supplier',
            },
            {
              label: '采购类别汇总',
              key: 'category',
            },
            {
              label: '供应商-门店汇总',
              key: 'supplierStore',
            },
            {
              label: '供应商-商品汇总',
              key: 'supplierItem',
            },
            {
              label: '供应商-门店-商品汇总',
              key: 'supplierStoreItem',
            },
          ]}
        ></XlbTabs>
      </div>
      <XlbTable
        style={{ height: 'calc(100% - 176px)', marginLeft: '16px' }}
        columns={cloneArr.map((v) => tableRender(v))}
        isLoading={isLoading}
        pagin={pagin}
        pageNum={pagin?.pageNum}
        pageSize={pagin.pageSize}
        total={pagin.total}
        dataSource={rowData}
        isFold={isFold}
        onPaginChange={(page: number, pageSize: number) => {
          pageChange(page, pageSize);
        }}
        onChangeSorts={(e) => {
          setSortType(e);
        }}
        // onPaginChange={tabsKey === 'basket' ? undefined : () => pageChange(pagin)}
        footerDataSource={footerData}
      />
    </XlbPageContainer>
  );
};

export default PurchaseLatestPrice;

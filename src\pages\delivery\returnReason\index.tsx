import { type FC } from 'react'
import { tableColumn } from './data'
import { hasAuth } from '@/utils/kit'
import { ErpFieldKeyMap } from '@/data/common/fieldListConfig'
import { XlbProPageContainer } from '@xlb/components'

const ReturnReasonIndex: FC<{ title: string }> = () => {
  const PRESET_NAMES = Object.freeze(['统配售后'])
  return (
    <XlbProPageContainer // 查询
      searchFieldProps={{
        formList: [{ id: 'keyword', name: 'keyword', label: '关键字' }],
        initialValues: {
          type: 'RETURN_REQUEST'
        }
      }}
      tableFieldProps={{
        url: '/erp/hxl.erp.reason.find',
        tableColumn: tableColumn,
        selectMode: 'single',
        keepDataSource: false,
        showColumnsSetting: false,
        immediatePost: true,
        afterPost: (list) =>
          list.map((item: any) => ({
            ...item,
            isPreset: PRESET_NAMES.includes(item.name)
          }))
      }}
      deleteFieldProps={{
        disabled: (selectList) => selectList?.some((item: any) => item.isPreset),
        name: '删除',
        showField: 'name',
        url: hasAuth(['退货申请原因', '删除']) ? '/erp/hxl.erp.reason.delete' : ''
      }}
      addFieldProps={{
        name: '新增',
        url: hasAuth(['退货申请原因', '编辑']) ? '/erp/hxl.erp.reason.save' : '',
        beforePost: (data) => {
          return {
            ...data,
            type: 'RETURN_REQUEST'
          }
        }
      }}
      details={{
        mode: 'modal',
        isCancel: true,
        width: 450,
        title: (obj) => {
          return <div>{obj?.id ? '编辑' : '新增'}</div>
        },
        hiddenSaveBtn: true,
        primaryKey: 'id',
        formList: [
          {
            componentType: 'form',
            fieldProps: {
              formList: [
                {
                  id: ErpFieldKeyMap.otherIncomeExpensesName,
                  itemSpan: 24,
                  label: '退货申请原因',
                  rules: [{ required: true, message: '退货申请原因不能为空' }],
                  dependencies: ['isPreset'],
                  disabled: (dataItem) => dataItem?.isPreset,
                  fieldProps: { maxLength: 20, width: '100%' }

                  // width:180
                },
                {
                  id: ErpFieldKeyMap.erpIsDefault,
                  label: '附件必传',
                  itemSpan: 24,
                  fieldProps: {
                    options: [{ label: '附件必传', value: 'file_required' }]
                  }
                }
              ]
            }
          }
        ],
        updateFieldProps: {
          url: '/erp/hxl.erp.reason.update'
        }
      }}
    />
  )
}

export default ReturnReasonIndex

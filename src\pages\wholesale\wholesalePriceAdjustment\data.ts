import { SearchFormType } from '@xlb/components';
import { XlbFetch as ErpRequest } from '@xlb/utils';
//单据状态
export const stateEmail = [
  { label: '制单', value: 'INIT', type: 'info' },
  { label: '审核', value: 'AUDIT', type: 'warning' },
  { label: '通过', value: 'PASS', type: 'success' },
  { label: '拒绝', value: 'DENY', type: 'danger' },
  { label: '已失效', value: 'INVALID', type: 'danger' },
  { label: '已生效', value: 'EFFECT', type: 'success' },
];

// 时间类型
export const timeType = [
  { label: '制单日期', value: 'CREATE' },
  { label: '审核日期', value: 'AUDIT' },
  { label: '处理日期', value: 'HANDLE' },
  { label: '失效日期', value: 'INVALID' },
  { label: '生效日期', value: 'EFFECT' },
];

// 批发类型
export const wholesaleType = [
  { label: ' ', value: 3 },
  { label: '按比例', value: 0 },
  { label: '按金额', value: 1 },
  { label: '固定金额', value: 2 },
];

//商品类型
export const goodsType = [
  {
    label: '主规格商品',
    value: 'MAINSPEC',
  },
  {
    label: '多规格商品',
    value: 'MULTIPLESPEC',
  },
  { label: '标准商品', value: 'STANDARD' },
  { label: '组合商品', value: 'COMBINATION' },
  { label: '成分商品', value: 'COMPONENT' },
  { label: '制单组合', value: 'MAKEBILL' },
  { label: '分级商品', value: 'GRADING' },
];
export const formList: SearchFormType[] = [
  {
    width: 388,
    type: 'compactDatePicker',
    label: '日期选择',
    name: 'compactDatePicker',
    allowClear: false,
    // disabled: true,
    // format: "YYYY-MM-DD HH:mm:ss",
    // @ts-ignore
  },
  {
    type: 'select',
    label: '单据状态',
    name: 'state',
    options: stateEmail,
  },
  {
    type: 'input',
    label: '单据号',
    name: 'fid',
    tooltip: '单据号不受其他查询条件限制',
  },
  {
    type: 'select',
    label: '时间类型',
    name: 'date_type',
    options: timeType,
    allowClear: false,
  },
  {
    type: 'inputDialog',
    label: '组织',
    name: 'org_ids',
    hidden: true,
    treeModalConfig: {
      title: '选择组织',
      url: '/erp-mdm/hxl.erp.org.tree',
      dataType: 'lists',
      checkable: true, // 是否多选
      primaryKey: 'id',
    },
  },
  {
    label: '应用门店',
    name: 'store_ids',
    type: 'inputDialog',
    allowClear: true,
    fieldNames: {
      idKey: 'id',
      nameKey: 'store_name',
    } as any,
    dialogParams: {
      type: 'store',
      dataType: 'lists',
      isMultiple: true,
      resetForm: true,
      data: {
        // enable_organization: false
        filter_org_levels: [1, 3],
      },
    },
  },
  {
    type: 'inputDialog',
    label: '商品档案',
    name: 'item_ids',
    dialogParams: {
      type: 'goods',
      dataType: 'lists',
      isLeftColumn: true,
      isMultiple: true,
      primaryKey: 'id',
      data: {
        enabled: true,
      },
    },
  },
  {
    type: 'input',
    label: '制单人',
    name: 'create_by',
  },
  {
    type: 'input',
    label: '审核人',
    name: 'audit_by',
  },
];
export default {
  /**
   * @name 批量删除
   */
  deleteItem: async (data: any) =>
    await ErpRequest.post('/erp/hxl.erp.wholesaleadjustorder.batchdelete', data),
  /**
   * @name 复制
   */
  copyItem: async (data: any) =>
    await ErpRequest.post('/erp/hxl.erp.wholesaleadjustorder.copy', data),
  /**
   * @name 导出
   */
  exportItem: async (data: any) =>
    await ErpRequest.post('/erp/hxl.erp.wholesaleadjustorder.export', data),
  /**
   * @name 详情
   */
  readItem: async (data: any) =>
    await ErpRequest.post('/erp/hxl.erp.wholesaleadjustorder.read', data),
  /**
   * @name 保存
   */
  updateItem: async (data: any) =>
    await ErpRequest.post('/erp/hxl.erp.wholesaleadjustorder.update', data),
  /**
   * @name 新增
   */
  saveItem: async (data: any) =>
    await ErpRequest.post('/erp/hxl.erp.wholesaleadjustorder.save', data),
  /**
   * @name 审核
   */
  auditItem: async (data: any) =>
    await ErpRequest.post('/erp/hxl.erp.wholesaleadjustorder.audit', data),
  /**
   * @name 反审核
   */
  unAuditItem: async (data: any) =>
    await ErpRequest.post('/erp/hxl.erp.wholesaleadjustorder.reaudit', data),
  /**
   * @name 通过
   */
  passItem: async (data: any) =>
    await ErpRequest.post('/erp/hxl.erp.wholesaleadjustorder.pass', data),
  /**
   * @name 拒绝
   */
  denyItem: async (data: any) =>
    await ErpRequest.post('/erp/hxl.erp.wholesaleadjustorder.deny', data),
  /**
   * @name 作废
   */
  invalidateItem: async (data: any) =>
    await ErpRequest.post('/erp/hxl.erp.wholesaleadjustorder.invalid', data),
};

export const GoodsBrandKeyMap = {
  brandLevel: 'brandLevel',
};
export const goodsBrandConfig: any[] = [
  // 品牌等级
  {
    tag: 'ERP',
    label: '品牌等级',
    id: 'brandLevel',
    name: 'brand_level',
    componentType: 'select',
    options: [],
    request: async (formValues: any, anybaseURL: any, globalFetch: any) => {
      const res = await globalFetch.post(
        anybaseURL + '/erp/hxl.erp.brandlevel.read',
        {},
      );
      if (res?.code === 0) {
        return res.data?.map((item: any) => {
          return {
            label: item?.label,
            value: item?.code,
          };
        });
      }

      return [];
    },
  },
];
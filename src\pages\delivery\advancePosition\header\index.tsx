import { FC, useState, useRef } from 'react'
import {  message } from 'antd'
import { ErpFieldKeyMap } from '@/data/common/fieldListConfig'
import Api from './server'
import {
  XlbBasicForm,
  XlbButton,
  XlbIcon,
  XlbImportModal,
  XlbTipsModal,
  XlbProPageContainer,
  XlbProPageModal,
  XlbProPageModalRef,
  XlbProPageContainerRef
} from '@xlb/components'
import { LStorage } from '@/utils/storage'
import { hasAuth } from '@/utils'
// import { useKeepAliveRefresh } from '@/hooks'
import { columnWidthEnum } from '@/data/common/constant'
import IconFont from '@xlb/components/dist/components/icon'
import { useBaseParams } from '@/hooks/useBaseParams'
import { XlbPageContainerRef } from '@xlb/components/dist/lowcodes/XlbPageContainer'
import Item from '../item'
import { wujieBus } from '@/wujie/utils'
const Index: FC = () => {
  // const { go } = useKeepAliveRefresh()
  const { enable_organization } = useBaseParams((state) => state)
  const [form] = XlbBasicForm.useForm()
  const [isLoading, setisLoading] = useState<boolean>(false)
  const refreshRef = useRef<(page?: number) => void>(() => {})
  const pageModalRef = useRef<XlbProPageModalRef>(null);
  const pageConatainerRef = useRef<XlbProPageContainerRef>(null);
  const [record, setRecord] = useState<any>({})
  const importData = async () => {
    const res = await XlbImportModal({
      importUrl: `${process.env.BASE_URL}/erp/hxl.erp.frontwarhouserelation.import`,
      templateUrl: `${process.env.BASE_URL}/erp/hxl.erp.frontwarhouserelation.template.download`,
      templateName: '前置仓导入模板'
    })
    if (res.code !== 0) return
    refreshRef?.current()
  }
  //导出
  const handleExport = async (requestForm: any | undefined, e) => {
    const res = await Api.exportFid({
      ...requestForm
    })
    if (res.code == 0) {
      wujieBus?.$emit('xlb_erp-event', { code: 'downloadEnd', target: e });
      message.success('导出受理成功，请前往下载中心查看')
    }
  }
  const deleteConfirm = async (ids: any[]) => {
    await XlbTipsModal({
      tips: `是否删除选中${ids.length}条记录？`,
      isCancel: true,
      onOkBeforeFunction: async () => {
        const res = await Api.deleteFid({ fids: ids })
        if (res.code == 0) {
          message.success('删除成功')
          refreshRef?.current()
          return true
        }
        return false
      }
    })
  }
  return (
    <>
    <XlbProPageModal
        ref={pageModalRef}
        Content={({ onClose }) => {
          return (
            <div>
              <Item
                onBack={(back: boolean) => {
                  if (back) {
                    pageConatainerRef?.current?.pageContainerRef.current?.fetchData?.();
                  }
                  pageModalRef.current?.setOpen(false);
                }}
                record={record}
              ></Item>
            </div>
          );
        }}
      >
        <div className="xlb-table-clickBtn"></div>
      </XlbProPageModal>
      <XlbProPageContainer
        ref={pageConatainerRef}
        searchFieldProps={{
          formList: [
            {
              label: '组织',
              name: 'org_ids',
              id: ErpFieldKeyMap?.erpOrgIds,
              dependencies: ['enable_organization'],
              hidden: () => {
                return !enable_organization
              }
            },
            {
              label: '前置仓',
              name: 'front_warehouse_ids',
              id: ErpFieldKeyMap?.erpCenterStoreIdsMultipleEnable
            },
            {
              label: '区域仓',
              name: 'back_warehouse_ids',
              id: ErpFieldKeyMap?.erpCenterStoreIdsMultipleEnable
            }
          ],
          initialValues: {
            company_id: LStorage.get('userInfo')?.company_id
          }
        }}
        tableFieldProps={{
          url: '/erp/hxl.erp.frontwarhouserelation.page',
          tableColumn:[
            {
              name: '序号',
              code: '_index',
              width: columnWidthEnum.INDEX,
              lock: true,
              align: 'center'
            },
            {
              name: '前置仓组织',
              code: 'front_store_org_name',
              hidden: !enable_organization,
              features: { sortable: true },
              width: 140
            },
            {
              name: '前置仓代码',
              code: 'front_store_code',
              width: 170,
              features: { sortable: true },
              render: (text, record, obj) => (
                <span
                  className="link"
                  onClick={(e) => {
                    setRecord(record)
                    pageModalRef.current?.setOpen(true);
                  }}
                >
                  {text}
                </span>
              )
            },
            {
              name: '前置仓名称',
              code: 'front_store_name',
              width: columnWidthEnum.STORE_NAME,
              features: { sortable: true }
            },
            {
              name: '区域仓组织',
              code: 'back_store_org_name',
              features: { sortable: true },
              hidden: !enable_organization,
              width: 140
            },
            {
              name: '区域仓代码',
              code: 'back_store_code',
              width: columnWidthEnum.STORE_NAME,
              features: { sortable: true }
            },
            {
              name: '区域仓名称',
              code: 'back_store_name',
              features: { sortable: true },
              width: 110
            },
            {
              name: '商品数',
              code: 'item_count',
              width: 140,
              features: { sortable: true }
            },
            {
              name: '更新人',
              code: 'update_by',
              features: { sortable: true },
              width: 140
            },
            {
              name: '更新时间',
              code: 'update_time',
              width: 140,
              features: { sortable: true, format: 'TIME' }
            }
          ],
          selectMode: 'multiple',
          keepDataSource: false,
          showColumnsSetting: true,
          primaryKey: 'fid',
          immediatePost: true
        }}
        extra={(context) => {
          const { selectRowKeys, loading, requestForm, selectRow, fetchData } = context
          refreshRef.current = fetchData
          return (
            <XlbButton.Group>
              {hasAuth(['前置仓配置', '新增']) && (
                <XlbButton
                  type="primary"
                  label="新增"
                  onClick={() => {
                    setRecord({ fid: 1 });
                    pageModalRef.current?.setOpen(true);
                  }}
                  icon={<IconFont name="jia" color="currentColor" size={16} />}
                />
              )}
              {hasAuth(['前置仓配置', '删除']) && (
                <XlbButton
                  type="primary"
                  label="删除"
                  disabled={selectRowKeys?.length === 0}
                  onClick={() => deleteConfirm(selectRowKeys)}
                  loading={isLoading}
                />
              )}
              {hasAuth(['前置仓配置', '导入']) && (
                <XlbButton
                  type="primary"
                  label="导入"
                  onClick={importData}
                  icon={<XlbIcon name="daoru" />}
                  loading={isLoading}
                />
              )}
              {hasAuth(['前置仓配置', '导出']) && (
                <XlbButton
                  type="primary"
                  label="导出"
                  onClick={(e) => handleExport(requestForm, e)}
                  icon={<XlbIcon name="daochu" />}
                />
              )}
            </XlbButton.Group>
          )
        }}
      ></XlbProPageContainer>
    </>
  )
}
export default Index

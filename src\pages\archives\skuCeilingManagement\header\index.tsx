import { hasAuth } from '@/utils/kit';
import { LStorage } from '@/utils/storage';
import { wujieBus } from '@/wujie/utils';
import {
  XlbBasicData,
  XlbBasicForm,
  XlbButton,
  XlbDropdownButton,
  XlbForm,
  XlbIcon,
  XlbImportModal,
  XlbInputNumber,
  XlbLayout,
  XlbPageContainer,
  XlbTipsModal,
  XlbTree,
} from '@xlb/components';
import { TreeData } from '@xlb/components/dist/components/XlbTree/index.component';
import { message } from 'antd';
import { useEffect, useRef, useState } from 'react';
import { formList } from '../data';
import Api from '../server';
import RecoedModal from './../components/RecordModal';
const { Table, ToolBtn, SearchForm } = XlbPageContainer;
const Index = () => {
  const handleSave = async (record: any, index: number) => {
    console.log(record, index);
    const check_data = {
      limit_num: 'SKU上限',
      collective_purchase_limit_num: '集采品SKU上限',
      ground_purchase_limit_num: '地采品SKU上限',
    };
    const warn_info = [];
    if (record.limit_num !== null && record.limit_num < record.down_limit_num) {
      warn_info.push('SKU上限不能小于最小值' + record.down_limit_num);
    }
    if (
      record.limit_num !== null &&
      record.up_limit_num !== null &&
      record.limit_num > record.up_limit_num
    ) {
      warn_info.push('SKU上限不能大于最大值' + record.up_limit_num);
    }

    if (
      record.collective_purchase_limit_num !== null &&
      record.collective_purchase_limit_num <
        record.collective_purchase_down_limit_num
    ) {
      warn_info.push(
        '集采品SKU上限不能小于最小值' +
          record.collective_purchase_down_limit_num,
      );
    }
    if (
      record.collective_purchase_limit_num !== null &&
      record.collective_purchase_up_limit_num !== null &&
      record.collective_purchase_limit_num >
        record.collective_purchase_up_limit_num
    ) {
      warn_info.push(
        '集采品SKU上限不能大于最大值' + record.collective_purchase_up_limit_num,
      );
    }
    if (
      record.ground_purchase_limit_num !== null &&
      record.ground_purchase_limit_num < record.ground_purchase_down_limit_num
    ) {
      warn_info.push(
        '地采品SKU上限不能小于最小值' + record.ground_purchase_down_limit_num,
      );
    }
    if (
      record.ground_purchase_limit_num !== null &&
      record.ground_purchase_up_limit_num !== null &&
      record.ground_purchase_limit_num > record.ground_purchase_up_limit_num
    ) {
      warn_info.push(
        '地采品SKU上限不能大于最大值' + record.ground_purchase_up_limit_num,
      );
    }
    console.log(warn_info);
    if (warn_info.length) {
      XlbTipsModal({
        tips: warn_info.map((_) => <div key={_}>{_}</div>),
      });
    } else {
      const change_info = [];
      Object.keys(check_data).forEach((_) => {
        if (record[_] === record[_ + '_copy']) {
          change_info.push(_);
        }
      });

      if (change_info.length === Object.keys(check_data).length) {
        // message.warning('未修改数据，不需要更新');
        return;
      }

      const result = await Api.update(record);
      if (result.code === 0) {
        message.success('更新成功');
        getData();
      } else {
        getData();
      }
    }
    if (record['limit_num'] === '' && record['limit_num_copy'] === '') {
      return;
    }
    // Object.keys(check_data);
  };
  const updateData = async (record: any, type = '') => {
    if (record[type] === '' && record[type + '_copy'] === '') {
      return;
    }
    if (record[type] === null && record[type + '_copy'] === null) {
      return;
    }
    if (record[type] === undefined && record[type + '_copy'] === undefined) {
      return;
    }
    if (record[type] === '' || record[type] === null) {
      const result = await Api.update(record);
      if (result.code === 0) {
        message.success('更新成功');
        getData();
      }
    }
    // const regex = /^(0|[1-9]\d*)$/;
    // if (!regex.test(record[type])) {
    //   message.warning('请输入数字');
    //   return;
    // }
    if (record[type] === record[type + '_copy']) {
      record._click = false;
      record._edit = false;
      return;
    }
    const values: any[] = Object.values(record);
    Object.keys(record).forEach((_) => {
      if (isNaN(record[_])) {
        record[_] = undefined;
      }
    });
    if (values.includes(NaN)) {
      return;
    }
    const res = await Api.update(record);
    if (res.code === 0) {
      message.success('更新成功');
      getData();
      // record.update_by = LStorage.get('userInfo').account;
      // record.update_time = dayjs().format('YYYY-MM-DD hh:mm:ss');
    }
  };
  const columns = [
    {
      name: '序号',
      code: '_index',
      width: 50,
      align: 'center',
      lock: 'left',
    },
    {
      name: '商品一级分类',
      code: 'level_one_category_name',
      width: 160,
      v: '1',
      lock: 'left',
      features: { sortable: true, copy: true },
    },
    {
      name: 'SKU上限',
      code: 'limit_num',
      width: 160,
      render(text, record) {
        return record._click && hasAuth(['SKU上限管理', '编辑']) ? (
          <XlbInputNumber
            // max={record.up_limit_num || **********}
            // min={record.down_limit_num || 0}
            precision={0}
            onMouseEnter={(e) => {
              e.preventDefault();
              e.stopPropagation();
              return false;
            }}
            onBlur={(e) => {
              if (record.down_limit_num && e.target.value) {
                if (e.target.value * 1 < record.down_limit_num) {
                  message.warning('不能小于最小值' + record.down_limit_num);
                  record.limit_num = record.limit_num_copy;
                  return;
                }
              }

              if (record.up_limit_num && e.target.value) {
                if (e.target.value * 1 > record.up_limit_num) {
                  message.warning('不能大于最大值' + record.up_limit_num);
                  record.limit_num = record.limit_num_copy;
                  return;
                }
              }
              console.log(1122222222211);

              // record.limit_num = e.target.value * 1;
              updateData(record, 'limit_num');
            }}
            onChange={(e) => (record.limit_num = e)}
            defaultValue={text}
          ></XlbInputNumber>
        ) : (
          text
        );
      },
      features: { sortable: true },
    },
    {
      name: '已存在SKU',
      code: 'exist_sku_num',
      width: 160,
      features: { sortable: true },
    },
    {
      name: '集采品SKU上限',
      code: 'collective_purchase_limit_num',
      width: 160,
      features: { sortable: true },
      render(text, record) {
        return record._click && hasAuth(['SKU上限管理', '编辑']) ? (
          <XlbInputNumber
            // max={record.collective_purchase_up_limit_num || **********}
            // min={record.collective_purchase_down_limit_num || 0}
            precision={0}
            onBlur={(e) => {
              console.log(e);
              if (record.collective_purchase_down_limit_num && e.target.value) {
                if (
                  e.target.value * 1 <
                  record.collective_purchase_down_limit_num
                ) {
                  message.warning(
                    '不能小于最小值' +
                      record.collective_purchase_down_limit_num,
                  );
                  record.collective_purchase_limit_num =
                    record.collective_purchase_limit_num_copy;
                  return;
                }
              }

              if (record.collective_purchase_up_limit_num && e.target.value) {
                if (
                  e.target.value * 1 >
                  record.collective_purchase_up_limit_num
                ) {
                  message.warning(
                    '不能大于最大值' + record.collective_purchase_up_limit_num,
                  );
                  record.collective_purchase_limit_num =
                    record.collective_purchase_limit_num_copy;
                  return;
                }
              }
              // record.collective_purchase_limit_num = e.target.value * 1;
              updateData(record, 'collective_purchase_limit_num');
            }}
            onChange={(e) => (record.collective_purchase_limit_num = e)}
            defaultValue={text}
          ></XlbInputNumber>
        ) : (
          text
        );
      },
    },
    {
      name: '集采品已存在SKU',
      code: 'collective_purchase_exist_sku_num',
      width: 160,
      features: { sortable: true },
    },

    // {
    //   name: '集售品SKU上限',
    //   code: 'collective_sale_limit_num',
    //   width: 160,
    //   features: { sortable: true },
    // },
    // {
    //   name: '集售品已存在SKU',
    //   code: 'collective_sale_exist_sku_num',
    //   width: 160,
    //   features: { sortable: true },
    // },
    {
      name: '地采品SKU上限',
      code: 'ground_purchase_limit_num',
      width: 160,
      features: { sortable: true },
      render(text, record) {
        return record._click && hasAuth(['SKU上限管理', '编辑']) ? (
          <XlbInputNumber
            // max={record.ground_purchase_up_limit_num || **********}
            // min={record.ground_purchase_down_limit_num || 0}
            precision={0}
            onBlur={(e) => {
              if (record.ground_purchase_down_limit_num && e.target.value) {
                if (
                  e.target.value * 1 <
                  record.ground_purchase_down_limit_num
                ) {
                  message.warning(
                    '不能小于最小值' + record.ground_purchase_down_limit_num,
                  );
                  record.ground_purchase_limit_num =
                    record.ground_purchase_limit_num_copy;
                  return;
                }
              }

              if (record.ground_purchase_up_limit_num && e.target.value) {
                if (e.target.value * 1 > record.ground_purchase_up_limit_num) {
                  message.warning(
                    '不能大于最大值' + record.ground_purchase_up_limit_num,
                  );
                  record.ground_purchase_limit_num =
                    record.ground_purchase_limit_num_copy;
                  return;
                }
              }
              // record.ground_purchase_limit_num = e.target.value * 1;
              updateData(record, 'ground_purchase_limit_num');
            }}
            onChange={(e) => (record.ground_purchase_limit_num = e)}
            defaultValue={text}
          ></XlbInputNumber>
        ) : (
          text
        );
      },
    },
    {
      name: '地采品已存在SKU',
      code: 'ground_purchase_exist_sku_num',
      width: 160,
      features: { sortable: true },
    },
    {
      name: '最后修改人',
      code: 'update_by',
      width: 160,
      features: { sortable: true },
    },
    {
      name: '最后修改时间',
      code: 'update_time',
      width: 160,
      render: (text) => {
        return text;
      },
      features: { sortable: true, format: 'TIME' },
    },
    // {
    //   name: '操作',
    //   code: 'opt',
    //   hidden: !hasAuth(['SKU上限管理', '编辑']),
    //   width: 120,
    //   lock: 'right',
    //   align: 'center',
    //   render: (text, record, item) => {
    //     return (
    //       <>
    //         <XlbButton
    //           onClick={() => {
    //             handleSave(record, item.index);
    //           }}
    //           type="primary"
    //         >
    //           保存
    //         </XlbButton>
    //       </>
    //     );
    //   },
    // },
  ];
  const [form] = XlbBasicForm.useForm();
  const userInfo = LStorage.get('userInfo');
  console.log(userInfo, 'usetInfousetInfousetInfousetInfousetInfousetInfo');
  // const summary_types = XlbBasicForm.useWatch('summary_types', form);
  const [visible, setVisible] = useState(false);
  const [formArr, setFormArr] = useState([...formList]);
  const selectData = useRef<any>({});
  const recordParams = useRef({
    org_id: 0,
    category_id: undefined,
    type: '1', //  '1' 修改记录 ’2‘ 排除商品操作记录
  });

  let getData = () => {};
  const [tableHeight, setTableHeight] = useState('300px');

  const pageRef = useRef(null);
  const tableRef = useRef<HTMLDivElement>(null);
  const prevPost = (p: any) => {
    const data = form.getFieldsValue(true);

    /**
     * 控制一级 二级 三级 显示隐藏 // ？暂时拿掉
     */
    // columns.forEach((_) => {
    //   _.hidden = false;
    //   if (_.v) {
    //     _.hidden = true;
    //   }
    //   if (data?.summary_types?.length && data?.summary_types.includes(_.v)) {
    //     _.hidden = false;
    //   }
    // });
    return { ...p, ...data }; // org_id: userInfo.org_id
  };

  const treeCallBack = async (node: TreeData) => {
    recordParams.current.org_id = node.id;
    await form.setFieldValue('org_id', node.id);
    getData();
  };
  const setHeight = () => {
    const { top = 100 } = tableRef.current?.getBoundingClientRect();
    console.log(top);
    setTableHeight(`calc(100vh - 100px - ${top}px)`);
  };
  useEffect(() => {
    // setHeight();
    console.log(hasAuth(['SKU上限管理/排除商品', '导入']));
  }, []);
  const exportItem = async (fn, e) => {
    console.log(pageRef);
    fn(true);
    const org_id = recordParams.current.org_id;
    const formQuery = form.getFieldsValue(true);
    const res = await Api.export({ org_id, ...formQuery });
    fn(false);
    if (res?.code === 0) {
      message.success(res.data);
      wujieBus?.$emit('xlb_erp-event', { code: 'downloadEnd', target: e });
    }
  };
  return (
    <XlbLayout
      leftNode={
        <>
          <div
            style={{
              backgroundColor: '#FFF',
              height: '100%',
              overflow: 'auto',
            }}
          >
            <XlbTree
              url="/erp-mdm/hxl.erp.org.find"
              // treeConfig={{ showLine: true }}
              type="base"
              requestParams={{
                level: 2,
              }}
              handleDefaultValue={(data) => {
                if (data?.length) {
                  return [data?.[0]];
                } else {
                  return [];
                }
              }}
              dataType={'lists'}
              topLevelTreeDisabled
              onSelect={(node) => treeCallBack(node)}
              fieldName={{ parent_id: 'parent_code', id: 'code' }}
            />
          </div>
        </>
      }
      rightNode={
        <>
          <XlbPageContainer
            url={'/erp-mdm/hxl.erp.org.skulimit.page'}
            tableColumn={[...columns]}
            keyboard={false}
            prevPost={prevPost}
            immediatePost={false}
            ref={pageRef}
            isOldBtn={true}
            style={{ overflow: 'hidden' }}
          >
            <RecoedModal
              visible={visible}
              recordParams={recordParams.current}
              close={() => setVisible(false)}
            ></RecoedModal>
            <div style={{ overflow: 'hidden' }}>
              <ToolBtn>
                {({
                  dataSource,
                  loading,
                  setLoading,
                  requestForm,
                  selectRow,
                  fetchData,
                }) => {
                  dataSource?.forEach((_: any) => {
                    _.limit_num_copy = _.limit_num;
                    _.collective_purchase_limit_num_copy =
                      _.collective_purchase_limit_num;
                    _.ground_purchase_limit_num_copy =
                      _.ground_purchase_limit_num;
                  });
                  getData = fetchData;
                  selectData.current = selectRow;
                  return (
                    <XlbButton.Group>
                      {hasAuth(['SKU上限管理', '查询']) ? (
                        <XlbButton
                          type="primary"
                          label="查询"
                          onClick={() => fetchData()}
                          icon={<XlbIcon size={16} name="sousuo" />}
                        />
                      ) : null}
                      {hasAuth(['SKU上限管理', '导入']) ? (
                        <XlbButton
                          type="primary"
                          label="导入"
                          // disabled={!dataSource?.length || loading}
                          onClick={async () => {
                            const res = await XlbImportModal({
                              templateUrl: `${process.env.BASE_URL}/erp-mdm/hxl.erp.org.skulimittemplate.download`,
                              importUrl: `${process.env.BASE_URL}/erp-mdm/hxl.erp.org.skulimit.update.import`,
                              templateName: '下载导入模板',
                              callback: (res) => {
                                console.log(res);
                                fetchData();
                              },
                            });
                          }}
                          // onClick={(e) => exportItem(setLoading, e)}
                          icon={<XlbIcon size={16} name="daoru" />}
                        />
                      ) : null}
                      {hasAuth(['SKU上限管理', '导出']) ? (
                        <XlbButton
                          type="primary"
                          label="导出"
                          // disabled={!dataSource?.length || loading}
                          onClick={(e) => exportItem(setLoading, e)}
                          icon={<XlbIcon size={16} name="daochu" />}
                        />
                      ) : null}
                      {hasAuth(['SKU上限管理/修改记录', '查询']) ? (
                        <XlbButton
                          type="primary"
                          label="修改记录"
                          disabled={loading || !selectRow?.length}
                          onClick={() => {
                            recordParams.current.type = '1';
                            recordParams.current.category_id =
                              selectData.current[0]?.['category_id'] ||
                              undefined;
                            setVisible(true);
                          }}
                          icon={<XlbIcon size={16} name="shenqing" />}
                        />
                      ) : null}
                      {hasAuth(['SKU上限管理/排除商品', '编辑']) ? (
                        <XlbDropdownButton
                          label="排除商品"
                          dropList={[
                            { label: '选择', value: 1 },
                            {
                              label: '导出',
                              value: 2,
                              disabled: !hasAuth([
                                'SKU上限管理/排除商品',
                                '导出',
                              ]),
                            },
                            {
                              label: '导入',
                              value: 3,
                              disabled: !hasAuth([
                                'SKU上限管理/排除商品',
                                '导入',
                              ]),
                            },
                            {
                              label: '操作记录',
                              value: 4,
                            },
                          ]}
                          dropdownItemClick={async (t, item, e) => {
                            if (item.value === 1) {
                              const excludeitems = await Api.excludeitem();
                              console.log(excludeitems);
                              const result = await XlbBasicData({
                                type: 'orgGoodsList',
                                nullable: true,
                                // url: '/erp/hxl.erp.item.short.page',
                                data: {
                                  status: 1,
                                },
                                isMultiple: true,
                                dataType: 'lists',
                                primaryKey: 'id',
                                idsKey: 'id', // 新增
                                resetForm: true,
                                selectedList: JSON.parse(
                                  JSON.stringify(excludeitems?.data || []),
                                ),
                                // selectedList: [...(excludeitems?.data || [])], // 选中数据
                              });
                              if (result) {
                                const org_items = result?.map((_) => {
                                  return {
                                    id: _.id,
                                    item_id: _.item_id,
                                    org_id: _.org_id,
                                  };
                                });
                                Api.exportExcludeSave(org_items).then((res) => {
                                  if (res.code === 0) {
                                    message.success('操作成功');
                                    fetchData();
                                  }
                                });
                              }
                            } else if (item.value === 2) {
                              const res = await Api.exportExcludeitem({});
                              if (res.code === 0) {
                                message.success(res.data);
                                wujieBus?.$emit('xlb_erp-event', {
                                  code: 'downloadEnd',
                                  target: e,
                                });
                              }
                            } else if (item.value === 3) {
                              const res = await XlbImportModal({
                                templateUrl: `${process.env.BASE_URL}/erp-mdm/hxl.erp.org.skulimit.excludeitemtemplate.download`,
                                importUrl: `${process.env.BASE_URL}/erp-mdm/hxl.erp.org.skulimit.excludeitem.import`,
                                templateName: '下载导入模板',
                                callback: (res) => {
                                  fetchData();
                                },
                              });
                            } else if (item.value === 4) {
                              recordParams.current.type = '2';
                              recordParams.current.category_id =
                                selectData.current[0]?.['category_id'] ||
                                undefined;
                              setVisible(true);
                            }
                          }}
                        />
                      ) : null}
                    </XlbButton.Group>
                  );
                }}
              </ToolBtn>
              <SearchForm>
                <XlbForm
                  formList={formArr}
                  initialValues={
                    {
                      // summary_types: ['1', '2', '3', '4'],
                    }
                  }
                  form={form}
                  isHideDate={true}
                  handleDialogClick={() => {
                    console.log(form);
                  }}
                ></XlbForm>
              </SearchForm>

              <Table
                style={{ height: 'calc(100vh - 250px)' }}
                keyboard={false}
                key="id"
                keepDataSource={true}
                selectMode="single"
              />
            </div>
          </XlbPageContainer>
        </>
      }
    ></XlbLayout>
  );
};
export default Index;
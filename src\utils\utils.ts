import { LStorage } from './storage';

export const PAGE = 'page';
export const EXPORT = 'export';

const SECOND = 1000;
const MIN = 60 * SECOND;
const PAGE_TIMEOUT = MIN;
const OTHER_TIMEOUT = 2 * MIN;

/* 后期支持多种类型的请求时间扩展 */
const TIMEOUT = {
  [PAGE]: PAGE_TIMEOUT,
  [EXPORT]: 14 * MIN,
};

/* 自定义列获取url超时时间 */
const URL_COLUMN = '/erp/hxl.erp.usercolumn.get';

/* 根据具体的url设置超时时间 */
const URL_TIMEOUT = {
  [URL_COLUMN]: SECOND,
};
export const configTime = (url: string) => {
  if (URL_TIMEOUT[url]) {
    return URL_TIMEOUT[url];
  } else {
    const data = url?.split('.');
    /* xxx.page 截取最后一个是请求的类型 */
    const type = data.pop() as string;

    return TIMEOUT[type] ?? OTHER_TIMEOUT;
  }
};

export const configToken = (options: any) => {
  let token = LStorage.get('access_token') || '';
  if (!token && options.data.headers?.token) {
    token = options.data.headers?.token;
  }
  let headers = {
    ...options.headers,
    'Api-Version': '1.5.0',
    ...(process.env.ISPRE
      ? {
          companyId: '10002',
        }
      : {}),
  };
  if (token) {
    headers = {
      ...options.headers,
      'Access-Token': `${token}`,
      'Api-Version': '1.5.0',
      ...(process.env.ISPRE
        ? {
            companyId: '10002',
          }
        : {}),
    };
  }

  const userInfo = LStorage.get('userInfo');
  const companyId = userInfo?.company_id;
  const userId = userInfo?.id;
  const orgIds = userInfo?.org_ids;

  if (companyId) {
    headers['Company-Id'] = process.env.ISPRE ? '10002' : companyId;
  }

  if (userId) {
    headers['User-Id'] = userId;
  }

  if (Array.isArray(orgIds) && orgIds.length) {
    headers['Org-Ids'] = orgIds.join(',');
  }

  return headers;
};
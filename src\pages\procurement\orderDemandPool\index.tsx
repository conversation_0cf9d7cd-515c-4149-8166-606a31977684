import { getAllStoreList } from '@/api/common';
import { MAX_INT } from '@/constants';
import { hasAuth } from '@/utils/kit';
import {
  ContextState,
  XlbBasicForm,
  XlbButton,
  XlbForm,
  XlbIcon,
  XlbPageContainer,
  XlbTipsModal,
} from '@xlb/components';
import { message } from 'antd';
import dayjs from 'dayjs';
import { FC, useEffect, useState } from 'react';
import { getFormList, tableColumn } from './data';
import { createPurchaseOrder, exportOrderDemandPool } from './server';

const Index: FC = () => {
  // from
  const [form] = XlbBasicForm.useForm();
  const prevPost = () => ({
    ...form.getFieldsValue(true),
    flag: form.getFieldsValue(true)?.checkbox?.includes('flag')
      ? Number(!form.getFieldsValue(true)?.checkbox?.includes('flag'))
      : void 0,
  });

  // filterData
  interface OptionType {
    label: string;
    value: number;
  }
  const [storeList, setStoreList] = useState<OptionType[]>([]);
  const getStoreList = async () => {
    const res = await getAllStoreList({
      center_flag: true,
      page_size: MAX_INT,
      enable_organization: false,
    });
    if (res?.code === 0) {
      const formatList =
        res?.data?.content?.map((item: any) => ({
          label: item.store_name,
          value: item.id,
        })) || [];
      setStoreList(formatList);
    }
  };
  useEffect(() => {
    getStoreList();
    form.setFieldsValue({
      create_date: [dayjs().format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')],
    });
  }, []);

  // operate
  const exportItem = async (requestForm: any) => {
    const res = await exportOrderDemandPool(requestForm);
    if (res?.code === 0) {
      message.success(res.data);
    }
  };
  const createOrder = async (row: any[]) => {
    const hasPurchaseOrder = row.some((item) => item.purchase_fid);
    if (hasPurchaseOrder) {
      await XlbTipsModal({
        title: '提示',
        tips: '存在已绑定采购订单的数据，不可生成采购订单！',
      });
      return;
    }
    await XlbTipsModal({
      title: '提示',
      tips: '确认生成采购订单？',
      isCancel: true,
      onOkBeforeFunction: async () => {
        const res = await createPurchaseOrder({
          ids: row.map((item) => item.id),
        });
        if (res?.code === 0) {
          message.success('生成采购订单成功');
        }
        return res?.code === 0;
      },
    });
  };

  return (
    <XlbPageContainer
      url={'/erp/hxl.erp.orderdemandpool.page'}
      tableColumn={tableColumn}
      immediatePost
      prevPost={prevPost}
    >
      <XlbPageContainer.ToolBtn showColumnsSetting>
        {({
          fetchData,
          loading,
          requestForm,
          selectRowKeys,
          selectRow,
        }: ContextState<any>) => {
          return (
            <XlbButton.Group>
              {hasAuth(['订单池', '查询'], 'ERP') && (
                <XlbButton
                  label="查询"
                  type="primary"
                  loading={loading}
                  onClick={() => {
                    fetchData();
                  }}
                  icon={<XlbIcon name="sousuo" />}
                />
              )}
              {hasAuth(['订单池', '导出'], 'ERP') && (
                <XlbButton
                  label="导出"
                  type="primary"
                  icon={<XlbIcon name="daochu" />}
                  onClick={() => exportItem(requestForm)}
                />
              )}
              {hasAuth(['订单池', '编辑'], 'ERP') && (
                <XlbButton
                  label="生成采购订单"
                  disabled={!selectRowKeys?.length}
                  type="primary"
                  icon={<XlbIcon name="daoru" />}
                  onClick={() => createOrder(selectRow || [])}
                />
              )}
            </XlbButton.Group>
          );
        }}
      </XlbPageContainer.ToolBtn>

      <XlbPageContainer.SearchForm>
        <XlbForm form={form} formList={getFormList({ storeList })} isHideDate />
      </XlbPageContainer.SearchForm>

      <XlbPageContainer.Table selectMode="multiple" />
    </XlbPageContainer>
  );
};
export default Index;

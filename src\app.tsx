import '@/assets/iconfont/iconfont.css';
import { accountLoginUsingPost } from '@/services/system';
import { LStorage } from '@/utils/storage';
import { isWUJIE } from '@/wujie/utils';
import type { RequestConfig } from '@umijs/max';
import { XlbFetch } from '@xlb/utils';
import { message } from 'antd';
import Cookies from 'js-cookie';
import React from 'react';
import { autoFixContext } from 'react-activation';
import jsxDevRuntime from 'react/jsx-dev-runtime';
import jsxRuntime from 'react/jsx-runtime';
import Provider from './provider';
// 本地开发模式
const isLocalMode = () => {
  return process.env.XLB_ENV === 'development';
};

const tokenKey = `${process.env.XLB_ENV}_qiankun_token`;

autoFixContext(
  [jsxRuntime, 'jsx', 'jsxs', 'jsxDEV'],
  [jsxDevRuntime, 'jsx', 'jsxs', 'jsxDEV'],
);

// @ts-ignore
XlbFetch.register([1005], () => {
  if (isWUJIE()) {
    window.$wujie?.props?.parentWindow.location.reload();
  }
});

// @ts-ignore
XlbFetch.register([2005], (data) => {
  return message.error(data.msg);
});

interface urlParamsProp {
  account?: string;
  companyId?: number | string;
}

export const request: RequestConfig = {
  timeout: 1000,
  errorConfig: {
    errorHandler() {},
    errorThrower() {},
  },
  requestInterceptors: [
    // 直接写一个 function，作为拦截器
    (url, options) => {
      return { url, options };
    },
    // 一个二元组，第一个元素是 request 拦截器，第二个元素是错误处理
    [
      (url, options) => {
        return { url, options };
      },
      (error) => {
        return Promise.reject(error);
      },
    ],
    // 数组，省略错误处理
    [
      (url, options) => {
        return { url, options };
      },
    ],
  ],
  responseInterceptors: [],
};

export async function getInitialState(): Promise<{ name: string }> {
  if (!isWUJIE()) {
    let userInfo = LStorage.get('userInfo');
    const authorities = userInfo?.authorities;
    const smsAuthorities = authorities?.filter(
      (item) => item?.app_type == 'ERP',
    );
    console.log(smsAuthorities);
    if (!userInfo) {
      let info: any = Cookies.get(tokenKey);
      if (info?.length) info = JSON.parse(info);
      if (info?.accessToken) {
        LStorage.set('access_token', info?.accessToken);
      }

      let res = await accountLoginUsingPost({
        account: info?.account || '***********',
        company_id: info?.companyId || 1000,
        source: 'WEB',
      });
      LStorage.set('userInfo', res?.data);
      userInfo = res?.data;
    }
  }
  return { name: 'ERP进销存管理' };
}

export const layout = () => {
  return {
    logo: 'https://img.alicdn.com/tfs/TB1YHEpwUT1gK0jSZFhXXaAtVXa-28-27.svg',
    menu: { locale: false },
    pure: true,
  };
};

// 根节点注入NiceModal.Provider
export function rootContainer(container: any) {
  return React.createElement(Provider, null, container);
}

// 注入父window到子globalThis
const customGlobalThis = window.__POWERED_BY_WUJIE__
  ? window.$wujie?.props?.parentWindow
  : window;

// if (window.__POWERED_BY_WUJIE__) {
//   Object.defineProperty(globalThis, 'customGlobalThis', {
//     value: customGlobalThis,
//     writable: false,
//   });
// }

// export { customGlobalThis };

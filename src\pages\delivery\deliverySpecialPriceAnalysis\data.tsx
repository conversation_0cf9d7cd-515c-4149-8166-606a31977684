import type { XlbTableColumnProps } from '@xlb/components';

export const tableList: XlbTableColumnProps<any>[] = [
  {
    name: '序号',
    code: '_index',
    width: 50,
    align: 'center',
  },
  {
    name: '商品代码',
    code: 'item_code',
    width: 130,
    features: { sortable: true },
  },
  {
    name: '商品条码',
    code: 'item_bar_code',
    width: 130,
    features: { sortable: true },
  },
  {
    name: '商品名称',
    code: 'item_name',
    width: 160,
    features: { sortable: true },
  },
  {
    name: '商品规格',
    code: 'item_spec',
    width: 140,
    features: { sortable: true },
  },
  {
    name: '配送单位',
    code: 'delivery_unit',
    width: 100,
    features: { sortable: true },
  },
  {
    name: '调出数量',
    code: 'quantity',
    width: 100,
    features: { sortable: true, format: 'QUANTITY' },
    align: 'right',
  },
  {
    name: '调出基本数量',
    code: 'basic_quantity',
    width: 140,
    features: { sortable: true, format: 'QUANTITY' },
    align: 'right',
  },
  {
    name: '促销调出金额',
    code: 'money',
    width: 120,
    features: { sortable: true, format: 'MONEY' },
    align: 'right',
  },
  {
    name: '促销调出均价',
    code: 'price',
    width: 120,
    features: { sortable: true, format: 'PRICE' },
    align: 'right',
  },
  {
    name: '原调出金额',
    code: 'original_money',
    width: 110,
    features: { sortable: true, format: 'MONEY' },
    align: 'right',
  },
  {
    name: '原调出均价',
    code: 'original_price',
    width: 110,
    features: { sortable: true, format: 'PRICE' },
    align: 'right',
  },
  {
    name: '配送优惠金额',
    code: 'discount_money',
    width: 130,
    features: { sortable: true, format: 'MONEY' },
    align: 'right',
  },
];

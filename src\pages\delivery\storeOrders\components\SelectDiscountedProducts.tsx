import {
  XlbModal,
  XlbBasicForm,
  XlbInputDialog,
  XlbInputNumber,
  XlbButton,
  XlbInput,
  XlbTabs
} from '@xlb/components'
import { useEffect, useState } from 'react'
// import styles from './index.less'
import { useDebounceFn } from 'ahooks'
import { ArtColumn, BaseTable, features, useTablePipeline } from 'ali-react-table'
import * as fusion from '@alifd/next'
import { tableStyle } from '@/data/common/data'
import { Form, message } from 'antd'
import { hasAuth } from '@/utils'
import { isNumber } from 'lodash'

const SelectDiscountedProducts = (props: any) => {
  const { visible, setVisible, deliverySpacialRowData, onOk } = props
  const [form] = Form.useForm()
  const [dataSource, setDataSource] = useState<any>([])

  useEffect(() => {
    if (visible) {
      form.setFieldsValue(deliverySpacialRowData)
      setDataSource(deliverySpacialRowData.delivery_spacial_price_list)
    } else {
      setDataSource([])
      form.setFieldsValue({})
      form.setFieldsValue({})
    }
  }, [visible])

  // const inputChangeDebounce = useDebounceFn(
  //   (e: any, index: any, key: any) => {
  //     inputChange(e, index, key)
  //   },
  //   {
  //     wait: 200
  //   }
  // )
  const inputChange = (e: any, index: any, record: any, key: any) => {
    console.log(e)
    const row = dataSource[index]
    row.amountOfMoney = (e * row.special_price).toFixed(2)
    row[key] = e
    setDataSource([...dataSource])
  }
  const onPressEnter = (code: any, index: any, e?: any) => {
    Promise.resolve()
      .then(() => {
        dataSource[index]['_edit'] = false
        index + 1 == dataSource.length
          ? (dataSource[0]['_edit'] = true)
          : (dataSource[index + 1]['_edit'] = true)
        setDataSource(JSON.parse(JSON.stringify(dataSource)))
      })
      .then(() => {
        let inputBox =
          index + 1 == dataSource.length
            ? document.getElementById(code + '-' + (0).toString())
            : document.getElementById(code + '-' + (index + 1).toString())
        inputBox?.focus()
      })
  }

  const tableList: ArtColumn[] = [
    {
      name: '批次',
      code: 'special_fid',
      align: 'left',
      render: (value: any, record: any, index: number) => (
        <div className="info overwidth">{value}</div>
      )
    },
    {
      name: '起始数量',
      code: 'initial_quantity',
      width: 80,
      align: 'center',
      render: (value: any, record: any, index: number) => (
        <div className="info overwidth">{value}</div>
      )
    },
    {
      name: '单位',
      code: 'unit',
      width: 80,
      align: 'left',
      render: (value: any, record: any, index: number) => (
        <div className="info overwidth">{value}</div>
      )
    },
    {
      name: '原价',
      code: 'special_detail_num',
      width: 80,
      align: 'center',
      render: (value: any, record: any, index: number) => (
        <div className="info overwidth">
          {hasAuth(['门店补货单/配送价', '查询'])
            ? Number(
                deliverySpacialRowData.basic_price * deliverySpacialRowData.delivery_ratio
              ).toFixed(4)
            : '*****'}
        </div>
      )
    },
    {
      name: '特价',
      code: 'special_price',
      width: 80,
      align: 'center',
      render: (value: any, record: any, index: number) => (
        <div className="info overwidth">{value}</div>
      )
    },
    {
      name: '数量',
      code: 'quantity',
      width: 120,
      render: (value: any, record: any, index: number) => {
        return record._edit ? (
          <div className="info overwidth">
            <XlbInputNumber
              // min={record?.initial_quantity || 1}
              // max={record?.remain_quantity || 999999999}
              precision={0}
              // className={styles.inputNumber}
              value={record?.quantity?.toFixed(0)}
              onChange={(e) => inputChange(e, index, record, 'quantity')}
              onClick={(e) => {
                e.stopPropagation()
              }}
              onBlur={(e) => {
                console.log(e, 1111)
                if (
                  isNumber(record.remain_quantity) &&
                  isNumber(record.initial_quantity) &&
                  (Number(e.target.value) > record.remain_quantity ||
                    Number(e.target.value) < record.initial_quantity)
                ) {
                  dataSource[index].quantity =
                    Number(e.target.value) > record.remain_quantity
                      ? record.remain_quantity
                      : record.initial_quantity
                  message.error('输入数量超出范围')
                  return
                }
              }}
              onPressEnter={(e) => onPressEnter('quantity', index)}
            />
          </div>
        ) : (
          <div className="info overwidth">{value}</div>
        )
      }
    },
    {
      name: '金额',
      code: 'amountOfMoney',
      width: 80,
      render: (value: any, record: any, index: number) => (
        <div className="info overwidth">{value}</div>
      )
    },
    {
      name: '备注',
      code: 'memo',
      width: 120,
      render: (value: any, record: any, index: number) => {
        return <div className="info overwidth">{value}</div>
      }
    }
  ]

  const pipeline = useTablePipeline({ components: fusion })
    .input({ dataSource: dataSource, columns: tableList })
    .primaryKey((row: any) => `${row.special_detail_num}-${row.special_fid}`)
    .use(
      features.columnResize({
        fallbackSize: 100,
        onChangeSizes: (nextSizes: number[]) => {}
      })
    )
    .use(
      features.singleSelect({
        highlightRowWhenSelected: true,
        clickArea: 'row',
        radioColumn: { hidden: true },
        onChange: (value: any) => {
          pipeline.getDataSource().map((v, index) => {
            v._edit = value == `${v.special_detail_num}-${v.special_fid}`
          })
        }
      })
    )

  return (
    <XlbModal
      wrapClassName={'xlbDialog'}
      title="选择特价商品"
      keyboard={false}
      visible={visible}
      maskClosable={false}
      isCancel={true}
      onOk={() => {
        onOk({ ...deliverySpacialRowData, delivery_spacial_price_list: dataSource })
        setVisible(false)
      }}
      onCancel={() => setVisible(false)}
      width={900}
      zIndex={2003}
      centered
    >
      <div style={{ margin: '16px 0' }}>
        <Form form={form} layout="inline" style={{ marginBottom: 16 }}>
          <Form.Item label="商品代码" name="code">
            <XlbInput disabled placeholder="请输入商品代码" style={{ width: 150 }} />
          </Form.Item>
          <Form.Item label="商品条码" name="bar_code">
            <XlbInput disabled placeholder="请输入商品条码" style={{ width: 150 }} />
          </Form.Item>
          <Form.Item label="商品名称" name="name">
            <XlbInput disabled placeholder="请输入商品名称" style={{ width: 150 }} />
          </Form.Item>
        </Form>
        <BaseTable
          className="jMuPhs"
          style={{
            ...tableStyle,
            overflow: 'auto',
            height: 420
          }}
          emptyCellHeight={210}
          {...pipeline.getProps()}
        />
      </div>
    </XlbModal>
  )
}

export default SelectDiscountedProducts

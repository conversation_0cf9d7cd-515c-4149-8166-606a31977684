import { columnWidthEnum } from '@/data/common/constant';
import { hasAuth } from '@/utils/kit';
import {
  SearchFormType,
  XlbBasicForm,
  XlbForm,
  XlbTable,
  XlbTableColumnProps,
} from '@xlb/components';
import { Tooltip } from 'antd';
import dayjs from 'dayjs';
import _ from 'lodash';
import { useEffect, useState } from 'react';
import { stockTransFormOrderReadInfo } from '../server';

const formList: SearchFormType[] = [
  {
    type: 'input',
    disabled: true,
    name: 'store_name',
    label: '转仓门店',
  },
  {
    type: 'input',
    disabled: true,
    name: 'storehouse_name',
    label: '转出仓库',
  },
  {
    type: 'input',
    disabled: true,
    name: 'in_storehouse_name',
    label: '转入仓库',
  },
  {
    type: 'input',
    disabled: true,
    name: 'fid',
    label: '单据号',
  },
  {
    type: 'select',
    disabled: true,
    name: 'state',
    label: '单据状态',
    options: [
      { label: '制单', value: 'INIT' },
      { label: '审核', value: 'AUDIT' },
      { label: '已完成', value: 'FINISH' },
      { label: '已作废', value: 'CANCEL' },
    ],
  },
  {
    type: 'datePicker',
    disabled: true,
    name: 'operate_date',
    label: '转仓日期',
  },
  {
    type: 'input',
    disabled: true,
    name: 'item_dept_names',
    label: '商品部门',
  },
  {
    type: 'input',
    disabled: true,
    name: 'memo',
    label: '留言备注',
    width: 648,
  },
];
const columnsList: XlbTableColumnProps<any>[] = [
  {
    name: '序号',
    code: 'index',
    width: columnWidthEnum.INDEX,
    align: 'center',
  },
  {
    name: '操作',
    code: 'operation',
    width: 60,
    align: 'center',
  },
  {
    name: '商品代码',
    code: 'code',
    width: 110,
    features: { sortable: true },
  },
  {
    name: '商品条码',
    code: 'bar_code',
    width: 130,
    features: { sortable: true },
  },
  {
    name: '商品名称',
    code: 'name',
    width: 160,
    features: { sortable: true },
  },
  {
    name: '采购规格',
    code: 'purchase_spec',
    width: 100,
    features: { sortable: true },
  },
  {
    name: '单位',
    code: 'unit',
    width: 90,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '数量',
    code: 'quantity',
    width: 90,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '单价',
    code: 'price',
    width: 90,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '金额',
    code: 'money',
    width: 90,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '基本单位',
    code: 'basic_unit',
    width: 90,
    features: { sortable: true },
  },
  {
    name: '基本数量',
    code: 'basic_quantity',
    width: 100,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '基本单价',
    code: 'basic_price',
    width: 100,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '生产日期',
    code: 'producing_date',
    width: columnWidthEnum.DATE,
    features: { sortable: true },
  },
  {
    name: '到期日期',
    code: 'expire_date',
    width: columnWidthEnum.DATE,
    features: { sortable: true },
  },
  {
    name: '批次号',
    code: 'batch_number',
    width: 100,
    features: { sortable: true },
  },
  {
    name: '保质期',
    code: 'period',
    width: 90,
    features: { sortable: true },
  },
  {
    name: '库存数量',
    code: 'stock_quantity',
    width: 100,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '备注',
    code: 'memo',
    width: columnWidthEnum.MEMO,
    features: { sortable: true },
  },
];

const StockTransFormOrder = (props: any) => {
  const { fid } = props;
  const [formModel] = XlbBasicForm.useForm<any>();
  const [fidDataList, setFidDataList] = useState<any>([]);
  const [tableLoading, setTableLoading] = useState<any>(false);
  const [itemArrdetail] = useState<XlbTableColumnProps<any>[]>(
    columnsList.map((i: any) => ({
      ...i,
      code: i.code == 'index' ? '_index' : i.code,
      hidden: i.code == 'operation' ? true : i.hidden,
    })) as any[],
  );

  const tableRender = (item: any) => {
    switch (item.code) {
      case 'money':
        item.render = (value: any, record: any) => {
          return (
            <div style={{ textAlign: 'right' }}>
              {hasAuth(['同店转仓/价格', '查询'])
                ? value
                  ? parseFloat(value).toFixed(2)
                  : '0.00'
                : '****'}
            </div>
          );
        };
        break;
      case 'price':
      case 'basic_price':
        item.render = (value: any, record: any) => {
          return (
            <div style={{ textAlign: 'right' }}>
              {hasAuth(['同店转仓/价格', '查询'])
                ? value
                  ? parseFloat(value).toFixed(4)
                  : '0.0000'
                : '****'}
            </div>
          );
        };
        break;
      case 'basic_quantity':
      case 'stock_quantity':
      case 'quantity':
        item.render = (value: any, record: any) => {
          return (
            <div style={{ textAlign: 'right' }}>
              {value ? parseFloat(value).toFixed(3) : '0.000'}
            </div>
          );
        };
        break;
      case 'memo':
        item.render = (value: any) => {
          return (
            <Tooltip placement="topLeft" autoAdjustOverflow title={value}>
              <div>{value}</div>
            </Tooltip>
          );
        };
        break;
    }
    return item;
  };

  const openRefOrder = _.debounce(async (fid, summary: boolean = false) => {
    setTableLoading(true);
    setFidDataList([]);
    formModel.setFieldsValue({});
    const res = await stockTransFormOrderReadInfo({
      fid: fid,
      summary: summary,
    });
    if (res?.code == 0) {
      // 表单的值
      const houseArr: any[] = [];
      houseArr.push({
        label: res.data.in_storehouse_name,
        value: res.data.in_storehouse_id,
      });
      houseArr.push({
        label: res.data.storehouse_name,
        value: res.data.storehouse_id,
      });
      formModel.setFieldsValue({
        ...res.data,
        operate_date: res.data.operate_date
          ? dayjs(res.data.operate_date)
          : null,
        in_storehouse_name: houseArr.find(
          (s) => s.value === res.data.in_storehouse_name,
        )?.label,
      });
      // 列表的值
      const goodsList: any[] = [];
      res.data.details.map((item: any) => {
        item.id = item.item_id;
        item.name = item.item_name;
        item.code = item.item_code;
        item.bar_code = item.item_bar_code;
        item.purchase_spec = item.item_spec;
        goodsList.push(item);
      });
      setFidDataList(goodsList);
    }
    setTableLoading(false);
  }, 50);

  useEffect(() => {
    openRefOrder(fid);
    itemArrdetail.map((v) => tableRender(v));
  }, []);

  return (
    <>
      <XlbForm
        style={{ marginTop: 15 }}
        formList={formList}
        form={formModel}
        isHideDate={true}
      />
      <XlbTable
        isLoading={tableLoading}
        style={{ height: 400, maxHeight: 400, overflowY: 'scroll' }}
        hideOnSinglePage={false}
        showSearch={true}
        columns={itemArrdetail}
        total={fidDataList?.length}
        dataSource={fidDataList}
        key={fidDataList?.length}
      ></XlbTable>
    </>
  );
};

export default StockTransFormOrder;

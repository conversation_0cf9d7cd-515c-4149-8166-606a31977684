import { ErpFieldKeyMap } from '@/data/common/fieldListConfig';
import { useBaseParams } from '@/hooks/useBaseParams';
import { hasAuth } from '@/utils/kit';
import type {
  ContextState,
  XlbProPageContainerRef,
  XlbProPageModalRef,
} from '@xlb/components';
import {
  XlbButton,
  XlbIcon,
  XlbProPageContainer,
  XlbProPageModal,
} from '@xlb/components';
import { useRef, useState } from 'react';
import { Fragment } from 'react/jsx-runtime';
import { DataType } from './data';
import Item from './item';
const StoreDeliveryDay = () => {
  const { enable_organization } = useBaseParams((state) => state);
  const [record, setRecord] = useState<any>({});
  const pageConatainerRef = useRef<XlbProPageContainerRef>(null);
  const pageModalRef = useRef<XlbProPageModalRef>(null);
  // let ref: ContextState;

  return (
    <Fragment>
      <XlbProPageModal
        ref={pageModalRef}
        Content={({ onClose }) => {
          return (
            <Item
              onBack={() => {
                pageConatainerRef?.current?.pageContainerRef?.current?.fetchData?.();
                pageModalRef.current?.setOpen(false);
              }}
              record={record}
            />
          );
        }}
      >
        <div className="xlb-table-clickBtn"></div>
      </XlbProPageModal>
      <XlbProPageContainer
        ref={pageConatainerRef}
        searchFieldProps={{
          formList: [
            { id: 'commonInput', label: '关键字', name: 'keyword' },
            { id: ErpFieldKeyMap?.erpStoreIdsForArea, label: '门店' },
          ],
        }}
        treeFieldProps={
          enable_organization
            ? {
                leftUrl: '/erp-mdm/hxl.erp.org.tree',
                dataType: DataType.LISTS,
                leftKey: 'org_id',
                onSelect: (data, formRef) => {
                  // console.log('onSelect', data)
                  formRef?.current?.setFieldsValue({
                    org_id: data?.level === 1 ? null : data?.id,
                  });
                  return { org_id: data?.level === 1 ? null : data?.id };
                },
              }
            : ''
        }
        extra={(content: ContextState) => {
          // ref = content;
          const { loading } = content;
          return (
            <>
              {hasAuth(['门店配送日', '编辑']) ? (
                <XlbButton
                  label="新增"
                  type="primary"
                  loading={loading}
                  onClick={() => {
                    setRecord({ id: -1 });
                    pageModalRef.current?.setOpen(true);
                  }}
                  icon={<XlbIcon name="jia" />}
                />
              ) : null}
            </>
          );
        }}
        tableFieldProps={{
          url: '/erp/hxl.erp.storedeliveryday.find',
          tableColumn: [
            {
              name: '序号',
              code: '_index',
              width: 50,
              align: 'center',
            },
            {
              name: '配送日名称',
              code: 'name',
              width: 180,
              features: { sortable: true },
              align: 'left',
              render: (text: any, record: any, index: any) => {
                return (
                  <span
                    className="link cursors"
                    onClick={() => {
                      setRecord(record);
                      pageModalRef.current?.setOpen(true);
                    }}
                  >
                    {text}
                  </span>
                );
              },
            },
            {
              name: '创建人',
              code: 'create_by',
              width: 110,
              features: { sortable: true },
              align: 'left',
            },
            {
              name: '组织',
              code: 'org_name',
              width: 140,
              features: { sortable: true },
            },
            {
              name: '创建时间',
              code: 'create_time',
              width: 180,
              features: { sortable: true, format: 'TIME' },
              align: 'left',
            },
          ].filter((v) => {
            if (!enable_organization) {
              return v.name !== '组织';
            }
            return v;
          }),
          selectMode: 'single',
          showColumnsSetting: true,
          immediatePost: true,
        }}
        deleteFieldProps={{
          order: 2,
          url: hasAuth(['门店配送日', '删除'])
            ? '/erp/hxl.erp.storedeliveryday.delete'
            : '',
          // params: (data: any, v: any) => {
          //   return { ids: data }
          // }
        }}
      />
    </Fragment>
  );
};

export default StoreDeliveryDay;
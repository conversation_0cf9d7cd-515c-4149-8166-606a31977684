import {
  businessDeptListType,
  supplierMessageTypes
} from './preViewData'
import dateManipulation from '@/utils/dateMainpulation'
import type { XlbTableColumnProps } from '@xlb/components'
import { XlbIcon, XlbModal, XlbTable } from '@xlb/components'
import type { FC } from 'react'
import { useEffect, useState } from 'react'
import { tableColumn, tableColumn1 } from './preViewData'
import styles from './index.less'
import { safeMath, toFixed } from '@xlb/utils'

type types = 'DELIVERY' | 'BASIC'

type Props = {
  visible?: boolean
  content: string
  dataSource: any
  type: 'DELIVERY' | 'RETAIL'
  order_unit_type?: types
  onOk?: () => void
  item_id?: string
  item_time?: string
  details?: any
  isRender?: boolean
}

const columnEmil: { [key in types]: XlbTableColumnProps<any>[] } = {
  DELIVERY: [
    { name: '配送单位', code: 'delivery_unit' },
    { name: '现配送价', code: 'price' }
  ],
  BASIC: [
    { name: '基本单位', code: 'basic_unit' },
    { name: '现配送价', code: 'basic_price' }
  ]
}

const PreView: FC<Props> = (props: Props) => {
  const {
    visible,
    content,
    dataSource,
    onOk,
    type,
    order_unit_type,
    item_id,
    item_time,
    details,
    isRender = false
  } = props

  const [columns, setColumns] = useState<XlbTableColumnProps<any>[]>([])
  const updateHiddenFields = (_columns: any[], hiddenFields: string[] = []) => {
    return _columns?.map((i: any) => {
      let item = { ...i }
      if (['标准售价', '售价2', '售价3', '售价4', '现配送价'].includes(i?.name)) {
        item = {
          ...i,
          hidden: !hiddenFields?.includes(i?.name)
        }
      }
      if (type === 'DELIVERY' && ['配送单位', '现配送价'].includes(i?.name)) {
        const column = order_unit_type ? columnEmil[order_unit_type] : []
        item = {
          ...item,
          ...(column?.[i?.name == '配送单位' ? 0 : 1] ?? {})
        }
        if (i.name === '现配送价') {
          item.code = isRender ? 'basic_price' : 'price'
        }
      }
      if (isRender) {
        switch (item?.code) {
          case 'store_retail_price':
          case 'store_retail_price_s':
          case 'store_retail_price_t':
          case 'store_retail_price_f':
            item.render = (value: any) => toFixed(value ?? 0, 'PRICE')
            break
          case 'price':
            item.render = (val: any, record: any) =>
              toFixed(safeMath.multiply(val ?? 0, record.delivery_ratio ?? 0), 'PRICE')
            break
          case 'basic_price':
            item.render = (val: any, record: any) => {
              const value =
                details?.order_unit_type === 'DELIVERY' && record.type !== 'RATIO'
                  ? Number(record.value) * Number(record.delivery_ratio)
                  : record.value
              const basic_price =
                details?.order_unit_type === 'DELIVERY'
                  ? Number(record.basic_price) * Number(record.delivery_ratio)
                  : record.basic_price
              if (record?.type == 'RATIO') {
                return toFixed(Number(basic_price) * (Number(value) * 0.01 + 1), 'PRICE')
              } else if (record?.type == 'MONEY') {
                return toFixed(Number(basic_price) + Number(value), 'PRICE')
              } else if (record?.type == 'FIXED_MONEY') {
                return toFixed(value, 'PRICE')
              }
            }
            break
        }
      }
      return item
    })
  }
  useEffect(() => {
    const tableColumnTmp = updateHiddenFields(tableColumn, details?.hidde_fields)
    const tableColumnTmp2 = updateHiddenFields(tableColumn1, details?.hidde_fields)
    setColumns(type === 'DELIVERY' ? tableColumnTmp2 : tableColumnTmp)
  }, [type, dataSource, details])
  if (typeof visible === 'boolean' && !visible) return null
  return (
    <>
      {typeof visible === 'boolean' ? (
        <XlbModal
          title={'公告详情'}
          visible={visible}
          onOk={onOk}
          okText={'关闭'}
          onCancel={onOk}
          width={1000}
          style={{ maxHeight: 'calc(100vh - 200px)' }}
          destroyOnClose
        >
          <div className={styles.titleBox}>
            <p className={styles.title}>
              {details.title}
              {supplierMessageTypes[details.type] || details.type ? (
                <span className={styles.announcementType}>
                  {supplierMessageTypes[details.type] || details.type}
                </span>
              ) : null}
            </p>
            <p className={styles.type}>
              {details?.create_business_dept
                ? `${businessDeptListType[details?.create_business_dept]}丨`
                : ''}
              {dateManipulation(details.create_time)}
              {details.create_time ? <>&nbsp;&nbsp;&nbsp;&nbsp;</> : null}
              <XlbIcon name="yuedu" />
              &nbsp;
              {details.read_count || 0}
            </p>
          </div>
          <div
            style={{ maxHeight: 'calc(100vh - 600px)', overflowY: 'auto' }}
            dangerouslySetInnerHTML={{ __html: content }}
          ></div>
          <div className={styles.tableTitle} style={{ padding: 0 }}>
            <div>调价单：{item_id || 'xxxxxxxxxxx'}</div>
            <div>生效日期：{item_time || 'xxxx-xx-xx'}</div>
          </div>
          <XlbTable
            style={{ marginTop: 12 }}
            dataSource={dataSource}
            columns={columns}
            total={dataSource?.length ?? 0}
            primaryKey="fid"
          />
        </XlbModal>
      ) : (
        <>
          <div className={styles.titleBox}>
            <p className={styles.title}>
              {details.title}
              <span className={styles.announcementType}>
                {supplierMessageTypes[details.type] || details.type}
              </span>
            </p>
            <p className={styles.type}>
              {details?.create_business_dept
                ? `${businessDeptListType[details?.create_business_dept]}丨`
                : ''}
              {dateManipulation(details.create_time)}&nbsp;&nbsp;&nbsp;&nbsp;
              <XlbIcon name="yuedu" />
              &nbsp;
              {details.read_count || 0}
            </p>
          </div>
          <div
            style={{ maxHeight: 'calc(100vh - 600px)', overflowY: 'auto' }}
            dangerouslySetInnerHTML={{ __html: content }}
          ></div>
          <div className={styles.tableTitle} style={{ padding: 0 }}>
            <div>调价单：{item_id || 'xxxxxxxxxxx'}</div>
            <div>生效日期：{item_time || 'xxxx-xx-xx'}</div>
          </div>
          <XlbTable
            style={{ marginTop: 12 }}
            dataSource={dataSource}
            columns={columns}
            total={dataSource?.length ?? 0}
            primaryKey="fid"
          />
        </>
      )}
    </>
  )
}

export default PreView

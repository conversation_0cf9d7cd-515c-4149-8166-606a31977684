import { XlbFetch as ErpRequest } from '@xlb/utils';

// 明细导出
const exportDetail = async (data: any) => {
  return await ErpRequest.post(
    process.env.BASE_URL + '/erp/hxl.erp.stockplan.export',
    { ...data },
  );
};
const save = async (data: any) => {
  return await ErpRequest.post('/erp/hxl.erp.stockplan.save', { ...data });
};
const itemExportDetail = async (data: any) => {
  return await ErpRequest.post(
    process.env.BASE_URL + '/erp/hxl.erp.stockplandetail.export',
    {
      ...data,
    },
  );
};

export default {
  exportDetail,
  itemExportDetail,
  save,
};

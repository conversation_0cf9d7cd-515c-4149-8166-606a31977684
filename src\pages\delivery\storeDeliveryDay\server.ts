import XlbFetch from '@/utils/XlbFetch';

export default class Api {
  //查询
  static getFind = async (data: any) => {
    return await XlbFetch('/erp/hxl.erp.storedeliveryday.find', {
      ...data,
    });
  };
  //新增
  static addItem = async (data: any) => {
    return await XlbFetch('/erp/hxl.erp.storedeliveryday.save', {
      ...data,
    });
  };
  //更新
  static updateItem = async (data: any) => {
    return await XlbFetch('/erp/hxl.erp.storedeliveryday.update', {
      ...data,
    });
  };
  //删除
  static delItem = async (data: any) => {
    return await XlbFetch('/erp/hxl.erp.storedeliveryday.delete', {
      ...data,
    });
  };
  //复制
  static readItem = async (data: any) => {
    return await XlbFetch('/erp/hxl.erp.storedeliveryday.read', {
      ...data,
    });
  };
  // static exportItem = (body, options) => {
  //   return request<any>('/erp/hxl.erp.storedeliveryday.store.export', {
  //     method: 'POST',
  //     headers: {
  //       'Content-Type': 'application/json',
  //     },
  //     data: body,
  //     ...(options || {}),
  //   });
  // };
}

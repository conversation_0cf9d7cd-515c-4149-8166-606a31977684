export const formList: any[] = [
  {
    label: '关键字',
    name: 'keyword',
    type: 'input',
    allowClear: true,
    check: true,
  },
];

export const tableList: any[] = [
  {
    name: '序号',
    code: '_index',
    width: 60,
    align: 'center',
  },
  {
    name: '门店代码',
    code: 'store_code',
    align: 'left',
    width: 120,
    features: { sortable: true },
  },
  {
    name: '门店名称',
    code: 'store_name',
    align: 'left',
    width: 200,
    features: { sortable: true },
  },
  {
    name: '货主范围',
    code: 'cargo_owner_range',
    align: 'left',
    width: 300,
    features: { sortable: true },
  },
  {
    name: '创建人',
    code: 'create_by',
    align: 'left',
    width: 150,
    features: { sortable: true },
  },
  {
    name: '创建时间',
    code: 'create_time',
    align: 'left',
    width: 162,
    features: { sortable: true, format: 'TIME' },
  },
  {
    name: '最后修改人',
    code: 'update_by',
    align: 'left',
    width: 100,
    features: { sortable: true },
  },
  {
    name: '最后修改时间',
    code: 'update_time',
    align: 'left',
    width: 162,
    features: { sortable: true, format: 'TIME' },
  },
];
export const cargoList = [
  {
    label: '组织',
    value: 'ORGANIZATION',
  },
  {
    label: '供应商',
    value: 'SUPPLIER',
  },
];
export const detailColumn = [
  {
    name: '序号',
    code: '_index',
    width: 60,
    align: 'center',
  },
  {
    name: '货主代码',
    code: 'owner_code',
    align: 'left',
    width: 130,
    features: { sortable: true },
  },
  {
    name: '组织代码/供应商代码',
    code: 'source_code',
    align: 'left',
    width: 175,
    features: { sortable: true },
  },
  {
    name: '组织名称/供应商名称',
    code: 'source_name',
    align: 'left',
    width: 245,
    render: (text: any, record: any) => {
      return (
        <div>
          {record.default_cargo_owner ? (
            <span
              style={{
                background: 'rgb(255, 111, 0)',
                color: 'rgb(255, 255, 255)',
                borderRadius: '3px',
                minWidth: '16px',
                minHeight: '16px',
                lineHeight: '16px',
                fontSize: 12,
                display: 'inline-block',
                textAlign: 'center',
              }}
            >
              默
            </span>
          ) : (
            ''
          )}
          {text}
        </div>
      );
    },
    features: { sortable: true },
  },
  {
    name: '货主类型',
    code: 'owner_type',
    align: 'left',
    width: 110,
    features: { sortable: true },
    render: (value: string) => {
      const curT = cargoList?.find((v) => v.value === value);
      return <div>{curT?.label}</div>;
    },
  },
];

import { columnWidthEnum } from '@/data/common/constant';
import { XlbModal, XlbTable } from '@xlb/components';
import { useEffect, useState } from 'react';
import { goodsType } from './../data';
import Api from './../server';
const Index = (props: {
  queryParams: any;
  visible: boolean;
  close: ((e: React.MouseEvent<HTMLButtonElement>) => void) | undefined;
}) => {
  const [dataSource, setDataSource] = useState([]);
  const [total, setTotal] = useState(0);
  const fetchDetail = (obj = {}) => {
    Api.fetchSummaryDetail({ ...props.queryParams, ...obj }).then((res) => {
      console.log(res?.data?.content);
      setDataSource(res?.data?.content || []);
      setTotal(res?.data?.total_elements || 0);
    });
  };
  useEffect(() => {
    console.log(1, props.queryParams);
    if (props.visible) {
      fetchDetail();
    } else {
      setDataSource([]);
      setTotal(0);
    }
  }, [props.visible]);
  return (
    <>
      <XlbModal
        width={900}
        onOk={props.close}
        onCancel={props.close}
        open={props.visible}
        isCancel={true}
      >
        <XlbTable
          style={{ height: '400px' }}
          total={total}
          onChangeSorts={(e) => {
            fetchDetail(e);
          }}
          onPaginChange={(page_number, page_size) => {
            fetchDetail({ page_number: page_number - 1, page_size });
          }}
          dataSource={dataSource}
          columns={[
            {
              name: '序号',
              code: '_index',
              width: columnWidthEnum.INDEX,
              align: 'center',
            },
            {
              name: '商品代码',
              code: 'code',
              width: 110,
              features: { sortable: true },
            },
            {
              name: '商品条码',
              code: 'bar_code',
              width: 130,
              features: { sortable: true },
            },
            {
              name: '商品名称',
              code: 'name',
              width: 180,
              features: { sortable: true },
            },
            {
              name: '分类名称',
              code: 'category_name',
              width: 120,
              features: { sortable: true },
            },
            {
              name: '商品类型',
              code: 'item_type',
              width: 110,
              render: (text) => {
                return goodsType?.find((i) => i.value === text)?.label || '';
              },
              features: { sortable: true },
            },
            {
              name: '采购规格',
              code: 'purchase_spec',
              width: 110,
              features: { sortable: true },
            },
          ]}
        ></XlbTable>
      </XlbModal>
    </>
  );
};
export default Index;

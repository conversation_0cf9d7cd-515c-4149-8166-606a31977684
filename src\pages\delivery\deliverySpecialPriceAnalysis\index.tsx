import { useBaseParams } from '@/hooks/useBaseParams';
import { hasAuth } from '@/utils/kit';
import { LStorage } from '@/utils/storage';
import {
  SearchFormType,
  XlbBasicForm,
  XlbButton,
  XlbForm,
  XlbIcon,
  XlbPageContainer,
} from '@xlb/components';
import { XlbFetch as ErpRequest } from '@xlb/utils';
import { message } from 'antd';
import dayjs from 'dayjs';
import { useRef, useState, type FC } from 'react';
import { tableList } from './data';

const { ToolBtn, Table, SearchForm } = XlbPageContainer;

const StrongDataAnalysis: FC = () => {
  const { enable_organization } = useBaseParams((state) => state);
  const [form] = XlbBasicForm.useForm();
  const [stateFormlist, setFormList] = useState<SearchFormType[]>([
    {
      width: 372,
      type: 'compactDatePicker',
      label: '日期范围',
      name: 'create_date',
      showTime: true,
      resultFormat: 'YYYY-MM-DD HH:mm',
      format: 'YYYY-MM-DD HH:mm',
      onChange: (value) => {
        console.log(value);
      },
      allowClear: false,
    },
    {
      label: '调出组织',
      name: 'org_ids',
      type: 'select',
      multiple: true,
      clear: true,
      hidden: !enable_organization,
      selectRequestParams: {
        url: '/erp-mdm/hxl.erp.org.find',
        responseTrans: {
          label: 'name',
          value: 'id',
        },
      },
    },
    {
      label: '调出门店',
      name: 'store_ids',
      type: 'inputDialog',
      clear: true,

      dialogParams: {
        type: 'store',
        dataType: 'lists',
        isLeftColumn: true,
        isMultiple: true,
        data: {
          status: true,
          center_flag: true,
        },
      },

      fieldNames: {
        idKey: 'id',
        nameKey: 'store_name',
      } as any,
    },
    {
      label: '促销组织',
      name: 'in_org_ids',
      clear: true,
      type: 'select',
      multiple: true,
      hidden: !enable_organization,
      selectRequestParams: {
        url: '/erp-mdm/hxl.erp.org.find',
        responseTrans: {
          label: 'name',
          value: 'id',
        },
      },
    },
    {
      label: '促销门店',
      name: 'in_store_ids',
      type: 'inputDialog',
      clear: true,

      dialogParams: {
        type: 'store',
        dataType: 'lists',
        isLeftColumn: true,
        isMultiple: true,
        data: {
          status: true,
        },
      },
      fieldNames: {
        idKey: 'id',
        nameKey: 'store_name',
      } as any,
    },
    {
      label: '商品档案',
      name: 'item_ids',
      type: 'inputDialog',
      allowClear: true,

      dialogParams: {
        type: 'goods',
        dataType: 'lists',
        isLeftColumn: true,
        isMultiple: true,
      },
    },
  ]);

  const tableRender = (item: any) => {
    switch (item.code) {
      case 'price':
      case 'original_price':
        item.render = (value: any, record: any, index: number) => {
          return (
            <div className="info overwidth">
              {hasAuth(['配送特价分析/配送价', '查询']) && value !== '****'
                ? value?.toFixed(4)
                : '****'}
            </div>
          );
        };
        break;
      case 'money':
      case 'original_money':
      case 'discount_money':
        item.render = (value: any, record: any, index: number) => {
          return (
            <div className="info overwidth">
              {hasAuth(['配送特价分析/配送价', '查询']) && value !== '****'
                ? value?.toFixed(2)
                : '****'}
            </div>
          );
        };
        break;
    }
    return item;
  };

  const tableRef = useRef(null);

  let refresh = () => {};
  //处理查询参数
  const prevPost = () => {
    const data = {
      ...form.getFieldsValue(),
      audit_date: form
        .getFieldValue('create_date')
        ?.map((item: string) => item.split(/\s+/)[0]),
      audit_time: form
        .getFieldValue('create_date')
        ?.map((item: string, index: number) =>
          index === 0
            ? `${item.split(/\s+/).pop()}:00.000`
            : `${item.split(/\s+/).pop()}:59.999`,
        ),
      org_ids: form.getFieldValue('org_ids'),
      store_ids: form.getFieldValue('store_ids'),
      in_org_ids: form.getFieldValue('in_org_ids'),
      in_store_ids: form.getFieldValue('in_store_ids'),
      item_ids: form.getFieldValue('item_ids'),
      company_id: LStorage.get('userInfo').company_id,
      operator_store_id: LStorage.get('userInfo').store_id,
    };
    return {
      ...data,
    };
  };

  // 导出
  const exportItem = async (requestForm: any, setLoading: Function, e: any) => {
    setLoading(true);
    const data = prevPost();
    const res = await ErpRequest.post(
      '/erp/hxl.erp.deliveryreport.specialpriceanalysis.export',
      { ...data },
    );

    if (res.code == 0) {
      message.success('导出受理成功，请前往下载中心查看');
    }
    setLoading(false);
  };

  // const onValuesChange = (e: any) => {
  //   if (e?.org_ids) {
  //     stateFormlist.find(
  //       (i) => i.name === 'store_ids',
  //     )!.dialogParams!.data!.org_ids = e?.org_ids ?? [];
  //     setFormList([...stateFormlist]);
  //     if (e?.org_ids?.length) {
  //       form.setFieldsValue({
  //         store_ids: [],
  //       });
  //     }
  //   }
  //   if (e?.in_org_ids) {
  //     stateFormlist.find(
  //       (i) => i.name === 'in_store_ids',
  //     )!.dialogParams!.data!.org_ids = e?.in_org_ids ?? [];
  //     setFormList([...stateFormlist]);
  //     if (e?.in_org_ids?.length) {
  //       form.setFieldsValue({
  //         in_store_ids: [],
  //       });
  //     }
  //   }
  // };

  return (
    <XlbPageContainer
      url={'/erp/hxl.erp.deliveryreport.specialpriceanalysis.page'}
      immediatePost={false}
      tableColumn={tableList.map((v) => tableRender(v))}
      prevPost={prevPost}
      footerDataSource={(data) => {
        const footerData = [
          {
            _index: '合计',
            quantity: data?.quantity?.toFixed(3),
            basic_quantity: data?.basic_quantity?.toFixed(3),
            money:
              hasAuth(['配送特价分析/配送价', '查询']) && data?.money !== '****'
                ? data?.money?.toFixed(2)
                : '****',
            original_money:
              hasAuth(['配送特价分析/配送价', '查询']) &&
              data?.original_money !== '****'
                ? data?.original_money?.toFixed(2)
                : '****',
            discount_money:
              hasAuth(['配送特价分析/配送价', '查询']) &&
              data?.discount_money !== '****'
                ? data?.discount_money?.toFixed(2)
                : '****',
          },
        ];
        return footerData;
      }}
    >
      <SearchForm>
        <XlbForm
          isHideDate
          formList={stateFormlist}
          form={form}
          // onValuesChange={onValuesChange}
          getFormRecord={refresh}
          initialValues={{
            create_date: [
              dayjs().format('YYYY-MM-DD 00:00'),
              dayjs().format('YYYY-MM-DD 23:59'),
            ],
            time_type: 0,
          }}
        />
      </SearchForm>
      <ToolBtn>
        {({
          fetchData,
          dataSource,
          requestForm,
          loading,
          setLoading,
        }: // setCurrentIndex,
        any) => {
          refresh = fetchData;

          return (
            <XlbButton.Group>
              <XlbButton
                loading={loading}
                label="查询"
                type="primary"
                onClick={() => {
                  fetchData();
                }}
                icon={<XlbIcon name="sousuo" />}
              />
              {hasAuth(['配送特价分析', '导出']) && (
                <XlbButton
                  label="导出"
                  type="primary"
                  loading={loading}
                  disabled={loading || !dataSource?.length}
                  onClick={(e) => exportItem(requestForm, setLoading, e)}
                  icon={<XlbIcon name="daochu" />}
                />
              )}
            </XlbButton.Group>
          );
        }}
      </ToolBtn>
      <Table
        selectMode={false}
        emptyCellHeight={300}
        bordered={false}
        primaryKey={'id'}
        ref={tableRef}
      />
    </XlbPageContainer>
  );
};

export default StrongDataAnalysis;
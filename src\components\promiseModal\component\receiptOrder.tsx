import { columnWidthEnum } from '@/data/common/constant';
import { hasAuth } from '@/utils/kit';
import safeMath from '@/utils/safeMath';
import toFixed from '@/utils/toFixed';
import {
  SearchFormType,
  XlbBasicForm,
  XlbForm,
  XlbTable,
  XlbTableColumnProps,
} from '@xlb/components';
import { Tabs, Tooltip } from 'antd';
import dayjs from 'dayjs';
import _ from 'lodash';
import { useEffect, useState } from 'react';
import { readReceiveOrder } from '../server';
const { TabPane } = Tabs;

const receiptStatus: any[] = [
  {
    label: '制单',
    value: 'INIT',
    type: 'info',
  },
  {
    label: '审核',
    value: 'AUDIT',
    type: 'warning',
  },
];
const formList: SearchFormType[] = [
  {
    type: 'input',
    disabled: true,
    name: 'supplier_name',
    label: '供应商',
  },
  {
    type: 'input',
    disabled: true,
    name: 'store_name',
    label: '收货门店',
  },
  {
    type: 'select',
    disabled: true,
    name: 'storehouse_name',
    label: '收货仓库',
    options: [],
  },
  {
    type: 'input',
    disabled: true,
    name: 'fid',
    label: '单据号',
  },
  {
    type: 'input',
    disabled: true,
    name: 'state',
    label: '单据状态',
  },
  {
    type: 'input',
    disabled: true,
    name: 'item_dept_names',
    label: '商品部门',
  },
  {
    type: 'datePicker',
    disabled: true,
    name: 'operate_date',
    label: '收货日期',
  },
  {
    type: 'datePicker',
    disabled: true,
    name: 'payment_date',
    label: '付款日期',
  },
  {
    type: 'input',
    disabled: true,
    name: 'purchase_order_fid',
    label: '采购订单',
  },
  {
    type: 'input',
    disabled: true,
    name: 'memo',
    label: '留言备注',
    width: 648,
  },
];
const oldItemTableListDetail: XlbTableColumnProps<any>[] = [
  {
    name: '序号',
    code: 'index',
    width: columnWidthEnum.INDEX,
    align: 'center',
  },
  {
    name: '操作',
    code: 'operation',
    align: 'center',
    width: 60,
  },
  {
    name: '商品代码',
    code: 'item_code',
    width: columnWidthEnum.ITEM_CODE,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '商品条码',
    code: 'item_bar_code',
    width: columnWidthEnum.ITEM_BAR_CODE,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '商品名称',
    code: 'item_name',
    width: 280,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '采购规格',
    code: 'item_spec',
    width: columnWidthEnum.ITEM_SPEC,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '单位',
    code: 'unit',
    width: 80,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '订购数量',
    code: 'purchase_quantity',
    width: 110,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '数量',
    code: 'quantity',
    width: 110,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '单价',
    code: 'price',
    width: 110,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '金额',
    code: 'money',
    width: 120,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '进项税率(%)',
    code: 'tax_rate',
    width: 120,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '单价(去税)',
    code: 'no_tax_price',
    width: 110,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '金额(去税)',
    code: 'no_tax_money',
    width: 120,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '运费',
    code: 'supplier_money',
    width: 110,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '基本单位',
    code: 'basic_unit',
    width: 110,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '换算率',
    code: 'ratio',
    width: 90,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '皮重',
    code: 'tare',
    width: 110,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '基本数量',
    code: 'basic_quantity',
    width: 110,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '基本单价',
    code: 'basic_price',
    width: 134,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '基本单价(去税)',
    code: 'no_tax_basic_price',
    width: 134,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '赠品单位',
    code: 'present_unit',
    width: 110,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '赠品数量',
    code: 'present_quantity',
    width: 110,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '生产日期',
    code: 'producing_date',
    width: columnWidthEnum.DATE,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '到期日期',
    code: 'expire_date',
    width: columnWidthEnum.DATE,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '批次号',
    code: 'batch_number',
    width: 140,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '保质期',
    code: 'period',
    width: 90,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '最近进价',
    code: 'latest_basic_price',
    width: 110,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '库存数量',
    code: 'stock_number',
    width: 120,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '压筐数量',
    code: 'frame_num',
    width: 110,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '压筐单价',
    code: 'frame_price',
    width: 110,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '压筐金额',
    code: 'frame_money',
    width: 120,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '备注',
    code: 'memo',
    width: columnWidthEnum.MEMO,
    features: { sortable: true },
    align: 'left',
  },
];
const oldItemTableList: XlbTableColumnProps<any>[] = [
  {
    name: '序号',
    code: 'index',
    width: columnWidthEnum.INDEX,
    align: 'center',
  },
  {
    name: '商品代码',
    code: 'item_code',
    width: columnWidthEnum.ITEM_CODE,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '商品条码',
    code: 'item_bar_code',
    width: columnWidthEnum.ITEM_BAR_CODE,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '商品名称',
    code: 'item_name',
    width: 280,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '采购规格',
    code: 'item_spec',
    width: columnWidthEnum.ITEM_SPEC,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '单位',
    code: 'unit',
    width: 80,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '订购数量',
    code: 'purchase_quantity',
    width: 110,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '数量',
    code: 'quantity',
    width: 110,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '单价',
    code: 'price',
    width: 110,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '金额(含税)',
    code: 'money',
    width: 120,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '进项税率(%)',
    code: 'tax_rate',
    width: 120,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '单价(去税)',
    code: 'no_tax_price',
    width: 110,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '金额(去税)',
    code: 'no_tax_money',
    width: 120,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '运费',
    code: 'supplier_money',
    width: 120,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '基本单位',
    code: 'basic_unit',
    width: 110,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '基本数量',
    code: 'basic_quantity',
    width: 110,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '基本单价',
    code: 'basic_price',
    width: 134,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '基本单价(去税)',
    code: 'no_tax_basic_price',
    width: 134,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '赠品单位',
    code: 'present_unit',
    width: 110,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '赠品数量',
    code: 'present_quantity',
    width: 110,
    features: { sortable: true },
    align: 'right',
  },
];

const ReceiptOrder = (props: any) => {
  const { fid } = props;
  const [formModel] = XlbBasicForm.useForm<any>();
  const [tableLoading, setTableLoading] = useState<any>(false);
  // 列表数据
  const [fidDataList, setFidDataList] = useState<any>([]);
  const [footerData, setFooterData] = useState<any[]>([]);
  // 表头
  const itemTableListDetail = oldItemTableListDetail?.map((v) => {
    if (v.code == 'index') {
      return { ...v, code: '_index' };
    }
    return v;
  });
  const itemTableList = oldItemTableList?.map((v) => {
    if (v.code == 'index') {
      return { ...v, code: '_index' };
    }
    return v;
  });
  const [itemArrdetail, setItemArrDetail] = useState<any[]>(
    JSON.parse(JSON.stringify(itemTableListDetail)),
  );

  const tableRender = (item: any) => {
    switch (item.code) {
      case 'stock_number':
        item.render = (value: any, record: any) => {
          return record.index === '合计' ? null : (
            <div className="overwidth">
              {' '}
              {toFixed(
                safeMath.divide(record.basic_stock_quantity, record.ratio),
                'QUANTITY',
                true,
              )}
            </div>
          );
        };
        break;
      case 'latest_basic_price':
        item.render = (value: any, record: any) => {
          return record.index === '合计' ? null : (
            <div className="overwidth">
              {hasAuth(['采购收货单/采购价', '查询'])
                ? (record.latest_basic_price * record.ratio).toFixed(4) != 'NaN'
                  ? toFixed(record.latest_basic_price * record.ratio, 'PRICE')
                  : ''
                : '****'}
            </div>
          );
        };
        break;
      case 'no_tax_money':
        item.render = (value: any) => {
          return (
            <div className="info overwidth">
              {hasAuth(['采购收货单/采购价', '查询'])
                ? toFixed(value, 'MONEY')
                : '****'}
            </div>
          );
        };
        break;
      case 'no_tax_price':
      case 'no_tax_basic_price':
        item.render = (value: any, record: any) => {
          return (
            <div className="info overwidth">
              {record.index === '合计'
                ? ''
                : hasAuth(['采购收货单/采购价', '查询'])
                  ? toFixed(value, 'PRICE')
                  : '****'}
            </div>
          );
        };
        break;
      case 'price':
        item.render = (value: any, record: any) => {
          let color = 'black';
          record.price > record.latest_basic_price * record.ratio
            ? (color = 'red')
            : null;
          record.price < record.latest_basic_price * record.ratio
            ? (color = 'green')
            : null;
          return (
            <div
              className="info overwidth"
              style={{
                color: hasAuth(['采购收货单/采购价', '查询']) ? color : 'black',
              }}
            >
              {record.index === '合计'
                ? ''
                : hasAuth(['采购收货单/采购价', '查询'])
                  ? toFixed(value, 'PRICE')
                  : '****'}
            </div>
          );
        };
        break;
      case 'frame_price':
      case 'frame_money':
        item.render = (value: any, record: any) => (
          <div className="info overwidth">
            {item.code !== 'frame_price'
              ? hasAuth(['采购收货单/采购价', '查询'])
                ? toFixed(value, 'PRICE')
                : '****'
              : record.index === '合计'
                ? ''
                : hasAuth(['采购收货单/采购价', '查询'])
                  ? toFixed(value, 'PRICE')
                  : '****'}
          </div>
        );
        break;
      case 'money':
        item.render = (value: any) => (
          <div className="info overwidth">
            {hasAuth(['采购收货单/采购价', '查询'])
              ? toFixed(value, 'MONEY')
              : '****'}
          </div>
        );
        break;
      case 'supplier_money':
        item.render = (value: any) => (
          <div className="info overwidth">
            {hasAuth(['采购收货单/采购价', '查询'])
              ? toFixed(value, 'MONEY')
              : '****'}
          </div>
        );
        break;
      case 'basic_price':
        item.render = (value: any, record: any) => (
          <div className="info overwidth">
            {record.index === '合计'
              ? ''
              : hasAuth(['采购收货单/采购价', '查询'])
                ? toFixed(value, 'PRICE')
                : '****'}
          </div>
        );
        break;
      case 'quantity':
      case 'purchase_quantity':
      case 'basic_quantity':
      case 'present_quantity':
      case 'frame_num':
        item.render = (value: any) => (
          <div className="info overwidth">
            {value ? toFixed(value, 'QUANTITY') : '0.00'}
          </div>
        );
        break;
      case 'memo':
        item.render = (value: any) => (
          <Tooltip title={value}>
            <div className="info overwidth">{value}</div>
          </Tooltip>
        );
        break;
      // default:
      //   return (item.render = (value: any, record: any, index: number) => (
      //     <div className="info overwidth">{value}</div>
      //   ))
    }
    return item;
  };

  const toDecimal = (x: any, fixed: any) => {
    let f = parseFloat(x);
    if (isNaN(f)) {
      return 0;
    }
    let mathNum = 0;
    switch (fixed) {
      case 2:
        mathNum = 100;
        break;
      case 3:
        mathNum = 1000;
        break;
      default:
        mathNum = 10000;
        break;
    }
    f = Math.round(x * mathNum) / mathNum;
    var s = f.toString();
    var rs = s.indexOf('.');
    if (rs < 0) {
      rs = s.length;
      s += '.';
    }
    while (s.length <= rs + fixed) {
      s += '0';
    }
    return s;
  };
  const openRefOrder = _.debounce(async (summary: boolean = false) => {
    setTableLoading(true);
    formModel.setFieldsValue({});
    const res = await readReceiveOrder({ fid: fid, summary: summary });
    if (res?.code == 0) {
      // 表单的值
      let obj: any = receiptStatus.find((v) => v.value === res.data.state);
      formModel.setFieldsValue({
        ...res?.data,
        state: obj.label,
        operate_date: res.data.operate_date
          ? dayjs(res.data.operate_date)
          : null,
        payment_date: res.data.payment_date
          ? dayjs(res.data.payment_date)
          : null,
      });
      // 列表的值
      let total = res.data.summary_details.map((v: any) => {
        let unit = { name: v.unit, ratio: v.ratio };
        v.ratio = v.ratio;
        v.no_tax_basic_price = v.no_tax_basic_price;
        let basic_unit = {
          name: v.basic_unit,
          ratio: v.basic_unit === v.unit ? unit.ratio : 1,
        };
        let delivery_unit = {
          name: v.delivery_unit,
          ratio: v.delivery_unit === v.unit ? unit.ratio : v.delivery_ratio,
        };
        let purchase_unit = {
          name: v.purchase_unit,
          ratio: v.purchase_unit === v.unit ? unit.ratio : v.purchase_ratio,
        };
        let stock_unit = {
          name: v.stock_unit,
          ratio: v.stock_unit === v.unit ? unit.ratio : v.stock_ratio,
        };
        let wholesale_unit = {
          name: v.wholesale_unit,
          ratio: v.wholesale_unit === v.unit ? unit.ratio : v.wholesale_ratio,
        };
        v.units = [
          ...new Set([
            JSON.stringify(basic_unit),
            JSON.stringify(delivery_unit),
            JSON.stringify(purchase_unit),
            JSON.stringify(stock_unit),
            JSON.stringify(wholesale_unit),
            JSON.stringify(unit),
          ]),
        ];
        return v;
      });
      let details = res.data.details.map((v: any) => {
        let unit = { name: v.unit, ratio: v.ratio };
        v.ratio = v.ratio;
        v.no_tax_basic_price = v.no_tax_basic_price;
        let basic_unit = {
          name: v.basic_unit,
          ratio: v.basic_unit === v.unit ? unit.ratio : 1,
        };
        let delivery_unit = {
          name: v.delivery_unit,
          ratio: v.delivery_unit === v.unit ? unit.ratio : v.delivery_ratio,
        };
        let purchase_unit = {
          name: v.purchase_unit,
          ratio: v.purchase_unit === v.unit ? unit.ratio : v.purchase_ratio,
        };
        let stock_unit = {
          name: v.stock_unit,
          ratio: v.stock_unit === v.unit ? unit.ratio : v.stock_ratio,
        };
        let wholesale_unit = {
          name: v.wholesale_unit,
          ratio: v.wholesale_unit === v.unit ? unit.ratio : v.wholesale_ratio,
        };
        v.units = [
          ...new Set([
            JSON.stringify(basic_unit),
            JSON.stringify(delivery_unit),
            JSON.stringify(purchase_unit),
            JSON.stringify(stock_unit),
            JSON.stringify(wholesale_unit),
            JSON.stringify(unit),
          ]),
        ];
        return v;
      });
      setFidDataList(summary ? total : details);
      // 计算合计
      const arr: any = JSON.parse(
        JSON.stringify(summary ? res.data.summary_details : res.data.details),
      );
      footerData[0] = {
        _index: '合计',
        frame_num: arr.reduce(
          (sum: any, v: any) => sum + Number(v.frame_num),
          0,
        ),
        money: hasAuth(['采购收货单/采购价', '查询'])
          ? toDecimal(
              arr.reduce((sum: any, v: any) => sum + Number(v.money), 0),
              2,
            )
          : '****',
        quantity: toDecimal(
          arr.reduce((sum: any, v: any) => sum + Number(v.quantity), 0),
          3,
        ),
        frame_money: hasAuth(['采购收货单/采购价', '查询'])
          ? toDecimal(
              arr.reduce((sum: any, v: any) => sum + Number(v.frame_money), 0),
              2,
            )
          : '****',
        no_tax_money: hasAuth(['采购收货单/采购价', '查询'])
          ? toDecimal(
              arr.reduce((sum: any, v: any) => sum + Number(v.no_tax_money), 0),
              2,
            )
          : '****',
        supplier_money: hasAuth(['采购收货单/采购价', '查询'])
          ? toDecimal(
              arr.reduce(
                (sum: any, v: any) => sum + Number(v.supplier_money),
                0,
              ),
              2,
            )
          : '****',
        basic_quantity: toDecimal(
          arr.reduce((sum: any, v: any) => sum + Number(v.basic_quantity), 0),
          3,
        ),
        tare: toDecimal(
          arr.reduce((sum: any, v: any) => sum + Number(v.tare), 0),
          3,
        ),
        present_quantity: toDecimal(
          arr.reduce((sum: any, v: any) => sum + Number(v.present_quantity), 0),
          3,
        ),
        purchase_quantity: toDecimal(
          arr.reduce(
            (sum: any, v: any) => sum + Number(v.purchase_quantity),
            0,
          ),
          3,
        ),
      };
      setFooterData([...footerData]);
    }
    setTableLoading(false);
  }, 50);

  useEffect(() => {
    console.log('useEffect props', props);
    openRefOrder();
    itemArrdetail.map((v) => {
      v.name === '操作' ? (v.hidden = true) : null;
    });
    itemArrdetail.map((v) => tableRender(v));
  }, []);

  return (
    <>
      <XlbForm
        style={{ marginTop: 15 }}
        formList={formList}
        form={formModel}
        isHideDate={true}
      />
      <Tabs
        defaultActiveKey={'detailTab'}
        onChange={(key) => {
          if (key === 'totalTab') {
            openRefOrder(true);
            setItemArrDetail(itemTableList);
          } else if (key === 'detailTab') {
            openRefOrder();
            setItemArrDetail(itemTableListDetail);
          }
        }}
      >
        <TabPane tab={'商品明细'} key={'detailTab'} />
        <TabPane tab={'商品汇总'} key={'totalTab'} />
      </Tabs>
      <XlbTable
        isLoading={tableLoading}
        style={{ height: 400, maxHeight: 400, overflowY: 'scroll' }}
        hideOnSinglePage={false}
        showSearch={true}
        columns={itemArrdetail}
        total={fidDataList?.length}
        dataSource={fidDataList}
        key={fidDataList?.length}
        footerDataSource={footerData}
        selectMode="single"
      ></XlbTable>
    </>
  );
};

export default ReceiptOrder;

import { useBaseParams } from '@/hooks/useBaseParams';
import { hasAuth, LStorage } from '@/utils';
import { FileAddOutlined } from '@ant-design/icons';
import type { SearchFormType, XlbTableColumnProps } from '@xlb/components';
import {
  XlbBasicData,
  XlbBasicForm,
  XlbButton,
  XlbDropdownButton,
  XlbForm,
  XlbIcon,
  XlbImportModal,
  XlbInputNumber,
  XlbPageContainer,
  XlbSelect,
  XlbShortTable,
  XlbTable,
  XlbTipsModal,
} from '@xlb/components';
import { safeMath, toFixed } from '@xlb/utils';
import { Form, message, Tabs } from 'antd';
import { cloneDeep, isNumber } from 'lodash';
import moment from 'moment';
import { useEffect, useState } from 'react';
import Api, { goodsType, stateEmail, wholesaleType } from '../data';
import styles from './index.less';

const { ToolBtn, SearchForm } = XlbPageContainer;

const WholesalePriceAdjustmentItem = (props) => {
  const { record, onBack } = props;
  const { enable_organization } = useBaseParams((state) => state);

  // table配置
  const wholesaleTypeRender = (text: any, record: any, wholesaleIndex = '') => {
    return record?._click && hasAuth(['批发调价/批发价', '编辑']) ? (
      <XlbSelect
        key={[text, record.id].join('_')}
        size="small"
        style={{ width: '100%' }}
        defaultValue={text ?? 3}
        onChange={(val) => onChangeWholesaleType(val, record, wholesaleIndex)}
        allowClear={false}
      >
        {wholesaleType.map((item: any) => (
          <XlbSelect.Option key={item.value} value={item.value}>
            <div style={{ minHeight: 22 }}>{item.label}</div>
          </XlbSelect.Option>
        ))}
      </XlbSelect>
    ) : hasAuth(['批发调价/批发价', '查询']) ? (
      <div>{wholesaleType.find((v) => v.value === text)?.label}</div>
    ) : (
      <div>{'****'}</div>
    );
  };
  const wholesaleValueRender = (
    text: any,
    record: any,
    wholesaleIndex = '',
  ) => {
    return record?._click &&
      record?.[`wholesale_type${wholesaleIndex}`] !== 3 &&
      record?.[`wholesale_type${wholesaleIndex}`] != null &&
      hasAuth(['批发调价/批发价', '编辑']) ? (
      <XlbInputNumber
        key={[text, record.id].join('_')}
        size="small"
        controls={false}
        style={{ width: '100%', height: 26 }}
        defaultValue={text ?? 0}
        step={0.01}
        stringMode
        min={0}
        max={99999}
        onChange={(val) => onChangeValue(val, record, wholesaleIndex)}
        onBlur={(e) => onBlur(e.target.value, record, wholesaleIndex)}
      />
    ) : hasAuth(['批发调价/批发价', '查询']) ? (
      <div>{toFixed(text ?? 0, 'MONEY')}</div>
    ) : (
      <div>{'****'}</div>
    );
  };
  const wholesalePriceRender = (text: any, record: any, wholesaleIndex = '') =>
    record?.[`wholesale_type${wholesaleIndex}`] === 3 ||
    record?.[`wholesale_type${wholesaleIndex}`] == null
      ? '——'
      : hasAuth(['批发调价/批发价', '查询'])
        ? toFixed(text ?? 0, 'PRICE')
        : '****';
  const clientColumns: XlbTableColumnProps<any>[] = [
    {
      name: '批发价类型',
      code: 'wholesale_type',
      width: 130,
      features: { sortable: true },
      render: (text: any, record: any) => wholesaleTypeRender(text, record),
    },
    {
      name: '数值',
      code: 'wholesale_value',
      width: 100,
      features: { sortable: true },
      render: (text: any, record: any) => wholesaleValueRender(text, record),
    },
    {
      name: '批发价',
      code: 'wholesale_price',
      width: 110,
      features: { sortable: true },
      render: (text: any, record: any) => wholesalePriceRender(text, record),
    },
  ];
  const storeColumns: XlbTableColumnProps<any>[] = [
    {
      name: '批发价类型1',
      code: 'wholesale_type',
      width: 130,
      features: { sortable: true },
      render: (text: any, record: any) => wholesaleTypeRender(text, record),
    },
    {
      name: '数值1',
      code: 'wholesale_value',
      width: 100,
      features: { sortable: true },
      render: (text: any, record: any) => wholesaleValueRender(text, record),
    },
    {
      name: '门店批发价1',
      code: 'wholesale_price',
      width: 110,
      features: { sortable: true },
      render: (text: any, record: any) => wholesalePriceRender(text, record),
    },
    {
      name: '批发价类型2',
      code: 'wholesale_type2',
      width: 130,
      features: { sortable: true },
      render: (text: any, record: any) =>
        wholesaleTypeRender(text, record, '2'),
    },
    {
      name: '数值2',
      code: 'wholesale_value2',
      width: 100,
      features: { sortable: true },
      render: (text: any, record: any) =>
        wholesaleValueRender(text, record, '2'),
    },
    {
      name: '门店批发价2',
      code: 'wholesale_price2',
      width: 110,
      features: { sortable: true },
      render: (text: any, record: any) =>
        wholesalePriceRender(text, record, '2'),
    },
    {
      name: '批发价类型3',
      code: 'wholesale_type3',
      width: 130,
      features: { sortable: true },
      render: (text: any, record: any) =>
        wholesaleTypeRender(text, record, '3'),
    },
    {
      name: '数值3',
      code: 'wholesale_value3',
      width: 100,
      features: { sortable: true },
      render: (text: any, record: any) =>
        wholesaleValueRender(text, record, '3'),
    },
    {
      name: '门店批发价3',
      code: 'wholesale_price3',
      width: 110,
      features: { sortable: true },
      render: (text: any, record: any) =>
        wholesalePriceRender(text, record, '3'),
    },
    {
      name: '批发价类型4',
      code: 'wholesale_type4',
      width: 130,
      features: { sortable: true },
      render: (text: any, record: any) =>
        wholesaleTypeRender(text, record, '4'),
    },
    {
      name: '数值4',
      code: 'wholesale_value4',
      width: 100,
      features: { sortable: true },
      render: (text: any, record: any) =>
        wholesaleValueRender(text, record, '4'),
    },
    {
      name: '门店批发价4',
      code: 'wholesale_price4',
      width: 110,
      features: { sortable: true },
      render: (text: any, record: any) =>
        wholesalePriceRender(text, record, '4'),
    },
  ];
  const tableColumns: XlbTableColumnProps<any>[] = [
    {
      name: '序号',
      code: '_index',
      width: 72,
      align: 'center',
    },
    {
      name: '操作',
      code: '_operator',
      width: 80,
    },
    {
      name: '商品代码',
      code: 'item_code',
      width: 120,
      features: { sortable: true },
    },
    {
      name: '商品条码',
      code: 'item_bar_code',
      width: 120,
      features: { sortable: true },
    },
    {
      name: '商品名称',
      code: 'item_name',
      width: 120,
      features: { sortable: true, showShort: true },
    },
    {
      name: '采购规格',
      code: 'purchase_spec',
      width: 110,
      features: { sortable: true },
    },
    {
      name: '商品类型',
      code: 'item_type',
      width: 120,
      features: { sortable: true },
      render(text: any) {
        return goodsType.find((v) => v.value === text)?.label;
      },
    },
    {
      name: '商品类别',
      code: 'category_name',
      width: 140,
      features: { sortable: true },
    },
    {
      name: enable_organization ? '组织采购价' : '档案采购价',
      code: 'purchase_price',
      width: 110,
      features: { sortable: true },
      align: 'right',
      render: (text: any) => {
        return (
          <div>
            {/* hasAuth(LStorage.get('userInfo'), ['门店公告', '编辑']) */}
            {hasAuth(['批发调价/档案采购价', '查询'])
              ? toFixed(text ?? 0, 'PRICE')
              : '****'}
          </div>
        );
      },
    },
    {
      name: '基本单位',
      code: 'unit',
      width: 112,
      hidden: true,
      features: { sortable: true },
    },
    {
      name: '批发单位',
      code: 'wholesale_unit',
      width: 112,
      features: { sortable: true },
    },
  ];

  // 调价规则弹窗table配置
  const dialogWholesalePriceRender = (
    text: any,
    record: any,
    wholesaleIndex = '',
  ) =>
    record?.[`wholesale_type${wholesaleIndex}`] === 3
      ? '——'
      : toFixed(text ?? 0, 'PRICE');

  const dialogClientColumns: XlbTableColumnProps<any>[] = [
    {
      name: '批发价类型',
      code: 'wholesale_type',
      width: 130,
      features: { sortable: true },
      render: (text: any, record: any) =>
        wholesaleType.find((v) => v.value === text)?.label,
    },
    {
      name: '数值',
      code: 'wholesale_value',
      width: 100,
      features: { sortable: true },
      render: (text: any, record: any) => toFixed(text ?? 0, 'MONEY'),
    },
    {
      name: '批发价',
      code: 'wholesale_price',
      width: 110,
      features: { sortable: true },
      render: (text: any, record: any) =>
        dialogWholesalePriceRender(text, record),
    },
  ];
  const dialogStoreColumns: XlbTableColumnProps<any>[] = [
    {
      name: '批发价类型1',
      code: 'wholesale_type',
      width: 130,
      features: { sortable: true },
      render: (text: any, record: any) =>
        wholesaleType.find((v) => v.value === text)?.label,
    },
    {
      name: '数值1',
      code: 'wholesale_value',
      width: 100,
      features: { sortable: true },
      render: (text: any, record: any) => toFixed(text ?? 0, 'MONEY'),
    },
    {
      name: '批发价1',
      code: 'wholesale_price',
      width: 110,
      features: { sortable: true },
      render: (text: any, record: any) =>
        dialogWholesalePriceRender(text, record),
    },
    {
      name: '批发价类型2',
      code: 'wholesale_type2',
      width: 130,
      features: { sortable: true },
      render: (text: any, record: any) =>
        wholesaleType.find((v) => v.value === text)?.label,
    },
    {
      name: '数值2',
      code: 'wholesale_value2',
      width: 100,
      features: { sortable: true },
      render: (text: any, record: any) => toFixed(text ?? 0, 'MONEY'),
    },
    {
      name: '批发价2',
      code: 'wholesale_price2',
      width: 110,
      features: { sortable: true },
      render: (text: any, record: any) =>
        dialogWholesalePriceRender(text, record, '2'),
    },
    {
      name: '批发价类型3',
      code: 'wholesale_type3',
      width: 130,
      features: { sortable: true },
      render: (text: any, record: any) =>
        wholesaleType.find((v) => v.value === text)?.label,
    },
    {
      name: '数值3',
      code: 'wholesale_value3',
      width: 100,
      features: { sortable: true },
      render: (text: any, record: any) => toFixed(text ?? 0, 'MONEY'),
    },
    {
      name: '批发价3',
      code: 'wholesale_price3',
      width: 110,
      features: { sortable: true },
      render: (text: any, record: any) =>
        dialogWholesalePriceRender(text, record, '3'),
    },
    {
      name: '批发价类型4',
      code: 'wholesale_type4',
      width: 130,
      features: { sortable: true },
      render: (text: any, record: any) =>
        wholesaleType.find((v) => v.value === text)?.label,
    },
    {
      name: '数值4',
      code: 'wholesale_value4',
      width: 100,
      features: { sortable: true },
      render: (text: any, record: any) => toFixed(text ?? 0, 'MONEY'),
    },
    {
      name: '批发价4',
      code: 'wholesale_price4',
      width: 110,
      features: { sortable: true },
      render: (text: any, record: any) =>
        dialogWholesalePriceRender(text, record, '4'),
    },
  ];
  const columns: XlbTableColumnProps<any>[] = [
    {
      name: '序号',
      code: '_index',
      width: 72,
      align: 'center',
    },
    {
      name: '商品代码',
      code: 'item_code',
      width: 120,
      features: { sortable: true },
    },
    {
      name: '商品条码',
      code: 'item_bar_code',
      width: 120,
      features: { sortable: true },
    },
    {
      name: '商品名称',
      code: 'item_name',
      width: 120,
      features: { sortable: true, showShort: true },
    },
    {
      name: ' 基本单位', // 6178370443
      code: 'unit',
      width: 100,
      hidden: false,
      features: { sortable: true },
    },
    {
      name: '批发单位', // 6178370443
      code: 'wholesale_unit',
      width: 100,
      hidden: true,
      features: { sortable: true },
    },
    {
      name: '档案采购价',
      code: 'purchase_price',
      width: 110,
      features: { sortable: true },
      align: 'right',
      render: (text: any) => {
        return toFixed(text ?? 0, 'PRICE');
      },
    },
  ];
  const [rowData, setRowData] = useState<any[]>([]);
  const formList1: SearchFormType[] = [
    {
      type: 'select',
      name: 'adjust_type',
      label: '调整类型',
      allowClear: false,
      defaultValue: 'STORE',
      disabled: rowData?.some((e) => e.item_id || e.id),
      options: [
        { label: '客户批发价', value: 'CLIENT' },
        { label: '门店批发价', value: 'STORE' },
      ],
    },
    {
      label: '批发客户',
      name: 'client_ids',
      type: 'inputDialog',
      disabled: rowData?.some((e) => e.item_id || e.id),
      allowClear: true,
      hidden: true,
      dialogParams: {
        data: { actived: 1 },
        type: 'wholesaler',
        dataType: 'lists',
        isLeftColumn: true,
        isMultiple: true,
      },
    },
    {
      label: '应用门店',
      name: 'store_ids',
      type: 'inputDialog',
      disabled: rowData?.some((e) => e.item_id || e.id),
      allowClear: true,
      fieldNames: {
        idKey: 'id',
        nameKey: 'store_name',
      } as any,
      dialogParams: {
        type: 'store',
        dataType: 'lists',
        isMultiple: true,
        resetForm: true,
        data: {
          filter_org_levels: [1, 3],
        },
        onOkBeforeFunction: (ids: any, list: any) => {
          return verifyStoreSelection(list);
        },
      },
      handleValueChange: (v: any, list: any[]) => {
        const flag = verifyStoreSelection(list, 'tips');
        if (flag) {
          form.setFieldsValue({
            org_id: list[0]?.enable_organization
              ? list[0].org_id
              : list[0]?.org_parent_id,
            org_name: list[0]?.enable_organization
              ? list[0].org_name
              : list[0]?.parent_org_name,
          });
          setTwoLevelOrgId(
            list[0]?.enable_organization
              ? list[0].org_id
              : list[0]?.org_parent_id,
          );
        } else {
          setTimeout(() => {
            form.setFieldValue('store_ids', v.slice(0, -1));
          }, 500);
        }
      },
    },
    {
      label: '组织(隐藏)',
      name: 'org_id',
      type: 'input',
      allowClear: false,
      disabled: true,
      hidden: true,
    },
    {
      label: '组织',
      name: 'org_name',
      type: 'input',
      allowClear: false,
      disabled: true,
      hidden: !enable_organization,
    },
    {
      label: '生效时间',
      name: 'effect_time',
      type: 'datePicker',
      disabledDate: (current) => current < moment().startOf('day'),
    },
    {
      label: '失效后价格',
      name: 'invalid_type',
      type: 'select',
      disabled: true,
      defaultValue: 0,
      options: [{ label: '按调整前价格', value: 0 }],
    },
    {
      label: '单据号',
      name: 'fid',
      type: 'input',
      allowClear: false,
      disabled: true,
    },
    {
      label: '单据状态',
      name: 'state',
      type: 'input',
      allowClear: false,
      disabled: true,
    },
    {
      label: '商品部门',
      name: 'item_dept_names',
      type: 'input',
      allowClear: false,
      disabled: true,
    },
    {
      label: '制单门店',
      name: 'store_name',
      type: 'input',
      allowClear: false,
      disabled: true,
    },
    {
      label: '失效时间',
      name: 'invalid_time',
      type: 'datePicker',
      disabledDate: (current) => current < moment().startOf('day'),
    },
    {
      label: '单位', // 6178370443 //
      name: 'unit_type',
      type: 'select',
      allowClear: false,
      defaultValue: 'WHOLESALE',
      options: [
        { label: '批发单位', value: 'WHOLESALE' },
        { label: '基本单位', value: 'BASIC' },
      ], //
    },
    {
      label: '留言备注',
      name: 'memo',
      type: 'input',
      width: 372,
      maxLength: 30,
      showCount: true,
      rows: 1,
    },
  ];
  const formList2: SearchFormType[] = [
    {
      label: '制单人',
      name: 'create_by',
      type: 'input',
      disabled: true,
    },
    {
      label: '制单时间',
      name: 'create_time',
      type: 'input',
      disabled: true,
    },
    {
      label: '审核人',
      name: 'audit_by',
      type: 'input',
      disabled: true,
    },
    {
      label: '审核时间',
      name: 'audit_time',
      type: 'input',
      disabled: true,
    },
    {
      label: '修改人',
      name: 'update_by',
      type: 'input',
      disabled: true,
    },
    {
      label: '修改时间',
      name: 'update_time',
      type: 'input',
      disabled: true,
    },
  ];
  const [form] = XlbBasicForm.useForm();
  const [branchForm] = Form.useForm();
  const [edit, setEdit] = useState(false);
  const [formList, setFormList] = useState<SearchFormType[]>(formList1);
  const [tableColumn, setTableColumn] =
    useState<XlbTableColumnProps<any>[]>(tableColumns);
  const [dialogForm, setDialogForm] = useState<any[]>([]);
  const [templateUrl, setTemplateUrl] = useState<string>('');
  const [importUrl, setImportUrl] = useState<string>('');
  const [dialogTableColumn, setDialogTableColumn] =
    useState<XlbTableColumnProps<any>[]>(columns);
  const [fid, setFid] = useState<any>();
  const [info, setInfo] = useState<any>({ state: 'INIT' });
  const [tabKey, setTabKey] = useState('1');
  const [twoLevelOrgId, setTwoLevelOrgId] = useState<any>('');

  const onChangeWholesaleType = (val: any, item: any, wholesaleIndex = '') => {
    item[`wholesale_type${wholesaleIndex}`] = val;
    item[`wholesale_type${wholesaleIndex}`] === 3 &&
      (item[`wholesale_value${wholesaleIndex}`] = 0);
    onChangeValue(
      item[`wholesale_value${wholesaleIndex}`],
      item,
      wholesaleIndex,
    );
  };

  const onChangeValue = (val: any, item: any, wholesaleIndex = '') => {
    item[`wholesale_value${wholesaleIndex}`] = val;
    const value =
      item[`wholesale_type${wholesaleIndex}`] == 2
        ? val
        : item[`wholesale_type${wholesaleIndex}`] == 1
          ? safeMath.add(item.purchase_price, val)
          : safeMath.multiply(
              item.purchase_price,
              safeMath.add(1, safeMath.divide(val, 100)),
            );
    item[`wholesale_price${wholesaleIndex}`] = value;
  };

  const onBlur = (val: any, item: any, wholesaleIndex = '') => {
    if (isNumber(val)) {
      onChangeValue(val, item, wholesaleIndex);
    }
  };

  const setTypeSetting = (isClient: boolean) => {
    const showColumns = isClient ? clientColumns : storeColumns;
    setTableColumn([...tableColumn, ...showColumns]);
    const showDialogColumns = isClient
      ? dialogClientColumns
      : dialogStoreColumns;
    setDialogTableColumn([...columns, ...showDialogColumns]);
    const dialogShowForm = showDialogColumns
      .filter((item) => item.code.includes('wholesale_type'))
      .map((item, index) => ({
        order: index ? index + 1 : '',
        label: item.name,
      }));
    setDialogForm(dialogShowForm);
    const downloadTemplateUrl = isClient
      ? '/erp/hxl.erp.wholesaleadjustorder.template.download'
      : '/erp/hxl.erp.storewholesaleadjustorder.template.download';
    setTemplateUrl(downloadTemplateUrl);
    const importDataUrl = isClient
      ? '/erp/hxl.erp.wholesaleadjustorder.import'
      : '/erp/hxl.erp.storewholesaleadjustorder.import';
    setImportUrl(importDataUrl);
  };

  const onValuesChange = (_val: any, values: any) => {
    const { adjust_type } = values;
    const isClient = adjust_type === 'CLIENT';
    setFormList(
      formList1.map((v: any) => {
        if (v.name === 'client_ids') {
          v.hidden = !isClient;
        }
        return v;
      }),
    );
    console.log(values, 'valuesvalues');

    values.unit_type = values.unit_type || 'WHOLESALE';
    const render = (item: any) => {
      switch (item.code) {
        case 'unit':
          item.hidden = values.unit_type === 'WHOLESALE';
          break;

        case 'wholesale_unit':
          item.hidden = values.unit_type === 'BASIC';
          break;
        default:
          item.hidden = false;
      }
    };
    tableColumn.map((v) => render(v));
    columns.map((v) => render(v));
    setTableColumn(() => tableColumn);
    setDialogTableColumn(() => columns);
    setTypeSetting(isClient);
  };
  const verifyStoreSelection = (list: any, type = '') => {
    const lv2s = list.filter((i: any) => i?.enable_organization);
    if (Array.isArray(lv2s) && lv2s.length > 1) {
      if (type === 'tips') {
        XlbTipsModal({
          tips: '请选择同一二级组织下的门店',
        });
        return false;
      } else {
        message.error('请选择同一二级组织下的门店');
      }
      return false;
    } else {
      let flag = true;
      list.reduce((pre: any, cur: any) => {
        if (
          pre?.org_id &&
          !pre?.enable_organization &&
          pre?.org_parent_id !== cur?.org_parent_id
        ) {
          flag = false;
        }
        return cur;
      }, {});
      if (!flag) {
        if (type === 'tips') {
          XlbTipsModal({
            tips: '请选择同一二级组织下的门店',
          });
        } else {
          message.error('请选择同一二级组织下的门店');
        }
        return false;
      }
    }
    return true;
  };
  const handleParams = () => {
    const {
      adjust_type,
      client_ids,
      store_ids,
      effect_time,
      invalid_time,
      memo,
      org_id,
      org_name,
    } = form.getFieldsValue();

    if (!rowData?.length) {
      message.error('请选择商品');
      return false;
    }
    if (adjust_type === 'CLIENT' && !client_ids?.length) {
      message.error('请选择批发客户');
      return false;
    }
    if (adjust_type === 'STORE' && !store_ids?.length) {
      message.error('请选择应用门店');
      return false;
    }
    if (!effect_time) {
      message.error('请选择生效时间');
      return false;
    }

    return {
      fid: fid !== -1 ? fid : null,
      adjust_type,
      memo,
      effect_time: effect_time
        ? moment(effect_time).startOf('day').format('YYYY-MM-DD HH:mm:ss')
        : null,
      invalid_time: invalid_time
        ? moment(invalid_time).startOf('day').format('YYYY-MM-DD HH:mm:ss')
        : null,
      adjust_clients:
        adjust_type === 'CLIENT' && form.getFieldValue('client_ids')?.length
          ? form
              .getFieldValue('client_ids')
              .map((v: string) => ({ client_id: v }))
          : null,
      adjust_stores: form.getFieldValue('store_ids')?.length
        ? form.getFieldValue('store_ids').map((v: string) => ({ store_id: v }))
        : null,
      details: rowData.filter((v) => v.item_id),
      org_id: org_id,
      org_name: org_name,
    };
  };

  const emptyPrice = Object.freeze({
    wholesale_type: 3,
    wholesale_value: 0,
    wholesale_price: 0,
    wholesale_type2: 3,
    wholesale_value2: 0,
    wholesale_price2: 0,
    wholesale_type3: 3,
    wholesale_value3: 0,
    wholesale_price3: 0,
    wholesale_type4: 3,
    wholesale_value4: 0,
    wholesale_price4: 0,
  });
  const onClick = async () => {
    const data = await XlbBasicData({
      type: 'goods',
      dataType: 'lists',
      isLeftColumn: true,
      isMultiple: true,
      primaryKey: 'id',
      url: '/erp/hxl.erp.wholesaleadjustorder.item.page',
      data: {
        enabled: true,
        store_id: LStorage.get('userInfo')?.store_id,
        two_level_org_id: twoLevelOrgId,
        unit_type: form.getFieldValue('unit_type') || 'WHOLESALE', //
      },
    });
    if (!data) return;

    const repeatData: any = [];
    const dataLi = data.map((v: any) => {
      if (rowData.every((i: any) => i.item_id !== v.id)) {
        return {
          ...v,
          item_id: v.id,
          item_code: v.code,
          item_bar_code: v.bar_code,
          item_name: v.name,
          ...emptyPrice,
          _click: false,
          _edit: false,
        };
      } else repeatData.push(`【${v.name}】`);
      return null;
    });
    if (repeatData?.length) {
      XlbTipsModal({
        tips: (
          <div>
            <div className="danger">
              以下商品已存在，不允许重复添加，系统已自动过滤！
            </div>
            <div>{repeatData.join('、')}</div>
          </div>
        ),
      });
    }
    setRowData([...rowData, ...dataLi.filter(Boolean)]);
  };

  const save = async () => {
    const params = handleParams();
    if (!params) return;
    params.unit_type = params.unit_type || 'WHOLESALE';
    const res =
      fid != -1 ? await Api.updateItem(params) : await Api.saveItem(params);
    if (res.code === 0) {
      message.success('操作成功');
      setFid(res.data.fid);
      getDetail(res.data.fid);
    }
  };

  const audit = async () => {
    const params = handleParams();
    if (!params) return;
    const res = await Api.auditItem(params);
    if (res.code === 0) {
      message.success('操作成功');
      getDetail(fid);
    }
  };

  const unAudit = async () => {
    const res = await Api.unAuditItem({ fid });
    if (res.code === 0) {
      message.success('操作成功');
      getDetail(fid);
    }
  };

  const pass = async () => {
    const res = await Api.passItem({ fid });
    if (res.code === 0) {
      message.success('操作成功');
      getDetail(fid);
    }
  };

  const deny = async () => {
    const res = await Api.denyItem({ fid });
    if (res.code === 0) {
      message.success('操作成功');
      getDetail(fid);
    }
  };

  const invalid = async () => {
    const res = await Api.invalidateItem({ fid });
    if (res.code === 0) {
      message.success('操作成功');
      getDetail(fid);
    }
  };

  const getDetail = async (_fid: any) => {
    const res = await Api.readItem({ fid: _fid });
    if (res.code === 0) {
      form.setFieldsValue({
        ...res.data,
        state: res.data?.state
          ? stateEmail.find((v: any) => v.value === res.data?.state)?.label
          : '',
        client_ids: res.data?.adjust_clients?.length
          ? res.data?.adjust_clients.map((v: any) => v.id)
          : [],
        client_ids_name: res.data?.adjust_clients?.length
          ? res.data?.adjust_clients
          : [],
        store_ids: res.data?.adjust_stores?.length
          ? res.data?.adjust_stores.map((v: any) => v.id)
          : [],
        store_ids_name: res.data?.adjust_stores?.length
          ? res.data?.adjust_stores
          : [],
        org_id: res.data?.two_level_org_id,
        org_name: res.data?.two_level_org_name,
      });
      setTwoLevelOrgId(res.data?.two_level_org_id);
      setInfo({ ...res.data });
      setRowData(
        res.data?.details?.map((v: any) => ({
          ...v,
          id: v.item_id,
          name: v.item_name,
          code: v.item_code,
          bar_code: v.item_bar_code,
        })),
      );
    }
  };

  const importItemConfirm = async (data: any) => {
    if (data?.details?.length) {
      const li = cloneDeep(rowData);
      const index = li.findIndex((v: any) => !v.item_id);
      const repeatData: any = [];
      const resData = data?.details
        ?.map((v: any) => {
          if (li.every((i: any) => i.item_id !== v.item_id)) {
            return {
              ...v,
              id: v.item_id,
              name: v.item_name,
              code: v.item_code,
              bar_code: v.item_bar_code,
            };
          } else repeatData.push(`【${v.item_name}】`);
          return null;
        })
        .filter(Boolean);
      if (repeatData?.length) {
        XlbTipsModal({
          tips: (
            <div>
              <div className="danger">
                以下商品已存在，不允许重复添加，系统已自动过滤！
              </div>
              <div>{repeatData.join('、')}</div>
            </div>
          ),
        });
      }
      if (index !== -1) {
        li.splice(index, 0, ...resData);
      } else {
        li.push(...resData);
      }
      setRowData(li);
    }
  };

  const dialogTypeChange = (val: any, order: number | string) =>
    val === 3 && branchForm.setFieldsValue({ [`value${order}`]: 0 });
  const batchUpdate = async () => {
    const data = await XlbTipsModal({
      title: '批量设置',
      tips: (
        <Form
          form={branchForm}
          className={styles.batchForm}
          style={{ margin: '24px 0 0' }}
        >
          {dialogForm.map((item) => {
            return (
              <Form.Item label={item.label} style={{ margin: 0 }}>
                <Form.Item
                  initialValue={2}
                  name={`type${item.order}`}
                  style={{ margin: 0 }}
                >
                  <XlbSelect
                    size="small"
                    style={{ width: '110px' }}
                    onChange={(val: any) => dialogTypeChange(val, item.order)}
                    allowClear={false}
                  >
                    {wholesaleType.map((selectItem) => (
                      <XlbSelect.Option
                        key={selectItem.value}
                        value={selectItem.value}
                      >
                        <div style={{ minHeight: 22 }}>{selectItem.label}</div>
                      </XlbSelect.Option>
                    ))}
                  </XlbSelect>
                </Form.Item>
                <Form.Item
                  dependencies={[`type${item.order}`]}
                  style={{ margin: 0, marginLeft: 5, width: 145 }}
                >
                  {({ getFieldValue }) => (
                    <Form.Item
                      name={`value${item.order}`}
                      initialValue={0}
                      rules={[
                        {
                          pattern: /^\d+(\.{0,1}\d+){0,1}$/,
                          message: '请输入正确格式的数据',
                          required: true,
                        },
                      ]}
                    >
                      <XlbInputNumber
                        controls={false}
                        step={0.01}
                        min={0}
                        max={99999}
                        disabled={getFieldValue(`type${item.order}`) === 3}
                        size="small"
                        style={{ height: 26, width: '100%' }}
                      />
                    </Form.Item>
                  )}
                </Form.Item>
              </Form.Item>
            );
          })}
        </Form>
      ),
      onOkBeforeFunction: () => {
        try {
          branchForm.validateFields();
        } catch (err) {
          return false;
        }
        const { value, value2, value3, value4 } =
          branchForm.getFieldsValue(true);
        const valueArr = [value, value2, value3, value4].slice(
          0,
          dialogForm.length,
        );
        if (valueArr.some((v) => v == null)) return false;
        return true;
      },
    });
    if (!data) return;
    showValue({ ...branchForm.getFieldsValue(true) });
    branchForm.resetFields();
  };

  const showValue = async (item: any) => {
    let data = cloneDeep(rowData);
    data = data.map((v: any) => {
      if (v.item_id) {
        dialogForm.map((formItem) => {
          const value =
            item[`type${formItem.order}`] === 3
              ? '——'
              : item[`type${formItem.order}`] == 2
                ? item[`value${formItem.order}`]
                : item[`type${formItem.order}`] == 1
                  ? safeMath.add(
                      v.purchase_price,
                      item[`value${formItem.order}`],
                    )
                  : safeMath.multiply(
                      v.purchase_price,
                      safeMath.add(
                        1,
                        safeMath.divide(item[`value${formItem.order}`], 100),
                      ),
                    );
          v[`wholesale_type${formItem.order}`] = item[`type${formItem.order}`];
          v[`wholesale_value${formItem.order}`] =
            item[`value${formItem.order}`];
          v[`wholesale_price${formItem.order}`] = value;
        });
      }
      return v;
    });
    const res = await XlbTipsModal({
      title: '提示',
      width: 800,
      tips: (
        <XlbTable
          columns={dialogTableColumn}
          total={data.filter((v) => v.item_id)?.length}
          dataSource={data.filter((v) => v.item_id)}
        />
      ),
    });
    if (!res) return;
    setRowData(data);
  };

  const Back = async (flag = false) => {
    onBack();
  };
  useEffect(() => {
    const { adjust_type } = form.getFieldsValue(true);
    const isClient = adjust_type === 'CLIENT';
    setTypeSetting(isClient);
    if (record?.fid) {
      setFid(record.fid);
      getDetail(record.fid);
    } else {
      setFid(-1);
    }
  }, []);

  return (
    <div
      style={{
        padding: 12,
        height: 'calc(100vh - 120px)',
        display: 'flex',
        flexDirection: 'column',
      }}
    >
      <XlbButton.Group>
        {hasAuth(['批发调价', '编辑']) && (
          <XlbButton
            disabled={info.state !== 'INIT'}
            icon={<XlbIcon name="baocun" />}
            type="primary"
            onClick={save}
          >
            保存
          </XlbButton>
        )}
        {hasAuth(['批发调价', '审核']) && (
          <XlbButton
            disabled={info.state !== 'INIT' || !info.fid}
            icon={<XlbIcon name="shenhe" />}
            type="primary"
            onClick={audit}
          >
            审核
          </XlbButton>
        )}
        {hasAuth(['批发调价', '反审核']) && (
          <XlbButton
            disabled={info.state !== 'AUDIT'}
            icon={<XlbIcon name="fanshenhe" />}
            type="primary"
            onClick={unAudit}
          >
            反审核
          </XlbButton>
        )}
        {hasAuth(['批发调价', '处理']) && (
          <XlbDropdownButton
            // @ts-ignore
            trigger="click"
            dropList={[
              {
                label: '处理通过',
                value: 'PASS',
                disabled: info.state !== 'AUDIT',
              },
              {
                label: '处理拒绝',
                value: 'DENY',
                disabled: info.state !== 'AUDIT',
              },
            ]}
            dropdownItemClick={(e) => {
              if (e === 0) {
                pass();
              } else {
                deny();
              }
            }}
            label={'处理'}
          />
        )}
        {hasAuth(['批发调价', '处理']) && (
          <XlbDropdownButton
            // @ts-ignore
            trigger="click"
            dropList={[
              {
                label: '失效',
                value: 'INVALID',
                disabled: info.state !== 'PASS' && info.state !== 'EFFECT',
              },
            ]}
            dropdownItemClick={() => invalid()}
            label={'业务操作'}
          />
        )}
        <XlbButton
          icon={<XlbIcon name="fanhui" />}
          type="primary"
          onClick={() => Back()}
        >
          返回
        </XlbButton>
      </XlbButton.Group>
      <Tabs activeKey={tabKey} onChange={setTabKey}>
        <Tabs.TabPane tab="基本信息" key="1" style={{ height: '100%' }}>
          <XlbForm
            isHideDate
            form={form}
            formList={formList}
            initialValues={{
              effect_time: moment().add(0, 'days').format('YYYY-MM-DD'),
              state: '制单',
              adjust_type: 'STORE',
              invalid_type: 0,
            }}
            onValuesChange={onValuesChange}
          />
        </Tabs.TabPane>
        <Tabs.TabPane tab="其他信息" key="2">
          <XlbForm isHideDate form={form} formList={formList2} />
        </Tabs.TabPane>
      </Tabs>
      <div style={{ display: 'flex', flexDirection: 'column', height: '100%' }}>
        <div
          style={{
            marginBottom: '12px',
            paddingLeft: '16px',
            paddingRight: '16px',
          }}
        >
          <XlbBasicForm form={form} onValuesChange={onValuesChange}>
            <XlbBasicForm.Item
              noStyle
              dependencies={['adjust_type', 'store_ids', 'client_ids']}
            >
              {({ getFieldValue }) => {
                return (
                  <XlbButton.Group>
                    {hasAuth(['批发调价', '编辑']) && (
                      <XlbButton
                        disabled={
                          info.state !== 'INIT' ||
                          (getFieldValue('adjust_type') == 'STORE' &&
                            !getFieldValue('store_ids')) ||
                          (getFieldValue('adjust_type') == 'CLIENT' &&
                            (!getFieldValue('client_ids') ||
                              !getFieldValue('store_ids')))
                        }
                        icon={<FileAddOutlined />}
                        type="primary"
                        onClick={onClick}
                      >
                        批量添加
                      </XlbButton>
                    )}
                    {hasAuth(['批发调价', '导入']) && (
                      <XlbButton
                        disabled={
                          info.state !== 'INIT' ||
                          (getFieldValue('adjust_type') == 'STORE' &&
                            !getFieldValue('store_ids')) ||
                          (getFieldValue('adjust_type') == 'CLIENT' &&
                            (!getFieldValue('client_ids') ||
                              !getFieldValue('store_ids')))
                        }
                        icon={<XlbIcon name="daoru" />}
                        type="primary"
                        onClick={async () => {
                          await XlbImportModal({
                            templateUrl: `${process.env.BASE_URL}${templateUrl}`,
                            importUrl: `${process.env.BASE_URL}${importUrl}?two_level_org_id=${form.getFieldValue('org_id')}`,
                            templateName: '门店导入模板',
                            params: {
                              two_level_org_id: twoLevelOrgId,
                            },
                            callback: (res) => {
                              importItemConfirm(res?.data);
                            },
                          });
                        }}
                      >
                        导入
                      </XlbButton>
                    )}
                    {hasAuth(['批发调价', '编辑']) && (
                      <XlbButton
                        disabled={
                          info.state !== 'INIT' ||
                          !rowData?.filter((v) => v.item_id)?.length
                        }
                        icon={<XlbIcon name="xiugai" />}
                        type="primary"
                        onClick={batchUpdate}
                      >
                        调价规则
                      </XlbButton>
                    )}
                  </XlbButton.Group>
                );
              }}
            </XlbBasicForm.Item>
          </XlbBasicForm>
        </div>
        <XlbShortTable
          showSearch
          style={{ flex: 1, paddingLeft: 16, paddingRight: 16 }}
          url={'/erp/hxl.erp.wholesaleadjustorder.item.page'}
          data={{
            store_id: LStorage.get('userInfo')?.store_id,
            two_level_org_id: twoLevelOrgId,
          }}
          onChangeData={(data) => {
            const originIds = rowData.map((v) => v.item_id);
            const getPrice = (v: any) =>
              originIds.includes(v.id) ? {} : emptyPrice;
            setRowData(
              data.map((v) => ({
                ...v,
                item_id: v.id,
                item_code: v.code,
                item_bar_code: v.bar_code,
                item_name: v.name,
                ...getPrice(v),
              })),
            );
          }}
          disabled={
            info.state !== 'INIT' ||
            (form.getFieldValue('adjust_type') == 'STORE' &&
              !form.getFieldValue('store_ids')) ||
            (form.getFieldValue('adjust_type') == 'CLIENT' &&
              (!form.getFieldValue('client_ids') ||
                !form.getFieldValue('store_ids')))
          }
          columns={tableColumn}
          dataSource={rowData}
          total={rowData?.length}
          selectMode="single"
          primaryKey="id"
          popoverPrimaryKey="id"
          repeatKey="name"
        />
      </div>
    </div>
  );
};

export default WholesalePriceAdjustmentItem;

import { columnWidthEnum } from '@/data/common/constant'

export const checkOptions = [
  {
    label: '停购商品',
    value: 'stop_purchase'
  },
  {
    label: '停售商品',
    value: 'stop_sale'
  },
  {
    label: '停止要货商品',
    value: 'stop_request'
  }
]

//经营类型
export const goodsType: any[] = [
  {
    label: '主规格商品',
    value: 'MAINSPEC'
  },
  {
    label: '多规格商品',
    value: 'MULTIPLESPEC'
  },
  {
    label: '标准商品',
    value: 'STANDARD'
  },
  {
    label: '组合商品',
    value: 'COMBINATION'
  },
  {
    label: '成分商品',
    value: 'COMPONENT'
  },
  {
    label: '制单组合',
    value: 'MAKEBILL'
  },
  {
    label: '分级商品',
    value: 'GRADING'
  }
]
//查询单位query_unit
export const queryUnit = [
  {
    label: '配送单位',
    value: 'DELIVERY'
  },
  {
    label: '基本单位',
    value: 'BASIC'
  }
]
// PURCHASE——采购单位,
// STOCK——库存单位,
// DELIVERY——配送单位,
// WHOLESALE——批发单位,
// BASIC——基本单位

export const types: any[] = [
  {
    label: '置空',
    value: 'NULL'
  },
  {
    label: '按比例',
    value: 'RATIO'
  },
  {
    label: '按金额',
    value: 'MONEY'
  },
  {
    label: '固定金额',
    value: 'FIXED_MONEY'
  }
]
export const types1: any[] = [
  {
    label: '　',
    value: 'NULL'
  },
  {
    label: '按比例',
    value: 'RATIO'
  },
  {
    label: '按金额',
    value: 'MONEY'
  },
  {
    label: '固定金额',
    value: 'FIXED_MONEY'
  }
]

export const formList: any[] = [
  {
    label: '组织',
    value: 'org_names',
    type: 'dialog',
    clear: true,
    check: true,
    options: [],
    hidden: true
  },
  {
    label: '门店',
    value: 'store_names',
    type: 'dialog',
    clear: false,
    check: true,
    rules: [{ required: true, message: '门店不能为空' }]
  },
  {
    label: '商品档案',
    value: 'item_names',
    type: 'dialog',
    clear: true,
    check: true
  },
  {
    label: '商品类别',
    value: 'item_category_names',
    type: 'dialog',
    clear: true,
    check: true,
    Change: () => {}
  },
  {
    label: '配送价类型',
    value: 'type',
    type: 'select',
    clear: true,
    check: true,
    options: types
  },
  {
    label: '经营范围',
    value: 'business_scope_names',
    type: 'dialog',
    clear: false,
    check: true
  },
  {
    label: '单位',
    value: 'unit_type',
    type: 'select',
    clear: false,
    check: true,
    initialValue: 'DELIVERY',
    options: queryUnit,
    Change: () => {}
  },
  {
    label: '商品类型',
    value: 'item_types',
    type: 'selects',
    clear: true,
    check: true,
    options: [
      {
        label: '主规格商品',
        value: 'MAINSPEC'
      },
      {
        label: '多规格商品',
        value: 'MULTIPLESPEC'
      },
      {
        label: '标准商品',
        value: 'STANDARD'
      },
      {
        label: '组合商品',
        value: 'COMBINATION'
      },
      {
        label: '成分商品',
        value: 'COMPONENT'
      },
      {
        label: '制单组合',
        value: 'MAKEBILL'
      }
    ]
  },
  {
    label: '价格比较',
    value: 'one',
    type: 'select',
    clear: false,
    check: true,
    initialValue: 'price',
    width: 90,
    options: [{ value: 'price', label: '配送价' }]
  },
  {
    label: '',
    value: 'two',
    type: 'select',
    clear: true,
    check: true,
    width: 55,
    options: [
      { value: '>', label: '>' },
      { value: '<', label: '<' },
      { value: '=', label: '=' },
      { value: '>=', label: '≥' },
      { value: '<=', label: '≤' }
    ]
  },
  {
    label: '',
    value: 'three',
    type: 'select',
    clear: false,
    check: true,
    width: 90,
    initialValue: 'purchasePrice',
    options: [{ value: 'purchasePrice', label: '采购价' }]
  },
  // {
  //   label: '过滤',
  //   value: 'isShow',
  //   type: 'select',
  //   colon: false,
  //   width: 80,
  //   initialValue: 1,
  //   options: [
  //     { value: 0, label: '仅显示' },
  //     { value: 1, label: '不显示' }
  //   ]
  // },
  // {
  //   label: '',
  //   colon: true,
  //   value: 'checkValue',
  //   type: 'checkGroup',
  //   options: checkOptions
  // }
]

export const tableList: any[] = [
  {
    name: '序号',
    code: 'index',
    width: 80,
    align: 'center'
  },
  {
    name: '组织',
    code: 'org_name',
    width: 140,
    hidden: true,
    features: { sortable: true }
  },
  {
    name: '门店',
    code: 'store_name',
    width: columnWidthEnum.STORE_NAME,
    features: { sortable: true }
  },
  {
    name: '商品代码',
    code: 'code',
    width: 110,
    features: { sortable: true }
  },
  {
    name: '商品条码',
    code: 'bar_code',
    width: 140,
    features: { sortable: true }
  },
  {
    name: '商品名称',
    code: 'name',
    width: 280,
    features: { sortable: true }
  },
  {
    name: '采购规格',
    code: 'purchase_spec',
    width: 160,
    features: { sortable: true }
  },
  {
    name: '商品类型',
    code: 'item_type',
    width: 100,
    features: { sortable: true }
  },
  {
    name: '商品类别',
    code: 'category_name',
    width: 140,
    features: { sortable: true }
  },
  {
    name: '单位',
    code: 'unit',
    width: 80,
    features: { sortable: true }
  },
  {
    name: '组织采购价',
    code: 'purchase_price',
    width: 120,
    align: 'right',
    features: { sortable: true }
  },
  {
    name: '组织配送价',
    code: 'delivery_price',
    width: 120,
    align: 'right',
    features: { sortable: true }
  },
  {
    name: '配送价类型',
    code: 'type',
    width: 130,
    features: { sortable: true }
  },
  {
    name: '数值',
    code: 'value',
    width: 100,
    align: 'right',
    features: { sortable: true }
  },
  {
    name: '配送价',
    code: 'price',
    width: 120,
    align: 'right',
    features: { sortable: true }
  },
  {
    name: '最后修改人',
    code: 'update_by',
    width: 150,
    features: { sortable: true }
  },
  {
    name: '最后修改时间',
    code: 'update_time',
    width: 180,
    features: { sortable: true }
  }
]

export const historyArr: any[] = [
  {
    name: '序号',
    code: '_index',
    width: columnWidthEnum.INDEX,
    align: 'center',
    lock: true
  },
  {
    name: '修改时间',
    code: 'create_time',
    width: columnWidthEnum.TIME,
    align: 'left'
  },
  {
    name: '修改人',
    code: 'create_by',
    width: columnWidthEnum.BY,
    align: 'left'
  },
  {
    name: '单位',
    code: 'unit',
    width: 100,
    align: 'left'
  },
  {
    name: '操作明细',
    code: 'memo',
    width: 324,
    align: 'left'
  }
]

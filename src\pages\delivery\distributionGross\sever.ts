import { XlbFetch } from '@xlb/utils';



// 门店汇总
const deliveryAnalyzePage = async (data: any) => {
  return await XlbFetch.post(
    '/erp/hxl.erp.deliveryreport.deliveryanalyze.page',
    { data }
  )
}
// 门店汇总
const getStoresummary = async (data: any) => {
  return await XlbFetch.post(
    '/erp/hxl.erp.deliveryreport.deliverygrossprofit.storesummary.page',
    { data }
  )
}

// 门店商品明细
const getStoreitemdetail = async (data: any) => {
  return await XlbFetch.post(
    '/erp/hxl.erp.deliveryreport.deliverygrossprofit.storeitemdetail.page',
    { data }
  )
}

// 门店商品汇总
const getStoreitemsummary = async (data: any) => {
  return await XlbFetch.post(
    '/erp/hxl.erp.deliveryreport.deliverygrossprofit.storeitemsummary.page',
    {
      data
    }
  )
}

// 门店明细
const getStoredetail = async (data: any) => {
  return await XlbFetch.post('/erp/hxl.erp.deliveryreport.deliverygrossprofit.storedetail.page', {
    data
  })
}

// 类别汇总
const getItemcategorysummary = async (data: any) => {
  return await XlbFetch.post(
    '/erp/hxl.erp.deliveryreport.deliverygrossprofit.itemcategorysummary.page',
    { data }
  )
}

//  账号管理仓库查询
const getStock = async (data: any) => {
  return await XlbFetch.post('/erp/hxl.erp.storehouse.store.find', { ...data })
}

// 获取商品标签
const getItemLabelRequest = async (data: any) => {
  return await XlbFetch.post('/erp-mdm/hxl.erp.itemlabel.find', { data })
}

// 获取业务核算分类
export const queryFinanceCode = async (data: any) => {
  return await XlbFetch.post('/erp-mdm/hxl.erp.settlementcategory.center.find', { data })
}

export default {
  getStoresummary,
  getStoreitemdetail,
  getStoreitemsummary,
  getStoredetail,
  getItemcategorysummary,
  getStock,
  getItemLabelRequest,
  deliveryAnalyzePage,
  queryFinanceCode,
}
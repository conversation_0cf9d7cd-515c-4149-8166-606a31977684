import { XlbFetch } from '@xlb/utils';

// 获取商品汇总数据
export const basketonway = async (data: any) => {
  return await XlbFetch.post(
    '/erp/hxl.erp.deliveryreport.basketonway.summary.page',
    data,
  );
};

// 获取明细数据
export const detailData = async (data: any) => {
  return await XlbFetch.post(
    '/erp/hxl.erp.deliveryreport.basketonway.detail.page',
    data,
  );
};

// 物资进出单明细
export const basketorder = async (data: any) => {
  return await XlbFetch.post(
    '/erp/hxl.erp.basketorder.read',
    data,
  );
};
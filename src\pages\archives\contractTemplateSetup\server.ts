import { XlbFetch } from "@xlb/utils";
const API = {
  
  fentchContractEnum : (data= {}) =>{
    return XlbFetch.post('/erp/hxl.erp.contract.enum',data)
  },
  // 腾讯模板id 集合
  fentchCquire : (data= {channel_template_id:'yDtBxUUckpxwkqvvUEkqQVkBxN17OwB0'}) =>{
    return XlbFetch.post('/erp/hxl.erp.contract.template.acquire',data)
  },
  fetchSave: (data={},params={}) =>{
    const url = params?.id ? '/erp/hxl.erp.contract.template.update' :'/erp/hxl.erp.contract.template.save' 
    return XlbFetch.post(url,{...data,id:params.id})

  },
  fentchDel : (data= {}) =>{
    return XlbFetch.post('/erp/hxl.erp.contract.template.delete',data)
  },

  readItem :  (data: any) => {
    return  XlbFetch.post('/erp/hxl.erp.contract.template.read', {
      ...data,
    })
  },
  
  checkTemplate :  (data: any) => {
    return  XlbFetch.post('/erp/hxl.erp.contract.template.check', {
      ...data,
    })
  }
  ///erp/hxl.erp.contract.template.acquire
}
export default API
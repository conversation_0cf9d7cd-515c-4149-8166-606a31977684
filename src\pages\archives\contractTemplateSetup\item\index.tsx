import {
  XlbBasicForm,
  XlbButton,
  XlbIcon,
  XlbInput,
  XlbSelect,
  XlbSteps,
  XlbStepsProps,
} from '@xlb/components';
import { Flex } from 'antd';
import {
  forwardRef,
  Ref,
  SetStateAction,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
} from 'react';
import API from './../server';
import generateChineseNumberMap from './get_index_num';
const chineseNumberMap = generateChineseNumberMap();
const Index = forwardRef(
  (
    props: { params: any; close?: any; getData?: any; setStep: Function },
    ref: Ref<unknown> | undefined,
  ) => {
    const { params } = props;
    console.log(params);
    const tecent_template_id = useRef(null);

    const [current, setCurrent] = useState(0);
    const items: XlbStepsProps['items'] = [
      {
        title: (
          <span style={{ fontSize: '14px', color: '#1677ff' }}>维护模板ID</span>
        ),
        // subTitle: '维护模板ID',
      },
      {
        title: (
          <span
            style={{
              fontSize: '14px',
              color: current === 1 ? '#1677ff' : '#666',
            }}
          >
            设置签署信息
          </span>
        ),

        // subTitle: '设置签署信息',
      },
    ];
    const [form] = XlbBasicForm.useForm();
    const [erp_options, set_erp_options] = useState([]);
    const [sign_recipient_json, set_sign_recipient_json] = useState([]);
    const [component_meta_json, set_component_meta_json] = useState([]);
    const next = async () => {
      try {
        await form.validateFields();
        const { channel_template_id, name } = form.getFieldsValue(true);
        const obj = params.id ? { id: params.id } : {};
        const result = await API.checkTemplate({
          ...obj,
          channel_template_id,
          name,
        });
        if (result.code === 0) {
          API.fentchCquire(form.getFieldsValue(true)).then((res) => {
            if (res?.code === 0) {
              setCurrent(1);
              set_sign_recipient_json(res?.data?.sign_recipient_json || []);
              set_component_meta_json(res?.data?.component_meta_json || []);
              tecent_template_id.current = res?.data?.tecent_template_id;
            }
          });
        }
        if (!params.id) {
          form.setFieldValue('list', [{}, {}, {}]); //
        }
      } catch (err: any) {
        return false;
      }
    };
    const onCancel = () => {
      setCurrent(0);
    };
    const save = async () => {
      const params = {
        channel_template_id: '',
        // "code": "",
        component_meta_json: [
          // {
          //   "component_name": "",
          //   "component_type": "",
          //   "sign_type": ""
          // }
        ],
        // "contract_type": "",
        // "module": "",
        name: '',
        sign_recipient_json: [
          // {
          //   "biz_sign_type": "",
          //   "recipient_name": "",
          //   "recipient_type": "",
          //   "sign_type": ""
          // }
        ],
        sign_types: [],
        tecent_template_id: tecent_template_id.current,
      };

      try {
        await form.validateFields();

        const { channel_template_id, name, list } = form.getFieldsValue(true);
        const json = { channel_template_id, name };
        Object.assign(params, { channel_template_id, name });
        list.forEach(
          (_: { name: any; component_name: any; recipient_name: any }) => {
            params.sign_types.push(_.name);
            component_meta_json.forEach((item) => {
              _?.component_name?.forEach((element) => {
                if (element === item.component_name) {
                  params.component_meta_json.push({
                    ...item,
                    sign_type: _.name,
                  });
                }
              });
            });

            sign_recipient_json.forEach((child) => {
              if (_.recipient_name === child.recipient_name) {
                params.sign_recipient_json.push({
                  ...child,
                  biz_sign_type: _.name,
                });
              }
            });
          },
        );
        API.fetchSave(params, props.params).then((res) => {
          if (res?.code === 0) {
            props.close();
            props.getData();
          }
        });
      } catch (err: any) {
        return false;
      }
    };
    useEffect(() => {
      if (params.name) {
        const { channel_template_id, name } = params;
        form.setFieldsValue({ channel_template_id, name });
        API.readItem(params).then((res) => {
          const list = [];
          res?.data?.sign_types?.forEach((element) => {
            const obj = { name: element, component_name: [] };
            res?.data?.sign_recipient_json?.forEach((_) => {
              if (_.biz_sign_type === element) {
                obj.recipient_name = _.recipient_name;
              }
            });
            res?.data?.component_meta_json?.forEach((_) => {
              if (_.sign_type === element) {
                obj.component_name.push(_.component_name);
              }
            });
            list.push(obj);
          });
          if (list.length === 0) {
            form.setFieldValue('list', [{}, {}, {}]);
          } else {
            form.setFieldValue('list', list);
          }
        });
      } else {
        form.setFieldsValue({
          channel_template_id: undefined,
          name: undefined,
        });
      }
      API.fentchCquire().then((res: any) => {
        set_sign_recipient_json(res?.data?.sign_recipient_json || []);
        set_component_meta_json(res?.data?.component_meta_json || []);
        tecent_template_id.current = res?.data?.tecent_template_id;
      });
      API.fentchContractEnum({ type: 'CONTRACT_SIGN_TYPE' }).then(
        (res: { code: number; data: SetStateAction<never[]> }) => {
          if (res?.code === 0) {
            set_erp_options(res?.data?.data || []);
          }
        },
      );
    }, []);
    const changeCurrent = () => props.setStep(current);
    useEffect(() => {
      changeCurrent();
    }, [current]);
    useImperativeHandle(ref, () => ({
      next,
      onCancel,
      save,
      changeCurrent,
    }));
    return (
      <>
        <div style={{ marginBottom: 20 }}>
          {/* <XlbBlueBar title="签署模板"></XlbBlueBar> */}
          <section style={{ justifyContent: 'center', marginTop: 30 }}>
            <div>
              <div style={{ padding: '0 230px' }}>
                <XlbSteps items={items} current={current} />
              </div>
              {/* <Steps items={items} current={current} /> */}

              {current === 0 ? (
                <XlbBasicForm
                  layout={'horizontal'}
                  style={{
                    marginTop: 20,
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                  }}
                  form={form}
                >
                  <XlbBasicForm.Item
                    name="channel_template_id"
                    label="腾讯模板ID"
                    rules={[{ required: true, message: '请输入腾讯模板ID' }]}
                  >
                    <XlbInput
                      placeholder="请输入"
                      maxLength={32}
                      width={250}
                    ></XlbInput>
                  </XlbBasicForm.Item>
                  <XlbBasicForm.Item
                    name="name"
                    label="合同模板名称"
                    rules={[{ required: true, message: '请输入合同模板名称' }]}
                  >
                    <XlbInput
                      placeholder="请输入"
                      maxLength={30}
                      width={250}
                    ></XlbInput>
                  </XlbBasicForm.Item>
                </XlbBasicForm>
              ) : (
                <>
                  <XlbBasicForm
                    style={{ marginTop: 20 }}
                    form={form}
                    initialValues={{
                      list: [
                        {
                          name: undefined,
                          component_name: undefined,
                          recipient_name: undefined,
                        },
                      ],
                    }}
                  >
                    {/* yDtBxUUckpxwkqvvUEkqQVkBxN17OwB0 */}
                    <XlbBasicForm.Item shouldUpdate>
                      {(form) => {
                        const data = form.getFieldValue('list');
                        return (
                          <XlbBasicForm.List name="list">
                            {(fields, actions, meta) => {
                              console.log(fields, actions, meta);
                              return (
                                <div>
                                  {fields.map((item, index) => {
                                    console.log(item);
                                    return (
                                      <>
                                        <div
                                          key={params}
                                          style={{
                                            fontSize: '14px',
                                            fontWeight: 600,
                                            color: '#1F2126',
                                            marginBottom: '12px',
                                          }}
                                        >
                                          签署方{chineseNumberMap[index + 1]}
                                        </div>
                                        <Flex gap={4}>
                                          <XlbBasicForm.Item
                                            // label={data[index]?.label}
                                            name={[index, 'name']}
                                            rules={[
                                              {
                                                required: true,
                                                message: '请选择ERP参数',
                                              },
                                            ]}
                                          >
                                            <XlbSelect
                                              onChange={(e, opt) => {
                                                console.log(e, opt);
                                              }}
                                              placeholder="请选择ERP参数"
                                            >
                                              {erp_options?.map((_: any) => (
                                                <XlbSelect.Option key={_.code}>
                                                  {_.name}
                                                </XlbSelect.Option>
                                              ))}
                                            </XlbSelect>
                                          </XlbBasicForm.Item>
                                          <XlbBasicForm.Item
                                            name={[index, 'component_name']}
                                            rules={[
                                              {
                                                required: true,
                                                message: '请选择合同签署控件',
                                              },
                                            ]}
                                          >
                                            <XlbSelect
                                              mode="multiple"
                                              placeholder="请选择合同签署控件"
                                            >
                                              {component_meta_json?.map(
                                                (_: any) => (
                                                  <XlbSelect.Option
                                                    key={_.component_name}
                                                  >
                                                    {_.component_name}
                                                  </XlbSelect.Option>
                                                ),
                                              )}
                                            </XlbSelect>
                                          </XlbBasicForm.Item>
                                          <XlbBasicForm.Item
                                            name={[index, 'recipient_name']}
                                            rules={[
                                              {
                                                required: true,
                                                message: '请选择合同签署节点',
                                              },
                                            ]}
                                          >
                                            <XlbSelect placeholder="请选择合同签署节点">
                                              {sign_recipient_json?.map(
                                                (_: any) => (
                                                  <XlbSelect.Option
                                                    key={_.recipient_name}
                                                  >
                                                    {_.recipient_name}
                                                  </XlbSelect.Option>
                                                ),
                                              )}
                                            </XlbSelect>
                                          </XlbBasicForm.Item>
                                          <XlbIcon
                                            style={{ marginTop: 5 }}
                                            type="primary"
                                            color={'#3d66fe'}
                                            onClick={() => {
                                              actions.remove(index);
                                            }}
                                            name="shanchu"
                                          ></XlbIcon>
                                        </Flex>
                                      </>
                                    );
                                  })}
                                  {data.length < sign_recipient_json.length ? (
                                    <div>
                                      <XlbButton
                                        type="text"
                                        onClick={() => actions.add({})}
                                        icon={
                                          <XlbIcon
                                            type="primary"
                                            color={'#3d66fe'}
                                            name="jia"
                                          ></XlbIcon>
                                        }
                                      >
                                        增加签署方
                                      </XlbButton>
                                    </div>
                                  ) : null}
                                </div>
                              );
                            }}
                          </XlbBasicForm.List>
                        );
                      }}
                    </XlbBasicForm.Item>
                  </XlbBasicForm>
                </>
              )}
              {/* <div style={{ marginTop: 100, textAlign: 'center' }}>
                <XlbButton.Group>
                  <XlbButton
                    onClick={() => {
                      setCurrent(1);
                      props.close();
                    }}
                    type="primary"
                  >
                    取消
                  </XlbButton>
                  {current === 2 ? (
                    <>
                      <XlbButton onClick={onCancel} type="primary">
                        上一步
                      </XlbButton>
                      <XlbButton onClick={save}>保存</XlbButton>
                    </>
                  ) : (
                    <XlbButton onClick={next}>下一步</XlbButton>
                  )}
                </XlbButton.Group>
              </div> */}
            </div>
          </section>
        </div>
      </>
    );
  },
);
export default Index;

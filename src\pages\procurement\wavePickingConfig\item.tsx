import { columnWidthEnum } from '@/constants';
import { hasAuth } from '@/utils/kit';
import NiceModal from '@ebay/nice-modal-react';
import {
  ContextState,
  XlbBasicForm,
  XlbButton,
  XlbForm,
  XlbIcon,
  XlbPageContainer,
  XlbSwitch,
  XlbTableColumnProps,
} from '@xlb/components';
import { message } from 'antd';
import { AddItemModal } from './component/addItemModal';
import { ENABLE_LIST, itemFormlist } from './data';
import { updateWavePickingDetailConfig } from './server';

const Item = (props: any) => {
  const { record, onBack } = props;
  // from
  const [form] = XlbBasicForm.useForm();
  const prevPost = () => ({
    ...form.getFieldsValue(true),
    config_id: record?.id,
  });

  // table 配置
  const tableColumn: XlbTableColumnProps<any>[] = [
    {
      name: '序号',
      code: '_index',
      width: columnWidthEnum.INDEX,
      align: 'center',
    },
    {
      name: '供应商',
      code: 'supplier_name',
      width: 120,
      features: { sortable: true },
      render: (text: any, record: any, operate: any) => {
        return (
          <div
            className="xlb-table-clickBtn"
            onClick={() => {
              NiceModal.show(AddItemModal, {
                title: '波次明细配置',
                type: 'supplier',
                config_id: record?.id,
                updateData: record,
                fetchData: operate?.fetchData,
              });
            }}
          >
            {text}
          </div>
        );
      },
    },
    {
      name: '波次配置',
      code: 'time',
      width: 160,
      align: 'center',
      render: (_text: any, record: any) => {
        return (
          <div>
            {record?.config?.times
              .sort((a: number, b: number) => a - b)
              ?.join(',')}
          </div>
        );
      },
    },
    {
      name: '状态',
      code: 'enable',
      width: 160,
      align: 'center',
      render: (text: any, record: any, operate: any) => {
        return (
          <div className="v-flex">
            <XlbSwitch
              value={text === 1}
              loading={record?._loading}
              disabled={!hasAuth(['波次配置', '编辑'], 'ERP')}
              onChange={async (e) => {
                record._loading = true;
                const res = await updateWavePickingDetailConfig({
                  ...record,
                  enable: Number(e),
                });
                if (res.code === 0) {
                  message.success('更新成功');
                  record.enable = Number(e);
                }
                operate?.fetchData?.();
              }}
            />
            <span style={{ marginLeft: 8 }}>
              {ENABLE_LIST.find((item) => item.value === text)?.label}
            </span>
          </div>
        );
      },
    },
    {
      name: '最新更新人',
      code: 'update_by',
      width: 160,
    },
    {
      name: '最近更新时间',
      code: 'update_time',
      width: columnWidthEnum.TIME,
      features: { sortable: true, format: 'TIME' },
    },
  ];

  const addItem = async (fetchData: () => void) => {
    await NiceModal.show(AddItemModal, {
      title: '波次明细配置',
      type: 'supplier',
      config_id: record?.id,
      fetchData,
    });
  };

  return (
    <XlbPageContainer
      url={'/erp/hxl.erp.wavepickingconfigdetail.find'}
      tableColumn={tableColumn}
      immediatePost
      prevPost={prevPost}
    >
      <XlbPageContainer.ToolBtn showColumnsSetting>
        {({ fetchData, loading }: ContextState<any>) => {
          return (
            <XlbButton.Group>
              {hasAuth(['波次配置', '查询'], 'ERP') && (
                <XlbButton
                  label="查询"
                  type="primary"
                  loading={loading}
                  onClick={() => fetchData()}
                  icon={<XlbIcon name="sousuo" />}
                />
              )}
              {hasAuth(['波次配置', '编辑'], 'ERP') && (
                <XlbButton
                  label="新增"
                  type="primary"
                  icon={<XlbIcon name="jia" />}
                  onClick={() => addItem(fetchData)}
                />
              )}
              <XlbButton
                label="返回"
                type="primary"
                icon={<XlbIcon name="fanhui" />}
                onClick={() => onBack()}
              />
            </XlbButton.Group>
          );
        }}
      </XlbPageContainer.ToolBtn>

      <XlbPageContainer.SearchForm>
        <XlbForm form={form} formList={itemFormlist} isHideDate />
      </XlbPageContainer.SearchForm>

      <XlbPageContainer.Table selectMode="single" keepDataSource={false} />
    </XlbPageContainer>
  );
};
export default Item;

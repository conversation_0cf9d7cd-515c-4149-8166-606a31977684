import {XlbFetch as ErpRequest } from '@xlb/utils'
import {
  PaymentMethodDeleteReqDTO,
  PaymentMethodFindReqDTO,
  PaymentMethodSaveReqDTO,
  PaymentMethodUpdateReqDTO
} from "./payMode";

export default {
  // 删除
  delete: async (data: PaymentMethodDeleteReqDTO) => {
    return await ErpRequest.post('/erp/hxl.erp.paymentmethod.delete', { data })
  },

  // 新增
  add: async (data: PaymentMethodSaveReqDTO) => {
    return await ErpRequest.post('/erp/hxl.erp.paymentmethod.save', { data })
  },

  // 修改
  update: async (data: PaymentMethodUpdateReqDTO) => {
    return await ErpRequest.post('/erp/hxl.erp.paymentmethod.update', { data })
  },

  // 获取数据
  getData: async (data: PaymentMethodFindReqDTO) => {
    return await ErpRequest.post('/erp/hxl.erp.paymentmethod.find', { data })
  },

  // 初始化
  init: async (data: PaymentMethodDeleteReqDTO) => {
    return await ErpRequest.post('/erp/hxl.erp.paymentmethod.initialize', { data })
  }
}

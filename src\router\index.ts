import { archivesRouteList } from './archives';
import { dataAnalysisRouteList } from './dataAnalysis';
import { deliveryRouteList } from './delivery';
import { operationRouteList } from './operation';
import { purchasementRouteList } from './procurement';
import { retailRouteList } from './retail';
import { stockRouteList } from './stock';
import { wholesaleRouteList } from './wholesale';
interface IRoute {
  component?: any;
  exact?: boolean;
  path?: string;
  routes?: IRoute[];
  wrappers?: string[];
  title?: string;
  __toMerge?: boolean;
  __isDynamic?: boolean;
  [key: string]: any;
}

export const routeList: IRoute[] = [
  {
    path: '/',
    redirect: '/board',
  },
  {
    path: '*',
    component: '@/pages/public/404/index',
    name: '404',
  },
  ...archivesRouteList, //档案
  ...deliveryRouteList, //配送
  ...purchasementRouteList, //采购
  ...stockRouteList, //库存
  ...wholesaleRouteList, //批发
  ...retailRouteList, //零售
  ...dataAnalysisRouteList, //数据
  ...operationRouteList, //经营
];

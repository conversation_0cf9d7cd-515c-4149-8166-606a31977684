import NiceModal from '@ebay/nice-modal-react';
import { XlbModal, XlbTable } from '@xlb/components';
import { useEffect, useState } from 'react';
import { findUnReceiveQuantity } from '../server';

interface IProps {
  item_id: number;
  store_id: number;
  storehouse_id: number;
}
const PurchaseQuantityModal = (props: IProps) => {
  const { item_id, store_id, storehouse_id } = props;
  const modal = NiceModal.useModal();
  const [dataSource, setDataSource] = useState([]);
  const [loading, setLoading] = useState<boolean>(false);

  const getData = async () => {
    setLoading(true);
    const { code, data } = await findUnReceiveQuantity({
      item_id,
      store_id,
      storehouse_id,
    });
    if (code === 0) {
      setDataSource(data || []);
    }
    setLoading(false);
  };

  useEffect(() => {
    if (modal.visible) {
      getData();
    }
  }, [modal.visible]);

  return (
    <XlbModal
      title="在订量详情"
      open={modal.visible}
      onCancel={() => {
        modal.hide();
        setDataSource([]);
      }}
      onOk={() => {
        modal.hide();
        setDataSource([]);
      }}
      width={800}
    >
      <XlbTable
        isLoading={loading}
        key={item_id + store_id + storehouse_id}
        style={{ marginTop: 10 }}
        columns={[
          {
            name: '序号',
            code: '_index',
            width: 60,
            align: 'center',
          },
          {
            name: '单据类型',
            code: 'order_type',
            width: 120,
            align: 'center',
          },
          {
            name: '单据号',
            code: 'fid',
            width: 120,
            align: 'center',
          },
          {
            name: '收货门店',
            code: 'store_name',
            width: 120,
            align: 'center',
          },
          {
            name: '发货供应商',
            code: 'supplier_name',
            width: 120,
            align: 'center',
          },
          {
            name: '商品代码',
            code: 'item_code',
            width: 120,
            align: 'center',
          },
          {
            name: '商品名称',
            code: 'item_name',
            width: 120,
            align: 'center',
          },
          {
            name: '在订量',
            code: 'un_receive_quantity',
            width: 120,
            align: 'center',
          },
        ]}
        total={dataSource.length}
        dataSource={dataSource}
        rowKey="id"
      />
    </XlbModal>
  );
};

export default NiceModal.create(PurchaseQuantityModal);

import { hasAuth } from '@/utils/kit';
import { LStorage } from '@/utils/storage';
import {
  XlbBasicForm,
  XlbButton,
  XlbDatePicker,
  XlbForm,
  XlbIcon,
  XlbInput,
  XlbInputDialogByRead,
  XlbInputNumber,
  XlbMessage,
  XlbSelect,
  XlbTipsModal,
} from '@xlb/components';
import XlbPageContainer, {
  XlbPageContainerRef,
} from '@xlb/components/dist/lowcodes/XlbPageContainer';
import { XlbFetch as ErpRequest } from '@xlb/utils';
import { message } from 'antd';
import { cloneDeep } from 'lodash';
import { useEffect, useRef, useState } from 'react';
import Api from './server';

import { formatWithCommas } from '@/utils/kit';
import toFixed from '@/utils/toFixed';

import { useBaseParams } from '@/hooks/useBaseParams';
import { useIRouter, wujieBus } from '@/wujie/utils';
import dayjs from 'dayjs';
import { flushSync } from 'react-dom';
import { useStore } from '../store';
import {
  goodsList4,
  item_type_list,
  maxLevel,
  newsCycleOptions,
  purchase_type,
  queryUnit,
  searchFormList,
  tableList,
  tableList1,
  tableList2,
  tableList3,
  tableList5,
} from './data';

const { Table, ToolBtn, SearchForm } = XlbPageContainer;

const StockSearch = () => {
  const { setParamsValue } = useStore();
  const pageRef = useRef<XlbPageContainerRef>(null);
  const contextRef = useRef(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const { enable_cargo_owner } = useBaseParams((state) => state);

  // 表单
  const [form] = XlbBasicForm.useForm();
  const [basicForm] = XlbBasicForm.useForm();
  const [formList, setFormList] = useState(searchFormList);
  const [chooseList, setChooseList] = useState<any[]>([]);
  // 表格
  const [itemArr, setItemArr] = useState<any[]>(tableList);
  //表格底部合计
  const [storeList, setStoreList] = useState<any[]>([]);
  const [costDateRange, setCostDateRange] = useState<any>([]); // 储存时间

  const onValuesChange = (changedValues: any, allValues: any) => {
    // 仅 明细 ，可筛选商品状态
    formList.find((v) => v.label == '商品状态')!.hidden =
      allValues?.query_mode !== 1;
    // 仅0 1 5 4 2，可筛选货主
    let is_query_mode_cargo = !![0, 1, 2, 4, 5].includes(allValues?.query_mode);
    formList.find((v) => v.label == '货主')!.hidden =
      !is_query_mode_cargo || !enable_cargo_owner;
    // 仅 汇总 明细 商品 ，可筛选商品类型
    formList.find((v) => v.label == '商品类型')!.hidden =
      allValues?.query_mode !== 0 &&
      allValues?.query_mode !== 1 &&
      allValues?.query_mode !== 4;
    setFormList([...formList]);
    showOpenNewsCycle();
    // 切换列表项
    if (changedValues?.query_mode || changedValues?.query_mode == 0) {
      if (contextRef.current) {
        contextRef.current?.setRequestForm({
          ...contextRef.current.requestForm,
          orders: [],
        });
      }
      getData();
    }
    if (changedValues?.unit_type) {
      const un = form.getFieldValue('unit_type');
      const qu = queryUnit.find((v) => v.value === un)?.label || '基本单位';
      const updatedArr = itemArr.map((v) => {
        if (v.code === 'unit') {
          return { ...v, name: qu };
        }
        return v;
      });
      pageRef.current?.setColumns(updatedArr);
      setItemArr(updatedArr);
    }
  };

  const onChangeKey = () => {
    let newArr: any[] = [];
    switch (form.getFieldValue('query_mode')) {
      case 0:
      case 1:
        // showItems ture展示，false 不展示
        const storehouse_ids = form.getFieldValue('storehouse_ids');
        const showItems = Array.isArray(storehouse_ids)
          ? storehouse_ids.length === 1
          : !!storehouse_ids;
        let tab0 = tableList;
        let tab1 = tableList1.filter(
          (v) => v.name !== '货主' || enable_cargo_owner,
        );
        let table = form.getFieldValue('query_mode') == 0 ? tab0 : tab1;
        newArr = table.filter(
          (v) =>
            showItems ||
            ![
              'valid_quantity',
              'lock_quantity',
              'basic_valid_quantity',
            ].includes(v.code),
        );
        break;
      case 2:
        newArr = cloneDeep(tableList2);
        break;
      case 3:
        newArr = cloneDeep(tableList3);
        break;
      case 4:
        newArr = cloneDeep(goodsList4);
        break;
      case 5:
        newArr = cloneDeep(tableList5);
        break;
    }
    newArr.map((v) => tableRender(v));
    flushSync(() => {
      setItemArr(newArr);
    });
    // pageRef.current?.setColumns(newArr);
  };

  // 处理数据
  const prevPost = () => {
    const { storehouse_ids, purchase_types } = form.getFieldsValue(true);
    let storehouseIds: any = '';
    if (Array.isArray(storehouse_ids)) {
      storehouseIds = storehouse_ids;
    } else if (storehouse_ids) {
      storehouseIds = [storehouse_ids];
    } else {
      storehouseIds = [];
    }
    const { ...rest } = form.getFieldsValue(true);
    const checkedKeys = form.getFieldValue('checkValue');
    const panelValue = form.getFieldValue('panelValue');
    const filter_item_types = ['COMPONENT', 'MULTIPLESPEC'];
    return {
      ...rest,
      storehouse_ids: storehouseIds,
      supplier_main_body_ids: form.getFieldValue('main_body_id')
        ? [form.getFieldValue('main_body_id') + ''].map(Number)
        : null,
      sale_summary: form.getFieldValue('query_mode') === 3 ? true : null,
      goe_lock_quantity:
        form.getFieldValue('query_mode') === 0
          ? form.getFieldValue('goe_lock_quantity')
          : null,
      loe_lock_quantity:
        form.getFieldValue('query_mode') === 0
          ? form.getFieldValue('loe_lock_quantity')
          : null,
      near_expiry_day:
        form.getFieldValue('left_near_expiry_day') ||
        form.getFieldValue('right_near_expiry_day')
          ? [
              form.getFieldValue('left_near_expiry_day')
                ? form.getFieldValue('left_near_expiry_day')
                : '',
              form.getFieldValue('right_near_expiry_day')
                ? form.getFieldValue('right_near_expiry_day')
                : '',
            ]
          : null,
      item_status_list:
        form.getFieldValue('query_mode') === 1 &&
        form.getFieldValue('item_status_list')
          ? [form.getFieldValue('item_status_list')]
          : null,
      item_types: [0, 1, 4].includes(form.getFieldValue('query_mode'))
        ? form.getFieldValue('item_types')
        : null,
      stop_purchase: panelValue?.find((v: any) => v.value == 'stop_purchase')
        ?.itemKey,
      stop_sale: panelValue?.find((v: any) => v.value == 'stop_sale')?.itemKey,
      stop_request: panelValue?.find((v: any) => v.value == 'stop_request')
        ?.itemKey,
      eliminate: panelValue?.find((v: any) => v.value == 'eliminate')?.itemKey,
      must_sell: panelValue?.find((v: any) => v.value == 'must_sell')?.itemKey,
      open_news_cycle: panelValue?.find(
        (v: any) => v.value == 'open_news_cycle',
      )?.itemKey,
      // checkedKeys拆分，放在 filter_item_types: ['COMPONENT', 'MULTIPLESPEC'] ,其余则为 key: true。
      filter_item_types: checkedKeys?.filter((i: any) =>
        filter_item_types.includes(i),
      ),
      filter_zero_stock: !!checkedKeys.includes('filter_zero_stock')
        ? true
        : null,
      query_main_supplier: !!checkedKeys.includes('query_main_supplier')
        ? true
        : null,
      show_batch_unit: !!checkedKeys.includes('show_batch_unit') ? true : null,
    };
  };
  // 查询按钮
  const pageContainerUrl = () => {
    const url =
      form.getFieldValue('query_mode') == 1
        ? '/erp/hxl.erp.stockdetail.page'
        : form.getFieldValue('query_mode') == 2 ||
            form.getFieldValue('query_mode') == 3
          ? '/erp/hxl.erp.stock.store.page'
          : form.getFieldValue('query_mode') == 4
            ? '/erp/hxl.erp.stock.store.item.page'
            : form.getFieldValue('query_mode') == 5
              ? '/erp/hxl.erp.stock.storehouse.page'
              : '/erp/hxl.erp.stock.page';
    return url;
  };
  const getData = () => {
    // 校验
    const isValidDecimal = (value: number | string): boolean => {
      if (value === null || value === undefined || value === '') return true;
      const str = String(value);
      const parts = str.split('.');
      return parts.length === 1 || parts[1].length <= 3;
    };
    if (
      !isValidDecimal(form.getFieldValue('goe_lock_quantity')) ||
      !isValidDecimal(form.getFieldValue('loe_lock_quantity'))
    ) {
      XlbMessage.warning('占用数量最多只能输入三位小数');
      return;
    }
    // 查询
    onChangeKey();
    pageRef.current?.fetchData();
  };
  // 导出按钮
  const exportItem = async (e: any) => {
    const data = prevPost();
    setIsLoading(true);
    const res =
      form.getFieldValue('query_mode') === 0
        ? await ErpRequest.post('/erp/hxl.erp.stock.export', data)
        : form.getFieldValue('query_mode') === 2
          ? await ErpRequest.post('/erp/hxl.erp.stock.store.export', data)
          : form.getFieldValue('query_mode') === 3
            ? await ErpRequest.post('/erp/hxl.erp.stock.store.export', data)
            : form.getFieldValue('query_mode') === 4
              ? await ErpRequest.post(
                  '/erp/hxl.erp.stock.store.item.export',
                  data,
                )
              : form.getFieldValue('query_mode') === 5
                ? await ErpRequest.post(
                    '/erp/hxl.erp.stock.storehouse.export',
                    data,
                  )
                : await ErpRequest.post(
                    // 1
                    '/erp/hxl.erp.stock.detail.export',
                    data,
                  );
    if (res?.code === 0) {
      wujieBus?.$emit('xlb_erp-event', { code: 'downloadEnd', target: e });
      XlbMessage.success('导出受理成功，请前往下载中心查看');
    }
    setIsLoading(false);
  };
  // 核算成本按钮
  const onValuesBasicChange = async (changedValues: any) => {
    setIsLoading(true);
    const { cost_date_range, cost_price_type } = changedValues;
    if (cost_date_range) {
      setCostDateRange(cost_date_range);
    }
    if (cost_price_type) {
      // 调接口获取成本单价
      const _basicForm = basicForm.getFieldsValue(true);
      const data = {
        cost_price_type: _basicForm?.cost_price_type,
        item_id: _basicForm?.item_id,
        store_id: _basicForm?.store_id,
        storehouse_id: _basicForm?.storehouse_id,
      };
      try {
        const res = await ErpRequest.post(
          `/erp/hxl.erp.stock.cost.account.price.read`,
          data,
        );
        if (res.code == 0) {
          basicForm.setFieldsValue({
            cost_price: res?.data?.cost_price,
          });
        }
      } catch (e) {}
    }
    setIsLoading(false);
  };
  const disabledDate = (_current: any) => {
    const current = _current.format('YYYY-MM-DD');
    return current && current > dayjs().endOf('day').format('YYYY-MM-DD');
  };
  const openCostModal = async (record: any) => {
    const _record = record?.[0];
    basicForm.setFieldsValue({
      store_name: _record.store_name,
      store_id: _record.store_id,
      storehouse_name: _record.storehouse_name,
      storehouse_id: _record.storehouse_id,
      item_id: _record.item_id,
      item_name: _record.item_name,
      cost_date_range: costDateRange?.length > 0 ? costDateRange : undefined,
      cost_price_type: '',
      cost_price: '',
    });
    await XlbTipsModal({
      width: 450,
      tips: (
        <>
          <XlbBasicForm onValuesChange={onValuesBasicChange} form={basicForm}>
            <XlbBasicForm.Item label="门店名称" name="store_name">
              <XlbInput style={{ width: 241 }} readOnly placeholder="" />
            </XlbBasicForm.Item>
            <XlbBasicForm.Item label="仓库名称" name="storehouse_name">
              <XlbInput style={{ width: 241 }} readOnly placeholder="" />
            </XlbBasicForm.Item>
            <XlbBasicForm.Item label="商品名称" name="item_name">
              <XlbInput style={{ width: 241 }} readOnly placeholder="" />
            </XlbBasicForm.Item>
            <XlbBasicForm.Item
              rules={[{ required: true, message: '请选择日期范围' }]}
              label="日期范围"
              name="cost_date_range"
            >
              <XlbDatePicker.RangePicker
                disabledDate={disabledDate}
                style={{ width: 241 }}
              />
            </XlbBasicForm.Item>
            <XlbBasicForm.Item
              rules={[{ required: true, message: '请选择成本取值' }]}
              label="成本取值"
              name="cost_price_type"
            >
              <XlbSelect
                style={{ width: 241 }}
                size="small"
                options={[
                  { label: '按实时配送价', value: 'DELIVERY' },
                  { label: '按实时库存成本价', value: 'STOCKCOST' },
                ]}
              ></XlbSelect>
            </XlbBasicForm.Item>
            <XlbBasicForm.Item
              rules={[{ required: true, message: '成本单价不能为空' }]}
              label="成本单价"
              name="cost_price"
            >
              <XlbInputNumber
                readOnly
                style={{ width: 241 }}
                precision={4}
              ></XlbInputNumber>
            </XlbBasicForm.Item>
          </XlbBasicForm>
        </>
      ),
      isCancel: true,
      onOkBeforeFunction: async () => {
        try {
          await basicForm.validateFields();
        } catch (err: any) {
          return false;
        }
        const _basicForm = basicForm.getFieldsValue(true);
        const data = {
          cost_price_type: _basicForm?.cost_price_type,
          item_id: _basicForm?.item_id,
          store_id: _basicForm?.store_id,
          storehouse_id: _basicForm?.storehouse_id,
          operate_date: basicForm.getFieldValue('cost_date_range'),
        };
        const res = await Api.recosting({ ...data });
        if (res && res?.code === 0) {
          message.success('核算成功');
          getData();
        }
        return true;
      },
      destroyOnClose: true,
      title: `重新核算成本`,
    });
  };

  const onClickStore = async (record: any) => {
    await XlbInputDialogByRead({
      url: '/erp/hxl.erp.lockstock.page', // 接口地址
      data: {
        item_id: record.item_id,
        storehouse_id: record.storehouse_id,
        query_unit: form.getFieldValue('unit_type'),
        cargo_owner_id: record.cargo_owner_id || '',
      },
      columns: [
        {
          name: '序号',
          code: '_index',
        },
        {
          name: '单据号',
          code: 'fid',
          width: 220,
          features: { sortable: true },
        },
        {
          name: '占用数量',
          code: 'quantity',
          width: 150,
          features: { sortable: true },
        },
        {
          name: '单位',
          code: 'unit',
          width: 130,
          features: { sortable: true },
        },
        {
          name: '占用时长',
          code: 'duration',
          width: 150,
          features: { sortable: true },
        },
        {
          name: '创建时间',
          code: 'create_time',
          width: 140,
          features: { sortable: true, format: 'TIME' },
        },
      ], // 表头信息
      title: '占用详情',
    });
  };
  const tableRender = (item: any) => {
    switch (item.code) {
      case 'item_type':
        item.render = (value: any, record: any, index: number) => {
          return (
            <div className="overwidth cursors">
              {item_type_list?.find((v) => v.value == value)?.label}
            </div>
          );
        };
        break;
      case 'purchase_type':
        item.render = (value: any) => (
          <div className="info overwidth">
            {value ? purchase_type.find((v) => v.value == value)?.label : ''}
          </div>
        );
        break;
      case 'store_id':
        item.render = (value: any, record: any, index: number) => {
          return <div className="overwidth cursors">{record.store_name}</div>;
        };
        break;
      case 'storehouse_name':
        item.render = (value: any) => {
          return <div className="info overwidth">{value}</div>;
        };
        break;
      case 'item_stop_purchase':
      case 'item_stop_sale':
      case 'item_stop_request':
      case 'item_stop_wholesale':
        item.render = (value: any, record: any) => {
          return record.index !== '合计' ? (
            <div className="info overwidth">{value ? '是' : '否'}</div>
          ) : null;
        };
        break;
      case 'basic_no_tax_cost_price': // 基本单价(去税)
      case 'no_tax_cost_price': // 单价(去税)
      case 'basic_cost_price': // 基本单价
      case 'sale_price': // 零售价
        item.render = (value: any, record: any) => {
          return hasAuth(['库存查询/价格', '查询']) && value !== '****' ? (
            <div className="info overwidth">
              {record.index !== '合计' && toFixed(value, 'PRICE')}
            </div>
          ) : (
            record.index !== '合计' && (
              <div className="info overwidth">{'****'}</div>
            )
          );
        };
        break;
      case 'cost_price': // 单价
        item.render = (value: any, record: any) => {
          return hasAuth(['库存查询/价格', '查询']) && value !== '****' ? (
            <div className="info overwidth">
              {record.index !== '合计' && Number(value || 0).toFixed(4)}
            </div>
          ) : (
            record.index !== '合计' && (
              <div className="info overwidth">{'****'}</div>
            )
          );
        };
        break;
      case 'valid_sale_money':
      case 'valid_no_tax_money':
      case 'valid_money':
        item.render = (value: any, record: any) => {
          return hasAuth(['库存查询/价格', '查询']) && value !== '****' ? (
            <div className="info overwidth">
              {record.index !== '合计' && toFixed(value, 'MONEY')}
            </div>
          ) : (
            record.index !== '合计' && (
              <div className="info overwidth">{'****'}</div>
            )
          );
        };
        break;
      case 'no_tax_money':
      case 'sale_money':
      case 'money':
        item.render = (value: any, record: any) => {
          return hasAuth(['库存查询/价格', '查询']) && value !== '****' ? (
            <div className="info overwidth">
              {formatWithCommas(toFixed(value, 'MONEY'))}
            </div>
          ) : (
            <div className="info overwidth">{'****'}</div>
          );
        };
        break;
      case 'basic_quantity':
      case 'basic_valid_quantity':
      case 'valid_quantity':
        item.render = (value: any, record: any) => (
          <div className="info overwidth">{toFixed(value, 'QUANTITY')}</div>
        );
        break;
      case 'lock_quantity':
        item.render = (value: any, record: any) => {
          if (value == '0') return '0';
          if (!value || value === '合计') {
            return null;
          }
          return (
            <a
              className="abutten"
              onClick={(e) => {
                e.stopPropagation();
                onClickStore(record);
              }}
            >
              {value}
            </a>
          );
        };
        break;
      case 'tare':
        item.render = (value: any, record: any) => (
          <div className="info overwidth">
            {record.index !== '合计' && toFixed(value, 'QUANTITY')}
          </div>
        );
        break;
      case 'quantity':
        item.render = (value: any, record: any, index: number) => {
          return record.index === '合计' ? (
            <div className="info overwidth">{Number(value)?.toFixed(3)}</div>
          ) : form.getFieldValue('query_mode') == 2 ||
            form.getFieldValue('query_mode') == 3 ? (
            <div className="info overwidth">{Number(value)?.toFixed(3)}</div>
          ) : (
            <div className="overwidth">
              <span
                className="link cursors"
                onClick={(e) => {
                  if (!hasAuth(['库存进出记录', '查询'])) {
                    message.error('当前账号没有库存进出记录查询权限');
                    return;
                  }
                  setParamsValue({ ...record, goStockLog: true });
                  e.stopPropagation();
                  const { navigate } = useIRouter();
                  navigate('/xlb_erp/stockLog/index', {});
                }}
              >
                {Number(value)?.toFixed(3)}
              </span>
            </div>
          );
        };
        break;
    }
    return item;
  };

  // 获取最大商品分类查询
  const gteMaxlevel = async () => {
    const res = await Api.maxlevelRead({});
    if (res && res.code === 0) {
      //截取数组前4位
      const labArr = maxLevel.slice(0, res.data + 1);
      formList.find((item) => item.label === '类别等级')!.options = labArr;
      setFormList([...formList]);
    }
  };

  const showOpenNewsCycle = () => {
    // “其他条件”是否可选“新品”
    const queryMode = form.getFieldValue('query_mode');
    const panelValueField = formList.find((v) => v.name == 'panelValue');
    if (queryMode === 1) {
      panelValueField!.options = newsCycleOptions;
    } else {
      panelValueField!.options = newsCycleOptions?.filter(
        (item) => item.value !== 'open_news_cycle',
      );
    }
    setFormList([...formList]);
  };

  itemArr.map((v) => tableRender(v));
  useEffect(() => {
    showOpenNewsCycle();
    gteMaxlevel();
    if (storeList.length == 0) {
      setStoreList([LStorage.get('userInfo').store]);
    }
    form.setFieldsValue({
      checkValue: ['show_batch_unit'],
      store_ids: [LStorage.get('userInfo').store_id],
    });
    // 当 enable_cargo_owner 关闭时，全局无货主
    formList.map((v) => {
      if (v.label === '货主') {
        v.hidden = !enable_cargo_owner;
      }
      return v;
    });
    setFormList([...formList]);
  }, []);

  useEffect(() => {
    // 清空占用数量、近效期
    const query_mode = form.getFieldValue('query_mode');
    if (query_mode !== 1) {
      form.setFieldValue('left_near_expiry_day', null);
      form.setFieldValue('right_near_expiry_day', null);
      form.setFieldValue('near_expiry_day', []);
    } else if (query_mode !== 0) {
      form.setFieldValue('goe_lock_quantity', null);
      form.setFieldValue('loe_lock_quantity', null);
    }
  }, [form.getFieldValue('query_mode')]);

  return (
    <XlbPageContainer
      ref={pageRef}
      url={pageContainerUrl()}
      tableColumn={itemArr}
      immediatePost={false}
      isShowTemplate
      prevPost={() => prevPost()}
      changeColumnAndResetDataSource={false}
      footerDataSource={(data) => {
        return [
          {
            _index: '合计',
            money:
              hasAuth(['库存查询/价格', '查询']) && data?.money !== '****'
                ? Number(data?.money)?.toFixed(2) || '0.00'
                : '****',
            quantity: Number(data?.quantity)?.toFixed(3) || '0.000',
            basic_quantity: Number(data?.basic_quantity)?.toFixed(3) || '0.000',
            no_tax_money:
              hasAuth(['库存查询/价格', '查询']) &&
              data?.no_tax_money !== '****'
                ? Number(data?.no_tax_money)?.toFixed(2) || '0.00'
                : '****',
            sale_money:
              hasAuth(['库存查询/价格', '查询']) && data?.sale_money !== '****'
                ? Number(data?.sale_money)?.toFixed(2) || '0.00'
                : '****',
          },
        ];
      }}
    >
      <SearchForm>
        <XlbForm
          form={form}
          formList={formList}
          initialValues={{
            store_ids: [LStorage.get('userInfo')?.store_id],
            unit_type: 'PURCHASE',
            query_mode: 0,
          }}
          isHideDate
          onValuesChange={onValuesChange}
        />
      </SearchForm>
      <ToolBtn showColumnsSetting>
        {(context) => {
          const { dataSource } = context;
          contextRef.current = context as any;
          return (
            <XlbButton.Group>
              {hasAuth(['库存查询', '查询']) && (
                <XlbButton
                  label="查询"
                  type={'primary'}
                  disabled={isLoading}
                  onClick={() => getData()}
                  icon={<XlbIcon name="sousuo" />}
                />
              )}
              {hasAuth(['库存查询', '导出']) && (
                <XlbButton
                  label="导出"
                  type={'primary'}
                  disabled={!dataSource?.length || isLoading}
                  onClick={(e: any) => exportItem(e)}
                  icon={<XlbIcon name="daochu" />}
                />
              )}
              {hasAuth(['库存查询/重新核算成本', '编辑']) && (
                <XlbButton
                  type={'primary'}
                  label="重新核算成本"
                  disabled={!chooseList.length}
                  onClick={() => openCostModal(chooseList)}
                />
              )}
            </XlbButton.Group>
          );
        }}
      </ToolBtn>
      <Table
        primaryKey="_index"
        selectMode={'single'}
        key="store_id"
        isLoading={isLoading}
        onSelectRow={(e: any, options: any) => {
          setChooseList(options);
        }}
      />
    </XlbPageContainer>
  );
};

export default StockSearch;

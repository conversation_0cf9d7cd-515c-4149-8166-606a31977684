import {
  SearchFormType,
  XlbBasicForm,
  XlbCheckbox,
  XlbTableColumnProps,
} from '@xlb/components';
//单位类型
export const unitType: any = [
  {
    label: '基本单位',
    value: 'BASIC',
  },
  {
    label: '配送单位',
    value: 'DELIVERY',
  },
  {
    label: '采购单位',
    value: 'PURCHASE',
  },
  {
    label: '库存单位',
    value: 'STOCK',
  },
  {
    label: '批发单位',
    value: 'WHOLESALE',
  },
];

const budgetSymbols = [
  { label: '≥', value: '>=' },
  { label: '≤', value: '<=' },
];

//是否
export const Isopen = [
  {
    label: '是',
    value: 1,
  },
  {
    label: '否',
    value: 0,
  },
];
export const formList: SearchFormType[] = [
  {
    label: '日期范围',
    name: 'date',
    width: 200,
    check: true,
    type: 'compactDatePicker',
  },
  {
    label: '组织',
    name: 'org_ids',
    type: 'select',
    multiple: true,
    clear: true,
    options: [],
    // @ts-ignore
    onChange: (e: any, formData: any) => {
      formData.setFieldsValue({
        store_ids: [],
      });
    },
    selectRequestParams: (params: Record<string, any>) => {
      return {
        url: '/erp-mdm/hxl.erp.org.find',
        responseTrans(data) {
          const options = data.map((item: any) => {
            const obj = {
              label: item.name,
              value: item.id,
            };
            return obj;
          });
          return options;
        },
      };
    },
  },
  {
    label: '门店',
    name: 'store_ids',
    type: 'inputDialog',
    dependencies: ['org_ids'],
    dialogParams: (params: any) => {
      return {
        type: 'store',
        dataType: 'lists',
        isLeftColumn: true,
        isMultiple: true,
        primaryKey: 'id',
        data: {
          enabled: true,
        },
      };
    },
    fieldNames: {
      idKey: 'id',
      nameKey: 'store_name',
    } as any,
  },
  {
    label: '商品档案',
    name: 'item_ids',
    type: 'inputDialog',
    dialogParams: {
      type: 'goods',
      dataType: 'lists',
      isMultiple: true,
      data: {
        enabled: true,
      },
    },
  },
  {
    label: '商品类别',
    name: 'item_category_ids',
    type: 'inputDialog',
    treeModalConfig: {
      title: '选择商品分类', // 标题
      url: '/erp-mdm/hxl.erp.category.find', // 请求地址
      dataType: 'lists',
      checkable: true, // 是否多选
      primaryKey: 'id',
      data: {
        enabled: true,
      },
      width: 360, // 模态框宽度
    } as any,
    clear: true,
    check: true,
  },
  {
    label: '行政区域',
    name: 'city_codes',
    type: 'inputDialog',
    treeModalConfig: {
      title: '选择区域',
      url: '/erp-mdm/hxl.erp.store.area.find.all', // /erp-mdm/hxl.erp.store.area.find.all
      dataType: 'lists',
      checkable: true, // 是否多选
      primaryKey: 'code',
      fieldName: {
        id: 'code',
        parent_id: 'parent_code',
      },
    } as any,
    fieldNames: {
      // @ts-ignore
      idKey: 'code',
      nameKey: 'name',
    },
    isCheckStrictly: false,
    clear: true,
    check: true,
    title: '选择行政区域',
  },
  {
    label: '单位类型',
    name: 'unit_type',
    type: 'select',
    options: unitType,
    clear: false,
    check: true,
  },
  {
    label: '允许经营',
    name: 'allow_business',
    type: 'selectTrueOrFalse',
    clear: true,
    check: true,
    options: Isopen,
  },
  {
    label: '停止补货',
    name: 'stop_request',
    type: 'selectTrueOrFalse',
    clear: true,
    check: true,
    options: Isopen,
  },
  {
    label: '停购',
    name: 'stop_purchase',
    type: 'selectTrueOrFalse',
    clear: true,
    check: true,
    options: Isopen,
  },
  {
    label: '总仓是否有货',
    name: 'center_has_stock',
    hidden: false,
    type: 'selectTrueOrFalse',
    clear: true,
    check: true,
    options: Isopen,
  },
  {
    type: 'group',
    label: '实时库存',
    span: 1,
    formList: [
      {
        type: 'select',
        name: 'stock_quantity_symbol',
        options: budgetSymbols,
        placeholder: '',
      },
      {
        type: 'inputNumber',
        name: 'stock_quantity',
        placeholder: '',
        min: 0,
      },
    ],
  },
  {
    type: 'group',
    label: '销量',
    span: 1,
    formList: [
      {
        type: 'select',
        name: 'sale_quantity_symbol',
        placeholder: '',
        options: budgetSymbols,
      },
      {
        type: 'inputNumber',
        name: 'sale_quantity',
        placeholder: '',
        min: 0,
      },
    ],
  },
  {
    type: 'group',
    label: '点货次数',
    span: 1,
    formList: [
      {
        type: 'select',
        name: 'request_count_symbol',
        options: budgetSymbols,
        placeholder: '',
      },
      {
        type: 'inputNumber',
        name: 'request_count',
        min: 0,
        placeholder: '',
      },
    ],
  },
  {
    type: 'group',
    label: '到货次数',
    span: 1,
    formList: [
      {
        type: 'select',
        name: 'delivery_count_symbol',
        options: budgetSymbols,
        placeholder: '',
      },
      {
        type: 'inputNumber',
        name: 'delivery_count',
        min: 0,
        placeholder: '',
      },
    ],
  },
  {
    type: 'group',
    label: '缺货次数',
    span: 1,
    formList: [
      {
        type: 'select',
        name: 'out_of_stock_symbol',
        options: budgetSymbols,
        placeholder: '',
      },
      {
        type: 'inputNumber',
        name: 'out_of_stock_count',
        min: 0,
        placeholder: '',
      },
    ],
  },
  {
    type: 'group',
    label: '未补货天数',
    span: 1,
    formList: [
      {
        type: 'select',
        name: 'un_request_symbol',
        options: budgetSymbols,
        placeholder: '',
      },
      {
        type: 'inputNumber',
        name: 'un_request_day',
        min: 0,
        placeholder: '',
      },
    ],
  },
  {
    type: 'group',
    label: '预计可用天数',
    span: 1,
    formList: [
      {
        type: 'select',
        name: 'valid_symbol',
        options: budgetSymbols,
        placeholder: '',
      },
      {
        type: 'inputNumber',
        name: 'valid_day',
        min: 0,
        placeholder: '',
      },
    ],
  },
  {
    label: '新品',
    name: 'open_news_cycle',
    hidden: false,
    type: 'selectTrueOrFalse',
    clear: true,
    check: true,
    options: Isopen,
  },
  {
    label: '过滤',
    name: 'checkValue',
    type: 'custom',
    render(itemData, form, itemSetting) {
      return (
        <XlbBasicForm.Item name={'checkValue'} label="过滤">
          <XlbCheckbox.Group
            onChange={(e) => {
              const calculate_stock_and_sale_as_makebill = e.includes(
                'calculate_stock_and_sale_as_makebill',
              );
              const calculate_stock_and_sale_as_mainspec = e.includes(
                'calculate_stock_and_sale_as_mainspec',
              );
              form?.setFieldsValue({
                calculate_stock_and_sale_as_makebill:
                  calculate_stock_and_sale_as_makebill,
                calculate_stock_and_sale_as_mainspec:
                  calculate_stock_and_sale_as_mainspec,
              });
            }}
          >
            <XlbCheckbox value="calculate_stock_and_sale_as_makebill">
              成分商品实时库存、销量按制单组合统计
            </XlbCheckbox>
            <XlbCheckbox value="calculate_stock_and_sale_as_mainspec">
              多规格商品实时库存、销量按主规格商品统计
            </XlbCheckbox>
          </XlbCheckbox.Group>
        </XlbBasicForm.Item>
      );
    },
  },
];
// 列表数据
export const tableList: XlbTableColumnProps<any>[] = [
  {
    name: '序号',
    code: '_index',
    width: 65,
    align: 'center',
  },
  {
    name: '组织',
    code: 'org_name',
    width: 140,
    hidden: true,
    features: { sortable: true },
  },
  {
    name: '门店名称',
    code: 'store_name',
    width: 140,
    features: { sortable: true },
  },
  {
    name: '商品代码',
    code: 'item_code',
    width: 120,
    features: { sortable: true },
  },
  {
    name: '商品条码',
    code: 'item_bar_code',
    width: 120,
    features: { sortable: true },
  },
  {
    name: '商品名称',
    code: 'item_name',
    width: 120,
    features: { sortable: true },
  },
  {
    name: '采购规格',
    code: 'item_spec',
    width: 120,
    features: { sortable: true },
  },
  {
    name: '单位',
    code: 'unit',
    width: 100,
    features: { sortable: true },
  },
  {
    name: '允许经营',
    code: 'allow_business',
    width: 100,
    features: { sortable: true },
  },
  {
    name: '停止补货',
    code: 'stop_request',
    width: 100,
    features: { sortable: true },
  },
  {
    name: '停购',
    code: 'stop_purchase',
    width: 100,
    features: { sortable: true },
  },
  {
    name: '总仓是否有货',
    code: 'center_has_stock',
    hidden: false,
    width: 140,
    features: { sortable: true },
  },
  {
    name: '实时库存',
    code: 'stock_quantity',
    width: 140,
    align: 'right',
    features: { sortable: true, format: 'QUANTITY' },
  },
  {
    name: '实时基本库存',
    code: 'basic_stock_quantity',
    width: 140,
    align: 'right',
    features: { sortable: true, format: 'QUANTITY' },
  },
  {
    name: '销量',
    code: 'sale_quantity',
    width: 110,
    align: 'right',
    features: { sortable: true, format: 'QUANTITY' },
  },
  {
    name: '基本销量',
    code: 'basic_sale_quantity',
    width: 110,
    align: 'right',
    features: { sortable: true, format: 'QUANTITY' },
  },
  {
    name: '日均销量',
    code: 'avg_sale_quantity',
    width: 110,
    align: 'right',
    features: { sortable: true, format: 'QUANTITY' },
  },
  {
    name: '日均基本销量',
    code: 'basic_avg_sale_quantity',
    width: 120,
    align: 'right',
    features: { sortable: true, format: 'QUANTITY' },
  },
  {
    name: '预计可用天数',
    code: 'valid_days',
    width: 150,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '点货次数',
    code: 'request_count',
    width: 140,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '到货次数',
    code: 'delivery_count',
    width: 140,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '缺货次数',
    code: 'out_of_stock_count',
    width: 140,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '未补货天数',
    code: 'un_request_day',
    align: 'right',
    width: 160,
    features: { sortable: true },
  },
];
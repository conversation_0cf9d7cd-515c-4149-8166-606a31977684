import React, { useEffect, useState } from 'react'
import NiceModal from '@ebay/nice-modal-react'
import { XlbModal, XlbTable } from '@xlb/components'
import { getStoreItemSupplierHistory } from '../../../server'

interface IProps {
  isHistoryLoading: boolean
  params: any
}
const History = (props: IProps) => {
  const { isHistoryLoading, params } = props
  const modal = NiceModal.useModal()
  const [dataSource, setDataSource] = useState([])
  const getHistoryData = async () => {
    const historyParams = {
      ...params
    }
    const res = await getStoreItemSupplierHistory({ ...historyParams })
    if (res.code === 0) {
      setDataSource(res.data?.content)
    }
  }
  useEffect(() => {
    if (modal.visible) {
      getHistoryData()
    }
  }, [modal.visible])
  return (
    <XlbModal
      width={800}
      open={modal.visible}
      title={'修改记录'}
      isCancel={true}
      onOk={async () => {
        modal.hide()
      }}
      onCancel={() => {
        modal.hide()
      }}
    >
      <XlbTable
        dataSource={dataSource}
        loading={isHistoryLoading}
        showSearch
        columns={[
          { title: '序号', code: '_index', lock: true },
          { title: '变更字段', code: 'field_name', features: { sortable: true } },
          {
            title: '原先内容',
            code: 'old_value',
            features: { sortable: true },
            render(value, record, index) {
              return <div>{`${value ? (value * 100)?.toFixed(2) : 0 * 100}%`}</div>
            }
          },
          {
            title: '变更后内容',
            code: 'new_value',
            features: { sortable: true },
            render(value, record, index) {
              return <div>{`${value ? (value * 100)?.toFixed(2) : 0 * 100}%`}</div>
            }
          },
          {
            title: '操作时间',
            code: 'create_time',
            width: 180,
            features: { sortable: true, format: 'TIME' }
          },
          { title: '操作人', code: 'create_by', features: { sortable: true } }
        ]}
        total={dataSource?.length}
        keepDataSource={false}
        pageSize={100}
        style={{
          height: 360,
          overflow: 'auto'
        }}
      ></XlbTable>
    </XlbModal>
  )
}
export default History

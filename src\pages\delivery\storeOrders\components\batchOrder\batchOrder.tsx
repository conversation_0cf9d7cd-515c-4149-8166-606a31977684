import { Modal, Form, DatePicker, Input, InputNumber, message, Checkbox } from 'antd'
import styles from './batchOrder.less'
import { useEffect, useRef, useState } from 'react'
import Decimal from 'decimal.js'
import { itemTableList } from './data'
import { hasAuth } from '@/utils'
import {XlbFetch as ErpRequest} from '@xlb/utils'
import {
  XlbProForm,
  XlbTipsModal,
  XlbBasicData,
  XlbCheckbox,
  XlbModal,
  XlbIcon,
  XlbButton,
  XlbMessage,
  XlbTable,
  XlbInputNumber,
  XlbSelect
} from '@xlb/components'
import { ErpFieldKeyMap } from '@/data/common/fieldListConfig'
import { batchOrderSave } from '../../server'
import { cloneDeep } from 'lodash'

const BatchOrderIndex = (props: any) => {
  const formRef = useRef<any>(null)
  const { open, setOpen } = props
  const [formUpdate] = Form.useForm()
  const [rowData, setRowData] = useState<any[]>([])
  const [isLoading, setIsLoading] = useState<boolean>(false)
  const [isEdit, setIsEdit] = useState<boolean>(false)
  const [itemArr, setItemArr] = useState<any>(JSON.parse(JSON.stringify(itemTableList)))
  const [goodList, setGoodList] = useState<any>([]) //商品数组
  const [selectedStoreList, setSelectedStoreList] = useState<string[]>([]) //已选择门店
  const [selectedGoodList, setSelectedGoodList] = useState<number[]>([]) //已选择商品
  const modalRef = useRef(null)

  const getMult = (num1: number, num2: number, precision: number = 4) => {
    return new Decimal(num1 || 0)
      .mul(new Decimal(num2 || 0))
      .toNumber()
      ?.toFixed(precision)
  }
  const inputChange = (
    e: number | string | null,
    index: number,
    renderv: any,
    record: any,
    type: string
  ) => {
    setRowData((prev) => {
      const list = cloneDeep(prev)
      if (type === 'quantity') {
        // 更新对应的quantity
        const curItem = list[index].detail?.find((v: any) => v.id === renderv) || {}
        curItem.quantity = e
        list[index][`${renderv}_quantity`] = e
      }
      if (type === 'price') {
        const curItem = list[index].detail?.find((v: any) => v.id === renderv) || {}
        curItem.price = e
        list[index][`${renderv}_price`] = e
      }
      if (type === 'ratio') {
        const curItem = list[index].detail?.find((v: any) => v.id === renderv) || {}
        curItem.ratio = e
        list[index][`${renderv}_ratio`] = e
        list[index][`${renderv}_price`] = getMult(list[index][`${renderv}_old_basic_price`], e)
      }
      return list
    })
  }

  //操作删除行
  const showDeleteModal = (record: any) => {
    // 删除选中的门店
    const updatedRowData = rowData.filter((store: any) => store.store_id !== record.store_id)

    // 若门店删光，清空商品相关数据，仅保留 itemArr 的第一个元素
    if (updatedRowData.length === 0) {
      setGoodList([])
      setItemArr([itemArr[0]]) // ✅ 只保留第一个元素
    }
    // updatedRowData 中不存在的门店，从 selectedStoreList 中删除
    setSelectedStoreList((prev) => prev.filter((store: any) => record.store_id != store))
    setRowData(updatedRowData)
  }

  const storeStrongRender = (item: any) => {
    if (item.children && item.children.length) {
      item.children.map((v: any) => {
        switch (v?.code) {
          case 'index':
            v.render = (value: any, record: any, index: number) => {
              return <div>{index + 1}</div>
            }
            break

          case 'action':
            v.render = (value: any, record: any, index: number) => {
              return (
                <div>
                  <XlbButton
                    icon={<XlbIcon name="shanchu" />}
                    type="text"
                    onClick={(e) => {
                      e.stopPropagation()
                      showDeleteModal(record)
                    }}
                  />
                </div>
              )
            }
            break
        }
      })
    }
  }

  const onChange = (e: any, list: any, i: number) => {
    const key = Number(e.target.value)

    // 获取modal中的table
    const checkBoxs = modalRef?.current
      ?.querySelector('thead')
      .querySelector('.art-table-header-row')!
      .getElementsByClassName('art-table-header-cell')

    if (key === 9999 || key === 1111) {
      for (let k = 0; k < checkBoxs.length - 1; k++) {
        k > 0 && (checkBoxs[k].querySelector('input[type="checkbox"]')!.checked = e.target.checked)
      }
      setSelectedGoodList(key === 9999 ? goodList.map((v) => v.id) : [])
    } else {
      setSelectedGoodList((prevList) => {
        const index = prevList.indexOf(key)
        if (index === -1) {
          return [...prevList, key]
        } else {
          const newList = [...prevList]
          newList.splice(index, 1)
          return newList
        }
      })
    }
  }
  const handleKeyDown = (e: any) => {
    if (e.key === 'Enter') {
      e.preventDefault()
    }
  }
  const handleAddClick = async () => {
    const list = await XlbBasicData({
      isMultiple: true,
      url: '/erp/hxl.erp.storeorder.item.union.page',
      type: 'goods',
      dataType: 'lists',
      primaryKey: 'id',
      selectedList: rowData?.[0].detail.map((i: any) => ({ ...i, id: i.item_id || i.id })),
      fieldNames: {
        idKey: 'id',
        nameKey: 'name'
      },
      data: {
        store_ids: rowData?.map((i) => i?.store_id),
        type: 'DIRECT_SUPPLY_RESERVE',
        enabled: true,
        supplier_id: formRef?.current?.getFieldValue('supplier_id') || ''
      }
    })
    if (list) {
      const listIds = list.map((t) => t.id)
      // 1. 过滤 itemArr 中已有的、出现在 list 中的项（跳过头部）
      let newItemArr = [itemArr[0]]
      let goodList = itemArr
        ?.slice(1) // 只处理非头部部分
        .filter((k: any) => listIds.includes(k.id) || listIds.includes(k.item_id))

      // 删除没有选中的
      goodList = goodList?.filter(
        (k: any, i: number) => listIds?.includes(k.id) || listIds?.includes(k.item_id)
      )
      newItemArr = [...newItemArr, ...goodList]
      // 2. 遍历 list，添加还没出现在 newItemArr 中的项
      for (let i = 0; i < list.length; i++) {
        const item = list[i]
        const isExist = newItemArr.some((g: any) => g.id === item.id)
        if (isExist) continue

        // 初始化字段
        // item.selected = false
        // item[`${item.id}_quantity`] = 0
        // item[`${item.id}_price`] = new Decimal(list[i]?.basic_price ?? 0)
        //   .mul(new Decimal(list[i]?.delivery_ratio ?? 0))
        //   .toNumber()
        // item[`${item.id}_ratio`] = list[i]?.delivery_ratio

        // 构建新的列结构
        const newCol = {
          title: (
            <span className={styles.checkText}>
              <input
                className={styles.diyCheckBox}
                type="checkbox"
                key={item.id}
                value={item.id}
                onChange={(e: any) => onChange(e, list, i)}
              />
              {item.name + '/' + item.code}
            </span>
          ),
          code: item.id,
          id: item.id,
          width: 300,
          align: 'center',
          children: [
            {
              title: `采购规格:${item.purchase_spec || ''}`,
              code: item.id,
              width: 160,
              align: 'center',
              children: [
                {
                  title: '采购单位',
                  code: `${list[i].id}_ratio`,
                  align: 'center',
                  render: (value: any, record: any, index: number) => {
                    return record._edit ? (
                      <XlbSelect
                        width={80}
                        options={record?.[`${list[i].id}_options`] || []}
                        value={value}
                        allowClear={false}
                        onChange={(e) => inputChange(e, index, list[i].id, record, 'ratio')}
                        onClick={(e) => {
                          e.stopPropagation()
                        }}
                      />
                    ) : (
                      <div style={{ textAlign: 'right' }}>
                        {/* 翻译 */}
                        {
                          record?.[`${list[i].id}_options`]?.find((v: any) => v.value === value)
                            ?.label
                        }
                      </div>
                    )
                  }
                },
                {
                  title: '数量',
                  code: `${list[i].id}_quantity`,
                  align: 'center',
                  render: (value: any, record: any, index: number) => {
                    return record._edit ? (
                      <XlbInputNumber
                        onKeyDown={handleKeyDown}
                        min={0}
                        precision={4}
                        formatter={(value) => {
                          return `${value}`.replace(/[^0-9.]/g, '')
                        }}
                        value={value}
                        parser={(value) => {
                          return value ? value.replace(/[^0-9.]/g, '') : ''
                        }}
                        onChange={(e) => inputChange(e, index, list[i].id, record, 'quantity')}
                        onClick={(e) => {
                          e.stopPropagation()
                        }}
                      />
                    ) : (
                      <div style={{ textAlign: 'right' }}>{value}</div>
                    )
                  }
                },
                {
                  title: `单价`,
                  code: `${list[i].id}_price`,
                  align: 'center',
                  render: (value: any, record: any, index: number) => {
                    return record._edit && hasAuth(['门店订单/采购价', '编辑']) && !record?.isAdd ? (
                      <XlbInputNumber
                        onKeyDown={handleKeyDown}
                        min={0}
                        precision={4}
                        formatter={(value) => {
                          return `${value}`.replace(/[^0-9.]/g, '')
                        }}
                        value={value}
                        parser={(value) => {
                          return value ? value.replace(/[^0-9.]/g, '') : ''
                        }}
                        onChange={(e) => inputChange(e, index, list[i].id, record, 'price')}
                        onFocus={(e) => e.target.select()}
                        onClick={(e) => {
                          e.stopPropagation()
                        }}
                      />
                    ) : hasAuth(['门店订单/采购价', '查询']) ? (
                      <div style={{ textAlign: 'right' }}>{value}</div>
                    ) : (
                      '****'
                    )
                  }
                }
              ]
            }
          ],
          obj: item
        }

        newItemArr.push(newCol)
      }

      // 3. 最终更新数据
      setItemArr([...newItemArr])
      setGoodList(JSON.parse(JSON.stringify(newItemArr.slice(1)))) // 去掉头部

      if (rowData.length > 0) {
        const listIdsT = new Set(list.map((t) => t.id))

        const updatedRows = rowData.map((v) => {
          // 保留已存在且在 list 中的明细
          v.detail =
            v.detail?.filter((k: any) => listIdsT.has(k.id) || listIdsT.has(k.item_id)) || []

          list.forEach((i: any) => {
            const keyPrice = `${i.id}_price`
            const keyQty = `${i.id}_quantity`
            const keyRatio = `${i.id}_ratio`
            const keyOptions = `${i.id}_options`
            const keybasic = `${i.id}_old_basic_price`

            // 保留已有的字段值
            const oldPrice = v[keyPrice]
            const oldQty = v[keyQty]
            const oldUnit = v[keyRatio]

            v[keyRatio] = oldUnit ?? i?.purchase_ratio
            v[keybasic] = i?.store_id_basic_price_map?.[v?.store_id] ?? 0
            v[keyPrice] =
              oldPrice ??
              new Decimal(i?.store_id_basic_price_map?.[v?.store_id] ?? 0)
                .mul(new Decimal(i.purchase_ratio ?? 0))
                .toNumber()
                ?.toFixed(4)
            v[keyQty] = oldQty ?? i.quantity ?? 0
            v[keyOptions] = Array.from(
              new Set([
                // JSON.stringify({
                //   label: i.delivery_unit,
                //   value: i.delivery_ratio,
                // }),
                JSON.stringify({ label: i.unit, value: 1 }),
                JSON.stringify({
                  label: i.purchase_unit,
                  value: i.purchase_ratio
                })
                // JSON.stringify({ label: i.stock_unit, value: i.stock_ratio }),
                // JSON.stringify({
                //   label: i.wholesale_unit,
                //   value: i.wholesale_ratio,
                // }),
              ])
            ).map((item) => JSON.parse(item))
            v._click = false
            v._edit = false

            const existsIndex = v.detail.findIndex((s: any) => s.item_id === i.id || s.id === i.id)

            if (existsIndex === -1) {
              // 不存在，才新增
              v.detail.push({
                ...i,
                price: new Decimal(i?.store_id_basic_price_map?.[v?.store_id] ?? 0)
                  .mul(new Decimal(i.purchase_ratio ?? 0))
                  .toNumber(),
                item_id: i.id,
                item_code: i.code,
                quantity: 0,
                item_bar_code: i.bar_code,
                item_name: i.name,
                ratio: i.purchase_ratio,
                basic_unit: i.unit
              })
            }
          })

          return v
        })

        setRowData([...updatedRows])
      }
    }
  }

  // 添加门店
  const handleBatchStore = async () => {
    const list = await XlbBasicData({
      isMultiple: true,
      url: '/erp-mdm/hxl.erp.store.short.page',
      selectedList: rowData?.map((v) => ({ ...v, id: v.store_id })),
      primaryKey: 'id',
      type: 'store',
      dataType: 'lists',
      data: {
        supplier_id: formRef?.current?.getFieldValue('supplier_id') || '',
        query_supply: true,
        status: true,
        center_flag: false
      },
      fieldNames: {
        idKey: 'id',
        nameKey: 'name'
      }
    })
    if (list) {
      const addres = await ErpRequest.post('/erp/hxl.erp.directsupplypoint.config.stores.exist', {
        data: {
          store_ids: list?.map((v) => v.id),
          supplier_id: formRef?.current?.getFieldValue('supplier_id')
        }
      })
      const newList = list.map((v: any) => {
        return {
          ...v,
          store_id: v.id,
          store_name: v.store_name,
          store_type: v.store_group ? v.store_group.name : '',
          store_type_id: v.store_group ? v.store_group.id : '',
          isAdd: addres?.data?.[v?.id],
          detail: []
        }
      })
      newList.map((item, index) => {
        if (item.newRow) {
          newList.splice(index, 1)
        }
      })
      setRowData([...newList])
    }
  }

  // 删除
  const deleteGoods = () => {
    if (selectedGoodList.length === 0 && selectedStoreList.length === 0) {
      return XlbMessage.error('请先选择要删除的门店或商品！')
    }
    // 处理门店删除
    if (selectedStoreList.length > 0) {
      setRowData((prevRowData) => {
        const newRowData = prevRowData.filter((item) => !selectedStoreList?.includes(item.id))
        if (!newRowData?.length) {
          // 店删光时品也要全删除
          setGoodList([])
          setItemArr([itemArr[0]])
        }
        return newRowData
      })
      setSelectedStoreList([])
    }

    // 处理商品删除
    if (selectedGoodList.length > 0) {
      // 处理 itemArr
      const newItemArr = itemArr.filter((item: any) => !selectedGoodList.includes(item.id))

      setItemArr(newItemArr)
      setGoodList(newItemArr.slice(1))

      // 处理 rowData
      setRowData((prevRowData) => {
        return prevRowData.map((row) => {
          const newRow = { ...row }

          // 删除与选中商品相关的字段
          selectedGoodList.forEach((id) => {
            delete newRow[`${id}_price`]
            delete newRow[`${id}_quantity`]
            delete newRow[`${id}_ratio`]
          })

          // 从 detail 中移除对应商品
          if (Array.isArray(newRow.detail)) {
            newRow.detail = newRow.detail.filter((item: any) => !selectedGoodList.includes(item.id))
          }

          return newRow
        })
      })

      // 清空选择列表
      setSelectedGoodList([])
    }
  }

  // 取消
  const onCancel = () => {
    formRef?.current?.resetFields()
    formUpdate.resetFields()
    setGoodList([])
    setSelectedGoodList([])
    setSelectedStoreList([])
    setRowData([])
    setIsEdit(false)
    setItemArr(JSON.parse(JSON.stringify(itemTableList)))
    setOpen(false)
  }

  // 确定
  const onConfirm = async () => {
    if (!goodList?.length) {
      // 保存前校验
      XlbMessage.error(`数据不能为空`)
      return
    }
    const data = {
      supplier_id: formRef?.current?.getFieldValue('supplier_id'),
      memo: formRef?.current?.getFieldValue('memo'),
      type: 'DIRECT_SUPPLY_RESERVE',
      store_order_save_req_dto_list: rowData.map((v: any) => {
        return {
          ...v,
          type: 'DIRECT_SUPPLY_RESERVE',
          details: v.detail,
          detail: null
        }
      })
    }
    setIsLoading(true)

    const res = await batchOrderSave(data)
    setIsLoading(false)
    if (res.code === 0) {
      if (!res?.data?.length) {
        XlbMessage.success(`生成门店订单成功`)
      } else {
        XlbTipsModal({
          title: '批量制单结果',
          tips: (
            <div style={{ fontSize: '14px' }}>
              <p style={{ color: '#86909c', marginBottom: '10px' }}>
                由于部分商品不在对应门店的经营范围内，或数量设置为0，以下直供单有部分商品未被添加到直供单中：
              </p>
              <p>{res?.data?.join('、')}</p>
            </div>
          ),
          isCancel: false,
          okText: '知道了'
        })
      }
      onCancel()
    }
  }

  if (rowData?.length) {
    itemArr.map((v: any) => storeStrongRender(v))
  }
  itemArr[0].title = (
    <Checkbox
      disabled={
        !goodList?.length
        // goodList.filter((v) => v.obj.generate_replenishment_order_state).length === goodList.length
      }
      value={selectedGoodList.length === goodList.length ? 1111 : 9999}
      checked={goodList.length && selectedGoodList.length === goodList.length}
      onChange={(e: any) => onChange(e, goodList, null)}
    >
      商品名称/商品代码
    </Checkbox>
  )

  return (
    <XlbModal
      open={open}
      centered
      // @ts-ignore
      width={'90%'}
      isCancel={true}
      className={styles.dragModel}
      wrapClassName="xlbDialog"
      title={'直供单批量制单'}
      confirmLoading={isLoading}
      onCancel={() => onCancel()}
      onOk={() => onConfirm()}
    >
      <div ref={modalRef} style={{ marginTop: '12px' }} className={styles.formWrapCus}>
        <XlbProForm
          formList={[
            {
              id: ErpFieldKeyMap?.erpSupplierId,
              label: '供应商',
              disabled: rowData?.length > 0,
              onChange: (e: any) => {
                setIsEdit(!!e)
              },
              fieldProps: {
                style: {
                  width: 265
                }
              }
            },
            {
              id: ErpFieldKeyMap.otherIncomeExpensesName,
              label: '留言备注',
              name: 'memo',
              fieldProps: {
                width: 624
              }
            }
          ]}
          ref={formRef}
        />
        <XlbButton.Group>
          <XlbButton
            label="添加门店"
            onClick={() => handleBatchStore()}
            type="primary"
            disabled={rowData?.some((i) => i?.detail?.length > 0) || !isEdit}
            icon={<XlbIcon name="jia" />}
          />
          <XlbButton
            label="添加商品"
            disabled={!rowData?.length}
            type="primary"
            onClick={() => handleAddClick()}
            icon={<XlbIcon name="jia" />}
          />
          <XlbButton
            label="删除"
            disabled={selectedGoodList.length === 0 && selectedStoreList.length === 0}
            onClick={() => deleteGoods()}
            type="primary"
            icon={<XlbIcon name="shanchu" />}
          />
          {/* <XlbButton
            label="批量修改"
            disabled={selectedGoodList.length === 0}
            onClick={() => batchUpdate()}
            type="primary"
            icon={<XlbIcon name="xiugai" />}
          /> */}
        </XlbButton.Group>
        {/* table */}
        <div className={rowData.length ? styles.table_box : ''} style={{ marginTop: 8 }}>
          <XlbTable
            dataSource={rowData}
            columns={itemArr}
            rowKey="store_id"
            key={rowData.length + '——' + goodList.length}
            isLoading={isLoading}
            emptyCellHeight={document.body.clientHeight - 335}
            style={{ height: 'calc(100vh - 335px - 120px)' }}
            hideOnSinglePage={true}
            selectMode="multiple"
            selectedRowKeys={selectedStoreList}
            onSelectRow={(value) => {
              setSelectedStoreList(value)
            }}
          />
        </div>
      </div>
    </XlbModal>
  )
}

export default BatchOrderIndex

import { LStorage } from '@/utils'

export const UserManageKeyMap = {
  /**公司角色 */
  erpCompanyRole: 'erpCompanyRole',
  /**用户部门 */
  erpUserDetId: 'erpUserDetId',
  /**所属区域 */
  erpAreaId: 'erpAreaId',
  /**组织显隐 */
  erpEnableOrganization: 'erpEnableOrganization',
  /**查询超过  天未登录的用户 */
  erpIsCheckAndLogDays: 'erpIsCheckAndLogDays',
  /**外部角色 */
  erpOutRole: 'erpOutRole',
  /**供应商角色 */
  erpSupplierRole: 'erpSupplierRole',
  /**POS角色 */
  erpPosRole: 'erpPosRole',
  /**批发角色 */
  erpClientRole: 'erpClientRole',
  /**仅查重复手机号 */
  erpRepeatTel: 'erpRepeatTel'
}

export const userManageConfig: any[] = [
  {
    tag: 'ERP',
    id: 'erpCompanyRole',
    componentType: 'select',
    label: '用户角色',
    name: 'role_id',
    request: async (formValues: any, anybaseURL: any, globalFetch: any) => {
      const res = await globalFetch.post(`${anybaseURL}/erp-mdm/hxl.erp.role.page`, {
        type: 'USER',
        inner_role: true,
        filter_user_id: LStorage.get('userInfo').id,
        filter_role_type: true,
        page_size: 2000
      })
      if (res.code == 0) {
        return res.data.content.map((item: any) => ({ label: item.name, value: item.id }))
      }
      return []
    }
  },
  {
    tag: 'ERP',
    id: 'erpUserDetId',
    componentType: 'select',
    label: '用户部门',
    name: 'user_det_id',
    request: async (formValues: any, anybaseURL: any, globalFetch: any) => {
      const res = await globalFetch.post(`${anybaseURL}/erp-mdm/hxl.erp.userdept.find`, {})
      if (res.code == 0) {
        return res.data.map((item: any) => ({ label: item.name, value: item.id }))
      }
      return []
    }
  },
  {
    tag: 'ERP',
    id: 'erpAreaId',
    componentType: 'select',
    label: '所属区域',
    name: 'store_area_id',
    request: async (formValues: any, anybaseURL: any, globalFetch: any) => {
      const res = await globalFetch.post(`${anybaseURL}/erp-mdm/hxl.erp.storearea.find`, {
        delivery_center: LStorage.get('userInfo').store?.enable_delivery_center ? undefined : false
      })
      if (res.code == 0) {
        return res.data.map((item: any) => ({ label: item.name, value: item.id }))
      }
      return []
    }
  },
  {
    tag: 'ERP',
    id: 'erpEnableOrganization',
    componentType: 'select',
    label: '是否组织',
    name: 'enable_organization',
    fieldProps: { defaultValue: false },
    options: [
      { label: '是', value: true },
      { label: '否', value: false }
    ],
    hidden: true
  },
  {
    tag: 'ERP',
    id: 'erpIsCheckAndLogDays',
    componentType: 'group',
    fieldProps: {
      formList: [
        {
          componentType: 'checkbox',
          name: 'un_login_days_flag',
          id: 'group1',
          tag: 'ERP',
          group: false,
          colon: false,
          fieldProps: {
            options: [{ label: '仅查询超', value: 'un_login_days_flag' }]
          }
        },
        {
          componentType: 'inputNumber',
          id: 'group2',
          tag: 'ERP',
          textAfter: '天未登录的用户',
          name: 'un_login_days',
          formItemProps: {
            name: 'un_login_days'
          },
          async request() {
            return {}
          },
          fieldProps: {
            min: -999,
            max: 999
          }
        }
      ]
    }
  },
  {
    tag: 'ERP',
    id: 'erpOutRole',
    componentType: 'select',
    label: '用户角色',
    name: 'role_id',
    request: async (formValues: any, anybaseURL: any, globalFetch: any) => {
      const res = await globalFetch.post(`${anybaseURL}/erp-mdm/hxl.erp.role.page`, {
        type: 'USER',
        inner_role: false,
        filter_user_id: LStorage.get('userInfo').id,
        filter_role_type: true,
        page_size: 2000
      })
      if (res.code == 0) {
        return res.data.content.map((item: any) => ({ label: item.name, value: item.id }))
      }
      return []
    }
  },
  {
    tag: 'ERP',
    id: 'erpSupplierRole',
    componentType: 'select',
    label: '用户角色',
    name: 'role_id',
    request: async (formValues: any, anybaseURL: any, globalFetch: any) => {
      const res = await globalFetch.post(`${anybaseURL}/erp-mdm/hxl.erp.role.page`, {
        type: 'SUPPLIER',
        filter_user_id: LStorage.get('userInfo').id,
        filter_role_type: true,
        page_size: 2000
      })
      if (res.code == 0) {
        return res.data.content.map((item: any) => ({ label: item.name, value: item.id }))
      }
      return []
    }
  },
  {
    tag: 'ERP',
    id: 'erpPosRole',
    componentType: 'select',
    label: '用户角色',
    name: 'role_id',
    request: async (formValues: any, anybaseURL: any, globalFetch: any) => {
      const res = await globalFetch.post(`${anybaseURL}/erp-mdm/hxl.erp.role.page`, {
        type: 'POS',
        filter_user_id: LStorage.get('userInfo').id,
        filter_role_type: true,
        page_size: 2000
      })
      if (res.code == 0) {
        return res.data.content.map((item: any) => ({ label: item.name, value: item.id }))
      }
      return []
    }
  },
  {
    tag: 'ERP',
    id: 'erpClientRole',
    componentType: 'select',
    label: '用户角色',
    name: 'role_id',
    request: async (formValues: any, anybaseURL: any, globalFetch: any) => {
      const res = await globalFetch.post(`${anybaseURL}/erp-mdm/hxl.erp.role.page`, {
        type: 'CLIENT',
        filter_user_id: LStorage.get('userInfo').id,
        filter_role_type: true,
        page_size: 2000
      })
      if (res.code == 0) {
        return res.data.content.map((item: any) => ({ label: item.name, value: item.id }))
      }
      return []
    }
  },
  {
    tag: 'ERP',
    label: '仅查重复手机号',
    id: UserManageKeyMap.erpRepeatTel,
    componentType: 'checkbox',
    fieldProps: {
      options: [{ label: '仅查重复手机号', value: 'repeat_tel' }]
    },
    formItemProps: {
      label: ' ',
      colon: false
    },
    group: false
  }
]
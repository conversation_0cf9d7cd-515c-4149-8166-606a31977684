.details {
  .ant-form-inline .ant-form-item {
    margin-bottom: 12px;
  }
  .xlb-form_container {
    &.xlb-detail-form-content {
      .details-row {
        .xlb-detail-form-item {
          .ant-form-item-label {
            width: auto !important;

            > label {
              color: #86909c;
              height: unset;
            }
          }
          .ant-form-item-control {
            min-width: 180px;
            .ant-form-item-control-input {
              min-height: unset;
              .ant-typography {
              }
            }
          }
        }
      }
    }
  }
}

.details-form-item-upload {
  > .ant-form-item-row {
    flex-wrap: nowrap;

    > .ant-form-item-label {
      // flex: 0 0 100%;
      width: 128px;
      padding-bottom: 12px;
    }
  }
}

.details-row {
  padding-top: 12px;
  // margin-top: 16px;
  // margin-bottom: 16px;
}
.details-row * {
  box-sizing: content-box;
}

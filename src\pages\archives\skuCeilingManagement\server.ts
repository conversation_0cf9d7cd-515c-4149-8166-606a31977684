import { XlbFetch as ErpRequest } from '@xlb/utils';

const Api = {
  /**
   * @name update
   */
  update: async (data: any) =>{

    const url =  '/erp-mdm/hxl.erp.org.skulimit.update'
  return  await ErpRequest.post(url, { ...data })
  },

export :async (data: any) =>{

    const url =  '/erp-mdm/hxl.erp.org.skulimit.export'
  return  await ErpRequest.post(url, { ...data })
  },
  excludeitem :async (data={}) =>{

    const url =  '/erp-mdm/hxl.erp.org.skulimit.excludeitem.find'
  return  await ErpRequest.post(url, { ...data })
  },
  exportExcludeitem :async (data: any) =>{

    const url =  '/erp-mdm/hxl.erp.org.skulimit.excludeitem.export'
  return  await ErpRequest.post(url, { ...data })
  },
  exportExcludeSave :async (org_items: any) =>{

    const url =  '/erp-mdm/hxl.erp.org.skulimit.excludeitem.save'
  return  await ErpRequest.post(url,  org_items)
  },

  }



export default Api
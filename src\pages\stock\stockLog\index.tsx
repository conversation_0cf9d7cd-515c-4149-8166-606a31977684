import PromiseModal from '@/components/promiseModal/PromiseModal';
import { useBaseParams } from '@/hooks/useBaseParams';
import { hasAuth } from '@/utils/kit';
import { LStorage } from '@/utils/storage';
import toFixed from '@/utils/toFixed';
import { wujieBus } from '@/wujie/utils';
import NiceModal from '@ebay/nice-modal-react';
import {
  SearchFormType,
  XlbButton,
  XlbColumns,
  XlbForm,
  XlbIcon,
  XlbMessage,
  XlbTableColumnProps,
  XlbTipsModal,
} from '@xlb/components';
import XlbPageContainer, {
  XlbPageContainerRef,
} from '@xlb/components/dist/lowcodes/XlbPageContainer';
import { XlbFetch as ErpRequest } from '@xlb/utils';
import { Form, Tooltip } from 'antd';
import classnames from 'classnames';
import dayjs from 'dayjs';
import { cloneDeep } from 'lodash';
import { useEffect, useRef, useState } from 'react';
import { useStore } from '../store';
import { searchFormList, tableList, tableList1 } from './data';

const { Table, SearchForm } = XlbPageContainer;

const StockLog = () => {
  const pageRef = useRef<XlbPageContainerRef>(null);
  const pageRef1 = useRef<XlbPageContainerRef>(null);
  const [isLoading, setisLoading] = useState<boolean>(false);
  const { paramsValue, setParamsValue } = useStore();
  const { enable_cargo_owner } = useBaseParams((state) => state);

  // from
  const [form] = Form.useForm();
  const [formList, setFormList] = useState<SearchFormType[]>(
    searchFormList.filter((v) => v.label !== '货主' || enable_cargo_owner),
  );
  // table
  const tableListData = tableList.filter(
    (v) => v.name !== '货主' || enable_cargo_owner,
  );
  const tableList1Data = tableList1.filter(
    (v) => v.name !== '货主' || enable_cargo_owner,
  );
  const [itemArr, setItemArr] = useState<XlbTableColumnProps<any>[]>(
    cloneDeep(tableListData),
  );
  //
  const [isExport, setIsExport] = useState<boolean>(false);
  const [isFold, setIsFold] = useState<boolean>(false);

  const tableRender = (item: any) => {
    switch (item.code) {
      case 'index':
        item.render = (value: any, record: any, index: number) => {
          return value == '合计' ? (
            <div className="info overwidth">{value}</div>
          ) : (
            ''
          );
        };
        break;
      case 'store_name':
        item.render = (value: any, record: any, index: number) => {
          return <div className="overwidth cursors">{value}</div>;
        };
        break;
      case 'order_fid':
        item.render = (value: any, record: any, index: number) => {
          let isReadOnly = ['积分兑换'].includes(record.order_type);
          return (
            <div
              className="link overwidth"
              onClick={(e) => {
                if (isReadOnly) return;
                e.stopPropagation();
                NiceModal.show(PromiseModal, record);
              }}
            >
              {value}
            </div>
          );
        };
        break;
      case 'basic_price':
      case 'price':
      case 'basic_cost_price':
      case 'basic_no_tax_cost_price':
      case 'cost_price':
      case 'no_tax_cost_price':
        item.render = (value: any, record: any, index: number) => {
          return (
            <div className="info overwidth">
              {record.index !== '合计'
                ? value !== '****'
                  ? Number(value).toFixed(4)
                  : '****'
                : ''}
            </div>
          );
        };
        break;
      case 'money':
      case 'no_adjust_cost_money':
      case 'no_tax_cost_money':
      case 'adjust_money':
      case 'no_tax_adjust_money':
        item.render = (value: any, record: any, index: number) => {
          return (
            <div className="info overwidth">
              {value !== '****' ? Number(value).toFixed(2) : '****'}
            </div>
          );
        };
        break;
      case 'balance_money':
        item.render = (value: any, record: any, index: number) => {
          return (
            <div className="info overwidth">
              {record.index !== '合计'
                ? value !== '****'
                  ? Number(value).toFixed(2)
                  : '****'
                : ''}
            </div>
          );
        };
        break;
      case 'quantity':
      case 'basic_quantity':
      case 'balance_basic_quantity':
      case 'balance_stock_quantity':
      case 'in_basic_quantity':
      case 'in_quantity':
      case 'out_basic_quantity':
      case 'out_quantity':
        item.render = (value: any, record: any, index: number) => {
          return (
            <div className="info overwidth">
              {toFixed(value, 'QUANTITY', true)}
            </div>
          );
        };
        break;
      case 'memo':
        item.render = (value: any, record: any) => {
          return (
            <Tooltip placement="topLeft" autoAdjustOverflow title={value}>
              <div className="info overwidth"> {value}</div>
            </Tooltip>
          );
        };
        break;
      default:
        item.render = (value: any) => (
          <div className="info overwidth">{value}</div>
        );
        break;
    }
    return item;
  };

  // 获取数据
  const pageContainerUrl = (type: number = 1): any => {
    if (type === 1) {
      return form.getFieldValue('query_mode') === 1
        ? '/erp/hxl.erp.itemstocklog.storeitemsummary.find'
        : '/erp/hxl.erp.itemstocklog.page';
    } else if (type === 2) {
      return form.getFieldValue('query_mode') === 1
        ? {
            key: 'hxl.erp.itemstocklog.storeitemsummary.find.page-columns',
            name: 'hxl.erp.itemstocklog.storeitemsummary.find.page',
          }
        : {
            key: 'hxl.erp.itemstocklog.page.page-columns',
            name: 'hxl.erp.itemstocklog.page.page',
          };
    }
  };
  const prevPost = () => {
    const time_type = form.getFieldValue('time_type');
    const [start_time, end_time] = form.getFieldValue('compactDatePicker') || [
      '2000-01-01 00:00:00',
      '2222-08-25 23:59:59',
    ];
    const formattedTimeRange = [
      start_time ? `${start_time}:00.000` : '2000-01-01 00:00:00.000',
      end_time ? `${end_time}:59.999` : '2222-08-25 23:59:59.999',
    ];
    const data: any = {
      ...form.getFieldsValue(),
      order_types: form.getFieldValue('order_types')
        ? form.getFieldValue('order_types')
        : null,
      flag:
        form.getFieldValue('flag') == 'true'
          ? true
          : form.getFieldValue('flag') == 'false'
            ? false
            : null,
      storehouse_ids: form.getFieldValue('storehouse_ids')
        ? [form.getFieldValue('storehouse_ids')]
        : null,
      create_date: time_type === 'create_date' ? formattedTimeRange : null,
      operate_date: time_type === 'operate_date' ? formattedTimeRange : null,
      keyword: form.getFieldValue('keyword')?.trim(),
      order_fid: form.getFieldValue('order_fid')?.trim(),
      batch_number: form.getFieldValue('batch_number')?.trim(),
      expire_date: form.getFieldValue('expire_date')
        ? dayjs(form.getFieldValue('expire_date'))?.format('YYYY-MM-DD') +
          ' 23:59:59'
        : null,
      producing_date: form.getFieldValue('producing_date')
        ? dayjs(form.getFieldValue('producing_date'))?.format('YYYY-MM-DD') +
          ' 00:00:00'
        : null,
      finance_code: form.getFieldValue('finance_code') || null,
    };
    delete data.compactDatePicker;
    delete data.Data_Compact_RangeType_compactDatePicker;
    return data;
  };
  const getData = async () => {
    const [startTime, endTime] = form.getFieldValue('compactDatePicker') || [];
    if (!startTime || !endTime) return;
    if (!startTime || !endTime) return;
    setisLoading(true);
    pageRef.current?.fetchData();
    setisLoading(false);
  };
  // 合计
  const setFooterDataTmp = (data: any) => {
    let _footerData: Record<string, any> = {
      _index: '合计',
      quantity: Number(data.quantity || 0)?.toFixed(3) || 0 || '0.000',
      basic_quantity:
        Number(data.basic_quantity || 0)?.toFixed(3) || 0 || '0.000',

      in_basic_quantity:
        Number(data.in_basic_quantity || 0)?.toFixed(3) || 0 || '0.000',
      in_quantity: Number(data.in_quantity || 0)?.toFixed(3) || 0 || '0.000',
      out_basic_quantity:
        Number(data.out_basic_quantity || 0)?.toFixed(3) || 0 || '0.000',
      out_quantity: Number(data.out_quantity || 0)?.toFixed(3) || 0 || '0.000',

      money:
        data?.money !== '****'
          ? Number(data?.money || 0)?.toFixed(2) || 0
          : '****',
      no_adjust_cost_money:
        data.no_adjust_cost_money !== '****'
          ? Number(data.no_adjust_cost_money || 0)?.toFixed(2) || 0
          : '****',
      adjust_money:
        data.adjust_money !== '****'
          ? Number(data.adjust_money || 0)?.toFixed(2) || 0
          : '****',
      no_tax_adjust_money:
        data.no_tax_adjust_money !== '****'
          ? Number(data?.no_tax_adjust_money || 0)?.toFixed(2) || 0
          : '****',
      no_tax_cost_money:
        data.no_tax_cost_money !== '****'
          ? Number(data.no_tax_cost_money || 0)?.toFixed(2) || 0
          : '****',
    };
    return Object.keys(_footerData).length ? [_footerData] : [];
  };
  // 导出
  const exportItem = async (e: any) => {
    const data = prevPost();
    const res = await ErpRequest.post(
      form.getFieldValue('query_mode') == 1
        ? '/erp/hxl.erp.itemstocklog.storeitemsummary.export'
        : '/erp/hxl.erp.itemstocklog.export',
      data,
    );
    if (res?.code === 0) {
      wujieBus?.$emit('xlb_erp-event', { code: 'downloadEnd', target: e });
      XlbMessage.success('导出受理成功，请前往下载中心查看');
    }
  };

  const onValuesChange = (changeValue: any) => {
    const { query_mode = null } = changeValue;
    if (query_mode === 0) {
      let tmp: any[] = tableListData.map((v: any) => tableRender(v));
      setItemArr([...tmp]);
    } else if (query_mode === 1) {
      let tmp: any[] = tableList1Data.map((v: any) => tableRender(v));
      setItemArr([...tmp]);
    }
  };

  itemArr.map((v) => tableRender(v));
  useEffect(() => {
    if (paramsValue?.goStockLog) {
      form.setFieldsValue({
        time_desc: 0,
        store_ids: [
          paramsValue?.store_id
            ? paramsValue?.store_id
            : LStorage.get('userInfo').store_id,
        ],
        store_names: paramsValue?.store_name
          ? paramsValue?.store_name
          : LStorage.get('userInfo').store_name,
        item_ids: [paramsValue?.item_id],
        item_names: paramsValue?.item_name,
        storehouse_ids: paramsValue?.storehouse_id
          ? paramsValue?.storehouse_id
          : '',
        storehouse_names: paramsValue?.storehouse_id
          ? paramsValue?.storehouse_id
          : '',
        time_type: 'create_date',
        compactDatePicker: [
          dayjs().startOf('day').format('YYYY-MM-DD HH:mm'),
          dayjs().endOf('day').format('YYYY-MM-DD HH:mm'),
        ],
        query_mode: 0,
        order_types: [],
        item_type_ids: null,
        item_type: null,
        flag: null,
        keyword: null,
        order_fid: null,
        batch_number: null,
        producing_date: null,
        expire_date: null,
      });
      getData();
    } else {
      // 初始化form
      form.setFieldsValue({
        time_desc: 0,
        compactDatePicker: [
          dayjs().startOf('day').format('YYYY-MM-DD HH:mm'),
          dayjs().endOf('day').format('YYYY-MM-DD HH:mm'),
        ],
        store_ids: [LStorage.get('userInfo').store_id],
        store_names: LStorage.get('userInfo').store_name,
        time_type: 'create_date',
        unit_type: 'BASIC',
      });
    }
  }, [paramsValue]);
  useEffect(() => {
    pageRef1.current?.setPagin({ pageNum: 1, pageSize: 200, total: 0 });
  }, [itemArr]);
  return (
    <XlbPageContainer
      ref={pageRef}
      url={pageContainerUrl()}
      tableColumn={itemArr}
      immediatePost={false}
      afterPost={(data = []) => {
        setIsExport(!!data?.content?.length);
        return data;
      }}
      prevPost={() => prevPost()}
      footerDataSource={(data?: any) => {
        return data ? setFooterDataTmp(data) : [];
      }}
    >
      <XlbPageContainer.ToolBtnNoStyle>
        {(context) => {
          pageRef1.current = context;
          return <></>;
        }}
      </XlbPageContainer.ToolBtnNoStyle>
      <div
        className={'button_box row-flex'}
        style={{ marginBottom: '10px', padding: '0 16px' }}
      >
        <div style={{ width: '90%' }} className="row-flex">
          <XlbButton.Group>
            <XlbButton
              label="查询"
              type={'primary'}
              disabled={isLoading}
              onClick={() => getData()}
              icon={<XlbIcon name="sousuo" />}
            />
            {hasAuth(['库存进出记录', '导出']) && (
              <XlbButton
                label="导出"
                type={'primary'}
                disabled={!isExport || isLoading}
                onClick={(e: any) => exportItem(e)}
                icon={<XlbIcon name="daochu" />}
              />
            )}
          </XlbButton.Group>
        </div>
        <div
          style={{
            width: '20%',
            height: '28px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'flex-end',
            columnGap: '8px',
          }}
        >
          <div
            style={{
              cursor: 'pointer',
              display: 'flex',
              alignItems: 'center',
              color: '#3d66fe',
              width: '100px',
              marginRight: '30px',
            }}
            onClick={() =>
              XlbTipsModal({
                title: '查询小贴士',
                tips: (
                  <div>
                    您好!由于门店众多(已超万家)且商品丰富，为提高查询效率，我们对查询做了以下调整:
                    <br />
                    1.当选择单品单店时，时间可不做限制，否则最长时间跨度为半年。
                    <br />
                    2.若选择的门店少于200家，可查询全部商品，最大时间跨度为半年。
                    <br />
                    3.若选择门店超过200家，查询商品最多200条商品，最大时间跨度为半年。
                  </div>
                ),
                okText: '我知道了',
              })
            }
          >
            <XlbIcon name="xiaotieshi" size={20} />
            <span style={{ marginLeft: '5px' }}>查询小贴士</span>
          </div>
          <Tooltip title={isFold ? '收起' : '展开'}>
            <XlbIcon
              data-type={'顶层展开收起'}
              size={20}
              onClick={() => setIsFold(!isFold)}
              name="shouqi"
              className={classnames('xlb-columns-main-btn', {
                'xlb-columns-fold': !isFold,
                'xlb-columns-expand-btn-dev': true,
              })}
            />
          </Tooltip>
          <XlbColumns
            isFold={isFold}
            isFoldChange={setIsFold}
            url={pageContainerUrl(2)?.key}
            originColumns={tableListData}
            value={itemArr}
            onChange={setItemArr}
            name={pageContainerUrl(2)?.name}
          />
        </div>
      </div>

      <div
        style={{ display: isFold ? 'none' : 'block' }}
        className={'form_header_box'}
      >
        <SearchForm>
          <XlbForm
            form={form}
            formList={formList}
            isHideDate
            onValuesChange={onValuesChange}
          />
        </SearchForm>
      </div>

      <Table
        primaryKey="id"
        selectMode={'single'}
        isFold={isFold}
        isLoading={isLoading}
      />
    </XlbPageContainer>
  );
};
export default StockLog;

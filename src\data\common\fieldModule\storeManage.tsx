export const StoreManageKeyMap = {
  // 门店标签
  storeLabels: 'storeLabels',
  // 门店状态
  erpStoreState: 'erpStoreState',
  // 门店管理-经营类型
  erpManagementType: 'erpManagementType',
  // 门店管理-地区属性
  erpAreaAttributes: 'erpAreaAttributes',
  // 门店管理-门店属性
  erpStoreAttributes: 'erpStoreAttributes',
  // 门店管理-商圈属性
  erpBusinessDistrictAttributes: 'erpBusinessDistrictAttributes',
  // 门店管理-是否启用
  erpStoreStatus: 'erpStoreStatus',
  // 门店管理-营业执照
  erpHasBusinessLicense: 'erpHasBusinessLicense',
  // 门店管理-行政区域
  erpAreaCodes: 'erpAreaCodes',
  // 门店管理-业财核算组织
  erpFinanceOrganization: 'erpFinanceOrganization'
}

export const storeManageConfig: any[] = [
  {
    tag: 'ERP',
    label: '门店标签',
    id: StoreManageKeyMap.storeLabels,
    name: 'store_labels',
    fieldProps: {
      dialogParams: {
        type: 'storeLabel',
        dataType: 'lists',
        isMultiple: true,
        isLeftColumn: false
      },
      fieldNames: {
        idKey: 'id',
        nameKey: 'store_label_name'
      }
    },
    componentType: 'inputDialog'
  },
  // 门店状态
  {
    tag: 'ERP',
    label: '门店状态',
    id: StoreManageKeyMap.erpStoreState,
    name: 'states',
    componentType: 'select',
    fieldProps: {
      options: [
        { value: 'TO_BE_DELIVERED', label: '待交付' },
        { value: 'BUILDING', label: '营建中' },
        { value: 'TO_BE_OPENED', label: '待营业' },
        { value: 'OPENED', label: '营业中' },
        { value: 'CLOSED', label: '已闭店' },
        { value: 'CANCELLED', label: '已取消' },
        { value: 'TO_BE_CLOSED', label: '待停业' }
      ]
    },
    formItemProps: {
      getValueFromEvent: (value: any) => {
        return value ? [value] : null
      }
    }
  },
  // 门店管理-经营类型
  {
    tag: 'ERP',
    label: '经营类型',
    id: StoreManageKeyMap.erpManagementType,
    name: 'management_type',
    componentType: 'select',
    fieldProps: {
      options: [
        {
          label: '直营店',
          value: 0
        },
        {
          label: '加盟店',
          value: 1
        }
      ]
    }
  },
  // 门店管理-地区属性
  {
    tag: 'ERP',
    label: '地区属性',
    id: StoreManageKeyMap.erpAreaAttributes,
    name: 'area_attributes',
    componentType: 'select',
    fieldProps: {
      options: [
        {
          label: '地级市',
          value: 0
        },
        {
          label: '县级市',
          value: 1
        },
        {
          label: '乡镇',
          value: 2
        }
      ]
    }
  },
  // 门店管理-门店属性
  {
    tag: 'ERP',
    label: '门店属性',
    id: StoreManageKeyMap.erpStoreAttributes,
    name: 'store_attributes',
    componentType: 'select',
    fieldProps: {
      options: [
        {
          label: '标准店',
          value: 0
        },
        {
          label: '综合店',
          value: 1
        },
        {
          label: '旗舰店',
          value: 2
        }
      ]
    }
  },
  // 门店管理-商圈属性
  {
    tag: 'ERP',
    label: '商圈属性',
    id: StoreManageKeyMap.erpBusinessDistrictAttributes,
    name: 'business_district_attributes',
    componentType: 'select',
    fieldProps: {
      options: [
        {
          label: '商业圈',
          value: 0
        },
        {
          label: '居民区',
          value: 1
        },
        {
          label: '校区',
          value: 2
        }
      ]
    }
  },
  // 是否启用
  {
    tag: 'ERP',
    label: '启用',
    id: StoreManageKeyMap.erpStoreStatus,
    name: 'status',
    componentType: 'select',
    fieldProps: {
      options: [
        {
          label: '是',
          value: true
        },
        {
          label: '否',
          value: false
        }
      ]
    }
  },
  // 营业执照
  {
    tag: 'ERP',
    label: '营业执照',
    id: StoreManageKeyMap.erpHasBusinessLicense,
    name: 'has_business_license',
    componentType: 'select',
    fieldProps: {
      options: [
        {
          label: '有',
          value: true
        },
        {
          label: '无',
          value: false
        }
      ]
    }
  },
  // 门店管理-行政区域
  {
    tag: 'ERP',
    label: '行政区域',
    name: 'city_codes',
    id: StoreManageKeyMap?.erpAreaCodes,
    fieldProps: {
      treeModalConfig: {
        zIndex: 2002,
        title: '选择区域', // 标题
        url: '/erp-mdm/hxl.erp.store.area.find.all', // 请求地址
        dataType: 'lists',
        checkable: true, // 是否多选
        primaryKey: 'code',
        fieldName: {
          id: 'code',
          parent_id: 'parent_code',
          name: 'name'
        },
        width: 360
      },
      fieldNames: {
        // @ts-ignore
        idKey: 'code',
        nameKey: 'name'
      }
    },
    componentType: 'inputDialog'
  },
  // 门店管理-业财核算组织
  {
    tag: 'ERP',
    id: StoreManageKeyMap?.erpFinanceOrganization,
    componentType: 'select',
    label: '业财核算组织',

    name: 'finance_organization',
    request: async (_formValues: any, anybaseURL: any, globalFetch: any) => {
      const res = await globalFetch.post(`${anybaseURL}/erp/hxl.erp.baseparam.read`, {})
      if (res.code == 0) {
        return res.data?.finance_organizations?.map((item: any) => ({ label: item, value: item }))
      }
      return []
    },
    fieldProps: {
      allowClear: true,
      popupMatchSelectWidth: false
    }
  }
]
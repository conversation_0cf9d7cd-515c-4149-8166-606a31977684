import { orderStatusIcons } from '@/components/data';
import { useBaseParams } from '@/hooks/useBaseParams';
import { hasAuth } from '@/utils';
import {
  XlbBasicData,
  XlbBasicForm,
  XlbBlueBar,
  XlbButton,
  XlbDatePicker,
  XlbDropdownButton,
  XlbIcon,
  XlbInput,
  XlbInputDialog,
  XlbInputNumber,
  XlbMessage,
  XlbSelect,
  XlbTable,
  XlbTableColumnProps,
  XlbTabs,
  XlbTipsModal,
} from '@xlb/components';
import { XlbFetch as ErpRequest, XlbFetch } from '@xlb/utils';
import { Col, ConfigProvider, Row, Spin } from 'antd';
import dayjs from 'dayjs';
import { useEffect, useRef, useState } from 'react';
import styles from './index.less';
import Api from './server';
const Item = (props: any) => {
  const { record, onBack } = props;
  const [info, setInfo] = useState<any>({ state: 'INIT', value: null });
  const [form] = XlbBasicForm.useForm();
  const [rowData, setRowData] = useState<any[]>([]);
  const [selectRow, setSelectRow] = useState<any[]>([]);
  const { enable_organization } = useBaseParams((state) => state);
  const [edit, setEdit] = useState<boolean>(false); //触发表格编辑
  const [storeIds, setStoreIds] = useState<string>(''); //触发表格编辑
  const [itemArrdetail, setdetailItemArr] =
    useState<XlbTableColumnProps<any>[]>();
  const [goodsPageSize, setGoodsPageSize] = useState<number>(200);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [fid, setFid] = useState<number | string>();
  const [orgList, setOrgList] = useState<any[]>([]);
  const [storeList, setStoreList] = useState<any[]>([]);
  const [fetching, setFetching] = useState<boolean>(false);
  const [orgFetching, setOrgFetching] = useState<boolean>(false);
  const [baseInfoActiveKey, setBaseInfoActiveKey] =
    useState<string>('baseInfo');
  //获取组织
  const getOrgList = async () => {
    if (enable_organization) {
      setOrgFetching(true);
      const data = { level: 3 };
      const res = await ErpRequest.post('/erp-mdm/hxl.erp.org.find', data);
      setOrgFetching(false);
      if (res.code == 0) {
        const org_list = res.data.map((i: any) => ({
          value: i.id,
          label: i.name,
        }));
        setOrgList(org_list);
      }
    }
  };
  const getStore = async (id?: string | number) => {
    setStoreList([]);
    form?.setFieldsValue({
      stores: [],
      storeList: [],
      delivery_center_store_id: '',
    });
    const data = {
      org_id: enable_organization
        ? id
          ? id
          : form.getFieldValue('org_id')
        : undefined,
    };
    setFetching(true);
    const res = await XlbFetch.post('/erp-mdm/hxl.erp.store.center.find', data);
    setFetching(false);
    if (res?.code === 0) {
      const arr = res.data?.map((v: any) => ({
        ...v,
        label: v.store_name,
        value: v.id,
      }));
      form?.setFieldsValue({
        storeList: arr,
      });
      setStoreList(arr);
    }
  };
  const dataParams = () => {
    const start_time = form.getFieldValue('start_time');
    const end_time = form.getFieldValue('end_time');
    const data = {
      org_id: enable_organization
        ? Array.isArray(form.getFieldValue('org_id'))
          ? form.getFieldValue('org_id')[0]
          : form.getFieldValue('org_id')
        : undefined,
      stores: form.getFieldValue('stores') || [],
      memo: form.getFieldValue('memo'),
      type: form.getFieldValue('type'),
      fid: form.getFieldValue('fid'),
      start_time: start_time
        ? dayjs(start_time).format('YYYY-MM-DD HH:mm:ss')
        : undefined,
      end_time: end_time
        ? dayjs(end_time).format('YYYY-MM-DD') + ' 23:59:59'
        : undefined,
      delivery_center_store_id: form.getFieldValue('delivery_center_store_id'),
      rules: rowData,
    };
    return data;
  };
  // 时间校验
  const validateTimeRange = (
    start: string | dayjs.Dayjs,
    end: string | dayjs.Dayjs,
  ): boolean => {
    if (!start || !end) return false;

    const startTime = dayjs(start);
    const endTime = dayjs(end);

    if (endTime.isBefore(startTime)) {
      return true;
    }
    return false;
  };
  // 保存
  const operateOrder = async () => {
    try {
      await form.validateFields();
    } catch (err: any) {
      return false;
    }

    rowData[rowData.length - 1]?.newRow &&
      rowData.splice(rowData.length - 1, 1);
    if (rowData[0]?.newRow || rowData.length == 0) {
      XlbTipsModal({
        tips: '请先添加商品！',
      });
      return false;
    }
    const start = form?.getFieldValue('start_time');
    const end = form?.getFieldValue('end_time');
    if (validateTimeRange(start, end)) {
      XlbMessage.error('结束日期不能早于开始日期');
      return;
    }
    const data = dataParams();
    const urlRequest = fid == 1 ? Api.addInfo(data) : Api.updateInfo(data);
    const res = await urlRequest;
    if (res.code == 0) {
      setEdit(false);
      XlbMessage.success('操作成功');
      readinfo(res.data?.fid || form.getFieldValue('fid'));
    }
  };

  // 作废
  const handleInvalid = async () => {
    const data = {
      fid: form.getFieldValue('fid'),
    };
    const res = await XlbFetch.post('/erp/hxl.erp.campaign.invalid', data);
    if (res?.code == 0) {
      XlbMessage.success('操作成功');
      readinfo(res.data?.fid || form.getFieldValue('fid'));
    }
  };
  // 审核
  const auditOrder = async () => {
    try {
      await form.validateFields();
    } catch (err: any) {
      return false;
    }

    rowData[rowData.length - 1]?.newRow &&
      rowData.splice(rowData.length - 1, 1);
    if (rowData[0]?.newRow || rowData.length == 0) {
      XlbTipsModal({
        tips: '请先添加商品！',
      });
      return false;
    }
    // 判断每条数据的condition_value是否为空
    const isConditionValueEmpty = rowData.some(
      (item: any) => !item.present_item_stock,
    );
    if (isConditionValueEmpty) {
      XlbMessage.error('请检查数据是否填写完整！');
      return false;
    }
    const start = form?.getFieldValue('start_time');
    const end = form?.getFieldValue('end_time');
    if (validateTimeRange(start, end)) {
      XlbMessage.error('结束日期不能早于开始日期！');
      return;
    }
    const data = dataParams();
    data.fid = form.getFieldValue('fid');
    const res = await Api.auditInfo(data);
    if (res.code == 0) {
      setEdit(false);
      XlbMessage.success('操作成功');
      readinfo(res.data?.fid || form.getFieldValue('fid'));
    }
  };
  //读取信息
  const readinfo = async (fid: any) => {
    const res = await Api.readInfo({ fid });
    if (res.code === 0) {
      setIsLoading(true);
      await getStore(res?.data?.org_id);
      form.setFieldsValue({
        ...res.data,
        org_id: [res.data.org_id],
        org_name: res.data.org_name,
        start_time: res.data.start_time?.slice(0, 10),
        end_time: res.data.end_time?.slice(0, 10),
        stores: res.data.stores,
        type: res.data.type,
        memo: res.data.memo,
        delivery_center_store_id: res.data.delivery_center_store_id,
        status: res?.data?.status,
      });
      setFid(res?.data?.fid);
      setRowData([...res.data.rules]);
      setStoreIds(res.data.delivery_center_store_id);
      setInfo({ ...res.data, state: res.data?.status });
      setIsLoading(false);
    }
  };
  const exportItem = async (e: any) => {
    setIsLoading(true);
    const data = { fid: form.getFieldValue('fid') };
    const res = await ErpRequest.post(
      '/erp/hxl.erp.deliverypriceadjust.detail.export',
      data,
    );

    if (res.code == 0) {
      XlbMessage.success('导出受理成功，请前往下载中心查看');
    }
    setIsLoading(false);
  };
  const goBack = () => {
    onBack(true);
  };
  useEffect(() => {
    if (record.fid !== 1) {
      readinfo(record.fid);
    }
    form.setFieldsValue({
      type: 'SPEND_GET',
    });
    setFid(record.fid);
    getOrgList();
  }, []);

  const addMainGoods = async () => {
    const data = await XlbBasicData({
      type: 'goods',
      isMultiple: true,
      isLeftColumn: true,
      dataType: 'lists',
      primaryKey: 'id',
      url: '/erp/hxl.erp.campaign.item.page',
      resetForm: true,
      data: {
        delivery_center_store_id: form?.getFieldValue(
          'delivery_center_store_id',
        ),
        store_ids: form?.getFieldValue('stores'),
      },
      selectedList:
        rowData.map((item: any) => {
          return {
            id: item.main_item_id,
          };
        }) || [],
    });
    if (data) {
      // 去除已经存在的商品
      const list = data?.filter(
        (item: any) =>
          !rowData?.some((row: any) => row.main_item_id === item.id),
      );
      const exclude_goods: any = [];
      list?.forEach((item: any, index: number) => {
        exclude_goods.push({
          fid: index,
          main_item_id: item.id,
          main_item_code: item.code,
          main_item_name: item.name,
          condition_value: null,
          main_item_stock: null,
          present_item_id: null,
          present_item_code: null,
          present_value: null,
          present_item_stock: null,
          main_item_weigh: item.weigh,
          present_weigh: false,
        });
      });
      setRowData((prev: any) => [...prev, ...exclude_goods]);
      setSelectRow([]);
    }
  };

  const deleteGoods = () => {
    const deleteItem = rowData.filter(
      (item: any) => !selectRow.includes(item?.main_item_id),
    );
    setRowData(deleteItem);
    setSelectRow([]);
  };

  // 计算赠品活动库存
  const calculateConditionValue = (
    record: any,
    main_item_stock = record?.main_item_stock,
    condition_value = record?.condition_value,
    present_value = record?.present_value,
  ) => {
    if (main_item_stock && present_value && condition_value) {
      const num = Math.floor(main_item_stock / condition_value);
      setRowData((prev: any) => {
        return prev?.map((item: any, i: any) =>
          item?.main_item_id === record?.main_item_id
            ? {
                ...item,
                main_item_stock: main_item_stock,
                present_value: present_value,
                condition_value: condition_value,
                present_item_stock: num * present_value,
              }
            : item,
        );
      });
    } else {
      setRowData((prev: any) => {
        return prev?.map((item: any, i: any) =>
          item?.main_item_id === record?.main_item_id
            ? {
                ...item,
                main_item_stock: main_item_stock,
                present_value: present_value,
                condition_value: condition_value,
                present_item_stock: 0,
              }
            : item,
        );
      });
    }
  };
  const referenceRef = useRef<HTMLDivElement | null>(null);
  const [dialogWidth, setDialogWidth] = useState<number>(180); // 默认值

  useEffect(() => {
    if (referenceRef.current) {
      const observer = new ResizeObserver(() => {
        const width = referenceRef.current?.offsetWidth - 118 || 180;
        setDialogWidth(width);
      });

      observer.observe(referenceRef.current);

      return () => observer.disconnect(); // 清理
    }
  }, []);
  return (
    <ConfigProvider
      theme={{
        token: {
          screenXLMin: 1280,
          screenXL: 1280,
        },
      }}
    >
      <div
        style={{
          padding: 12,
          height: 'calc(100vh - 104px)',
          display: 'flex',
          flexDirection: 'column',
        }}
        className={styles.form_container_wrapper_Campaign}
      >
        <div>
          <XlbButton.Group>
            {hasAuth(['配送营销活动', '编辑']) && (
              <XlbButton
                label="保存"
                disabled={info.state !== 'INIT' || isLoading}
                onClick={() => operateOrder()}
                type="primary"
                icon={<XlbIcon name="baocun" />}
              />
            )}
            {hasAuth(['配送营销活动', '审核']) && (
              <XlbButton
                label="审核"
                type="primary"
                disabled={info.state !== 'INIT' || fid === 1 || isLoading}
                onClick={() => auditOrder()}
                icon={<XlbIcon name="shenhe" />}
              />
            )}
            {hasAuth(['配送营销活动', '处理']) && (
              <XlbDropdownButton
                label="处理"
                dropList={[
                  {
                    label: '处理通过',
                    disabled:
                      info.state !== 'AUDIT' ||
                      !hasAuth(['配送营销活动', '处理']) ||
                      isLoading,
                  },
                  {
                    label: '处理拒绝',
                    disabled:
                      info.state !== 'AUDIT' ||
                      !hasAuth(['配送营销活动', '处理']) ||
                      isLoading,
                  },
                  {
                    label: '处理中止',
                    disabled:
                      !['HANDLE', 'EFFECT'].includes(info.state) ||
                      !hasAuth(['配送营销活动', '处理']) ||
                      isLoading,
                  },
                ]}
                dropdownItemClick={async (value: number, item: any) => {
                  let res: any = null;
                  setIsLoading(true);
                  switch (item?.label) {
                    case '处理拒绝':
                      res = await Api.handleInfo({
                        fid: form.getFieldValue('fid') || fid,
                        status: 'HANDLE_REFUSE',
                      });
                      break;
                    case '处理通过':
                      res = await Api.handleInfo({
                        fid: form.getFieldValue('fid') || fid,
                        status: 'HANDLE',
                      });
                      break;
                    case '处理中止':
                      res = await Api.handleInfo({
                        fid: form.getFieldValue('fid') || fid,
                        status: 'TERMINATE',
                      });
                      break;
                    default:
                      break;
                  }
                  setIsLoading(false);
                  if (res.code === 0) {
                    XlbMessage.success('处理成功');
                    readinfo(form.getFieldValue('fid'));
                  }
                }}
              />
            )}
            {hasAuth(['配送营销活动', '作废']) && (
              <XlbButton
                label="作废"
                type="primary"
                disabled={
                  !['AUDIT', 'INIT'].includes(info?.state) ||
                  fid === 1 ||
                  isLoading
                }
                onClick={() => handleInvalid()}
              />
            )}
            <XlbButton
              label="返回"
              type="primary"
              onClick={goBack}
              icon={<XlbIcon name="fanhui" />}
            />
          </XlbButton.Group>
        </div>
        <XlbBasicForm form={form} style={{ margin: '12px 0 0 0' }}>
          <XlbTabs
            defaultActiveKey="baseInfo"
            activeKey={baseInfoActiveKey}
            onChange={(e) => setBaseInfoActiveKey(e)}
            items={[
              {
                label: '基本信息',
                key: 'baseInfo',
                children: (
                  <div
                    className={styles.form_container_transferDocument}
                    style={{ display: 'flex' }}
                  >
                    <div className="row-flex">
                      <div
                        className="row-flex"
                        style={{ flexWrap: 'wrap', flex: 1 }}
                      >
                        <Row gutter={12} wrap>
                          <Col xs={8} sm={8} md={8} lg={8} xl={6} xxl={6}>
                            {enable_organization && (
                              <XlbBasicForm.Item name="org_id" label="组织">
                                <XlbSelect
                                  options={orgList}
                                  allowClear
                                  style={{ width: dialogWidth - 3 }}
                                  disabled={
                                    info.state !== 'INIT' || rowData?.length > 0
                                  }
                                  onChange={() => getStore()}
                                  notFoundContent={
                                    orgFetching && <Spin size="small" />
                                  }
                                  showSearch
                                  filterOption={(input, option) => {
                                    return (
                                      (
                                        `${option!.label ? option!.label.toString() : ''}` as unknown as string
                                      )
                                        .toLowerCase()
                                        .includes(input.toLowerCase()) ||
                                      (
                                        `${option!.value ? option!.value.toString() : ''}` as unknown as string
                                      )
                                        .toLowerCase()
                                        .includes(input.toLowerCase())
                                    );
                                  }}
                                ></XlbSelect>
                              </XlbBasicForm.Item>
                            )}
                          </Col>
                          <Col xs={8} sm={8} md={8} lg={8} xl={6} xxl={6}>
                            <XlbBasicForm.Item
                              noStyle
                              shouldUpdate
                              dependencies={['org_id']}
                            >
                              {({ getFieldValue }) => {
                                return (
                                  <XlbBasicForm.Item
                                    rules={[
                                      {
                                        required: true,
                                        message: '请选择配送中心',
                                      },
                                    ]}
                                    label="配送门店"
                                    name="delivery_center_store_id"
                                  >
                                    <XlbSelect
                                      showSearch
                                      filterOption={(input, option) => {
                                        return (
                                          (
                                            `${option!.label ? option!.label.toString() : ''}` as unknown as string
                                          )
                                            .toLowerCase()
                                            .includes(input.toLowerCase()) ||
                                          (
                                            `${option!.value ? option!.value.toString() : ''}` as unknown as string
                                          )
                                            .toLowerCase()
                                            .includes(input.toLowerCase())
                                        );
                                      }}
                                      notFoundContent={
                                        fetching && <Spin size="small" />
                                      }
                                      style={{ width: dialogWidth - 3 }}
                                      allowClear={true}
                                      onChange={(value: any) => {
                                        setStoreIds(value);
                                        form?.setFieldsValue({
                                          stores: [],
                                        });
                                      }}
                                      disabled={
                                        !getFieldValue('org_id') ||
                                        info.state !== 'INIT' ||
                                        rowData?.length > 0
                                      }
                                      options={
                                        form?.getFieldValue('storeList') ||
                                        storeList
                                      }
                                    />
                                  </XlbBasicForm.Item>
                                );
                              }}
                            </XlbBasicForm.Item>
                          </Col>
                          <Col xs={8} sm={8} md={8} lg={8} xl={6} xxl={6}>
                            <XlbBasicForm.Item
                              noStyle
                              shouldUpdate
                              dependencies={['delivery_center_store_id']}
                            >
                              {({ getFieldValue }) => {
                                return (
                                  <XlbBasicForm.Item
                                    label="应用门店"
                                    name="stores"
                                  >
                                    <XlbInputDialog
                                      width={dialogWidth - 3}
                                      disabled={
                                        info.state !== 'INIT' ||
                                        !getFieldValue(
                                          'delivery_center_store_id',
                                        ) ||
                                        rowData?.length > 0
                                      }
                                      dialogParams={{
                                        type: 'store',
                                        isMultiple: true,
                                        //过滤直营店
                                        data: {
                                          upstream_center_id: getFieldValue(
                                            'delivery_center_store_id',
                                          ),
                                          center_flag: false,
                                          management_type: '1',
                                          delivery_type: 'JOIN',
                                          is_filter_join: true,
                                        },
                                      }}
                                      fieldNames={{
                                        idKey: 'id',
                                        nameKey: 'store_name',
                                      }}
                                    ></XlbInputDialog>
                                  </XlbBasicForm.Item>
                                );
                              }}
                            </XlbBasicForm.Item>
                          </Col>
                          <Col xs={8} sm={8} md={8} lg={8} xl={6} xxl={6}>
                            <XlbBasicForm.Item
                              rules={[
                                { required: true, message: '请选择开始日期' },
                              ]}
                              label="开始日期"
                              name="start_time"
                            >
                              <XlbDatePicker
                                style={{ width: '100%' }}
                                format="YYYY-MM-DD"
                                disabled={info.state !== 'INIT'}
                                disabledDate={(current) => {
                                  return (
                                    current && current < dayjs().endOf('day')
                                  );
                                }}
                              ></XlbDatePicker>
                            </XlbBasicForm.Item>
                          </Col>
                          <Col xs={8} sm={8} md={8} lg={8} xl={6} xxl={6}>
                            <XlbBasicForm.Item
                              rules={[
                                { required: true, message: '请选择结束日期' },
                              ]}
                              label="结束日期"
                              name="end_time"
                            >
                              <XlbDatePicker
                                style={{ width: '100%' }}
                                format="YYYY-MM-DD"
                                disabled={info.state !== 'INIT'}
                                disabledDate={(current) => {
                                  return (
                                    current && current < dayjs().endOf('day')
                                  );
                                }}
                              ></XlbDatePicker>
                            </XlbBasicForm.Item>
                          </Col>
                          <Col xs={8} sm={8} md={8} lg={8} xl={6} xxl={6}>
                            <XlbBasicForm.Item name="type" label="活动类型">
                              <XlbSelect
                                options={[
                                  {
                                    label: '满赠',
                                    value: 'SPEND_GET',
                                  },
                                ]}
                                allowClear
                                disabled
                                style={{ width: '100%' }}
                              ></XlbSelect>
                            </XlbBasicForm.Item>
                          </Col>
                          <Col xs={8} sm={8} md={8} lg={8} xl={6} xxl={6}>
                            <div style={{ width: '100%' }} ref={referenceRef}>
                              <XlbBasicForm.Item label="单据号" name="fid">
                                <XlbInput
                                  allowClear={false}
                                  disabled
                                  style={{ width: '100%' }}
                                ></XlbInput>
                              </XlbBasicForm.Item>
                            </div>
                          </Col>
                          {/* <Col xs={8} sm={8} md={8} lg={8} xl={6} xxl={6}>
                            <XlbBasicForm.Item label="单据状态" name="status">
                              <XlbSelect
                                style={{ width: '100%' }}
                                allowClear={false}
                                disabled
                                options={Options1}
                              />
                            </XlbBasicForm.Item>
                          </Col> */}
                          <Col xs={16} sm={16} md={16} lg={16} xl={12} xxl={12}>
                            <XlbBasicForm.Item label="留言备注" name="memo">
                              <XlbInput
                                disabled={info.state !== 'INIT'}
                                style={{ width: '100%' }}
                                maxLength={200}
                              ></XlbInput>
                            </XlbBasicForm.Item>
                          </Col>
                        </Row>
                      </div>
                    </div>
                    {info?.state && (
                      <div
                        style={{
                          width: '150px',
                          flexBasis: '150px',
                          display: 'flex',
                          justifyContent: 'center',
                          marginLeft: 55,
                        }}
                      >
                        <img
                          src={orderStatusIcons[info?.state]}
                          width={86}
                          height={78}
                        />
                      </div>
                    )}
                  </div>
                ),
              },
              {
                label: '其他信息',
                key: 'other',
                children: (
                  <>
                    <div
                      style={{ display: 'flex' }}
                      className={styles.form_container_transferDocument_other}
                    >
                      <div className="row-flex">
                        <Row gutter={12} wrap>
                          <Col xs={8} sm={8} md={8} lg={8} xl={6} xxl={6}>
                            <XlbBasicForm.Item label="制单人" name="create_by">
                              <XlbInput style={{ width: '100%' }} disabled />
                            </XlbBasicForm.Item>
                          </Col>
                          <Col xs={8} sm={8} md={8} lg={8} xl={6} xxl={6}>
                            <XlbBasicForm.Item
                              label="制单时间"
                              name="create_time"
                            >
                              <XlbInput style={{ width: '100%' }} disabled />
                            </XlbBasicForm.Item>
                          </Col>
                          <Col xs={8} sm={8} md={8} lg={8} xl={6} xxl={6}>
                            <XlbBasicForm.Item label="审核人" name="audit_by">
                              <XlbInput style={{ width: '100%' }} disabled />
                            </XlbBasicForm.Item>
                          </Col>
                          <Col xs={8} sm={8} md={8} lg={8} xl={6} xxl={6}>
                            <XlbBasicForm.Item
                              label="审核时间"
                              name="audit_time"
                            >
                              <XlbInput style={{ width: '100%' }} disabled />
                            </XlbBasicForm.Item>
                          </Col>
                        </Row>
                      </div>
                    </div>
                  </>
                ),
              },
            ]}
          ></XlbTabs>
        </XlbBasicForm>
        <XlbBlueBar title="活动商品"></XlbBlueBar>
        <div style={{ margin: '10px 0' }}>
          <XlbButton.Group>
            <XlbButton
              label="添加商品"
              type="primary"
              onClick={addMainGoods}
              disabled={info.state !== 'INIT' || !storeIds}
              icon={<XlbIcon size={16} name="jia" />}
            />
            <XlbButton
              label="删除"
              type="primary"
              onClick={deleteGoods}
              disabled={info.state !== 'INIT' || selectRow?.length === 0}
              icon={<XlbIcon size={16} name="shanchu" />}
            />
          </XlbButton.Group>
        </div>
        <XlbTable
          // clickArea="checkbox"
          style={{ flex: 1 }}
          columns={[
            {
              name: '序号',
              code: '_index',
              width: 65,
              align: 'center',
            },
            {
              name: '主品代码',
              code: 'main_item_code',
              width: 140,
              features: { sortable: true },
            },
            {
              name: '主品名称',
              code: 'main_item_name',
              width: 200,
              features: { sortable: true },
            },
            {
              name: '活动库存',
              code: 'main_item_stock',
              width: 100,
              align: 'right',
              features: { sortable: true },
              render: (value: any, record: any, index: any) => {
                return record?._click && info.state === 'INIT' ? (
                  <XlbInputNumber
                    style={{ width: 80, marginBottom: -4 }}
                    value={value || 0}
                    min={0}
                    precision={record.main_item_weigh ? 3 : 0}
                    onChange={(e: any) => {
                      calculateConditionValue(record, e);
                    }}
                  ></XlbInputNumber>
                ) : (
                  Number(value || 0)?.toFixed(3)
                );
              },
            },
            // 剩余活动库存
            {
              name: '剩余活动库存',
              code: 'main_item_remain_stock',
              width: 120,
              align: 'right',
              hidden: ![
                'EFFECT',
                'EXPIRE',
                'TERMINATE',
                'INVALID',
                'HANDLE',
              ].includes(info.state),
              features: { sortable: true, format: 'QUANTITY' },
            },
            {
              name: '赠品代码',
              code: 'present_item_code',
              width: 140,
              features: { sortable: true },
            },
            {
              name: '赠品名称',
              code: 'present_item_id',
              width: 220,
              align: 'left',
              render: (text: string, record: any, index: any) => {
                return record?._click && info.state === 'INIT' ? (
                  <XlbInputDialog
                    dialogParams={{
                      type: 'goods',
                      isMultiple: false,
                      isLeftColumn: true,
                      dataType: 'lists',
                      url: '/erp/hxl.erp.campaign.item.page',
                      data: {
                        delivery_center_store_id: form?.getFieldValue(
                          'delivery_center_store_id',
                        ),
                        store_ids: form?.getFieldValue('stores'),
                      },
                    }}
                    fieldNames={{
                      idKey: 'id',
                      nameKey: 'name',
                    }}
                    placeholder="请选择"
                    value={text}
                    handleValueChange={(value: any, option: any) => {
                      if (option?.length > 0) {
                        // record.present_item_id = option?.[0]?.id;
                        // record.present_item_code = option?.[0]?.name;
                        // record.present_item_code = option?.[0]?.code;
                        setRowData((prev: any) =>
                          prev?.map((item: any, i: any) =>
                            i === index?.index
                              ? {
                                  ...item,
                                  present_item_id: option?.[0]?.id,
                                  present_item_name: option?.[0]?.name,
                                  present_item_code: option?.[0]?.code,
                                  present_weigh: option?.[0]?.weigh,
                                  present_item_stock: null,
                                  present_value: null,
                                  condition_value: null,
                                }
                              : item,
                          ),
                        );
                      } else {
                        setRowData((prev: any) =>
                          prev?.map((item: any, i: any) =>
                            i === index?.index
                              ? {
                                  ...item,
                                  present_item_id: null,
                                  present_item_name: null,
                                  present_item_code: null,
                                  present_value: null,
                                  present_item_stock: null,
                                  condition_value: null,
                                }
                              : item,
                          ),
                        );
                      }
                    }}
                    width={200}
                  />
                ) : (
                  <span>{record.present_item_name || ''}</span>
                );
              },
            },
            {
              name: '主品买满',
              code: 'condition_value',
              width: 100,
              align: 'right',
              render: (value: any, record: any, index: any) => {
                return record?._click &&
                  !!record.present_item_id &&
                  info.state === 'INIT' ? (
                  <XlbInputNumber
                    style={{ width: 80, marginBottom: -4 }}
                    value={value || 0}
                    min={0}
                    max={9999}
                    precision={record.main_item_weigh ? 3 : 0}
                    onChange={(e: any) => {
                      calculateConditionValue(
                        record,
                        record.main_item_stock,
                        e,
                        record.present_value,
                      );
                    }}
                  ></XlbInputNumber>
                ) : (
                  Number(value || 0)?.toFixed(3)
                );
              },
            },
            {
              name: '赠送数量',
              code: 'present_value',
              width: 100,
              align: 'right',
              render: (value: any, record: any, index: any) => {
                return record?._click &&
                  !!record.present_item_id &&
                  info.state === 'INIT' ? (
                  <XlbInputNumber
                    style={{ width: 80, marginBottom: -4 }}
                    value={value || 0}
                    min={0}
                    max={9999}
                    precision={record.present_weigh ? 3 : 0}
                    onChange={(e: any) => {
                      calculateConditionValue(
                        record,
                        record.main_item_stock,
                        record.condition_value,
                        e,
                      );
                    }}
                  ></XlbInputNumber>
                ) : (
                  Number(value || 0)?.toFixed(3)
                );
              },
            },
            {
              name: '赠品活动库存',
              code: 'present_item_stock',
              width: 120,
              align: 'right',
              render: (value: any, record: any, index: any) => {
                return Number(value || 0)?.toFixed(3);
              },
            },
            // 剩余赠品库存
            {
              name: '剩余赠品活动库存',
              code: 'present_item_remain_stock',
              width: 150,
              align: 'right',
              hidden: ![
                'EFFECT',
                'EXPIRE',
                'TERMINATE',
                'INVALID',
                'HANDLE',
              ].includes(info.state),
              features: { sortable: true, format: 'QUANTITY' },
            },
          ]}
          dataSource={rowData}
          total={rowData?.length}
          pageSize={goodsPageSize}
          onPaginChange={(page, pageSize) => {
            setGoodsPageSize(pageSize);
          }}
          onClickRow={(row, index) => {
            setRowData((prev: any) =>
              prev?.map((item: any) => ({
                ...item,
                _click: row?.main_item_id === item?.main_item_id,
              })),
            );
          }}
          clickArea={'checkbox'}
          primaryKey={'main_item_id'}
          selectMode="multiple"
          selectedRowKeys={selectRow}
          emptyCellHeight={400}
          isLoading={isLoading}
          onSelectRow={(record) => setSelectRow(record)}
        />
      </div>
    </ConfigProvider>
  );
};
export default Item;

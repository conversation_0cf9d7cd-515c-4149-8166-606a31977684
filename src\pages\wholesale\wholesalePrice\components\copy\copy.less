.box {
  position: relative;
  // width: 860px;
  margin: 10px auto;
  padding: 20px 10px 10px 10px;
  border: 1px solid #d8d8d8;
  border-radius: 5px;
  .title {
    position: absolute;
    top: -13px;
    left: 20px;
    padding: 0 13px;
    background: white;
  }
  :global .ant-form-item {
    display: inline-block;
    margin: 5px 30px 5px 0px !important;
    padding:0 7px;
  }
  :global .ant-space-item {
    display: inline-block;
  }
  :global label.ant-checkbox-wrapper.ant-checkbox-wrapper-in-form-item {
    width: 154px;
  }
  :global .ant-radio-wrapper {
    line-height: 26px;
  }
  // :global .ant-input-affix-wrapper {
  //   margin: 0 10px;
  // }
  // :global .ant-select {
  //   display: inline-block;
  //   width: 140px;
  // }
  // :global .ant-input-suffix{
  //   height: 26px;
  // }
  // :global .ant-checkbox-wrapper {
  //   width: 120px;
  // }
}

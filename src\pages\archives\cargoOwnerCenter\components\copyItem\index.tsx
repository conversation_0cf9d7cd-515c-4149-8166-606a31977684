import type { BaseModalProps } from '@xlb/components';
import { XlbBasicForm, XlbInputDialog, XlbModal } from '@xlb/components';
import type { FC } from 'react';

import NiceModal from '@ebay/nice-modal-react';
import { message } from 'antd';
import Api from '../../server';

interface Props extends BaseModalProps {
  fetchData?: any;
}

const AddItem: FC<Props> = ({ fetchData }) => {
  const [form] = XlbBasicForm.useForm();
  const modal = NiceModal.useModal();

  const handleOk = async (values: any) => {
    console.log(values, 'handleOk');
    const { target_store_id_list, refer_store_id } = values;
    const res = await Api.copyInfo({
      refer_store_id: refer_store_id?.[0] || undefined,
      target_store_id_list,
    });
    if (res?.code === 0) {
      form.resetFields();
      modal.hide();
      fetchData?.();
      message.success('操作成功');
    }
  };
  return (
    <XlbModal
      width={450}
      open={modal.visible}
      title={'复制门店'}
      isCancel={true}
      onOk={async () => {
        form.submit();
      }}
      onCancel={() => {
        form.resetFields();
        modal.resolve(false);
        modal.hide();
      }}
    >
      <div style={{ marginLeft: '12px' }}>
        <XlbBasicForm
          form={form}
          style={{ margin: '20px 0' }}
          onFinish={() => {
            const values = form.getFieldsValue(true);
            handleOk(values);
          }}
        >
          <XlbBasicForm.Item
            name="target_store_id_list"
            label="修改门店"
            rules={[{ required: true, message: '请选择修改门店' }]}
          >
            <XlbInputDialog
              dialogParams={{
                type: 'store',
                isMultiple: true,
                data: {
                  center_flag: true,
                  enable_organization: false,
                },
              }}
              fieldNames={{
                idKey: 'id',
                nameKey: 'store_name',
              }}
              width={156}
            />
          </XlbBasicForm.Item>
          <XlbBasicForm.Item
            name="refer_store_id"
            label="参照门店"
            rules={[{ required: true, message: '请选择参照门店' }]}
          >
            <XlbInputDialog
              dialogParams={{
                type: 'store',
                // isMultiple: true,
                data: {
                  center_flag: true,
                  enable_organization: false,
                },
              }}
              fieldNames={{
                idKey: 'id',
                nameKey: 'store_name',
              }}
              width={156}
            />
          </XlbBasicForm.Item>
        </XlbBasicForm>
      </div>
    </XlbModal>
  );
};
export default NiceModal.create(AddItem);

import { FC, useEffect, useMemo, useState } from 'react'
import { XlbFetch } from '@xlb/utils'
import { XlbBasicForm } from '@xlb/components'
import { moneyFormat, formatValue } from '@xlb/components/dist/components/XlbBasicTable/utils'
import { PurchasePlanResDTO } from '../type'
import dayjs from 'dayjs'
import { Space } from 'antd'
import styles from './total.less'

const Total: FC<{ id: number }> = ({ id }) => {
  const [data, setData] = useState<PurchasePlanResDTO>({})

  const initDetails = async () => {
    if (!id) {
      return
    }

    const result = await XlbFetch.post('/erp/hxl.erp.purchaseplan.read', {
      id
    })

    if (result.code == 0) {
      const data: PurchasePlanResDTO = result.data
      setData(data)
    }
  }

  const range = useMemo(() => {
    const timeRange = [data?.begin_time, data?.end_time]
      .filter((item) => !!item)
      .map((item) => dayjs(item).format('YYYY-MM-DD'))

    if (!timeRange.length) {
      return '-'
    }

    return timeRange.join('~')
  }, [data?.begin_time, data?.end_time])

  useEffect(() => {
    initDetails()
  }, [])

  return (
    <Space size={'large'} className={styles.item2}>
      <XlbBasicForm.Item label="计划名称">
        {data?.name}
      </XlbBasicForm.Item>
      <XlbBasicForm.Item label="所属组织">
        {data?.org_name}
      </XlbBasicForm.Item>
      <XlbBasicForm.Item label="时间周期">
        {range}
      </XlbBasicForm.Item>
      <XlbBasicForm.Item label="采购数量合计">
        {moneyFormat(formatValue(data?.quantity || 0, 3, 'ROUND_DOWN'))}
      </XlbBasicForm.Item>
      <XlbBasicForm.Item label="采购金额合计（元）">
        {moneyFormat(formatValue(data?.money || 0, 2, 'ROUND_DOWN'))}
      </XlbBasicForm.Item>
    </Space>
  )
}

export default Total

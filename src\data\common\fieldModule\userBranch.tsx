export const UserBranchKeyMap = {
  erpLeader: 'erpLeader'
}

export const userBranchConfig: any[] = [
  // 部门
  {
    tag: 'ERP',
    label: '部门负责人',
    id: UserBranchKeyMap.erpLeader,
    name: 'leader',
    fieldProps: {
      width: 180,
      dialogParams: {
        type: 'user',
        isLeftColumn: false,
        nullable: true,
        dataType: 'lists',
        isMultiple: false,
        idsKey: 'leader',
        immediatePost: false
      },
      // @ts-ignore
      fieldNames: { idKey: 'name', nameKey: 'name' }
    },
    componentType: 'inputDialog'
  }
]

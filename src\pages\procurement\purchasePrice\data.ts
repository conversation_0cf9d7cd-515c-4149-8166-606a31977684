import {
  SearchFormType,
  SelectType,
  XlbTableColumnProps,
} from '@xlb/components';

//状态
export const statusType: SelectType[] = [
  {
    label: '审核中',
    value: 'INIT',
    type: 'warning',
  },
  {
    label: '已通过',
    value: 'APPROVE',
    type: 'success',
  },
  {
    label: '未通过',
    value: 'REFUSE',
    type: 'danger',
  },
];

export const formList: SearchFormType[] = [
  {
    width: 392,
    type: 'compactDatePicker',
    label: '日期选择',
    name: 'date',
    allowClear: false,
  },
  {
    label: '商品档案',
    name: 'item_ids',
    type: 'inputDialog',
    allowClear: true,

    dialogParams: {
      type: 'goods',
      dataType: 'lists',
      isLeftColumn: true,
      isMultiple: true,
    },
  },

  {
    label: '商品品牌',
    name: 'item_brand_id',
    type: 'inputDialog',
    clear: true,
    check: true,
    dialogParams: {
      type: 'productBrand',
      dataType: 'lists',
      isLeftColumn: false,
      isMultiple: false,
      primaryKey: 'id',
      data: {
        enabled: true,
      },
    },
  },

  {
    label: '商品分类',
    name: 'item_category_ids',
    type: 'inputDialog',
    treeModalConfig: {
      title: '选择商品分类', // 标题
      url: '/erp/hxl.erp.category.find', // 请求地址
      dataType: 'lists',
      checkable: true, // 是否多选
      primaryKey: 'id',
      data: {
        enabled: true,
      },
    },
    clear: true,
    check: true,
  },

  {
    label: '提交人',
    name: 'create_by',
    type: 'input',
    clear: true,
    check: true,
  },
];

export const tableColumn: XlbTableColumnProps<any>[] = [
  {
    name: '序号',
    code: '_index',
    width: 60,
    align: 'center',
  },
  {
    name: '采价单号',
    code: 'fid',
    width: 150,
    features: { sortable: true },
  },
  {
    name: '对标店铺',
    code: 'contrast_store_name',
    width: 172,
    features: { sortable: true },
  },
  {
    name: '店铺位置',
    code: 'contrast_store_address',
    width: 172,
    features: { sortable: true },
  },
  {
    name: '我方店铺',
    code: 'store_name',
    width: 172,
    features: { sortable: true },
  },
  {
    name: '商品代码',
    code: 'item_code',
    width: 172,
    features: { sortable: true },
  },
  {
    name: '商品条码',
    code: 'bar_code',
    width: 142,
    features: { sortable: true },
  },
  {
    name: '商品名称',
    code: 'item_name',
    width: 312,
    features: { sortable: true },
  },
  {
    name: '对标价格',
    code: 'contrast_item_price',
    width: 130,
    features: { sortable: true },
  },

  {
    name: '我方价格',
    code: 'item_price',
    width: 130,
    features: { sortable: true },
  },
  {
    name: '商品品牌',
    code: 'item_brand_name',
    width: 116,
    features: { sortable: true },
  },
  {
    name: '统计时间',
    code: 'statistic_time',
    width: 180,
    features: { sortable: true },
  },
  {
    name: '状态',
    code: 'state',
    width: 180,
    features: { sortable: true },
  },
  {
    name: '提交时间',
    code: 'create_time',
    width: 180,
    features: { sortable: true },
  },
  {
    name: '提交人',
    code: 'create_by',
    width: 116,
    features: { sortable: true },
  },
  {
    name: '图片',
    code: 'files',
    width: 88,
    features: { sortable: false },
  },
  {
    name: '备注',
    code: 'memo',
    width: 312,
    features: { sortable: false },
  },
];

export const form_basic = [
  {
    label: '商品代码：',
    value: 'item_code',
  },
  {
    label: '商品条码：',
    value: 'bar_code',
  },
  {
    label: '商品品牌：',
    value: 'item_brand_name',
  },
  {
    label: '商品类型：',
    value: 'item_category_name',
  },
  {
    label: '采价单号：',
    value: 'fid',
  },
  {
    label: '提交人：',
    value: 'create_by',
  },
  {
    label: '提交时间：',
    value: 'create_time',
  },
  {
    label: '统计时间：',
    value: 'statistic_time',
  },
];
export const form_price = [
  {
    label: '对标店铺：',
    value: 'contrast_store_name',
  },
  {
    label: '店铺位置：',
    value: 'contrast_store_address',
  },
  {
    label: '我方店铺：',
    value: 'store_name',
  },
  {
    label: '对标价格：',
    value: 'contrast_item_price',
  },
  {
    label: '我方价格：',
    value: 'item_price',
  },
  {
    label: '备注：',
    value: 'memo',
  },
];

export const form_audit = [
  {
    label: '审批结果：',
    value: 'state',
  },
  {
    label: '批复建议：',
    value: 'reply_type',
  },
  {
    label: '备注：',
    value: 'audit_memo',
  },
];

export const f_state = {
  APPROVE: '已通过',
  REFUSE: '未通过',
};

export const f_advice = {
  BUDGET_NOT_ENOUGH: '内部预算不足',
  VALUE_MAXIMIZE: '价值效益最大化',
  OTHER: '其他',
};

import { XlbFetch as ErpRequest } from '@xlb/utils';

// 获取数据
export const getAllData = async (data: any) => {
  return await ErpRequest.post('/erp/hxl.erp.storeitemattr.page', { data });
};
//更新
export const updateInfo = async (data: any) => {
  return await ErpRequest.post('/erp/hxl.erp.storeitemattr.update', { data });
};
//批量设置
export const batchUpdate = async (data: any) => {
  return await ErpRequest.post('/erp/hxl.erp.storeitemattr.batchupdate', {
    data,
  });
};
//复制
export const copyInfo = async (data: any) => {
  return await ErpRequest.post('/erp/hxl.erp.storeitemattr.copy', { data });
};
//获取修改记录
export const gethistory = async (data: any) => {
  return await ErpRequest.post('/erp/hxl.erp.storeitemdistribution.attribute.log', data);
};
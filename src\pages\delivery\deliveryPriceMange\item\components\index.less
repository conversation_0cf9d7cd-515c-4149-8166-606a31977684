.editorBox {
    :global(.bf-content) {
      height: auto;
      min-height: 200px;
    }
  }
  
  .help {
    color: #86909c !important;
  }
  
  .help:hover {
    color: #3d66fe !important;
  }
  
  .formBox {
    :global(.ant-select-selector) {
      display: flex;
      align-items: center;
  
      :global(.ant-select-selection-item) {
        margin-top: 0;
        line-height: inherit;
      }
    }
  
    .form_item {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      width: 815px;
      height: 32px;
  
      :global .xlb-ant-form-item {
        margin-bottom: 0px !important;
      }
    }
  }
  
  .tableTitle {
    display: flex;
    gap: 19px;
    padding: 0 16px;
    font-weight: 400;
    font-size: 14px;
  }
  
  .title {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 28px;
    color: #000;
    font-weight: bold;
    font-size: 18px;
  }
  .type {
    display: flex;
    align-items: center;
    justify-content: center;
    color: #86909c;
    font-size: 14px;
  }
  .titleBox {
    width: 100%;
    padding-top: 12px;
    padding-bottom: 8px;
    background-color: white;
    p {
      text-align: center;
    }
  }
  .announcementType {
    height: 18px;
    margin-left: 4px;
    padding: 0 8px;
    overflow: hidden;
    color: #f53f3f;
    font-weight: 400;
    font-size: 12px;
    white-space: nowrap;
    text-overflow: ellipsis;
    word-break: break-all;
    background-color: #ffece8;
    border-radius: 4px;
  }
  .cusStyle{
    // height: 'calc(100vh - 78px)';
    // overflow: auto;
    :global{
      .toolBtn.withHr {
        position: sticky;
        background: #fff;
        z-index: 32;
        width: 100%;
        top: 0;
    }
     .xlb-pageContainer-toolBtn{
      margin-top:0 !important;
      padding-top: 8px;
     }
    } 
  }
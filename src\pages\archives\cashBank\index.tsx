import { hasAuth } from '@/utils/kit';
import {
  XlbButton,
  XlbIcon,
  XlbModal,
  XlbProPageContainer,
} from '@xlb/components';
import { Checkbox, Form, Input, message } from 'antd';
import { useState } from 'react';
import { tableColumn } from './data';
import Api from './server';
import type { CashBankFindReqDTO } from './type';

enum titleType {
  'add' = '新增现金银行',
  'update' = '现金银行',
}
const Index: React.FC = () => {
  let refresh = () => {};
  let setIsLoading = (v: boolean) => {};
  const [form] = Form.useForm();
  const [preId, setId] = useState(-1); // -1 新增
  const [visible, setVisible] = useState<boolean>(false);
  const [title, setTitle] = useState<titleType>(titleType.add);
  const [detailInfo, setDetailInfo] = useState<any>({});
  const [editorStatus, setEditorStatus] = useState(false);
  const handleCancel = () => {
    form.resetFields();
    setEditorStatus(false);
    setVisible(false);
  };
  // 保存
  const saveOrder = async () => {
    const data = { status: editorStatus, name: form.getFieldValue('name') };
    setIsLoading(true);
    const res = detailInfo.id
      ? await Api.update({ ...data, id: detailInfo.id })
      : await Api.add(data);
    if (res.code === 0) {
      setDetailInfo({});
      message.success('操作成功');
      refresh();
      handleCancel();
    }
  };
  const handleUpdate = (record: any) => {
    console.log("🚀 ~ handleUpdate ~ record:", record)
    setVisible(true);
    setDetailInfo(record);
    form.setFieldValue('name', record.name);
    form.setFieldValue('status', record.status);
    setEditorStatus(record.status);
  };

  const render = (item: any) => {
    switch (item.code) {
      case 'name':
        item.render = (value: any, record: any) => {
          return (
            <span
              className="link cursors"
              onClick={(e) => {
                e.stopPropagation();
                console.log('12121212');
                
                hasAuth(['现金银行', '编辑']) ? handleUpdate(record) : null;
              }}
            >
              {value}
            </span>
          );
        };
        break;
      case 'status':
        item.render = (value: any) => {
          return (
            <div className={` ${value ? 'success' : 'danger'}`}>
              {' '}
              {value ? '开启' : '禁用'}{' '}
            </div>
          );
        };
        break;
    }
  };

  // 编辑
  const editItem = async (
    dataSource: CashBankFindReqDTO[],
    selectRowKeys: React.Key[],
    fetchData: Function,
    status: boolean,
  ) => {
    //选择中的数据
    const selectedData = dataSource.find((v: any) =>
      selectRowKeys.includes(v.id),
    );
    const res = await Api.update({ ...selectedData, status });
    if (res.code === 0) {
      message.success('修改成功');
      fetchData();
    }
  };
  tableColumn.map((v) => render(v));

  return (
    <>
      <XlbModal
        isCancel={false}
        footerExtroContent={() => {
          return (
            <XlbButton onClick={() => handleCancel()} type="line">
              关闭
            </XlbButton>
          );
        }}
        title={title}
        open={visible}
        centered
        onOk={() => saveOrder()}
        onCancel={() => {
          handleCancel();
        }}
      >
        <div style={{ padding: 30 }}>
          <div className={'baseInfo'}>
            <Form
              autoComplete="off"
              labelCol={{ span: 8 }}
              layout="horizontal"
              form={form}
            >
              <Form.Item required label="现金银行名称" name="name">
                <Input style={{ width: 180 }} size="small" allowClear />
              </Form.Item>
              <Form.Item
                label="状态："
                name="status"
                initialValue={form.getFieldValue('flag')}
              >
                <Checkbox
                  style={{ width: 180 }}
                  onChange={(e) => setEditorStatus(e.target.checked)}
                  checked={editorStatus}
                >
                  开启
                </Checkbox>
              </Form.Item>
              {/* <Form.Item label="应用范围：" name="flag" initialValue={form.getFieldValue('flag')}>
                        <Select style={{ width: 180 }} >
                          <Option value={0}>不限</Option>
                          <Option value={1}>配送中心</Option>
                          <Option value={2}>非配送中心</Option>
                        </Select>
                      </Form.Item> */}
            </Form>
          </div>
        </div>
      </XlbModal>
      <XlbProPageContainer
        searchFieldProps={{
          formList: () => [{ id: 'keyword', label: '关键字' }],
        }}
        extra={(context) => {
          const { selectRowKeys, loading, setLoading, fetchData, dataSource } =
            context;
          refresh = fetchData;
          setIsLoading = setLoading;
          // @ts-ignore
          const statusData = dataSource.find((v) =>
            selectRowKeys.includes(v.id),
          );
          return (
            <XlbButton.Group>
              {hasAuth(['现金银行', '编辑']) && (
                <XlbButton
                  type="primary"
                  onClick={() => {
                    setTitle(titleType.add);
                    setId(-1);
                    setVisible(true);
                  }}
                  disabled={loading}
                  icon={<XlbIcon size={16} name="jia" />}
                >
                  新增
                </XlbButton>
              )}
              {hasAuth(['现金银行', '编辑']) && (
                <XlbButton
                  type="primary"
                  disabled={
                    !(
                      typeof statusData?.status === 'boolean' &&
                      statusData?.status === false
                    ) || loading
                  }
                  onClick={() =>
                    editItem(dataSource, selectRowKeys, fetchData, true)
                  }
                  icon={<XlbIcon size={16} name="tongguo" />}
                >
                  开启
                </XlbButton>
              )}
              {hasAuth(['现金银行', '编辑']) && (
                <XlbButton
                  type="primary"
                  disabled={
                    !(
                      typeof statusData?.status === 'boolean' &&
                      statusData?.status === true
                    ) || loading
                  }
                  onClick={() =>
                    editItem(dataSource, selectRowKeys, fetchData, false)
                  }
                  icon={<XlbIcon size={16} name="xianxingjianshao" />}
                >
                  禁用
                </XlbButton>
              )}
            </XlbButton.Group>
          );
        }}
        tableFieldProps={{
          url: '/erp/hxl.erp.cashbank.find',
          primaryKey: 'id',
          tableColumn: tableColumn,
          selectMode: 'single',
          showColumnsSetting: true,
          immediatePost: true,
          keepDataSource: false,
        }}
      />
    </>
  );
};

export default Index;

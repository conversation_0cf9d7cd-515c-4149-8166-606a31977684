import { useBaseParams } from '@/hooks/useBaseParams';
import { hasAuth } from '@/utils';
import { wujieBus } from '@/wujie/utils';
import { SearchOutlined, UploadOutlined } from '@ant-design/icons';
import {
  XlbBasicForm,
  XlbButton,
  XlbColumns,
  XlbForm,
  XlbIcon,
  XlbMessage,
  XlbPageContainer,
  XlbTable,
  XlbTableColumnProps,
} from '@xlb/components';
import { LStorage, XlbFetch } from '@xlb/utils';
import { Tabs, Tooltip } from 'antd';
import classnames from 'classnames';
import { cloneDeep } from 'lodash-es';
import moment from 'moment';
import { useEffect, useState } from 'react';

import NiceModal from '@ebay/nice-modal-react';
import Order from './components/order';
import { BasketArr, DetailArr, formList, SummaryArr } from './data';
import { getbasket, getdetail, getsummary } from './server';
const Goodsway = () => {
  const { enable_cargo_owner } = useBaseParams((state) => state);
  const { TabPane } = Tabs;
  const [isFold, setIsFold] = useState<boolean>(false);
  const [form] = XlbBasicForm.useForm();
  const [tabsKey, setTabsKey] = useState('summary');
  const [footerData, setFooterData] = useState<any[]>([]);
  const [pagin, setPagin] = useState({ pageSize: 200, pageNum: 1, total: 0 });
  const [rowData, setRowData] = useState<any[]>([]);
  const [summaryArrRowData, setSummaryArrRowData] = useState<any[]>([]);
  const [detailArrRowData, setDetailArrRowData] = useState<any[]>([]);
  const [basketArrRowData, setBasketArrRowData] = useState<any[]>([]);
  const [summaryArrFooterData, setSummaryArrFooterData] = useState<any[]>([]);
  const [detailArrFooterData, setDetailArrFooterData] = useState<any[]>([]);
  const [basketArrFooterData, setBasketArrFooterData] = useState<any[]>([]);
  const [sortType, setSortType] = useState<{ order: string; code: string }>({
    order: '',
    code: '',
  });
  const [summaryArrPagin, setSummaryArrPagin] = useState({
    pageSize: 200,
    pageNum: 1,
    total: 0,
  });
  const [detailArrPagin, setDetailArrPagin] = useState({
    pageSize: 200,
    pageNum: 1,
    total: 0,
  });
  const [basketArrPagin, setBasketArrPagin] = useState({
    pageSize: 200,
    pageNum: 1,
    total: 0,
  });
  const [itemArr, setItemArr] = useState<XlbTableColumnProps<any>[]>(
    JSON.parse(JSON.stringify(SummaryArr)),
  );
  const [isTabksKeyChange, setIsTabksKeyChange] = useState<boolean>(false);
  const [isLoading, setisLoading] = useState<boolean>(false);

  const fIdClick = (data: any) => {
    NiceModal.show(NiceModal.create(Order), data);
  };
  const [formLists, setSearchFormLists] = useState(cloneDeep(formList));
  //表格数据
  const tableRender = (item: any) => {
    switch (item.code) {
      case 'fid':
        item.render = (value: any) => {
          return (
            <div className="overwidth">
              <span
                className="link cursors"
                onClick={(e) => {
                  e.stopPropagation();
                  fIdClick({ order_fid: value });
                }}
              >
                {value}
              </span>
            </div>
          );
        };
        break;
      case 'money':
      case 'stock_money':
      case 'price':
        item.render = (value: any) => (
          <div className="info overwidth">
            {value ? Number(value).toFixed(2) : null}
          </div>
        );
        break;
    }
  };

  // 存储表单数据
  const setFormData = () => {
    LStorage.set('basketStat', {
      ...form.getFieldsValue(true),
    });
  };

  useEffect(() => {
    sortType.code !== '' && getData();
  }, [sortType]);

  const checkData = (page_number: number = 1, page_size?: number) => {
    // console.log(form.getFieldsValue(true), 'checkData');
    const data: any = {
      company_id: LStorage.get('userInfo')?.company_id,
      operator_store_id: LStorage.get('userInfo')?.store_id,
      audit_date:
        tabsKey !== 'basket'
          ? [
              form.getFieldValue('compactDatePicker')?.[0] + ' 00:00:00',
              form.getFieldValue('compactDatePicker')?.[1] + ' 23:59:59',
            ]
          : undefined,
      out_store_ids: form.getFieldValue('out_store_ids')?.length
        ? form.getFieldValue('out_store_ids')
        : undefined,
      store_ids:
        tabsKey !== 'basket' && form.getFieldValue('store_ids')?.length
          ? form.getFieldValue('store_ids')
          : undefined,

      basket_ids: form.getFieldValue('basket_ids')?.length
        ? form.getFieldValue('basket_ids')
        : undefined,
      query_num_greater_than_zero:
        tabsKey === 'summary'
          ? form
              .getFieldValue('checkValue')
              ?.includes('query_num_greater_than_zero')
            ? true
            : undefined
          : undefined,
      out_org_ids: form.getFieldValue('out_org_ids')?.length
        ? form.getFieldValue('out_org_ids')
        : undefined,
      page_size: page_size || pagin.pageSize,
      page_number: page_number - 1,
    };
    data.orders =
      sortType.code && !isTabksKeyChange
        ? [
            {
              direction: sortType.order.toUpperCase(),
              property: sortType.code,
            },
          ]
        : undefined;
    return data;
  };
  // 获取数据
  const getData = async (page_number: number = 1, page_size?: number) => {
    setFormData();
    setisLoading(true);
    const data: any = checkData(page_number, page_size);
    let res = null;
    switch (tabsKey) {
      case 'summary':
        res = await getsummary({ ...data });
        break;
      case 'detail':
        res = await getdetail({ ...data });
        break;
      case 'basket':
        res = await getbasket({ ...data });
        break;
    }
    setisLoading(false);
    if (res?.code === 0) {
      if (tabsKey === 'basket') {
        setRowData(res?.data || []);
      } else {
        setRowData(res?.data.content || []);
      }
      //合计行
      footerData[0] = {};
      footerData[0]._index = '合计';
      footerData[0].money = res?.data.money?.toFixed(2) || '0.00';
      footerData[0].stock_money = res?.data?.stock_money?.toFixed(2) || '0.00';
      footerData[0].out_quantity = res?.data?.out_quantity || '0';
      footerData[0].price = null;
      footerData[0].stock_quantity = res?.data?.stock_quantity || '0';
      footerData[0].owe_quantity = res?.data?.owe_quantity || '0';
      footerData[0].quantity = res.data.quantity || '0';
      setFooterData([...footerData]);
      const paginData = {
        ...pagin,
        pageSize: page_size || pagin?.pageSize,
        pageNum: page_number,
        total: res.data?.total_elements,
      };
      setPagin(paginData);
      switch (tabsKey) {
        case 'summary':
          setSummaryArrRowData(res.data.content || []);
          setSummaryArrPagin(paginData);
          setSummaryArrFooterData([...footerData]);
          break;
        case 'detail':
          setDetailArrRowData(res.data.content || []);
          setDetailArrPagin(paginData);
          setDetailArrFooterData(footerData);
          break;
        case 'basket':
          setBasketArrRowData(res.data || []);
          setBasketArrPagin(paginData);
          setBasketArrFooterData(footerData);
          break;
      }
    }
  };

  const handleUid = () => {
    let uid: any = {
      key: '',
      name: '',
    };
    switch (tabsKey) {
      case 'summary':
        uid.key = 'hxl.erp.deliveryreport.basketorder.summary.page-columns';
        uid.name = 'hxl.erp.deliveryreport.basketorder.summary.page';
        break;
      case 'detail':
        uid.key = 'hxl.erp.deliveryreport.basketorder.detail.page-columns';
        uid.name = 'hxl.erp.deliveryreport.basketorder.detail.page';
        //'hxl.erp.deliveryreport.basketorder.detail.page-columns-name';
        break;
      case 'basket':
        uid.key = 'hxl.erp.deliveryreport.basketorder.basket.page-columns';
        uid.name = 'hxl.erp.deliveryreport.basketorder.basket.page';
        break;
      default:
        uid.key = 'hxl.erp.deliveryreport.basketorder.summary.page-columns';
        uid.name = 'hxl.erp.deliveryreport.basketorder.summary.page';
    }
    return uid;
  };
  const oldArr = () => {
    let tableArr: any = [];
    switch (tabsKey) {
      case 'summary':
        tableArr = cloneDeep(SummaryArr);
        break;
      case 'detail':
        tableArr = cloneDeep(DetailArr);
        break;
      case 'basket':
        tableArr = cloneDeep(BasketArr);
        break;
      default:
        tableArr = cloneDeep(SummaryArr);
    }
    return tableArr;
  };

  // 导出
  const exportItem = async (e: any) => {
    const data = { ...checkData(pagin.pageNum) };
    let res: any;
    // let xlsxName: any;
    setisLoading(true);
    switch (tabsKey) {
      case 'summary':
        res = await XlbFetch.post(
          '/erp/hxl.erp.deliveryreport.basketorder.summary.export',
          {
            ...data,
          },
        );
        // xlsxName = '汇总查询';
        break;
      case 'detail':
        res = await XlbFetch.post(
          '/erp/hxl.erp.deliveryreport.basketorder.detail.export',
          {
            ...data,
          },
        );
        // xlsxName = '按明细查询';
        break;
      default:
        res = await XlbFetch.post(
          '/erp/hxl.erp.deliveryreport.basketorder.basket.export',
          {
            ...data,
          },
        );
      // xlsxName = '中心筐合计';
    }
    setisLoading(false);
    if (res.code === 0) {
      wujieBus?.$emit('xlb_erp-event', { code: 'downloadEnd', target: e });
      XlbMessage.success('导出受理成功，请前往下载中心查看');
    }
  };
  const initData = async () => {
    const formData = LStorage.get('basketStat');
    if (formData) {
      form.setFieldsValue({
        ...formData,
        start_time: moment(formData.start_time),
        end_time: moment(formData.end_time),
        compactDatePicker: [
          LStorage.get('basketStat').compactDatePicker?.[0],
          LStorage.get('basketStat').compactDatePicker?.[1],
        ],
      });
    } else {
      form.setFieldsValue({
        // time_desc: 0,
        // time_type: 'create_date',
        compactDatePicker: [
          moment().format('YYYY-MM-DD'),
          moment().format('YYYY-MM-DD'),
        ],
      });
    }
  };

  // 切换表格数据
  const onChangeKey = (key: any) => {
    console.log(pagin, 'pagin');
    // setSortType({ order: '', code: '' });
    // setChooseIndex('');
    setIsTabksKeyChange(true);
    let formListT = cloneDeep(formLists);
    setTabsKey(key);
    if (key !== 'detail') {
      formListT = formListT.map((v) => {
        if (v.name === 'out_store_ids') {
          return {
            ...v,
            dialogParams: {
              ...v?.dialogParams,
              data: {
                status: true,
                center_flag: true,
              },
            },
          };
        } else if (v.name === 'store_ids') {
          return {
            ...v,
            dialogParams: {
              ...v?.dialogParams,
              data: {
                ...v?.dialogPlogParams?.data,
                center_flag: false,
              },
            },
          };
        } else {
          return v;
        }
      });
    } else {
      formListT = formListT.map((v) => {
        if (v.name === 'out_store_ids') {
          return {
            ...v,
            dialogParams: {
              ...v?.dialogParams,
              data: {
                status: true,
                center_flag: undefined,
              },
            },
          };
        } else if (v.name === 'store_ids') {
          return {
            ...v,
            dialogParams: {
              ...v?.dialogParams,
              data: {
                ...v?.dialogPlogParams?.data,
                center_flag: undefined,
              },
            },
          };
        } else {
          return v;
        }
      });
    }
    // 清除门店数据
    form.setFieldsValue({
      out_store_names: '',
      out_store_ids: '',
      store_names: '',
      store_ids: '',
    });
    let tableArr: any = [];
    switch (key) {
      case 'summary':
        tableArr = SummaryArr;
        setRowData(summaryArrRowData);
        setFooterData(summaryArrFooterData);
        setPagin(summaryArrPagin);
        break;
      case 'detail':
        tableArr = DetailArr;
        setRowData(detailArrRowData);
        setFooterData(detailArrFooterData);
        setPagin(detailArrPagin);

        break;
      case 'basket':
        tableArr = BasketArr;
        setRowData(basketArrRowData);
        setFooterData(basketArrFooterData);
        setPagin(basketArrPagin);

        break;
      default:
        tableArr = SummaryArr;
        setRowData(summaryArrRowData);
        setFooterData(summaryArrFooterData);
        setPagin(summaryArrPagin);
        break;
    }

    if (key === 'summary') {
      formListT.find((v) => v.name === 'checkValue')!.hidden = false;
    } else {
      formListT.find((v) => v.name === 'checkValue')!.hidden = true;
    }
    if (key === 'basket') {
      formListT.find((v) => v.name === 'store_ids')!.hidden = true;
      formListT.find((v) => v.name === 'compactDatePicker')!.hidden = true;
      // setHiddenData(true);
    } else {
      formListT.find((v) => v.name === 'store_ids')!.hidden = false;
      formListT.find((v) => v.name === 'compactDatePicker')!.hidden = false;
      // setHiddenData(false);
    }
    setSearchFormLists(formListT);
    setItemArr(tableArr);
  };

  useEffect(() => {
    initData();
  }, []);
  itemArr.map((v) => tableRender(v));
  return (
    <XlbPageContainer>
      <div
        className={'button_box row-flex'}
        style={{ marginBottom: '10px', padding: '0 16px' }}
      >
        <div style={{ width: '90%' }} className="row-flex">
          <XlbButton.Group>
            {hasAuth(['物资统计', '查询']) && (
              <XlbButton
                label={'查询'}
                disabled={isLoading}
                type="primary"
                onClick={() => getData()}
                icon={<SearchOutlined />}
              />
            )}
            {hasAuth(['物资统计', '导出']) && (
              <XlbButton
                type="primary"
                label={'导出'}
                disabled={!rowData?.length || isLoading}
                onClick={(e: any) => exportItem(e)}
                icon={<UploadOutlined />}
              />
            )}
          </XlbButton.Group>
        </div>
        <div
          style={{
            width: '10%',
            height: '28px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'flex-end',
            columnGap: '8px',
          }}
        >
          <Tooltip title={isFold ? '收起' : '展开'}>
            <XlbIcon
              data-type={'顶层展开收起'}
              size={20}
              onClick={() => setIsFold(!isFold)}
              name="shouqi"
              className={classnames('xlb-columns-main-btn', {
                'xlb-columns-fold': !isFold,
                'xlb-columns-expand-btn-dev': true,
              })}
            />
          </Tooltip>
          <XlbColumns
            isFold={isFold}
            isFoldChange={setIsFold}
            url={handleUid()?.key}
            originColumns={oldArr()}
            value={itemArr}
            onChange={setItemArr}
            name={handleUid()?.name}
            // formList={formLists}
            // originFormList={formLists}
            // onFormChange={setSearchFormLists}
          />
        </div>
      </div>

      <div
        style={{ display: isFold ? 'none' : 'block' }}
        className={'form_header_box'}
      >
        <XlbForm
          formList={formLists.filter((v) => {
            if (!enable_cargo_owner) {
              return v.name !== 'out_org_ids';
            }
            return v;
          })}
          form={form}
          isHideDate={true}
        />
      </div>

      <div>
        <Tabs
          defaultActiveKey={tabsKey}
          style={{ paddingLeft: '16px' }}
          className="contractTab"
          onChange={(key) => onChangeKey(key)}
        >
          <TabPane tab="汇总查询" key={'summary'}></TabPane>
          <TabPane tab="按明细查询" key={'detail'}></TabPane>
          {!LStorage.get('userInfo').store.enable_delivery_center ? null : (
            <TabPane tab="物资合计" key={'basket'}></TabPane>
          )}
        </Tabs>
      </div>
      <XlbTable
        tableKey={tabsKey}
        columns={itemArr.filter((v) => {
          if (!enable_cargo_owner) {
            return v.code !== 'out_org_name';
          }
          return v;
        })}
        key={`${isFold?.toString()}_${tabsKey}`}
        // keepDataSource={false}
        isLoading={isLoading}
        style={{ flex: 1, margin: '0 16px' }}
        // pagin={pagin}
        pageSize={pagin.pageSize}
        total={tabsKey === 'basket' ? rowData?.length : pagin?.total}
        dataSource={rowData}
        isFold={isFold}
        onPaginChange={(page: number, pageSize: number) => {
          if (tabsKey === 'basket') return;
          const paginT = {
            total: pagin.total,
            pageNum: page,
            pageSize,
          };
          // setPagin(paginT);
          switch (tabsKey) {
            case 'summary':
              setSummaryArrPagin(paginT);
              break;
            case 'detail':
              setDetailArrPagin(paginT);
              break;
            case 'basket':
              setBasketArrPagin(paginT);
          }
          getData(page, pageSize);
        }}
        onChangeSorts={(e) => {
          setSortType(e);
          setIsTabksKeyChange(false);
        }}
        // onPaginChange={tabsKey === 'basket' ? undefined : () => pageChange(pagin)}
        footerDataSource={tabsKey === 'basket' ? undefined : footerData}
      />
    </XlbPageContainer>
  );
};
export default Goodsway;

import { Form, Space } from 'antd';
import { useState } from 'react';
// @ts-ignore
import {
  XlbInputDialog,
  XlbMessage,
  XlbModal,
  XlbTipsModal,
} from '@xlb/components';
import { copy } from '../../../server';
import style from './copy.less';
const Copy = (props: any) => {
  const { visible, handleCancel, getData } = props;
  const [form] = Form.useForm();
  const [loding, setloding] = useState<boolean>(false);

  const handleOk = async () => {
    if (!form.getFieldValue('target_store_ids')) {
      XlbTipsModal({
        tips: `请先选择修改门店`,
      });
      return false;
    } else {
      if (!form.getFieldValue('source_store_id')) {
        XlbTipsModal({
          tips: `请先选择参照门店`,
        });
        return false;
      }
    }
    if (
      form.getFieldValue('target_store_ids')?.length === 1 &&
      form.getFieldValue('target_store_ids')?.[0] ===
        form.getFieldValue('source_store_id')?.[0]
    ) {
      XlbTipsModal({
        tips: `修改门店和参照门店不能相同！`,
      });
      return false;
    }
    const data = {
      ...form.getFieldsValue(),
      source_store_id: form.getFieldValue('source_store_id')?.[0],
    };
    form
      .getFieldValue('checkValue')
      ?.forEach((item: any) => (data[item] = true));
    setloding(true);
    const res = await copy(data);
    setloding(false);
    if (res.code === 0) {
      XlbMessage.success('操作成功');
      getData();
      form.resetFields();
      handleCancel();
    }
  };

  return (
    <XlbModal
      title={'复制'}
      centered
      visible={visible}
      maskClosable={false}
      onOk={handleOk}
      onCancel={() => {
        handleCancel();
        form.resetFields();
      }}
      bodyStyle={{ padding: 20 }}
      isCancel={true}
      width={500}
      confirmLoading={loding}
    >
      <Form form={form}>
        <div className={style.box}>
          <p className={style.title}>复制门店</p>
          <Form.Item>
            <Space
              direction="vertical"
              style={{
                width: 300,
                display: 'inline-block',
                marginLeft: 20,
                marginBottom: 7,
              }}
            >
              <Form.Item
                name="target_store_ids"
                style={{ display: 'inline-block' }}
                label="修改门店"
              >
                <XlbInputDialog
                  dialogParams={{
                    type: 'store',
                    isMultiple: true,
                    data: {
                      status: true,
                    },
                  }}
                  fieldNames={{
                    idKey: 'id',
                    nameKey: 'store_name',
                  }}
                  width={220}
                />
              </Form.Item>
            </Space>
            <Space
              direction="vertical"
              style={{ width: 300, display: 'inline-block', marginLeft: 20 }}
            >
              <Form.Item
                name="source_store_id"
                style={{ display: 'inline-block' }}
                label="参照门店"
              >
                <XlbInputDialog
                  dialogParams={{
                    type: 'store',
                    isMultiple: false,
                    data: {
                      status: true,
                    },
                  }}
                  fieldNames={{
                    idKey: 'id',
                    nameKey: 'store_name',
                  }}
                  width={220}
                />
              </Form.Item>
            </Space>
          </Form.Item>
        </div>
      </Form>
    </XlbModal>
  );
};
export default Copy;

import { columnWidthEnum } from '@/data/common/constant'
import { ArtColumn } from 'ali-react-table'
import { XlbTableColumnProps } from '@xlb/components'

export const Units = [
  {
    label: '采购单位',
    value: 'PURCHASE'
  },
  {
    label: '库存单位',
    value: 'STOCK'
  },
  {
    label: '配送单位',
    value: 'DELIVERY'
  },
  {
    label: '批发单位',
    value: 'WHOLESALE'
  },
  {
    label: '基本单位',
    value: 'BASIC'
  }
]
export const Rules = [
  {
    label: '补货订购点',
    value: 'point'
  },
  {
    label: '库存上限',
    value: 'upper_limit'
  },
  {
    label: '补货订购量',
    value: 'quantity'
  },
  {
    label: '基础库存',
    value: 'base_stock_quantity'
  }
]
export const checkOptions = [
  {
    label: '停购',
    value: 'stop_purchase'
  },
  {
    label: '停售',
    value: 'stop_sale'
  },
  {
    label: '停止要货',
    value: 'stop_request'
  }
]
export const checkOptions1 = [
  {
    label: '主供应商',
    value: 'main_supplier'
  }
]

export const checkOptions2 = [
  {
    label: '调出单',
    value: 'delivery_out_order'
  },
  {
    label: '批发销售单',
    value: 'wholesale_order'
  },
  {
    label: '前台销售',
    value: 'pos_order'
  }
]
// 批量修改
export const batch = [
  {
    label: '补货订购点',
    value: 'point'
  },
  {
    label: '库存上限',
    value: 'upper_limit'
  },
  {
    label: '补货订购量',
    value: 'quantity'
  },
  {
    label: '基础库存',
    value: 'base_stock_quantity'
  },
  {
    label: '预警周转天数',
    value: 'warn_day'
  }
]

//商品类型
export const goodsType = [
  {
    label: '主规格商品',
    value: 'MAINSPEC'
  },
  {
    label: '多规格商品',
    value: 'MULTIPLESPEC'
  },
  {
    label: '标准商品',
    value: 'STANDARD'
  },
  {
    label: '组合商品',
    value: 'COMBINATION'
  },
  {
    label: '成分商品',
    value: 'COMPONENT'
  },
  {
    label: '制单组合',
    value: 'MAKEBILL'
  },
  {
    label: '分级商品',
    value: 'GRADING'
  }
]
export const formList: any[] = [
  // {
  //   label: '供应商',
  //   value: 'supplier_names',
  //   type: 'dialog',
  //   clear: true,
  //   check: true,
  // },
  {
    label: '组织',
    value: 'org_ids',
    type: 'selects',
    clear: true,
    check: true,
    options: [],
    hidden: true
  },
  {
    label: '门店',
    value: 'store_names',
    type: 'dialog',
    clear: false,
    check: true
  },
  {
    label: '商品类别',
    value: 'item_types',
    type: 'dialog',
    clear: true,
    check: true
  },
  {
    label: '商品档案',
    value: 'item_names',
    type: 'dialog',
    clear: true,
    check: true
  },
  {
    label: '显示单位',
    value: 'unit_type',
    type: 'select',
    clear: true,
    check: true,
    options: Units
  }
]

export const tableList: XlbTableColumnProps<any>[] = [
  {
    name: '序号',
    code: '_index',
    width: columnWidthEnum.INDEX,
    align: 'center',
    features: { sortable: false }
  },
  {
    name: '组织',
    code: 'org_name',
    width: 140,
    hidden: true,
    features: { sortable: false }
  },
  {
    name: '门店',
    code: 'store_id',
    width: columnWidthEnum.STORE_NAME,
    features: { sortable: true },
    align: 'left'
  },
  {
    name: '商品代码',
    code: 'item_code',
    width: columnWidthEnum.ITEM_CODE,
    features: { sortable: true },
    align: 'left'
  },
  {
    name: '商品条码',
    code: 'item_bar_code',
    width: columnWidthEnum.ITEM_BAR_CODE,
    features: { sortable: true },
    align: 'left'
  },
  {
    name: '商品名称',
    code: 'item_name',
    width: 280,
    features: { sortable: true },
    align: 'left'
  },
  {
    name: '采购规格',
    code: 'item_spec',
    width: columnWidthEnum.ITEM_SPEC,
    features: { sortable: true },
    align: 'left'
  },
  {
    name: '单位',
    code: 'unit',
    width: 80,
    features: { sortable: true },
    align: 'left'
  },
  {
    name: '商品类别',
    code: 'item_category_name',
    width: 140,
    features: { sortable: true },
    align: 'left'
  },
  {
    name: '供应商',
    code: 'supplier_names',
    width: 280,
    features: { sortable: true },
    align: 'left'
  },
  {
    name: '采购员',
    code: 'purchase_by',
    width: 110,
    features: { sortable: true },
    align: 'left'
  },
  {
    name: '补货订购点',
    code: 'point',
    width: 110,
    features: { sortable: true },
    align: 'right'
  },
  {
    name: '库存上限',
    code: 'upper_limit',
    width: 110,
    features: { sortable: true },
    align: 'right'
  },
  {
    name: '补货订购量',
    code: 'quantity',
    width: 110,
    features: { sortable: true },
    align: 'right'
  },
  {
    name: '基础库存',
    code: 'base_stock_quantity',
    width: 110,
    features: { sortable: true },
    align: 'right'
  },
  {
    name: '预警周转天数',
    code: 'warn_day',
    width: 130,
    features: { sortable: true },
    align: 'right'
  },
  // {
  //   name: '关联采购员',
  //   code: 'purchase_by',
  //   width: 110,
  //   features: { sortable: true },
  //   align: 'left'
  // },
  {
    name: '停购',
    code: 'stop_purchase',
    width: 70,
    features: { sortable: true },
    align: 'left'
  },
  {
    name: '停售',
    code: 'stop_sale',
    width: 70,
    features: { sortable: true },
    align: 'left'
  },
  {
    name: '停止要货',
    code: 'stop_request',
    width: 100,
    features: { sortable: true },
    align: 'left'
  },
  {
    name: '最近销量',
    code: 'sale_quantity',
    width: 110,
    features: { sortable: true },
    align: 'right'
  },
  {
    name: '日均销量',
    code: 'avg_sale_quantity',
    width: 110,
    features: { sortable: true },
    align: 'right'
  },
  {
    name: '交货周期',
    code: 'purchase_period',
    width: 100,
    features: { sortable: true },
    align: 'right'
  },
  {
    name: '保质期',
    code: 'period',
    width: 110,
    features: { sortable: true },
    align: 'left'
  },
  {
    name: '最后更新时间',
    code: 'update_time',
    width: 160,
    features: { sortable: true, format: 'TIME' },
    align: 'left'
  },
  {
    name: '最后更新人',
    code: 'update_by',
    width: 110,
    features: { sortable: true },
    align: 'left'
  }
]

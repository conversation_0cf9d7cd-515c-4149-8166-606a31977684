import { SearchFormType, XlbTableColumnProps } from '@xlb/components';

export const searchFormList: SearchFormType[] = [
  {
    type: 'compactDatePicker',
    label: '日期选择',
    name: 'query_date',
    allowClear: false,
    format: 'YYYY-MM-DD',
    width: 392,
  },
  {
    label: '时间类型',
    name: 'date_type',
    type: 'select',
    allowClear: false,
    check: true,
    options: [
      {
        label: '制单时间',
        value: 'CREATE',
      },
      {
        label: '审核时间',
        value: 'AUDIT',
      },
      {
        label: '供应商已确认',
        value: 'SUPPLIER_CONFIRM_TIME',
      },
      {
        label: '已确认收货',
        value: 'STORE_CONFIRM_TIME',
      },
    ],
  },
  {
    label: '汇总条件',
    name: 'group_by',
    type: 'select',
    allowClear: false,
    check: true,
    multiple: true,
    options: [
      {
        label: '门店',
        value: 'STORE',
      },
      {
        label: '供应商',
        value: 'SUPPLIER',
      },
      {
        label: '单据',
        value: 'FID',
      },
      {
        label: '商品',
        value: 'ITEM',
      },
    ],
    rules: [{ required: true, message: '请选择汇总条件' }],
  },
  {
    label: '汇总条件-时间',
    name: 'date_by',
    type: 'select',
    allowClear: true,
    check: true,
    multiple: false,
    options: [
      {
        label: '日',
        value: 'DAY',
      },
      {
        label: '周',
        value: 'WEEK',
      },
      {
        label: '月',
        value: 'MONTH',
      },
    ],
  },
  {
    label: '收货门店',
    name: 'store_ids',
    type: 'inputDialog',
    allowClear: false,
    dialogParams: {
      type: 'store',
      dataType: 'lists',
      isLeftColumn: true,
      isMultiple: true,
      data: {
        status: true,
      },
    },
    fieldNames: {
      // @ts-ignore
      idKey: 'id',
      nameKey: 'store_name',
    },
  },
  {
    label: '供应商',
    name: 'supplier_ids',
    type: 'inputDialog',
    dialogParams: {
      type: 'supplier',
      dataType: 'lists',
      isLeftColumn: true,
      isMultiple: true,
      primaryKey: 'id',
      data: {},
    },
  },
  {
    label: '商品档案',
    name: 'item_ids',
    type: 'inputDialog',
    dialogParams: {
      type: 'goods',
      dataType: 'lists',
      isLeftColumn: true,
      isMultiple: true,
      primaryKey: 'id',
      data: {
        enabled: true,
      },
    },
  },
];

export const basicTable: XlbTableColumnProps<any>[] = [
  {
    name: '时间',
    code: 'time',
    width: 240,
    features: { format: 'TIME' },
  },
  {
    name: '下单数量',
    code: 'quantity',
    width: 150,
    align: 'right',
  },
  {
    name: '下单金额（元）',
    code: 'money',
    width: 200,
    align: 'right',
  },
  {
    name: '实发数量',
    code: 'actual_delivered_quantity',
    width: 150,
    align: 'right',
  },
  {
    name: '实发金额（元）',
    code: 'actual_delivered_money',
    width: 200,
    align: 'right',
  },
  {
    name: '实收数量',
    code: 'actual_received_quantity',
    width: 150,
    align: 'right',
  },
  {
    name: '实收金额（元）',
    code: 'actual_received_money',
    width: 200,
    align: 'right',
  },
  {
    name: '差异值',
    code: 'diff_quantity',
    width: 90,
    align: 'right',
  },
];

export const inFid: XlbTableColumnProps<any>[] = [
  {
    name: '单据号',
    code: 'fid',
    width: 140,
  },
];

export const inStore: XlbTableColumnProps<any>[] = [
  {
    name: '收货门店',
    code: 'store_name',
    width: 140,
  },
];

export const inSupplier: XlbTableColumnProps<any>[] = [
  {
    name: '供应商',
    code: 'supplier_name',
    width: 140,
  },
];

export const inItem: XlbTableColumnProps<any>[] = [
  {
    name: '商品名称',
    code: 'item_name',
    width: 200,
  },
];

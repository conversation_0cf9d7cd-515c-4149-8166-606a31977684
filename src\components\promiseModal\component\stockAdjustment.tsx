import { columnWidthEnum } from '@/data/common/constant';
import { hasAuth } from '@/utils/kit';
import safeMath from '@/utils/safeMath';
import toFixed from '@/utils/toFixed';
import {
  SearchFormType,
  XlbBasicForm,
  XlbForm,
  XlbTable,
  XlbTableColumnProps,
} from '@xlb/components';
import { Tooltip } from 'antd';
import _ from 'lodash';
import { useEffect, useState } from 'react';
import { stockReadInfo } from '../server';

const formList: SearchFormType[] = [
  {
    type: 'input',
    disabled: true,
    name: 'store_name',
    label: '调整门店',
  },
  {
    type: 'input',
    disabled: true,
    name: 'storehouse_name',
    label: '调整仓库',
  },
  {
    type: 'select',
    disabled: true,
    name: 'reason_name',
    label: '调整原因',
  },
  {
    type: 'input',
    disabled: true,
    name: 'flag_name',
    label: '出入库标记',
  },
  {
    type: 'input',
    disabled: true,
    name: 'fid',
    label: '单据号',
  },
  {
    type: 'select',
    disabled: true,
    name: 'state',
    label: '单据状态',
    options: [
      { label: '制单', value: 'INIT' },
      { label: '审核', value: 'AUDIT' },
      { label: '完成', value: 'FINISH' },
      { label: '作废', value: 'INVALID' },
    ],
  },
  {
    type: 'input',
    disabled: true,
    name: 'item_dept_names',
    label: '商品部门',
  },
  {
    type: 'datePicker',
    disabled: true,
    name: 'operate_date',
    label: '调整日期',
  },
  {
    type: 'input',
    disabled: true,
    name: 'memo',
    label: '留言备注',
    width: 648,
  },
];
const columnsList: XlbTableColumnProps<any>[] = [
  {
    name: '序号',
    code: '_index',
    width: columnWidthEnum.INDEX,
    align: 'center',
  },
  {
    name: '操作',
    code: 'operation',
    align: 'center',
    width: 60,
  },
  {
    name: '商品代码',
    code: 'item_code',
    width: columnWidthEnum.ITEM_CODE,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '商品条码',
    code: 'item_bar_code',
    width: columnWidthEnum.ITEM_BAR_CODE,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '商品名称',
    code: 'item_name',
    width: 280,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '采购规格',
    code: 'item_spec',
    width: columnWidthEnum.ITEM_SPEC,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '单位',
    code: 'unit',
    width: 80,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '数量',
    code: 'quantity',
    width: 110,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '单价',
    code: 'price',
    width: 110,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '金额',
    code: 'money',
    width: 120,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '金额(去税)',
    code: 'no_tax_money',
    width: 140,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '基本单位',
    code: 'basic_unit',
    width: 110,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '基本数量',
    code: 'basic_quantity',
    width: 110,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '基本单价',
    code: 'basic_price',
    width: 110,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '生产日期',
    code: 'producing_date',
    width: columnWidthEnum.DATE,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '到期日期',
    code: 'expire_date',
    width: columnWidthEnum.DATE,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '批次号',
    code: 'batch_number',
    width: 140,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '零售价',
    code: 'sale_price',
    width: 110,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '盈亏零售金额',
    code: 'diff_retail_money',
    width: 120,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '保质期',
    code: 'period',
    width: 90,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '库存数量',
    code: 'stock_number',
    width: 120,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '备注',
    code: 'memo',
    width: columnWidthEnum.MEMO,
    features: { sortable: true },
    align: 'left',
  },
];

const StockAdjustment = (props: any) => {
  const { fid } = props;
  const [formModel] = XlbBasicForm.useForm<any>();
  const [tableLoading, setTableLoading] = useState<any>(false);
  // 列表数据
  const [fidDataList, setFidDataList] = useState<any>([]);
  const [footerData, setFooterData] = useState<any[]>([]);
  const [itemArrdetail] = useState<XlbTableColumnProps<any>[]>(
    columnsList.map((i: any) => ({
      ...i,
      code: i.code == 'index' ? '_index' : i.code,
      hidden: i.code == 'operation' ? true : i.hidden,
    })) as any[],
  );

  const tableRender = (item: any) => {
    switch (item.code) {
      case 'stock_number':
        item.render = (value: any, record: any) => {
          return record.index === '合计' ? null : (
            <div>
              {' '}
              {toFixed(
                safeMath.divide(record.basic_stock_quantity, record.ratio),
                'QUANTITY',
                true,
              )}
            </div>
          );
        };
        break;
      case 'latest_basic_price':
        item.render = (value: any, record: any) => {
          return record.index === '合计' ? null : (
            <div>
              {hasAuth(['库存调整/价格', '查询'])
                ? (record.latest_basic_price / record.ratio).toFixed(4) != 'NaN'
                  ? (record.latest_basic_price / record.ratio).toFixed(4)
                  : ''
                : '****'}
            </div>
          );
        };
        break;
      case 'price':
        item.render = (value: any, record: any) => (
          <div>
            {record.index === '合计'
              ? ''
              : hasAuth(['库存调整/价格', '查询'])
                ? value.toFixed(4)
                : '****'}
          </div>
        );
        break;
      case 'money':
      case 'retail_money':
        item.render = (value: any, record: any) => (
          <div>
            {hasAuth(['库存调整/价格', '查询']) ? value.toFixed(2) : '****'}
          </div>
        );
        break;
      case 'basic_price':
        item.render = (value: any, record: any) => (
          <div>
            {record.index === '合计'
              ? ''
              : hasAuth(['库存调整/价格', '查询'])
                ? value.toFixed(4)
                : '****'}
          </div>
        );
        break;
      case 'quantity':
      case 'basic_quantity':
        item.render = (value: any, record: any) => (
          <div>{value ? value.toFixed(3) : '0.000'}</div>
        );
        break;
      case 'memo':
        item.render = (value: any) => {
          return (
            <Tooltip placement="topLeft" autoAdjustOverflow title={value}>
              <div>{value}</div>
            </Tooltip>
          );
        };
        break;
      case 'sale_price':
        item.render = (value: any) => {
          return <div>{value ? value.toFixed(2) : '0.00'}</div>;
        };
        break;
    }
    return item;
  };

  const openRefOrder = _.debounce(async (fid, summary: boolean = false) => {
    setTableLoading(true);
    setFidDataList([]);
    formModel.setFieldsValue({});
    const res = await stockReadInfo({ fid: fid, summary: summary });
    if (res?.code == 0) {
      // 表单的值
      formModel.setFieldsValue({
        ...res?.data,
        retail_money: (
          res?.data.basic_retail_price * res?.data.basic_quantity
        ).toFixed(2),
        flag_name: res?.data?.flag ? '出库' : '入库',
      });
      // 列表的值
      setFidDataList(res?.data?.details);
      // 计算合计
      footerData[0] = {
        index: '合计',
        frame_num: res.data.details.reduce(
          (sum: any, v: any) => sum + Number(v.frame_num),
          0,
        ),
        money: hasAuth(['库存调整/价格', '查询'])
          ? res.data.details
              .reduce((sum: any, v: any) => sum + Number(v.money), 0)
              .toFixed(2)
          : '****',
        quantity: res.data.details
          .reduce((sum: any, v: any) => sum + Number(v.quantity), 0)
          .toFixed(3),
        basic_quantity: res.data.details
          .reduce((sum: any, v: any) => sum + Number(v.basic_quantity), 0)
          .toFixed(3),
      };
      setFooterData([...footerData]);
    }
    setTableLoading(false);
  }, 50);

  useEffect(() => {
    openRefOrder(fid);
    itemArrdetail.map((v) => tableRender(v));
  }, []);

  return (
    <>
      <XlbForm
        style={{ marginTop: 15 }}
        formList={formList}
        form={formModel}
        isHideDate={true}
      />
      <XlbTable
        isLoading={tableLoading}
        style={{ height: 400, maxHeight: 400, overflowY: 'scroll' }}
        hideOnSinglePage={false}
        showSearch={true}
        columns={itemArrdetail}
        total={fidDataList?.length}
        dataSource={fidDataList}
        key={fidDataList?.length}
      ></XlbTable>
    </>
  );
};

export default StockAdjustment;

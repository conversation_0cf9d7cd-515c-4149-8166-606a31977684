import { useModal } from '@ebay/nice-modal-react';
import type { BaseModalProps } from '@xlb/components';
import { XlbBasicForm, XlbInputNumber, XlbModal } from '@xlb/components';
import { message } from 'antd';
import { useEffect, useState, type FC } from 'react';
import api from '../../server';
interface Props extends BaseModalProps {
  fetchData?: any;
  id?: number;
}

const EditSCMRules: FC<Props> = ({ fetchData, id = -1 }) => {
  const [form] = XlbBasicForm.useForm();
  const modal = useModal();
  const [rowData, setRowData] = useState<any[]>([]);
  const [goodsExcludeRowData, setGoodsExcludeRowData] = useState<any[]>([]);
  const [isLoading, setisLoading] = useState<boolean>(false);

  useEffect(() => {
    setisLoading(true);
    api.scmRead({ id: id }).then((res: any) => {
      form.setFieldsValue({
        ...res?.data,
      });
      setisLoading(false);
    });
  }, []);

  const handleOk = async (values: any) => {
    setisLoading(true);
    const res = await api.scmUpdate({
      ...values,
      id: id,
    });
    setisLoading(false);
    if (res?.code === 0) {
      modal.hide();
      modal.resolve(false);
      fetchData?.();
      message.success('操作成功');
    }
  };
  return (
    <XlbModal
      // width={}
      open={modal.visible}
      title={'编辑SCM自动统配规则'}
      isCancel={true}
      onOk={async () => {
        form.submit();
      }}
      width={480}
      onCancel={() => {
        modal.resolve(false);
        modal.hide();
      }}
    >
      <div>
        <XlbBasicForm
          form={form}
          style={{ margin: '20px 0' }}
          layout={'horizontal'}
          disabled={isLoading}
          onFinish={() => {
            const values = form.getFieldsValue(true);
            handleOk(values);
          }}
        >
          <XlbBasicForm.Item label="门店" name="store_name">
            {form.getFieldValue('store_name')}
          </XlbBasicForm.Item>
          <XlbBasicForm.Item label="商品" name="item_name">
            <div style={{ display: 'flex', gap: 12 }}>
              <div>{form.getFieldValue('item_code')}</div>
              <div style={{ whiteSpace: 'nowrap' }}>
                {form.getFieldValue('item_name')}
              </div>
              <div>{form.getFieldValue('item_spec')}</div>
            </div>
          </XlbBasicForm.Item>
          <XlbBasicForm.Item
            label="自动统配数量"
            name="quantity"
            extra="此处单位为配送单位"
            rules={[{ required: true, message: '自动统配数量不能为空' }]}
          >
            <XlbInputNumber
              min={0}
              max={9999999999}
              defaultValue={0}
              style={{ width: 306 }}
              placeholder="请输入"
            />
          </XlbBasicForm.Item>
        </XlbBasicForm>
      </div>
    </XlbModal>
  );
};
export default EditSCMRules;

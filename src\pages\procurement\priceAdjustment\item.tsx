import review3 from '@/assets/images/xlb_erp/auditing.png';
import review1 from '@/assets/images/xlb_erp/doPass.png';
import review2 from '@/assets/images/xlb_erp/noPass.png';
import { columnWidthEnum } from '@/data/common/constant';
import { hasAuth } from '@/utils/kit';
import {
  XlbBaseUpload,
  XlbBasicForm,
  XlbBlueBar,
  XlbButton,
  XlbForm,
  XlbIcon,
  XlbTable,
  XlbTipsModal,
  type SearchFormType,
  type XlbTableColumnProps,
} from '@xlb/components';
import { Col, Form, Input, message, Row, Select, Tooltip } from 'antd';
import copy from 'copy-to-clipboard';
import { Fragment, useEffect, useState } from 'react';
import TemplateModel from './components/TemplateModel';
import { orderTypes, replyTypeOptions } from './data';
import './index.less';
import Api from './server';

const PriceAdjustmentItem = (props: any) => {
  const { record, onBack } = props;
  const [pagin, setPagin] = useState({ pageSize: 200, pageNum: 1, total: 0 });
  const [info, setInfo] = useState<any>({ state: 'INIT' });
  //   const [fid, setFid] = useState<string | number>('');

  const [rowData, setRowData] = useState<any[]>([]);
  const [priceTemplateVisible, setPriceTemplateVisible] = useState<{
    show: boolean;
    id: string;
    type?: 'show' | 'update';
    showDelivery: boolean;
    record: any;
  }>({
    show: false,
    id: '',
    type: 'update',
    showDelivery: false,
    record: {},
  });
  const [form] = Form.useForm();
  const [xlbForm] = XlbBasicForm.useForm();

  //读取信息
  const readInfo = async (id: any, flag?: boolean) => {
    const res = await Api.readPriceAdjustment({ fid: id });
    if (res.code == 0) {
      setPagin({
        ...pagin,
        total: res.data.details.length,
      });
      xlbForm.setFieldsValue({
        ...res.data,
        store_ids_name: res?.data?.stores?.map((v: any) => {
          return { ...v, id: v.store_id };
        }),
      });
      setInfo({
        ...res.data,
        supplier_id: res.data.suppliers.map((v: any) => v.supplier_id),
        supplier_name: res.data.suppliers
          .map((v: any) => v.supplier_name)
          .join(','),
      });
      const list = res.data?.details.map((v: any) => {
        return {
          ...v,
        };
      });
      if (!flag) {
        setRowData(list);
      } else {
        setRowData(rowData);
      }
    }
  };
  const formList: SearchFormType[] = [
    {
      label: '应用门店',
      name: 'store_ids',
      type: 'inputDialog',
      removeIcon: null,
      dialogParams: {
        type: 'store',
        dataType: 'lists',
        isLeftColumn: true,
        isMultiple: true,
        primaryKey: 'id',
        data: {
          enabled: true,
        },
      },
      fieldNames: {
        idKey: 'id',
        nameKey: 'store_name',
      } as any,
      handleOnChange: async (_: any, list: any) => {
        const data = {
          fid: record?.fid,
          item_ids: rowData.map((v: any) => v.item_id),
          stores: list?.map((v: any) => {
            return { store_id: v.id, store_name: v.store_name };
          }),
        };
        const res = await Api.allcation(data);
        if (res.code === 0) {
          message.success('操作成功');
          readInfo(info.fid, true);
        }
      },
      width: 230,
    },
    {
      label: '调价单号',
      name: 'fid',
      type: 'input',
      disabled: true,
      width: 230,
    },
    {
      label: '生效时间',
      name: 'effective_time',
      type: 'datePicker',
      disabled: true,
      width: 230,
    },
    {
      label: '调价类型',
      name: 'order_type',
      type: 'select',
      disabled: true,
      options: orderTypes,
      width: 230,
    },
    {
      label: '供应商',
      name: 'supplier_name',
      type: 'input',
      disabled: true,
      width: 230,
    },
    {
      label: '制单人',
      name: 'create_by',
      type: 'input',
      disabled: true,
      width: 230,
    },
    {
      label: '留言备注',
      name: 'memo',
      type: 'textArea',
      disabled: true,
      autoSize: { minRows: 2, maxRows: 2 },
      width: 580,
    },
  ];

  const denyGood = async (item: any) => {
    const res = await Api.denyGood({
      fid: record?.fid,
      item_ids: [item.item_id],
    });
    if (res.code === 0) {
      message.success('操作成功');
      readInfo(info.fid);
    }
  };

  const itemTableList: XlbTableColumnProps<any>[] = [
    {
      name: '序号',
      code: '_index',
      width: columnWidthEnum.INDEX,
      align: 'center',
    },
    {
      name: '商品代码',
      code: 'item_code',
      width: columnWidthEnum.ITEM_CODE,
      features: { sortable: true },
      align: 'left',
    },
    {
      name: '商品条码',
      code: 'item_bar_code',
      width: columnWidthEnum.ITEM_BAR_CODE,
      features: { sortable: true },
      align: 'left',
    },
    {
      name: '商品名称',
      code: 'item_name',
      width: 280,
      features: { sortable: true },
      align: 'left',
    },
    {
      name: '基本单位',
      code: 'basic_unit',
      width: 110,
      features: { sortable: true },
      align: 'left',
    },
    {
      name: '原基本单价',
      code: 'origin_basic_price',
      width: 110,
      features: { sortable: true },
      align: 'right',
      render: (value: any) => (
        <div className="info overwidth">￥{(value ?? 0).toFixed(4)}</div>
      ),
    },
    {
      name: '现基本单价',
      code: 'basic_price',
      width: 110,
      features: { sortable: true },
      align: 'right',
      render: (value: any, record: any) => (
        <div className="info overwidth">￥{(value ?? 0).toFixed(4)}</div>
      ),
    },
    {
      name: '采购规格',
      code: 'purchase_spec',
      width: columnWidthEnum.ITEM_SPEC,
      features: { sortable: true },
      align: 'left',
    },
    {
      name: '采购单位',
      code: 'purchase_unit',
      width: columnWidthEnum.ITEM_SPEC,
      features: { sortable: true },
      align: 'left',
    },
    {
      name: '原采购价',
      code: 'origin_purchase_price',
      width: columnWidthEnum.ITEM_SPEC,
      features: { sortable: true },
      align: 'right',
      render: (value: any, record: any) => (
        <div className="info overwidth">￥{(value ?? 0).toFixed(4)}</div>
      ),
    },
    {
      name: (
        <Fragment>
          现采购价&nbsp;
          <Tooltip title="此价格为供货关系价格">
            <span className="priceAdjustmentCopy">
              <XlbIcon name="bangzhu" />
            </span>
          </Tooltip>
        </Fragment>
      ) as any,
      code: 'purchase_price',
      width: 160,
      features: { sortable: true },
      lock: true,
      align: 'right',
      render: (value: any, record: any) => (
        <div className="info overwidth">￥{(value ?? 0).toFixed(4)}</div>
      ),
    },
    {
      name: '配送价',
      code: 'delivery_price',
      width: 102,
      align: 'right',
      features: { sortable: true },
      lock: true,
      render: (_value: any, record: any) => (
        <div>
          {info.state == 'AUDIT' ? (
            <XlbButton
              type="text"
              onClick={(e) => {
                e.stopPropagation();
                setPriceTemplateVisible({
                  id: record.item_id,
                  show: true,
                  type: 'update',
                  showDelivery: true,
                  record,
                });
              }}
            >
              设置
            </XlbButton>
          ) : (
            <XlbButton
              type="text"
              onClick={(e) => {
                e.stopPropagation();
                setPriceTemplateVisible({
                  id: record.item_id,
                  show: true,
                  type: 'show',
                  showDelivery: true,
                  record,
                });
              }}
            >
              查看
            </XlbButton>
          )}
        </div>
      ),
    },
    {
      name: '标准零售价',
      code: 'retail_price',
      width: 110,
      align: 'right',
      features: { sortable: true },
      lock: true,
      render: (_value: any, record: any) => (
        <div>
          {info.state == 'AUDIT' ? (
            <XlbButton
              type="text"
              onClick={(e) => {
                e.stopPropagation();
                setPriceTemplateVisible({
                  id: record.item_id,
                  show: true,
                  type: 'update',
                  showDelivery: false,
                  record,
                });
              }}
            >
              设置
            </XlbButton>
          ) : (
            <XlbButton
              type="text"
              onClick={(e) => {
                e.stopPropagation();
                setPriceTemplateVisible({
                  id: record.item_id,
                  show: true,
                  type: 'show',
                  showDelivery: false,
                  record,
                });
              }}
            >
              查看
            </XlbButton>
          )}
        </div>
      ),
    },
    {
      name: '操作',
      code: 'state',
      width: 110,
      features: { sortable: true },
      lock: true,
      render: (value: any, record: any) => (
        <div>
          <XlbButton
            type="text"
            disabled={
              value == 'DENY' || info.state === 'PASS' || info.state === 'DENY'
            }
            onClick={(e) => {
              e.stopPropagation();
              denyGood(record);
            }}
            style={{ backgroundColor: 'rgba(0, 0, 0, 0)' }}
          >
            {info.state === 'PASS'
              ? '已通过'
              : value === 'DENY'
                ? '已拒绝'
                : '拒绝'}
          </XlbButton>
        </div>
      ),
    },
  ];

  // 审核拒绝
  const onOkBeforeFunction = async () => {
    return form
      .validateFields()
      .then(async ({ reply_type, audit_memo }) => {
        const res = await Api.priceAdjustmentDeny({
          fid: record?.fid,
          reply_type: reply_type,
          audit_memo: audit_memo,
        });
        if (res.code === 0) {
          message.success('操作成功');
          onBack();
        }
        return true;
      })
      .catch(() => false);
  };

  // 审核通过
  const audit = async () => {
    const data = {
      fid: record?.fid,
    };
    const res = await Api.priceAdjustmentPass(data);
    if (res.code == 0) {
      message.success('操作成功');
      onBack();
    }
  };

  // 审核拒绝
  const deny = async () => {
    XlbTipsModal({
      title: '批复拒绝',
      tips: (
        <Fragment>
          <Form
            form={form}
            name="control-hooks"
            style={{
              maxWidth: 500,
              marginTop: 16,
            }}
            labelCol={{ span: 6 }}
            wrapperCol={{ span: 14 }}
          >
            <Row wrap>
              <Col span={24}>
                <Form.Item
                  name="reply_type"
                  label="批复建议"
                  rules={[{ required: true, message: '请选择批复建议' }]}
                >
                  <Select placeholder="请选择" allowClear>
                    {replyTypeOptions.map((item) => (
                      <Select.Option key={item.value} value={item.value}>
                        {item.label}
                      </Select.Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
              <Col span={24}>
                <Form.Item name="audit_memo" label="备注">
                  <Input.TextArea rows={4} maxLength={300} showCount />
                </Form.Item>
              </Col>
            </Row>
          </Form>
        </Fragment>
      ),
      onOkBeforeFunction: onOkBeforeFunction,
      isCancel: true,
      width: 500,
    });
  };

  const onClick = async () => {
    if (!info?.stores?.length) {
      XlbTipsModal({
        tips: '请选择应用门店后审核',
      });

      return;
    }
    XlbTipsModal({
      tips: `当前选择应用门店共${info?.stores?.length ?? 0}家，请确认是否通过供应商的调价申请`,
      isCancel: true,
      onOk: audit,
    });
  };

  //分页切换事件
  const pageChange = (p: any) => {
    setPagin({
      ...pagin,
      pageNum: p.page_number,
    });
  };

  // 模版确认,携带更改后的模版价格｜取消事件
  const handleCancel = async (data: any) => {
    readInfo(record?.fid);
    setPriceTemplateVisible({
      ...priceTemplateVisible,
      show: false,
    });
  };

  const goBack = () => {
    onBack();
  };

  useEffect(() => {
    readInfo(record.fid);
  }, []);

  return (
    <div className="priceAdjustmentItemBox">
      {priceTemplateVisible?.show && (
        <TemplateModel
          id={priceTemplateVisible?.id}
          visible={priceTemplateVisible?.show}
          handleCancel={handleCancel}
          showDelivery={priceTemplateVisible?.showDelivery}
          footer={priceTemplateVisible?.type == 'show' ? null : '1'}
          record={priceTemplateVisible?.record}
          fid={info?.fid}
          effect_time={info?.effect_time}
        />
      )}
      <div
        className="priceAdjustmentItem"
        style={{ paddingTop: 8, minWidth: 1300 }}
      >
        <XlbButton.Group>
          {hasAuth(['调价管理', '审核']) && info.state === 'AUDIT' && (
            <XlbButton
              type={'primary'}
              label="处理通过"
              onClick={() => onClick()}
              loading={info.state !== 'AUDIT'}
              icon={<XlbIcon name="shenhe" />}
            />
          )}
          {hasAuth(['调价管理', '审核']) && info.state === 'AUDIT' && (
            <XlbButton
              type={'primary'}
              label="处理拒绝"
              loading={info.state !== 'AUDIT'}
              onClick={() => deny()}
              icon={<XlbIcon name="bohui1" />}
            />
          )}
          <XlbBaseUpload
            uploadText="附件"
            multiple={true}
            disabledDelete={true}
            disabled={true}
            mode="primaryButton"
            action="/scm/hxl.scm.file.upload"
            listType={'table'}
            accept={'all'}
            data={{ refType: '供应商调价单', refId: '供应商调价单' }}
            maxCount={99}
            fileList={info?.files || []}
          />
          <XlbButton
            type="primary"
            label="返回"
            onClick={goBack}
            icon={<XlbIcon name="fanhui" />}
          />
        </XlbButton.Group>
        <XlbBlueBar title={'基本信息'} hasMargin />
        <div className="priceAdjustmentInfo">
          <div className="priceAdjustmentTextInfo">
            {info.state == 'AUDIT' ? (
              <XlbForm
                isHideDate
                layout="inline"
                form={xlbForm}
                formList={formList}
                initialValues={{ store_ids: info?.stores }}
              />
            ) : (
              <>
                {info.state === 'PASS' ? (
                  <div className="item" style={{ display: 'flex' }}>
                    应用门店:{' '}
                    <Tooltip
                      placement="topLeft"
                      title={info?.stores
                        ?.map((v: any) => v.store_name)
                        .join(',')}
                    >
                      <span style={{ display: 'inline-block', width: 230 }}>
                        {info?.stores?.map((v: any) => v.store_name).join(', ')}
                      </span>
                    </Tooltip>
                  </div>
                ) : null}
                <div className="item">
                  调价单号: <span>{info.fid}</span>
                  <span
                    className="iconfont icon-fuzhi priceAdjustmentCopy"
                    onClick={() => {
                      copy(info.fid);
                      message.success('复制成功');
                    }}
                  />
                </div>
                <div className="item">
                  生效时间: <span>{info.effect_time}</span>
                </div>
                <div className="item">
                  调价类型:{' '}
                  <span>
                    {info.order_type == 'SUPPLIER_APPLY' && '供应商申请'}
                    {info.order_type == 'COMPETITIVE_PRICE' && '竞品采价申请'}
                    {info.order_type == 'OLD_NEW_PRICE' && '老品新开调价'}
                    {!info.order_type && '-'}
                  </span>
                </div>
                {/* TODO: 待定是否替换为supplier_names */}
                <div className="item" style={{ display: 'flex' }}>
                  供应商:
                  <Tooltip title={info.supplier_name || '-'}>
                    <span style={{ display: 'inline-block', width: 230 }}>
                      {info.supplier_name || '-'}
                    </span>
                  </Tooltip>
                </div>
                <div className="item">
                  制单人: <span>{info.create_by ? info.create_by : '-'}</span>
                </div>
                <div>
                  <span style={{ color: '#86909c' }}>留言备注:&nbsp;</span>
                  <span
                    style={{
                      display: 'inline-block',
                      width: 240,
                      lineHeight: 1,
                      marginBottom: -2,
                    }}
                  >
                    {info.memo ? info.memo : '-'}
                  </span>
                </div>
                {info.state == 'DENY' && (
                  <>
                    <div className="item">
                      批复建议:{' '}
                      <span>
                        {
                          replyTypeOptions.find(
                            (v) => v.value === info.reply_type,
                          )?.label
                        }
                      </span>
                    </div>
                    <div className="item">
                      备注:{' '}
                      <span>{info.audit_memo ? info.audit_memo : '-'}</span>
                    </div>
                  </>
                )}
              </>
            )}
          </div>
          <div className="priceAdjustmentImageInfo">
            {info.state == 'PASS' && (
              <img src={review1} width={90} height={78} />
            )}
            {info.state == 'DENY' && (
              <img src={review2} width={90} height={78} />
            )}
            {info.state == 'AUDIT' && (
              <img src={review3} width={90} height={78} />
            )}
          </div>
        </div>
        <XlbBlueBar title={'商品信息'} hasMargin />
        {/* <div style={{ height: 400 }}> */}
        <XlbTable
          dataSource={rowData}
          columns={itemTableList}
          total={pagin.total}
          pageSize={pagin.pageSize}
          pageNum={pagin.pageNum}
          onChange={pageChange}
        />
        {/* </div> */}
      </div>
    </div>
  );
};

export default PriceAdjustmentItem;

import { columnWidthEnum } from '@/data/common/constant';
import { useBaseParams } from '@/hooks/useBaseParams';
import { hasAuth } from '@/utils/kit';
import { wujieBus } from '@/wujie/utils';
import NiceModal from '@ebay/nice-modal-react';
import {
  ContextState,
  SearchFormType,
  XlbBasicForm,
  XlbButton,
  XlbForm,
  XlbIcon,
  XlbMessage,
  XlbPageContainer,
  XlbProPageModal,
  XlbProPageModalRef,
  XlbTableColumnProps,
  XlbTipsModal,
} from '@xlb/components';
import { XlbPageContainerRef } from '@xlb/components/dist/lowcodes/XlbPageContainer';
import { XlbFetch as ErpRequest } from '@xlb/utils';
import { Tooltip } from 'antd';
import moment from 'moment';
import { useEffect, useRef, useState } from 'react';
import { Options1, Options3 } from './data';
import Item from './item';
import Api from './server';
import StoreModal from './storeModal';
const { Table, ToolBtn, SearchForm } = XlbPageContainer;

const deliveryPriceMange = () => {
  const [form] = XlbBasicForm.useForm();
  const [isLoading, setisLoading] = useState<boolean>(false);
  const [pagin, setPagin] = useState({ pageSize: 200, pageNum: 1, total: 0 });
  const { enable_organization } = useBaseParams((state) => state);
  let refresh = () => { };
  const selectRowRef = useRef<any>(null);
  const formRef = useRef<any>(null);
  const dataSourceRef = useRef<any>(null);
  const pageModalRef = useRef<XlbProPageModalRef>(null);
  const pageConatainerRef = useRef<XlbPageContainerRef>(null);
  const [record, setRecord] = useState<any>({});
  const [visible, setVisible] = useState(false);
  const [tableLoading, setTableLoading] = useState<boolean>(false);
  const [storeTableData, setStoreTableData] = useState<any[]>([]);
  const [fid, setFid] = useState<any>();
  const formList: SearchFormType[] = [
    {
      width: 372,
      type: 'compactDatePicker',
      label: '日期选择',
      name: 'compactDatePicker',
      allowClear: false,
    },
    {
      label: '时间类型',
      name: 'time_type',
      type: 'select',
      clear: false,
      check: true,
      options: Options3,
    },
    {
      label: '单据状态',
      name: 'status',
      type: 'select',
      clear: true,
      check: true,
      options: Options1,
    },
    {
      label: '单据号',
      name: 'fid',
      type: 'input',
      tooltip: '单据号不受其他查询条件限制',
      clear: true,
      check: true,
    },
    {
      label: '应用组织',
      name: 'org_ids',
      type: 'inputDialog',
      treeModalConfig: {
        title: '选择组织',
        url: '/erp-mdm/hxl.erp.org.tree',
        dataType: 'lists',
        checkable: true, // 是否多选
        primaryKey: 'id',
      },
      hidden: !enable_organization,
    },
    {
      label: '配送中心',
      name: 'delivery_center_store_ids',
      type: 'inputDialog',
      dependencies: ['org_ids'],
      fieldNames: {
        idKey: 'id',
        nameKey: 'store_name',
      } as any,
      // rules: [{ required: true, message: '应用门店必填' }],
      dialogParams: (form) => {
        const data = {
          org_ids: enable_organization && form?.org_ids ? form?.org_ids : [],
        };
        if (!form?.org_ids) {
          delete data.org_ids;
        }
        return {
          type: 'store',
          dataType: 'lists',
          isLeftColumn: true,
          isMultiple: true,
          data: {
            ...data,
            center_flag: true,
          },
        };
      },
    },
    {
      label: '应用门店',
      name: 'store_ids',
      type: 'inputDialog',
      dependencies: ['org_ids'],
      fieldNames: {
        idKey: 'id',
        nameKey: 'store_name',
      } as any,
      // rules: [{ required: true, message: '应用门店必填' }],
      dialogParams: (form) => {
        const data = {
          org_ids: enable_organization && form?.org_ids ? form?.org_ids : [],
        };
        if (!form?.org_ids) {
          delete data.org_ids;
        }
        return {
          type: 'store',
          dataType: 'lists',
          isLeftColumn: true,
          isMultiple: true,
          data: {
            ...data,
            center_flag: false,
          },
        };
      },
    },
    {
      label: '商品档案',
      name: 'item_ids',
      type: 'inputDialog',
      dialogParams: {
        type: 'goods',
        dataType: 'lists',
        isMultiple: true,
      },
    },
  ];
  const tableList: XlbTableColumnProps<any>[] = [
    {
      name: '序号',
      code: '_index',
      width: columnWidthEnum.INDEX,
      align: 'center',
      lock: true,
    },
    {
      name: '活动单号',
      code: 'fid',
      width: columnWidthEnum.fid,
      features: { sortable: true },
      align: 'left',
      render: (value: any, record: any, index: any) => {
        return (
          <>
            <div className="cursors">
              <span
                className="link cursors"
                onClick={(e) => {
                  e.stopPropagation();
                  setRecord(record);
                  pageModalRef.current?.setOpen(true);
                }}
              >
                {value}
              </span>
            </div>
          </>
        );
      },
    },
    {
      name: '配送中心',
      code: 'delivery_center_store_id',
      width: 160,
      features: { sortable: true },
      align: 'left',
      render: (value: any, record: any) => {
        return record?.delivery_center_store_name;
      },
    },
    {
      name: '应用门店',
      code: 'storeCount',
      width: 160,
      features: { sortable: true },
      align: 'left',
      render: (value: any, record) => {
        return (
          <div style={{ display: 'flex' }}>
            {/* <div style={{ padding: 0 }}>
              {!record?.store_count }
            </div> */}
            {record?.store_count >= 1 ? (
              <a
                className="abutten"
                onClick={async (e) => {
                  e.stopPropagation();

                  NiceModal.show(NiceModal.create(StoreModal), {
                    fid: record?.fid,
                    stores: record?.stores
                  });
                }}
              >
                ({record?.store_count})
              </a>
            ) : '--'}
          </div>
        );
      },
    },
    {
      name: '应用组织',
      code: 'org_id',
      width: 160,
      features: { sortable: true },
      align: 'left',
      hidden: !enable_organization,
      render: (value: any, record: any) => {
        return record?.org_name;
      },
    },
    {
      name: '活动类型',
      code: 'type',
      features: { sortable: true },
      align: 'left',
      render: (value: any, record: any) => {
        return record?.type_name;
      },
    },
    {
      name: '单据状态',
      code: 'status',
      width: columnWidthEnum.ORDER_STATE,
      features: { sortable: true },
      align: 'left',
      render: (value: any) => {
        const item = Options1.find((v) => v.value === value);
        return (
          <div className={`${item ? item.type : ''}`}>
            {item ? item.label : ''}
          </div>
        );
      },
    },
    {
      name: '活动开启时间',
      code: 'start_time',
      width: 140,
      features: { sortable: true, format: 'TIME' },
      align: 'left',
    },
    {
      name: '活动结束时间',
      code: 'end_time',
      width: 140,
      features: { sortable: true, format: 'TIME' },
      align: 'left',
    },
    {
      name: '制单人',
      code: 'create_by',
      width: columnWidthEnum.BY,
      features: { sortable: true },
      align: 'left',
    },
    {
      name: '审核人',
      code: 'audit_by',
      width: columnWidthEnum.BY,
      features: { sortable: true },
      align: 'left',
    },
    {
      name: '制单时间',
      code: 'create_time',
      width: columnWidthEnum.TIME,
      features: { sortable: true, format: 'TIME' },
      align: 'left',
    },
    {
      name: '审核时间',
      code: 'audit_time',
      width: columnWidthEnum.TIME,
      features: { sortable: true, format: 'TIME' },
      align: 'left',
    },
    {
      name: '留言备注',
      code: 'memo',
      width: columnWidthEnum.MEMO,
      features: { sortable: true },
      align: 'left',
      render: (value: any, record: any) => {
        return (
          <Tooltip placement="topLeft" autoAdjustOverflow title={value}>
            <div className="info overwidth"> {value}</div>
          </Tooltip>
        );
      },
    },
  ];

  const prevPost = async () => {
    return getParams(pagin?.pageNum);
  };
  const getParams = (pageNum: number) => {
    const { compactDatePicker } = form.getFieldsValue(true);
    const _compactDatePicker = [
      compactDatePicker[0] + ' 00:00:00',
      compactDatePicker[1] + ' 23:59:59',
    ];
    const data = {
      ...form.getFieldsValue(),
      store_ids: form.getFieldValue('store_ids')
        ? form.getFieldValue('store_ids')
        : null,
      audit_date:
        form.getFieldValue('time_type') === 'audit_date'
          ? _compactDatePicker
          : null,
      create_date:
        form.getFieldValue('time_type') === 'create_date'
          ? _compactDatePicker
          : null,
      start_date:
        form.getFieldValue('time_type') === 'start_date'
          ? _compactDatePicker
          : null,
      end_date:
        form.getFieldValue('time_type') === 'end_date'
          ? _compactDatePicker
          : null,
      org_ids: form.getFieldValue('org_ids')
        ? form.getFieldValue('org_ids')
        : [],
      item_ids: form.getFieldValue('item_ids')
        ? form.getFieldValue('item_ids')
        : null,
      page_size: pagin.pageSize,
      page_number: pageNum,
    };
    return data;
  };

  const exportItem = async (e: any) => {
    const data = getParams(1);
    setisLoading(true);
    const res = await ErpRequest.post('/erp/hxl.erp.campaign.export', data);
    if (res.code === 0) {
      wujieBus?.$emit('xlb_erp-event', {
        code: 'downloadEnd',
        target: e,
      });
      XlbMessage.success(res.data);
    }
    setisLoading(false);
  };
  //删除
  const deleteItem = async () => {
    let errFid: any = [];
    dataSourceRef?.current?.map((v) => {
      selectRowRef?.current?.map((j) => {
        v.fid === j && v.state !== 'INIT' && errFid.push(j);
      });
    });
    if (errFid.length > 0) {
      XlbTipsModal({
        tips: '只能删除单据状态为制单的单据!',
      });
      return;
    }
    await XlbTipsModal({
      tips: `已选择${selectRowRef?.current?.length}张单据，是否确认删除!`,
      onOkBeforeFunction: async () => {
        const deleteFids = selectRowRef?.current?.map((v) => v.fid);
        const res = await Api.deleteInfo({ fids: deleteFids });
        if (res.code === 0) {
          XlbTipsModal({
            tips: `已删除${deleteFids.length}张单据!`,
          });
          refresh();
        }
        return true;
      },
    });
  };
  const onValuesChange = (changedValues: any, allValues: any) => {
    if (Object.keys(changedValues).includes('org_ids')) {
      form.setFieldsValue({
        store_ids: [],
      });
    }
  };
  useEffect(() => {
    form.setFieldsValue({
      time_type: 'create_date',
      compactDatePicker: [
        moment().format('YYYY-MM-DD'),
        moment().format('YYYY-MM-DD'),
      ],
    });
  }, []);

  return (
    <>
      <XlbProPageModal
        ref={pageModalRef}
        Content={({ onClose }) => {
          return (
            <div>
              <Item
                onBack={(back: boolean) => {
                  if (back) {
                    pageConatainerRef?.current?.fetchData?.();
                  }
                  pageModalRef.current?.setOpen(false);
                }}
                record={record}
              ></Item>
            </div>
          );
        }}
      >
        <div className="xlb-table-clickBtn"></div>
      </XlbProPageModal>
      <XlbPageContainer
        ref={pageConatainerRef}
        url={'/erp/hxl.erp.campaign.page'}
        tableColumn={tableList}
        prevPost={prevPost}
        immediatePost={true}
      >
        <ToolBtn showColumnsSetting>
          {({
            fetchData,
            loading,
            dataSource,
            requestForm,
            selectRowKeys,
            selectRow,
            form,
          }: ContextState<any>) => {
            refresh = fetchData;
            selectRowRef.current = selectRow;
            dataSourceRef.current = dataSource;
            formRef.current = form;
            return (
              <XlbButton.Group>
                {hasAuth(['配送营销活动', '查询']) && (
                  <XlbButton
                    label="查询"
                    type="primary"
                    disabled={isLoading}
                    onClick={() => {
                      fetchData();
                    }}
                    icon={<XlbIcon name="sousuo" />}
                  />
                )}
                {hasAuth(['配送营销活动', '编辑']) && (
                  <XlbButton
                    label="新增"
                    type="primary"
                    disabled={isLoading}
                    onClick={() => {
                      setRecord({ fid: 1 });
                      pageModalRef.current?.setOpen(true);
                    }}
                    icon={<XlbIcon name="jia" />}
                  />
                )}
                {hasAuth(['配送营销活动', '删除']) && (
                  <XlbButton
                    label="批量删除"
                    type="primary"
                    disabled={!selectRowKeys?.length || isLoading}
                    onClick={deleteItem}
                    icon={<XlbIcon name={'shanchu'} />}
                  />
                )}
                {hasAuth(['配送营销活动', '导出']) && (
                  <XlbButton
                    label="导出"
                    type="primary"
                    disabled={isLoading}
                    onClick={(e) => exportItem(e)}
                    icon={<XlbIcon name={'daochu'} />}
                  />
                )}
              </XlbButton.Group>
            );
          }}
        </ToolBtn>
        <SearchForm>
          <XlbForm
            formList={formList}
            form={form}
            isHideDate={true}
            getFormRecord={() => refresh()}
            onValuesChange={onValuesChange}
          />
        </SearchForm>
        <Table key="fid" selectMode="multiple" />
      </XlbPageContainer>
    </>
  );
};
export default deliveryPriceMange;

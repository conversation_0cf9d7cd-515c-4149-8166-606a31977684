.table_box {
  :global .art-table-body {
    min-height: calc(100vh - 430px);
  }
}

.table_fold_box {
  :global .art-table-body {
    min-height: calc(100vh - 600px);
  }
}
.form_container_wrapper {
  width: 100%;
  overflow-x: auto;
  .form_container_storeDeliveryDay {
    margin: 5px 0 5px 0;
    max-width: 1920px;
    width: 100%;
    :global .ant-form {
      // display: flex;
      // flex-direction: column;

      // .ant-form-item {
      //   width: 300px;
      // }
      .ant-form-item-label {
        width: 120px !important;
      }
    }
  }
}

.link {
  color: #3d66fe;
  text-decoration: underline;
  cursor: pointer;
}
@media (max-width: 991.98px) {
  .form_container_wrapper {
    width: 845px; /* 小于 992px 时保持最小宽度，触发滚动条 */
    min-width: 845px;
  }
}

export const StoreAreaKeyMap = {
  /**@name 门店区域类别 inputDialog 单选 */
  erpStoreAreaCategoriesId: 'erpStoreAreaCategoriesId',
  erpStoreAreaIds: 'erpStoreAreaIds',
};

export const storeAreaConfig: any[] = [
  {
    tag: 'ERP',
    label: '门店区域类别',
    id: StoreAreaKeyMap.erpStoreAreaCategoriesId,
    name: 'store_area_categories_id',
    fieldProps: {
      dialogParams: {
        type: 'storeAreaCategories',
        dataType: 'lists',
        isLeftColumn: false,
        isMultiple: false,
      },
    },
    componentType: 'inputDialog',
  },
  {
    tag: 'ERP',
    id: StoreAreaKeyMap.erpStoreAreaIds,
    label: '门店分组',
    name: 'store_group_ids',
    componentType: 'select',
    options: [],
    request: async (_formValues, anybaseURL, globalFetch) => {
      const res = await globalFetch.post(
        `${anybaseURL}/erp-mdm/hxl.erp.storegroup.find`,
        {},
      );
      if (res.code === 0) {
        return res.data.map((i: any) => ({
          ...i,
          label: i.name,
          value: i.id,
          disabled: i.name == '所有分组',
        }));
      }
      return [];
    },
    fieldProps: { mode: 'multiple' },
    formItemProps: {
      tooltip: '应用时：门店分组与下方门店信息的门店明细取并集',
    },
  },
];
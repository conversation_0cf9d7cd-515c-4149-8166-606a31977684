import React, { useEffect, useState, useRef } from 'react';
import { Progress, Button, Tooltip } from 'antd';
import style from './copy.less'
const RandomProgress = ({ show, onCancel }) => {
  const [progress, setProgress] = useState(0);
  const timerRef = useRef<any>(null);
  const [name, setName] = useState('');
  useEffect(() => {
    if (progress >= 100) {
      clearInterval(timerRef.current);
    }
  }, [progress]);

  RandomProgress.set = (name, progress) => {
    setName(name)
    setProgress(progress > 100 ? 100 : progress)
  };


  RandomProgress.start = () => {
    if (timerRef.current) {
      // 避免重复调用start方法时创建多个定时器
      return;
    }
    setProgress(1)
    timerRef.current = setInterval(() => {
      const increment = Math.floor(Math.random() * 10) + 1; // 生成1到10之间的随机数
      setProgress((prevProgress) => {
        if (prevProgress + increment >= 91) {
          return 91
        }
        return prevProgress + increment
      });
    }, 3000); // 每1000毫秒增加一次进度
  };

  RandomProgress.done = () => {
    clearInterval(timerRef.current);
    timerRef.current = null
    setProgress(100);
    const time = setTimeout(() => {
      setProgress(0)
      clearTimeout(time)
    }, 300)
  };

  return (
    <div className={style.copyProgress} style={{ display: show ? 'flex' : 'none' }}>
      <div className={style.copyProgress__text}>
        正在复制  <span style={{
          marginRight: '5px',
          marginLeft: '5px',
          color: 'rgba(29, 33, 41, 1)'
        }}>
          {name}</span>
      </div>
      <div style={{
        display: 'flex',
        flexDirection: 'row',
        alignItems: 'center',
        position: 'relative'
      }}>
        <span className={style.copyProgress__progressText}>{Math.ceil(progress)}%</span>
        <Progress
          style={{
            flex: 1,
          }}
          strokeWidth={20}
          showInfo={false}
          percent={progress}
          trailColor={'rgba(201, 205, 212, 1)'}
          strokeColor={'rgba(61, 102, 254, 1)'}
        />

        <Tooltip color={'white'} title={<>

          <div style={{
            color: 'black'
          }}>
            <div className={style.copyProgress__title}>
              提示
            </div>
            <div style={{
              marginBottom: '10px',
              marginTop: '10px',
              width: '180px',
              color: 'rgba(29, 33, 41, 1)'
            }}>
              请确认是否取消?
            </div>
          </div>
          <div>

            <Button onClick={onCancel} className={style.copyProgress__confirm}>确定取消</Button>

          </div>
        </>

        }   >
          <svg width="16px" height="16px" style={{
            marginLeft: '14px',
            zIndex: 1000
          }} viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg">
            <title>切片</title>
            <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
              <g id="进度条" transform="translate(-1109.000000, -585.000000)" fill="#86909C">
                <g id="清除" transform="translate(1109.000000, 585.000000)">
                  <path d="M12.9497475,3.05025253 C15.6834175,5.78392257 15.6834175,10.2160774 12.9497475,12.9497475 C10.2160774,15.6834175 5.78392257,15.6834175 3.05025253,12.9497475 C0.31658249,10.2160774 0.31658249,5.78392257 3.05025253,3.05025253 C5.78392257,0.31658249 10.2160774,0.31658249 12.9497475,3.05025253 Z M10.2539029,5.3041554 C10.1562718,5.20652432 9.99798055,5.20652432 9.90034947,5.3041554 L8,7.20379776 L6.09965053,5.3041554 C6.00201945,5.20652432 5.84372821,5.20652432 5.74609713,5.3041554 L5.3041554,5.74609713 C5.20652432,5.84372821 5.20652432,6.00201945 5.3041554,6.09965053 L7.20450487,7.99929289 L5.3041554,9.90034947 C5.20652432,9.99798055 5.20652432,10.1562718 5.3041554,10.2539029 L5.74609713,10.6958446 C5.84372821,10.7934757 6.00201945,10.7934757 6.09965053,10.6958446 L8,8.79478802 L9.90034947,10.6958446 C9.99798055,10.7934757 10.1562718,10.7934757 10.2539029,10.6958446 L10.6958446,10.2539029 C10.7934757,10.1562718 10.7934757,9.99798055 10.6958446,9.90034947 L8.79549513,7.99929289 L10.6958446,6.09965053 C10.7934757,6.00201945 10.7934757,5.84372821 10.6958446,5.74609713 Z" id="形状结合" transform="translate(8.000000, 8.000000) rotate(-360.000000) translate(-8.000000, -8.000000) "></path>
                </g>
              </g>
            </g>
          </svg>

        </Tooltip>

      </div>

    </div>
  )


};


export default RandomProgress;

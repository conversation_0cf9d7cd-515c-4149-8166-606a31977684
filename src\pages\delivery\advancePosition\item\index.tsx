import { useEffect, useState, } from 'react'
import {  message } from 'antd'
import Api from '../server'
// import { useKeepAliveRefresh } from '@/hooks'
import {
  XlbBasicForm,
  XlbButton,
  XlbConfigProvider,
  XlbIcon,
  XlbSelect,
  XlbBlueBar,
  XlbTable,
  XlbInputDialog,
  XlbTableColumnProps,
  XlbImportModal,
  XlbBasicData
} from '@xlb/components'
import { XlbFetch } from '@xlb/utils'
import { tableListGoods, goodsType } from '../data'
import { config } from '@/constants/baseDataConfig'
import { hasAuth } from '@/utils'
const SupplierExpensesItem = (props) => {
  // const { back } = useKeepAliveRefresh()
  const [rowData, setRowData] = useState<any>([])
  const [info, setInfo] = useState<any>({ state: 'INIT', fid: 1, settlementState: '' })
  const [isLoading, setIsLoading] = useState<boolean>(false)
  const [form] = XlbBasicForm.useForm<any>()
  const [storeList, setStoreList] = useState<any[]>([])
  const [itemArr, setItemArr] = useState<XlbTableColumnProps<any>[]>(
    JSON.parse(JSON.stringify(tableListGoods))
  )
  const [pagin, setPagin] = useState({ pageSize: 200, pageNum: 1, total: 0 })
  const [chooseList, setChooseList] = useState<any[]>([])
  const [isDisabled, setIsDisabled] = useState(true)
  const [isStoreIds, setIsStoreIds] = useState<any[]>([])
  const [isAreaStoreIds, setIsAreaStoreIds] = useState<any[]>([])
  const [saveType, setSaveType] = useState('add')
  const { record, onBack } = props;

  useEffect(() => {
    if (isStoreIds?.length > 0 && isAreaStoreIds?.length > 0) {
      setIsDisabled(false)
    } else {
      setIsDisabled(true)
    }
  }, [isStoreIds, isAreaStoreIds])
  // 获取门店列表
  const getStoreList = async () => {
    const res = await Api.getStores({})
    const store_list = res.data.map((i: any) => ({ value: i.id, label: i.store_name }))
    setStoreList(store_list)
  }
  useEffect(() => {
    getStoreList()
  }, [])
  const tableRender = (item: any) => {
    switch (item.code) {
      case 'stop_purchase':
      case 'stop_request':
        item.render = (value: any) => {
          return <div className="info overwidth"> {value ? '是' : '否'}</div>
        }
        break
      case 'item_type':
        item.render = (value: any) => {
          return (
            <div className="info overwidth">
              {' '}
              {goodsType.find((e) => e.value == value)?.label || value}
            </div>
          )
        }
        break
    }
  }
  rowData?.length && itemArr.map((v) => tableRender(v))
  const readInfo = async (fid: any) => {
    const res = await Api.read({ fid })
    form.setFieldsValue({
      front_store_id: [res?.data?.front_store_id],
      back_store_id: res?.data?.back_store_id,
    })
    const renDetails = res?.data?.details.map((v: any) => {
      return {
        ...v,
        id: v.item_id,
        name: v.item_name,
        code: v.item_code,
        bar_code: v.item_bar_code,
        purchase_spec: v.item_spec,
        unit: v.basic_unit
      }
    })
    setRowData(renDetails)
    setIsStoreIds(res?.data?.front_store_id)
    setIsAreaStoreIds(res?.data?.back_store_id)
    setSaveType('edit')
  }
  const operateOrder = async (t: string) => {
    try {
      await form.validateFields()
    } catch (err: any) {
      throw err
    }
    const data = {
      ...form.getFieldsValue(true),
      details: rowData.map((v) => {
        return {
          item_id: v.id
        }
      }),
      front_store_id: form.getFieldValue('front_store_id')?.[0],
      back_store_id: form.getFieldValue('back_store_id')?.[0]
    }
    if (saveType == 'edit') {
      data.fid = info.fid
    }
    const saveEdit = saveType == 'add' ? Api.save(data) : Api.update(data)
    const res = await saveEdit
    if (res.code == 0) {
      message.success('保存成功')
      onBack(true)
    }
  }
  const addGoods = async () => {
    const data = await XlbBasicData({
      type: 'goods',
      isMultiple: true,
      dataType: 'lists',
      primaryKey: 'id',
      resetForm: true,
      nullable: false,
      url: '/erp/hxl.erp.frontwarhouserelation.item.page',
      data: {
        store_id: form.getFieldValue('back_store_id')?.[0]
      }
    })
    if (Array.isArray(data)) {
      setRowData((prevRowData) => {
        const dataMap = new Map([...(prevRowData || []), ...data].map((item) => [item.id, item]))
        return Array.from(dataMap.values())
      })
    }
  }
  const deleteGoods = async () => {
    setRowData(rowData.filter((v) => chooseList.indexOf(v.id) === -1))
    setChooseList([])
  }

  const importStores = async () => {
    //
    const res = await XlbImportModal({
      importUrl: `${
        process.env.BASE_URL
      }/erp/hxl.erp.frontwarhouserelation.item.import?back_store_id=${form.getFieldValue(
        'back_store_id'
      )}&front_store_id=${form.getFieldValue('front_store_id')}`,
      templateUrl: `${process.env.BASE_URL}/erp/hxl.erp.frontwarhouserelation.item.template.download`,
      templateName: '商品导入模板'
    })
    if (res.code !== 0) return
    const data = res.data?.details
    const _data = data.map((v) => {
      return {
        ...v,
        id: v.item_id,
        name: v.item_name,
        code: v.item_code,
        bar_code: v.item_bar_code,
        purchase_spec: v.item_spec,
        unit: v.basic_unit
      }
    })
    if (Array.isArray(_data)) {
      setRowData((prevRowData) => {
        const dataMap = new Map([...(prevRowData || []), ..._data].map((item) => [item.id, item]))
        return Array.from(dataMap.values())
      })
    }
  }
  const getId = (tree: any, ids: any = []) => {
    for (const item of tree) {
      ids.push(item.id)
      if (item.children && item.children.length > 0) {
        getId(item.children, ids)
      }
    }
    return ids
  }
  useEffect(() => {
    if (record.fid !== 1) {
      readInfo(record.fid)
      setInfo({
        fid: record.fid
      })
    }
    if (record.fid == 1) {
      setSaveType('add')
      setInfo({
        fid: 1
      })
    }
  }, [])
  return (
    <XlbConfigProvider.Provider
      value={{
        baseURL: process.env.BASE_URL as string,
        config: config,
        globalFetch: XlbFetch,
        isOldBtn: true
      }}
    >
      <div style={{ margin: '10px 0 0 10px' }}>
        <XlbButton.Group>
          {hasAuth(['前置仓配置', '编辑']) ? (
            <XlbButton
              icon={<XlbIcon name="baocun" />}
              type="primary"
              onClick={() => operateOrder('保存')}
            >
              保存
            </XlbButton>
          ) : null}
          <XlbButton
            icon={<XlbIcon name="fanhui" />}
            type="primary"
            onClick={() =>  onBack()}
          >
            返回
          </XlbButton>
        </XlbButton.Group>
      </div>
      <XlbBasicForm form={form} style={{ width: '100%', margin: '12px 0 8px 0' }}>
        <XlbBasicForm.Item
          name="front_store_id"
          label="前置仓"
          rules={[{ required: true, message: '请选择前置仓' }]}
        >
          <XlbInputDialog
            disabled={rowData?.length > 0}
            dialogParams={{
              type: 'store',
              dataType: 'lists',
              isMultiple: false,
              showDialogByDisabled: true,
              initCustomValue: '所有门店',
              data: {
                status: true,
                center_flag: true,
                enable_organization: false
              }
            }}
            fieldNames={{
              idKey: 'id',
              nameKey: 'store_name'
            }}
            onChange={(value) => {
              setIsStoreIds(value)
            }}
            width={260}
          />
        </XlbBasicForm.Item>
        <XlbBasicForm.Item
          name="back_store_id"
          rules={[{ required: true, message: '请选择区域仓' }]}
          label="区域仓"
        >
          <XlbInputDialog
            disabled={rowData?.length > 0}
            dialogParams={{
              type: 'store',
              dataType: 'lists',
              isMultiple: false,
              showDialogByDisabled: true,
              initCustomValue: '所有门店',
              data: {
                status: true,
                center_flag: true,
                enable_organization: false
              }
            }}
            fieldNames={{
              idKey: 'id',
              nameKey: 'store_name'
            }}
            onChange={(value) => {
              setIsAreaStoreIds(value)
            }}
            width={260}
          />
        </XlbBasicForm.Item>
      </XlbBasicForm>
      <XlbBlueBar title={'商品信息'}></XlbBlueBar>
      <div style={{ margin: 10 }}>
        <XlbButton.Group>
          {hasAuth(['前置仓配置', '编辑']) ? (
            <XlbButton
              icon={<XlbIcon name="jia" />}
              disabled={isDisabled}
              onClick={addGoods}
              type="primary"
            >
              新增
            </XlbButton>
          ) : null}
          <XlbButton
            icon={<XlbIcon name="shanchu" />}
            disabled={!chooseList.length || isDisabled}
            onClick={deleteGoods}
            type="primary"
          >
            删除
          </XlbButton>
          <XlbButton
            icon={<XlbIcon name="daoru" />}
            disabled={isDisabled}
            onClick={importStores}
            type="primary"
          >
            导入
          </XlbButton>
        </XlbButton.Group>
      </div>
      <div style={{ height: 'calc(100vh - 300px)' }}>
        <XlbTable
          isLoading={isLoading}
          columns={itemArr}
          style={{ height: '100%', overflow: 'auto' }}
          dataSource={rowData}
          selectMode="multiple"
          total={rowData.length}
          showSearch
          pageSize={pagin.pageSize}
          useVirtual={true}
          selectedRowKeys={chooseList}
          onSelectRow={(selectedRowKeys, selectedRows) => {
            setChooseList(selectedRowKeys)
          }}
        />
      </div>
    </XlbConfigProvider.Provider>
  )
}

export default SupplierExpensesItem

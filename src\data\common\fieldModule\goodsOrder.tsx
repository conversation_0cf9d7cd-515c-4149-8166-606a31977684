export const GoodsOrderKeyMap = {
  billStoreCenterId: 'billStoreCenterId'
}

export const goodsOrderConfig: any[] = [
  {
    tag: 'ERP',
    label: '应用门店',
    id: GoodsOrderKeyMap.billStoreCenterId,
    name: 'store_id',
    dependencies: ['org_ids'],
    fieldProps: (form) => {
      const data = {
        ...form.getFieldsValue(['org_ids']),
        center_flag: true,
        status: true
      }
      if (!form.getFieldValue('org_ids')) {
        delete data.org_ids
      }
      return {
        fieldNames: {
          idKey: 'id',
          nameKey: 'store_name'
        } as any,
        dialogParams: {
          type: 'store',
          dataType: 'lists',
          isMultiple: false,
          data: data
        }
      }
    },
    componentType: 'inputDialog'
  }
]

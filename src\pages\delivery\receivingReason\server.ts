import { XlbFetch } from '@xlb/utils';

// 新增
export const save = async (data: any) => {
  return await XlbFetch.post(
    '/erp/hxl.erp.requisitionreason.save',
    data,
  );
};
// 编辑
export const update = async (data: any) => {
  return await XlbFetch.post(
    '/erp/hxl.erp.requisitionreason.update',
    data,
  );
};
// 查看
export const read = async (data: any) => {
  return await XlbFetch.post(
    '/erp/hxl.erp.requisitionreason.read',
    data,
  );
};


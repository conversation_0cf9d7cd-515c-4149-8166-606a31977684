import type { XlbTableColumnProps } from '@xlb/components'

export const applicabilityMap = Object.freeze([
  {
    label: '不限',
    value: 0
  },
  {
    label: '配送中心',
    value: 1
  },
  {
    label: '非配送中心',
    value: 2
  }
])
export const flagMap = Object.freeze([
  {
    label: '出库',
    value: true
  },
  {
    label: '入库',
    value: false
  }
])

export const tableColumn: XlbTableColumnProps<any>[] = [
  {
    name: '序号',
    code: '_index',
    width: 50,
    align: 'center'
  },
  {
    name: '库存调整原因名称',
    code: 'name',
    width: 190,
    features: { sortable: true, details: true }
  },
  {
    name: '出入库标记',
    code: 'flag',
    width: 120,
    render: (value: any, record: any, index: any) => {
      return <div>{flagMap.find((item) => item.value === value)?.label}</div>
    }
    // features: { sortable: true }
  },
  {
    name: '应用范围',
    code: 'applicability',
    width: 120,
    render: (value: any, record: any, index: any) => {
      return <div>{applicabilityMap.find((item) => item.value === value)?.label}</div>
    }
    // features: { sortable: true }
  }
]

export interface StockAdjustmentReasonFindResDTO {
  /**
   * @name 出入库标记
   */
  flag?: boolean

  /**
   * @name id
   */
  id?: number

  /**
   * @name 原因名字
   */
  name?: string
}

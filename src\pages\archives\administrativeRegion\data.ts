import { SearchFormType, XlbTableColumnProps } from '@xlb/components'

export const formList: SearchFormType[] = [
  {
    label: '关键字',
    name: 'keyword',
    type: 'input',
    allowClear: true,
    placeholder: ''
  },
  {
    label: '门店',
    name: 'store_ids',
    type: 'inputDialog',
    dialogParams: {
      type: 'store',
      dataType: 'lists',
      isLeftColumn: true,
      isMultiple: true,
      primaryKey: 'id',
      data: {
        enabled: true
      }
    },
    allowClear: true,
    fieldNames: {
      // @ts-ignore
      idKey: 'id',
      nameKey: 'store_name'
    },
  }
]

export const columns: XlbTableColumnProps<any>[] = [
  {
    name: '序号',
    code: '_index',
    width: 50,
    align: 'center'
  },
  {
    name: '省',
    code: 'province',
    width: 160,
    features: { sortable: true, copy: true }
  },
  {
    name: '市',
    code: 'city',
    width: 160,
    features: { sortable: true }
  },
  {
    name: '区',
    code: 'district',
    width: 160,
    features: { sortable: true }
  },
  {
    name: '门店代码',
    code: 'store_code',
    width: 160,
    features: { sortable: true }
  },
  {
    name: '门店名称',
    code: 'store_name',
    width: 312,
    features: { sortable: true }
  }
]

.erpButton {
  margin-left: 10px;
}

.erpButtonRight {
  margin-right: 10px;
}

.button_box {
  border-bottom: 1px solid @color_line2;
}
.contractTab {
  :global .ant-tabs-top > .ant-tabs-nav {
    margin: 0 0 4px 0;
  }
}
.contractTabs {
  :global .ant-tabs-top > .ant-tabs-nav {
    margin: 0;
  }
}
.form_container_wrapper {
  width: 100%;
  .form_container_transferDocument {
    margin: 5px 0 5px 0;
    width: 100%;
    max-width: 1700px;
    :global .ant-form {
      // display: flex;
      // flex-direction: column;

      // .ant-form-item {
      //   width: 300px;
      // }
      .ant-form-item-label {
        width: 120px !important;
      }
    }
    :global {
      .xlb-ant-form-inline {
        display: unset !important;
      }
      .xlb-ant-form-item-label {
        flex-shrink: 0 !important;
      }
      .xlb-input-dialog {
        width: 100% !important;
      }
      .-item-explain-error {
        color: #f53f3f !important;
      }
    }
  }
}

@media (max-width: 991.98px) {
  .form_container_transferDocument {
    width: 845px; /* 小于 992px 时保持最小宽度，触发滚动条 */
    min-width: 845px;
  }
}

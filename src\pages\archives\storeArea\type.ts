
export interface StoreAreaBatchDTO {

    /**
     * @name 门店区域id
     */
    store_area_ids?: Array<string | number>;

    /**
     * @name 门店id
     */
    store_ids?: Array<string | number>;

}

export interface BaseResponse {

    /**
     * @name 状态代码
     */
    code?: number;

    /**
     * @name 结果
     */
    data?: object;

    /**
     * @name 错误信息
     */
    msg?: string;

}

export interface StoreAreaFindDTO {

    /**
     * @name 配送中心属性
     */
    delivery_center?: boolean;

    /**
     * @name 关键字
     */
    keyword?: string;

    /**
     * @name 门店id
     */
    store_ids?: Array<string | number>;

}


export interface StoreAreaFindResDTO {

    /**
     * @name 创建人
     */
    create_by?: string;

    /**
     * @name 创建时间
     */
    create_time?: string;

    /**
     * @name 主键id
     */
    id?: number;

    /**
     * @name 门店区域名称
     */
    name?: string;

    /**
     * @name 门店列表 仅read接口返回
     */
    stores?: ShortStoreResDTO[];

    /**
     * @name 更新人
     */
    update_by?: string;

    /**
     * @name 更新时间
     */
    update_time?: string;

}

export interface ShortStoreResDTO {

    /**
     * @name 配送类型  1直营  2加盟
     */
    delivery_type?: string;

    /**
     * @name 是否配送中心
     */
    enable_delivery_center?: boolean;

    /**
     * @name 门店id
     */
    id?: number;

    /**
     * @name 店铺模式 0-直营店 1-加盟店
     */
    management_type?: string;

    /**
     * @name 门店代码
     */
    store_code?: string;

    /**
     * @name 门店分组ID
     */
    store_group_id?: number;

    /**
     * @name 门店分组名称
     */
    store_group_name?: string;

    /**
     * @name 门店名称
     */
    store_name?: string;

    /**
     * @name 门店编码
     */
    store_number?: number;

    /**
     * @name 上游配送中心id
     */
    upstream_center_id?: number;

    /**
     * @name 老系统id
     */
    wms_id?: number;

}

export interface StoreAreaSaveDTO {

    /**
     * @name 门店区域名称
     */
    name?: string;

    /**
     * @name 关联门店id列表
     */
    store_ids?: Array<string | number>;

}

export interface StoreAreaUpdateDTO extends StoreAreaSaveDTO {

    /**
     * @name 门店区域id
     */
    id: number;
}

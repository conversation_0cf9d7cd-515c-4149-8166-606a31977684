import { XlbTableColumnProps } from '@xlb/components';

export const formList = (enable_organization: boolean) => [
  {
    label: '组织',
    name: 'org_ids',
    type: 'select',
    multiple: true,
    clear: true,
    hidden: !enable_organization,
    options: [],
    // @ts-ignore
    onChange: (e: any, formData: any) => {
      formData.setFieldsValue({
        store_ids: [],
      });
    },
    selectRequestParams: () => {
      return {
        url: '/erp-mdm/hxl.erp.org.find',
        responseTrans(data) {
          const options = data.map((item: any) => {
            const obj = {
              label: item.name,
              value: item.id,
            };
            return obj;
          });
          return options;
        },
      };
    },
  },
  {
    label: '门店',
    name: 'store_ids',
    type: 'inputDialog',
    dependencies: ['org_ids'],
    rules: [{ required: true, message: '门店不能为空' }],
    dialogParams: (params: any) => {
      return {
        type: 'store',
        dataType: 'lists',
        isLeftColumn: true,
        isMultiple: true,
        primaryKey: 'id',
        data: {
          center_flag: true,
          enabled: true,
          org_ids: params?.org_ids || [],
        },
      };
    },
    fieldNames: {
      idKey: 'id',
      nameKey: 'store_name',
    } as any,
  },
  {
    label: '供应商',
    name: 'supplier_ids',
    width: 270,
    type: 'inputDialog',
    dialogParams: {
      type: 'supplier',
      dataType: 'lists',
      isMultiple: true,
    },
  },
  {
    label: '商品档案',
    name: 'item_ids',
    type: 'inputDialog',
    allowClear: true,

    dialogParams: {
      type: 'goods',
      dataType: 'lists',
      isLeftColumn: true,
      isMultiple: true,
    },
  },
  {
    label: '',
    name: 'checkValue',
    type: 'checkbox',
    clear: true,
    colon: false,
    options: [
      {
        label: '主供应商',
        value: 'main',
      },
    ],
  },
];

export const tableColumn: XlbTableColumnProps<any>[] = [
  {
    name: '序号',
    code: '_index',
    width: 60,
    align: 'center',
  },
  {
    name: '组织',
    code: 'org_name',
    width: 120,
    features: { sortable: true },
  },
  {
    name: '门店',
    code: 'store_name',
    width: 120,
    features: { sortable: true },
  },
  {
    name: '供应商代码',
    code: 'supplier_code',
    width: 120,
    features: { sortable: true },
  },
  {
    name: '供应商',
    code: 'supplier_name',
    width: 120,
    features: { sortable: true },
  },
  {
    name: '商品代码',
    code: 'item_code',
    width: 120,
    features: { sortable: true },
  },
  {
    name: '商品名称',
    code: 'item_name',
    width: 120,
    features: { sortable: true },
  },
  {
    name: '一级类目',
    code: 'one_category_name',
    width: 120,
    features: { sortable: false },
  },

  {
    name: '二级类目',
    code: 'two_category_name',
    width: 120,
    features: { sortable: false },
  },
  {
    name: '三级类目',
    code: 'three_category_name',
    width: 120,
    features: { sortable: false },
  },
  {
    name: '近期实际交货周期（天）',
    code: 'latest_purchase_period',
    width: 120,
    features: { sortable: true },
  },
  {
    name: '交货周期（天）',
    code: 'ad_purchase_period',
    width: 140,
    features: { sortable: true },
  },
  {
    name: '订货频次（天）',
    code: 'order_freq',
    width: 140,
    features: { sortable: true },
  },
  {
    name: '建议安全库存天数（天）',
    code: 'recommend_safety_days',
    width: 120,
    features: { sortable: true },
  },
  {
    name: '导入安全库存天数（天）',
    code: 'safe_days',
    width: 120,
    features: { sortable: false },
  },
  {
    name: '最近更新人',
    code: 'oc_update_by',
    width: 100,
    features: { sortable: false },
  },
  {
    name: '最近更新时间',
    code: 'oc_update_time',
    width: 220,
    features: { sortable: false },
  },
];

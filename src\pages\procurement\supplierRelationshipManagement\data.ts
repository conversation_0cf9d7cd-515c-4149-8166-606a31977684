import type { ArtColumn } from 'ali-react-table'
import { columnWidthEnum } from '@/data/common/constant'
import type { XlbTableColumnProps } from '@xlb/components'

export const filterArr = [
  {
    label: '过滤停购商品',
    value: 'stop_buying'
  },

  {
    label: '过滤停售商品',
    value: 'stop_saling'
  },
  {
    label: '过滤未启用供应商',
    value: 'noStartS'
  },
  {
    label: '仅显示主供应商关系',
    value: 'showmain'
  }
]

//经营类型
export const goodsType: any[] = [
  {
    label: '主规格商品',
    value: 'MAINSPEC'
  },
  {
    label: '多规格商品',
    value: 'MULTIPLESPEC'
  },
  {
    label: '标准商品',
    value: 'STANDARD'
  },
  {
    label: '组合商品',
    value: 'COMBINATION'
  },
  {
    label: '成分商品',
    value: 'COMPONENT'
  },
  {
    label: '制单组合',
    value: 'MAKEBILL'
  },
  {
    label: '分级商品',
    value: 'GRADING'
  }
]

export const spghgxArr: XlbTableColumnProps<any>[] = [
  {
    name: '序号',
    code: '_index',
    width: columnWidthEnum.INDEX,
    align: 'center'
  },
  {
    name: '组织',
    code: 'org_name',
    width: 140,
    hidden: true,
    features: { sortable: true }
  },
  {
    name: '门店',
    code: 'store_name',
    width: 160,
    features: { sortable: true, copy: true },
    align: 'left'
  },
  {
    name: '供应商',
    code: 'supplier_name',
    width: 140,
    features: { sortable: true },
    align: 'left'
  },
  {
    name: '供应商类型',
    code: 'supplier_type',
    width: 120,
    features: { sortable: true },
    align: 'left'
  },
  {
    name: '物流模式',
    code: 'logistics_mode',
    width: 120,
    features: { sortable: true },
    align: 'left'
  },
  {
    name: '生产商',
    code: 'producer_supplier_name',
    width: 180,
    features: { sortable: true },
    align: 'left'
  },
  {
    name: '商品代码',
    code: 'item_code',
    width: columnWidthEnum.ITEM_CODE,
    features: { sortable: true },
    align: 'left'
  },
  {
    name: '商品条码',
    code: 'item_bar_code',
    width: columnWidthEnum.ITEM_BAR_CODE,
    features: { sortable: true, copy: true },
    align: 'left'
  },
  {
    name: '商品名称',
    code: 'item_name',
    width: 160,
    features: { sortable: true, copy: true },
    align: 'left'
  },
  {
    name: '采购规格',
    code: 'purchase_spec',
    width: 100,
    features: { sortable: true },
    align: 'left'
  },
  {
    name: '商品类型',
    code: 'item_type',
    width: 100,
    features: { sortable: true },
    align: 'left'
  },
  {
    name: '商品类别',
    code: 'item_category_name',
    width: 120,
    features: { sortable: true },
    align: 'left'
  },
  {
    name: '基本单位',
    code: 'basic_unit',
    width: 90,
    features: { sortable: true },
    align: 'left'
  },
  {
    name: '基本单价',
    code: 'basic_price',
    width: 100,
    features: { sortable: true },
    align: 'right'
  },
  {
    name: '采购单位',
    code: 'purchase_unit',
    width: 90,
    features: { sortable: true },
    align: 'left'
  },

  {
    name: '采购单价',
    code: 'purchase_price',
    width: 100,
    features: { sortable: true },
    align: 'right'
  },
  {
    name: '下单默认单位',
    code: 'default_unit_type',
    width: 120,
    features: { sortable: true },
    align: 'right'
  },
  {
    name: '交货周期（天）',
    code: 'purchase_period',
    width: 130,
    features: { sortable: true },
    align: 'right'
  },
  {
    name: '安全天数（天）',
    code: 'safe_days',
    width: 130,
    features: { sortable: true },
    align: 'right'
  },
  {
    name: '进项税率(%)',
    code: 'input_tax_rate',
    width: 120,
    features: { sortable: true },
    align: 'right'
  },
  {
    name: '主供应商',
    code: 'main',
    width: 90,
    features: { sortable: true },
    align: 'left'
  },
  {
    name: '起送量',
    code: 'minimum_delivery_quantity',
    width: 90,
    features: { sortable: true },
    align: 'left'
  },
  {
    name: '起送金额',
    code: 'minimum_delivery_money',
    width: 120,
    features: { sortable: true },
    align: 'left'
  },
  {
    name: '停售',
    code: 'stop_sale',
    width: 70,
    features: { sortable: true },
    align: 'left'
  },
  {
    name: '停购',
    code: 'stop_purchase',
    width: 70,
    features: { sortable: true },
    align: 'left'
  },
  {
    name: '合作模式',
    code: 'collaboration_mode',
    width: 90,
    features: { sortable: true },
    align: 'left'
  },
  {
    name: '返点比例',
    code: 'rebate_ratio',
    width: 90,
    features: { sortable: true },
    align: 'left'
  },

  {
    name: '预警天数',
    code: 'warning_days',
    width: 90,
    features: { sortable: true },
    align: 'left'
  },

  {
    name: '创建时间',
    code: 'create_time',
    width: columnWidthEnum.TIME,
    features: { sortable: true, format: 'TIME' },
    align: 'left'
  },
  {
    name: '最后更新时间',
    code: 'update_time',
    width: 160,
    features: { sortable: true, format: 'TIME' },
    align: 'left'
  },
  {
    name: '最后更新人',
    code: 'update_by',
    width: 110,
    features: { sortable: true },
    align: 'left'
  },
  {
    name: '最近收货日期',
    code: 'latest_time',
    width: 160,
    features: { sortable: true, format: 'TIME' },
    align: 'left'
  }
]

export const spghgxArr2: ArtColumn[] = [
  {
    name: '序号',
    code: 'index',
    width: columnWidthEnum.INDEX,
    align: 'center'
  },
  {
    name: '生产商',
    code: 'name',
    width: 120,
    align: 'center'
  },
  {
    name: '执行标准',
    code: 'executive_standard',
    width: 120,
    align: 'center'
  }
]

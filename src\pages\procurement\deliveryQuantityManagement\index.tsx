import { ErpFieldKeyMap } from '@/data/common/fieldListConfig';
import { hasAuth } from '@/utils';
import { XlbProPageContainer } from '@xlb/components';
import dayjs from 'dayjs';
import { tableColumn, typeOptions } from './data';

const DeliveryQuantityManagement = () => {
  return (
    <XlbProPageContainer // 查询
      searchFieldProps={{
        initialValues: {
          date: [dayjs()?.format('YYYY-MM-DD'), dayjs()?.format('YYYY-MM-DD')],
        },
        formList: [
          // TODO: 待后端完成后添加
          // {
          //   id: ErpFieldKeyMap?.erpOrgIdsMultiple,
          //   name: 'org_ids',
          //   label: '组织',
          //   onChange: (_, form) => {
          //     form?.setFieldsValue({ center_store_ids: [] });
          //   },
          // },
          {
            id: ErpFieldKeyMap?.erpCenteMultipleStoreId,
            name: 'center_store_ids',
            label: '配送中心',
          },
          {
            id: ErpFieldKeyMap?.erpSupplierIds,
          },
          'erpitemIds',
          {
            id: ErpFieldKeyMap.erpStoreStatus,
            name: 'strong_validation',
            label: '是否强校验',
          },
          {
            id: 'commonSelect',
            label: '起订量类型',
            name: 'types',
            fieldProps: { mode: 'multiple' },
            options: typeOptions,
          },
        ],
      }}
      addFieldProps={{
        url: hasAuth(['起送量配置', '编辑'])
          ? '/erp/hxl.erp.mindeliveryconfig.save'
          : undefined,
        beforePost: (data) => {
          return {
            ...data,
          };
        },
      }}
      deleteFieldProps={{
        name: '删除',
        url: hasAuth(['起送量配置', '删除'])
          ? '/erp/hxl.erp.mindeliveryconfig.delete'
          : '',
        params: (data: any, v: any) => {
          return { id: v[0].id };
        },
      }}
      details={{
        width: 500,
        mode: 'modal',
        hiddenSaveBtn: true,
        isCancel: true,
        primaryKey: 'id',
        title: (obj) => {
          return <div>{obj?.id ? '编辑' : '新增'}</div>;
        },
        queryFieldProps: {
          url: hasAuth(['起送量配置', '查询'])
            ? '/erp/hxl.erp.mindeliveryconfig.read'
            : '',
          params: (row: any) => {
            return {
              id: row.id,
            };
          },
          afterPost(res) {
            return {
              ...res,
              item_ids: res?.details?.length
                ? res.details?.map((item: any) => item.item_id)
                : [],
            };
          },
        },
        updateFieldProps: {
          url: hasAuth(['起送量配置', '编辑'])
            ? '/erp/hxl.erp.mindeliveryconfig.update'
            : undefined,
          beforePost: (data) => {
            return {
              ...data,
              item_ids: data?.item_ids || [],
            };
          },
        },
        formList: [
          {
            componentType: 'form',
            fieldProps: {
              formList: [
                {
                  id: 'erpSupplierId',
                  itemSpan: 22,
                  name: 'supplier_id',
                  label: '供应商',
                  rules: [{ required: true, message: '请选择供应商' }],
                },
                {
                  id: 'erpCenterSingleStoreId',
                  itemSpan: 22,
                  label: '配送中心',
                  name: 'center_store_id',
                  fieldProps: {
                    placeholder: '请选择',
                  },
                  rules: [{ required: true, message: '请选择配送中心' }],
                },
                {
                  id: 'commonSelect',
                  label: '起订量类型',
                  name: 'type',
                  options: typeOptions,
                  itemSpan: 22,
                  rules: [{ required: true, message: '请选择起订量类型' }],
                  onChange: (e, form) => {
                    form?.setFieldsValue({
                      item_ids: [],
                    });
                  },
                },
                {
                  id: 'erpitemIds',
                  itemSpan: 22,
                  name: 'item_ids',
                  label: '商品档案',
                  dependencies: ['type'],
                  disabled: (formValues: any) => {
                    return formValues?.type === 'single.warehouse';
                  },
                },
                {
                  id: ErpFieldKeyMap?.purchasePlanBuyNumber,
                  label: '起订量',
                  name: 'quantity',
                  itemSpan: 22,
                  fieldProps: { placeholder: '请输入' },
                  rules: [{ pattern: /^(0|[1-9]\d*)$/, message: '请输入整数' }],
                },
                {
                  id: ErpFieldKeyMap?.purchasePlanBuyNumber,
                  label: '起送金额',
                  name: 'money',
                  itemSpan: 22,
                  fieldProps: { placeholder: '请输入', min: 1, precision: 0 },
                },
              ],
            },
          },
        ],
      }}
      tableFieldProps={{
        url: hasAuth(['起送量配置', '查询'])
          ? '/erp/hxl.erp.mindeliveryconfig.page'
          : undefined,
        tableColumn: tableColumn,
        selectMode: 'single',
      }}
      // 暂时隐藏
      // uploadFieldProps={{
      //   order: 30,
      //   url: hasAuth(['起送量配置', '导入'])
      //     ? '/erp/hxl.erp.mindeliveryconfig.import'
      //     : '',
      //   templateUrl: '/erp/hxl.erp.mindeliveryconfig.download',
      // }}
      // 导出
      // exportFieldProps={{
      //   url: hasAuth(['设备分析', '导出']) ? '/erp/hxl.erp.alipaymetricsdevice.export' : '',
      //   fileName: '设备分析.xlsx'
      // }}
    />
  );
};

export default DeliveryQuantityManagement;

export const StorePropertiesKeyMap = {
  // 去掉管理中心门店
  erpStoreIdsEnableOrganization: 'erpStoreIdsEnableOrganization',
  erpSelectTypeForStoreProperties: 'erpSelectTypeForStoreProperties'
}

export const storePropertiesConfig: any[] = [
  {
    tag: 'ERP',
    label: '门店',
    id: StorePropertiesKeyMap?.erpStoreIdsEnableOrganization,
    name: 'store_ids',
    dependencies: ['org_ids', 'summary_types'],
    fieldProps: (form) => {
      const data = {
        ...form.getFieldsValue(['org_ids', 'summary_types'])
      }
      if (!form?.getFieldsValue(true).org_ids) {
        delete data?.org_ids
      }
      return {
        dialogParams: {
          type: 'store',
          dataType: 'lists',
          isMultiple: true,
          data: {
            ...data,
            enable_organization: false
          }
        },
        fieldNames: {
          idKey: 'id',
          nameKey: 'store_name'
        }
      }
    },
    formItemProps: {
      label: '门店'
    },
    componentType: 'inputDialog'
  },
  {
    tag: 'ERP',
    label: '商品状态',
    id: StorePropertiesKeyMap.erpSelectTypeForStoreProperties,
    name: 'item_status',
    componentType: 'select'
  }
]

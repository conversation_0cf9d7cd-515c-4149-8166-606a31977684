import { hasAuth } from '@/utils/kit';
import { ProForm } from '@ant-design/pro-form';
import {
  XlbInput,
  XlbSelect,
  XlbShortTable,
  XlbShortTableProps,
  XlbTableColumnProps,
} from '@xlb/components';
import { FormItemProps } from 'antd/lib';
import { FC } from 'react';

export interface CustomerShortTableProps
  extends Omit<XlbShortTableProps, 'dataSource'>,
    FormItemProps {
  value?: Array<any>;
}

const CustomerTable: FC<CustomerShortTableProps> = ({
  value,
  onChange,
  ...rest
}) => {
  const tableEditColumn: XlbTableColumnProps<any>[] = [
    {
      name: '序号',
      code: '_index',
      width: 60,
      align: 'center',
    },
    {
      name: '操作',
      code: '_operator',
      width: 60,
      align: 'center',
    },
    {
      name: '开户银行',
      title: '开户银行',
      code: 'bank',
      width: 200,
      // features: { sortable: true },
      align: 'left',
      render: (text, record, index) => {
        return (
          <div
            onClick={(e) => {
              e.stopPropagation();
            }}
          >
            <ProForm.Item
              noStyle
              shouldUpdate
              name={['company_invoice_accounts', index['index'], 'bank']}
            >
              <XlbInput />
            </ProForm.Item>
          </div>
        );
      },
    },
    {
      name: '开户名',
      title: '开户名',
      code: 'bank_account_name',
      width: 140,
      align: 'left',
      render: (text, record, index) => {
        return (
          <div
            onClick={(e) => {
              e.stopPropagation();
            }}
          >
            <ProForm.Item
              noStyle
              name={[
                'company_invoice_accounts',
                index['index'],
                'bank_account_name',
              ]}
            >
              <XlbInput />
            </ProForm.Item>
          </div>
        );
      },
    },
    {
      name: '银行账号',
      title: '银行账号',
      code: 'bank_account',
      width: 200,
      align: 'left',
      render: (text, record, index) => {
        return (
          <div
            onClick={(e) => {
              e.stopPropagation();
            }}
          >
            <ProForm.Item
              noStyle
              name={[
                'company_invoice_accounts',
                index['index'],
                'bank_account',
              ]}
            >
              <XlbInput />
            </ProForm.Item>
          </div>
        );
      },
    },
    {
      name: '开户行联行号',
      title: '开户行联行号',
      code: 'bank_unionpay',
      width: 200,
      align: 'left',
      render: (text, record, index) => {
        return (
          <div
            onClick={(e) => {
              e.stopPropagation();
            }}
          >
            <ProForm.Item
              noStyle
              name={[
                'company_invoice_accounts',
                index['index'],
                'bank_unionpay',
              ]}
            >
              <XlbInput />
            </ProForm.Item>
          </div>
        );
      },
    },
    {
      name: '是否默认',
      code: 'default_flag',
      width: 140,
      align: 'left',
      render: (text, record, index) => {
        return (
          <div
            onClick={(e) => {
              e.stopPropagation();
            }}
          >
            <ProForm.Item
              noStyle
              name={[
                'company_invoice_accounts',
                index['index'],
                'default_flag',
              ]}
            >
              <XlbSelect
                size="small"
                key={[text, index].join('-')}
                value={text}
                allowClear={false}
                style={{ width: '100%' }}
                disabled={!hasAuth(['供应商管理', '编辑'])}
                onChange={(e) => {
                  const _index = index['index'];
                  if (e) {
                    value &&
                      value.forEach((item: any, idx: number) => {
                        if (idx == _index) {
                          item.default_flag = e;
                        } else {
                          item.default_flag = false;
                        }
                      });
                    onChange?.(value);
                  }
                }}
                options={[
                  { label: '是', value: true },
                  { label: '否', value: false },
                ]}
              />
            </ProForm.Item>
          </div>
        );
      },
    },
  ];
  return (
    <XlbShortTable
      {...rest}
      primaryKey="id"
      popoverPrimaryKey="id"
      columns={tableEditColumn}
      dataSource={value ?? []}
    />
  );
};

export default CustomerTable;

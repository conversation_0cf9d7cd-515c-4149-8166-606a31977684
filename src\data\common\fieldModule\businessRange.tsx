export const BusinessRangeMap = {
  // 经营范围类别
  erpRangeCategoryIdsBusiness: 'erpRangeCategoryIdsBusiness'
}

export const businessRangeConfig: any[] = [
  {
    tag: 'ERP',
    label: '类别',
    id: BusinessRangeMap.erpRangeCategoryIdsBusiness,
    name: 'category_ids',
    fieldProps: {
      treeModalConfig: {
        // @ts-ignore
        title: '经营范围类别',
        url: '/erp-mdm/hxl.erp.businessscopecategory.find',
        multiple: true,
        checkable: true,
        dataType: 'lists',
        params: {
          business_type: 1
        }
      }
    },
    componentType: 'inputDialog'
  }
]
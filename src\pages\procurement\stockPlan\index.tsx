import { ErpFieldKeyMap } from '@/data/common/fieldListConfig';
import { useBaseParams } from '@/hooks/useBaseParams';
import { hasAuth } from '@/utils/kit';
import { wujieBus } from '@/wujie/utils';
import {
  ContextState,
  XlbBasicForm,
  XlbButton,
  XlbIcon,
  XlbInputDialog,
  XlbInputNumber,
  XlbProPageContainer,
  XlbProPageModal,
  XlbProPageModalRef,
  XlbSelect,
  XlbTableColumnProps,
  XlbTipsModal,
} from '@xlb/components';
import { XlbFetch } from '@xlb/utils';
import { message } from 'antd';
import { useEffect, useRef, useState, type FC } from 'react';
import { status_type } from './data';
import Item from './item';
import Api from './server';

const StockPlanProForm: FC<{ title: string }> = () => {
  let ref: ContextState;
  const [form] = XlbBasicForm.useForm();
  const { enable_organization } = useBaseParams((state) => state);
  const [orgList, setOrgList] = useState<any>([]);
  const [loading, setLoading] = useState(false);
  const pageModalRef = useRef<XlbProPageModalRef>(null);
  const pageConatainerRef = useRef<any>(null);
  const [record, setRecord] = useState<any>({});
  const tableColumn: XlbTableColumnProps<any>[] = [
    {
      name: '序号',
      code: '_index',
      width: 80,
      lock: true,
      align: 'center',
    },
    {
      name: '所属组织',
      code: 'org_name',
      width: 140,
      features: { sortable: true },
    },
    {
      name: '所属门店',
      code: 'store_name',
      width: 140,
      features: { sortable: true },
    },
    {
      name: '商品名称',
      code: 'item_name',
      width: 180,
      features: { sortable: true },
      render: (value, record) => (
        <span
          className="link cursors"
          onClick={() => {
            setRecord(record);
            pageModalRef.current?.setOpen(true);
          }}
        >
          {value}
        </span>
      ),
    },
    {
      name: '商品代码',
      code: 'item_code',
      width: 120,
      features: { sortable: true },
    },
    {
      name: '商品条码',
      code: 'item_barcode',
      width: 120,
      features: { sortable: true },
    },
    {
      name: '采购规格',
      code: 'item_spec',
      width: 120,
      features: { sortable: true },
    },
    {
      name: '单位',
      code: 'purchase_unit',
      width: 80,
      features: { sortable: true },
    },
    {
      name: '状态',
      code: 'enable',
      width: 80,
      features: { sortable: true },
      render: (value) => (value ? '启用' : '禁用'),
    },
    {
      name: '商品品类',
      code: 'item_category_name',
      width: 100,
      features: { sortable: true },
    },
    {
      name: '品类数量',
      code: 'item_category_quantity',
      width: 120,
      features: { sortable: true },
      align: 'right',
    },
    {
      name: '品类金额',
      code: 'item_category_money',
      width: 120,
      features: { sortable: true },
      align: 'right',
    },
    {
      name: '预测数量',
      code: 'prediction_quantity',
      width: 100,
      align: 'right',
      features: { sortable: true },
    },
    {
      name: '预测金额(元)',
      code: 'prediction_money',
      align: 'right',
      features: { sortable: true },
      width: 120,
    },
    {
      name: '实际备货数量',
      code: 'request_quantity',
      align: 'right',
      features: { sortable: true },
      width: 120,
    },
    {
      name: '实际备货金额(元)',
      code: 'request_money',
      align: 'right',
      features: { sortable: true },
      width: 140,
    },
    {
      name: '实际下单数量',
      code: 'purchase_quantity',
      align: 'right',
      features: { sortable: true },
      width: 120,
    },
    {
      name: '实际下单金额',
      code: 'purchase_money',
      align: 'right',
      features: { sortable: true },
      width: 140,
    },
    {
      name: '下单率',
      code: 'purchase_rate_str',
      features: { sortable: true },
      width: 80,
    },
    {
      name: '实际到货数量',
      code: 'receive_quantity',
      align: 'right',
      features: { sortable: true },
      width: 120,
    },
    {
      name: '实际到货金额',
      code: 'receive_money',
      align: 'right',
      features: { sortable: true },
      width: 140,
    },
    {
      name: '到货率',
      code: 'receive_rate_str',
      features: { sortable: true },
      width: 80,
    },
    {
      name: '创建人',
      code: 'create_by',
      features: { sortable: true },
      width: 100,
    },
    {
      name: '创建时间',
      code: 'create_time',
      features: { sortable: true, format: 'TIME' },
      width: 160,
    },
  ];
  const getOrgList = async () => {
    const res = await XlbFetch.post(
      process.env.BASE_URL + '/erp-mdm/hxl.erp.org.find',
      {},
    );
    if (res.code == 0) {
      const org_list = res.data.map((i: any) => ({
        value: i.id,
        label: i.name,
        level: i.level,
      }));
      setOrgList(org_list);
    }
  };
  useEffect(() => {
    getOrgList();
  }, [enable_organization]);
  const onValuesChange = (changedValues: any, allValues: any) => {
    if (
      ['prediction_quantity', 'prediction_money'].some((key) =>
        Object.keys(changedValues).includes(key),
      )
    ) {
      const itemId = form.getFieldValue('item_id');
      const money = form.getFieldValue('purchase_price');
      if (!itemId) {
        message.error('请选择商品');
        form.setFieldsValue({
          prediction_quantity: null,
          prediction_money: null,
        });
        return;
      }
      if (money > 0) {
        const { prediction_quantity, prediction_money } = allValues;
        if (changedValues.prediction_quantity !== undefined) {
          form.setFieldsValue({
            prediction_money: (Number(prediction_quantity) * money).toFixed(3),
          });
        }
        if (changedValues.prediction_money !== undefined) {
          form.setFieldsValue({
            prediction_quantity: Math.ceil(Number(prediction_money) / money),
          });
        }
      }
    }
  };
  const exportItem = async (setIsLoading: any, requestForm: any, e) => {
    setIsLoading(true);
    let res = null;
    res = await Api.exportDetail(requestForm);

    if (res.code == 0) {
      wujieBus?.$emit('xlb_erp-event', { code: 'downloadEnd', target: e });
      message.success('导出受理成功，请前往下载中心查看');
    }
    setIsLoading(false);
  };
  const openDialog = () => {
    XlbTipsModal({
      tips: (
        <div>
          <XlbBasicForm
            onValuesChange={onValuesChange}
            layout="inline"
            form={form}
            colon={false}
            preserve={false}
          >
            {enable_organization && (
              <XlbBasicForm.Item
                name="org_id"
                rules={[{ required: true, message: '请选择组织' }]}
                label="所属组织"
              >
                <XlbSelect
                  style={{ width: 260 }}
                  options={orgList.filter((i) => i.level == 2)}
                  showSearch
                  // disabled={!hasAuth(['', '编辑'])}
                  filterOption={(input, option) => {
                    return (
                      (
                        `${option!.label ? option!.label.toString() : ''}` as unknown as string
                      )
                        .toLowerCase()
                        .includes(input.toLowerCase()) ||
                      (
                        `${option!.value ? option!.value.toString() : ''}` as unknown as string
                      )
                        .toLowerCase()
                        .includes(input.toLowerCase())
                    );
                  }}
                  onChange={(value) => {
                    form.setFieldsValue({
                      store_name: null,
                      store_id: null,
                    });
                  }}
                />
              </XlbBasicForm.Item>
            )}
            <XlbBasicForm.Item noStyle dependencies={['org_id']}>
              {({ getFieldValue }) => (
                <XlbBasicForm.Item
                  name="store_id"
                  label="所属门店"
                  rules={[{ required: true, message: '请选择门店' }]}
                >
                  <XlbInputDialog
                    dialogParams={{
                      type: 'store',
                      dataType: 'lists',
                      isMultiple: false,
                      showDialogByDisabled: true,
                      initCustomValue: '所有门店',
                      data: !getFieldValue('org_id')
                        ? { status: true, center_flag: true }
                        : {
                            org_ids: [getFieldValue('org_id')],
                            status: true,
                            center_flag: true,
                          },
                    }}
                    fieldNames={{
                      idKey: 'id',
                      nameKey: 'store_name',
                    }}
                    width={260}
                  />
                </XlbBasicForm.Item>
              )}
            </XlbBasicForm.Item>
            <XlbBasicForm.Item
              name="item_id"
              label="商品"
              rules={[{ required: true, message: '请选择商品' }]}
            >
              <XlbInputDialog
                dialogParams={{
                  type: 'goods',
                  dataType: 'lists',
                  isMultiple: false,
                }}
                onChange={(e, options, form) => {
                  form.setFieldsValue({
                    purchase_price: options?.[0]?.purchase_price,
                  });
                }}
                width={260}
              />
            </XlbBasicForm.Item>
            <XlbBasicForm.Item
              name="prediction_quantity"
              label="预测数量"
              rules={[{ required: true, message: '请输入预测数量' }]}
            >
              <XlbInputNumber
                min={0}
                precision={0}
                style={{ width: 260 }}
              ></XlbInputNumber>
            </XlbBasicForm.Item>
            <XlbBasicForm.Item
              name="prediction_money"
              label="预测金额"
              rules={[{ required: true, message: '请输入预测金额' }]}
            >
              <XlbInputNumber
                min={0}
                precision={0}
                style={{ width: 260 }}
              ></XlbInputNumber>
            </XlbBasicForm.Item>
            <XlbBasicForm.Item
              name="enable"
              label="状态"
              rules={[{ required: true, message: '请选择状态' }]}
            >
              <XlbSelect
                style={{ width: 260 }}
                options={[
                  { value: true, label: '启用' },
                  { value: false, label: '禁用' },
                ]}
              ></XlbSelect>
            </XlbBasicForm.Item>
          </XlbBasicForm>
        </div>
      ),
      onOkBeforeFunction: async () => {
        try {
          await form.validateFields();
        } catch (err: any) {
          return false;
        }
        setLoading(true);
        const params = form.getFieldsValue(true);
        const res = await Api.save({
          ...params,
          item_id: params?.item_id[0],
          store_id: params?.store_id[0],
        });
        if (res.code == 0) {
          message.success('保存成功');
          ref?.fetchData();
          setLoading(true);
          return true;
        }
        return false;
      },
      isCancel: true,
      confirmLoading: loading,
      title: '新增备货计划',
    });
  };
  return (
    <>
      <XlbProPageModal
        ref={pageModalRef}
        Content={({ onClose }) => {
          return (
            <div>
              <Item
                onBack={() => {
                  pageConatainerRef?.current?.pageContainerRef?.current?.fetchData?.();
                  pageModalRef.current?.setOpen(false);
                }}
                record={record}
              ></Item>
            </div>
          );
        }}
      >
        <div className="xlb-table-clickBtn"></div>
      </XlbProPageModal>
      <XlbProPageContainer // 查询
        ref={pageConatainerRef}
        searchFieldProps={{
          formList: [
            {
              id: ErpFieldKeyMap.erpOrgIdsLevel2,
              label: '所属组织',
              hidden: !enable_organization,
              onChange: (e: string[], form: any, _: any) => {
                if (e.length > 0) {
                  form.setFieldsValue({
                    store_ids: null,
                  });
                }
              },
            },
            {
              id: ErpFieldKeyMap.erpCenterStoreIdsMultiple,
              label: '所属门店',
            },
            {
              id: 'erpitemIds',
              label: '商品',
            },
            {
              id: 'status',
              label: '状态',
              name: 'enable',
              fieldProps: {
                options: status_type,
              },
            },
          ],
        }}
        tableFieldProps={{
          url: '/erp/hxl.erp.stockplan.page',
          tableColumn: tableColumn,
          selectMode: 'single',
          keepDataSource: false,
          showColumnsSetting: false,
          immediatePost: true,
        }}
        extra={(context) => {
          const { loading, requestForm, setLoading, fetchData, dataSource } =
            context;
          ref = context;
          return (
            <XlbButton.Group>
              {hasAuth(['备货计划', '编辑']) && (
                <XlbButton
                  type="primary"
                  onClick={openDialog}
                  disabled={loading}
                  icon={<XlbIcon size={16} name="jia" />}
                >
                  新增
                </XlbButton>
              )}
              {hasAuth(['备货计划', '导出']) && (
                <XlbButton
                  disabled={loading || !dataSource?.length}
                  type="primary"
                  onClick={(e) => exportItem(setLoading, requestForm, e)}
                  icon={<XlbIcon size={16} name="daochu" />}
                >
                  导出
                </XlbButton>
              )}
            </XlbButton.Group>
          );
        }}
      />
    </>
  );
};

export default StockPlanProForm;

import {XlbFetch as  ErpRequest } from '@xlb/utils'

// 查询
export const allQuery = async (data: any) => {
  return await ErpRequest.post('/erp/hxl.erp.storeitemreplenish.page',data)
}
//更新
export const update = async (data: any) => {
  return await ErpRequest.post('/erp/hxl.erp.storeitemreplenish.update',data)
}
// 复制
export const copy = async (data: any) => {
  return await ErpRequest.post('/erp/hxl.erp.storeitemreplenish.copy',data)
}
//
export const batchUpdate = async (data: any) => {
  return await ErpRequest.post('/erp/hxl.erp.storeitemreplenish.batchupdate',data)
}
export const getExport = async (data: any) => {
  return await ErpRequest.post('/erp/hxl.erp.storeitemreplenish.export',data)
}
//
export const batchUpdateDetail = async (data: any) => {
  return await ErpRequest.post('/erp/hxl.erp.storeitemreplenish.batchupdatedetail',data)
}

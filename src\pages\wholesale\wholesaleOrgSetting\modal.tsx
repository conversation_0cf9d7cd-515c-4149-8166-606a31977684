import NiceModal from '@ebay/nice-modal-react'
import type { BaseModalProps } from '@xlb/components'
import {
  XlbBasicForm,
  XlbButton,
  XlbImportModal,
  XlbInput,
  XlbInputDialog,
  XlbModal,
  XlbSelect,
  XlbTable,
  XlbTipsModal
} from '@xlb/components'
import type { FC } from 'react'
import { useEffect, useRef, useState } from 'react'
import { wholesalePriceValues } from './data'
// import styles from './index.less'
import Api from './server'

interface Props extends Pick<BaseModalProps, 'title'> {
  type: 'add' | 'edit'
  initialValues?: any
}

export const Modal: FC<Props> = ({ title = '弹窗', type, initialValues }) => {
  const [form] = XlbBasicForm.useForm()
  const modal = NiceModal.useModal()
  const [rowData, setRowData] = useState<any[]>([{}])
  const [shareStore, setShareStore] = useState<any[]>([])
  const [selectedRowKeys, setSelectedRowKeys] = useState<any[]>([])
  const [key, setKey] = useState<string>('1')
  const tableRef = useRef<any>(null)

  // 获取批发共享中心
  const queryWholeWareCenterStores = async (client_id: string) => {
    const res = await Api.getWholeWareCenterStores({ client_id })
    if (res.code === 0) {
      setShareStore(
        res.data?.map((i: any) => ({
          value: i.id,
          label: i.store_name
        }))
      )
    }
  }
  const queryCooperations = async (value: string) => {
    let cooperationsT = []
    const resC = await Api.getCoperateStore({ share_center_store_id: value })
    if (resC.code === 0) {
      cooperationsT = resC.data.map((i: { id: string; store_name: string }) => {
        return {
          value: i.id,
          label: i.store_name
        }
      })
    }
    // console.log(cooperationsT, 'cooperationsT')
    return cooperationsT
  }
  // const handleClick = async () => {
  //   const res = await XlbImportModal({
  //     importUrl: `${process.env.ERP_URL}/erp-mdm/hxl.erp.commonstorename.import`,
  //     templateUrl: `${process.env.ERP_URL}/erp-mdm/hxl.erp.storenametemplate.download`
  //   })
  //   if (res.code === 0) {
  //     form.setFieldValue('store_ids', res.data?.store_ids)
  //     setKey(res.data?.store_ids?.join('_') + Math.random().toString())
  //   }
  // }

  const getInfo = async (client_id: string) => {
    const res = await Api.getDetails({ client_id })
    if (res.code === 0) {
      queryWholeWareCenterStores(client_id)
      const reArr = await Promise.all(
        res?.data?.map((i: any) => {
          return queryCooperations(i?.share_center_store_id)
        })
      )
      const newRowData: any = res?.data?.map((i: any, index: any) => {
        return {
          ...i,
          cooperations: reArr[index]
        }
      })
      setRowData(newRowData)
    }
  }

  const addDetails = () => {
    setRowData([...rowData, {}])
  }

  const delDetails = () => {
    if (!selectedRowKeys?.length || rowData?.length < 2) {
      return
    }
    const li = rowData.filter((i, y) => !selectedRowKeys.includes(y + ''))
    setRowData(li)
    setSelectedRowKeys([])
  }

  useEffect(() => {
    if (initialValues?.client_id) {
      if (type === 'edit') {
        form.setFieldsValue({
          client_ids: [initialValues?.client_id]
        })
      }
      getInfo(initialValues?.client_id)
    }
  }, [])

  return (
    <XlbModal
      width={1050}
      open={modal.visible}
      title={title}
      isCancel={true}
      onOk={async () => {
        form.submit()
      }}
      onCancel={() => {
        modal.resolve(false)
        modal.hide()
      }}
    >
      <div style={{ display: 'flex', flexDirection: 'column', paddingBottom: 5 }}>
        <XlbBasicForm
          form={form}
          style={{ width: '100%', position: 'relative', marginTop: 16 }}
          labelCol={{ span: 8 }}
          onFinish={(formValues) => {
            const params = { ...formValues }
            params.details = rowData.filter((i) => Object.keys(i)?.length > 0)
            const headerMap = {
              share_center_store_name: '批发共享中心',
              cooperate_store_name: '合作门店',
              wholesale_price_val: '批发价取值'
            }
            const incompleteFieldsList = rowData
              .map((item: any, index: number) => {
                const missingFields = []
                if (!item.share_center_store_name)
                  missingFields.push(headerMap.share_center_store_name)
                if (!item.cooperate_store_name) missingFields.push(headerMap.cooperate_store_name)
                if (!item.wholesale_price_val) missingFields.push(headerMap.wholesale_price_val)
                return { ...item, rowIndex: index + 1, missingFields }
              })
              .filter((item: any) => item.missingFields.length > 0)
            if (incompleteFieldsList.length) {
              XlbTipsModal({
                tips: '以下数据未填写完整，请检查！',
                tipsList: incompleteFieldsList.map(
                  (v: any) => `【第${v.rowIndex}行】缺少：${v.missingFields.join('、')}`
                )
              })
              return
            }

            // //重复批发共享中心校验
            const nameLinesMap: any = {}

            rowData.forEach((item, index) => {
              const name = item.share_center_store_name
              if (!nameLinesMap[name]) {
                nameLinesMap[name] = []
              }
              nameLinesMap[name].push(index + 1) // 从1开始计数行号
            })

            // 检查重复的行号
            const duplicates = []
            for (const name in nameLinesMap) {
              const lines = nameLinesMap[name]
              if (lines.length > 1) {
                duplicates.push({ name, lines })
              }
            }
            if (duplicates.length) {
              XlbTipsModal({
                tips: '以下数据重复，请检查！',
                tipsList: duplicates.map(
                  (v: { name: any; lines: any }) =>
                    `批发共享中心${v.name}" 在【第${v.lines.join(', ')}行】重复`
                )
              })
              return
            }
            modal.resolve(params)
            modal.hide()
          }}
          initialValues={initialValues}
        >
          <XlbBasicForm.Item
            name="client_ids"
            label="批发客户"
            rules={[{ required: true, message: '门店不能为空' }]}
          >
            <XlbInputDialog
              dialogParams={{
                type: 'wholesaler',
                dataType: 'lists',
                isMultiple: false
              }}
              key={key}
              fieldNames={{
                idKey: 'id',
                nameKey: 'name'
              }}
              style={{ width: 150 }}
              onChange={(val: any) => {
                queryWholeWareCenterStores(val[0])
              }}
              disabled={!!initialValues?.client_id}
            />
          </XlbBasicForm.Item>
        </XlbBasicForm>
        <div>设置批发中心</div>
        <XlbButton.Group>
          <XlbButton
            style={{ marginTop: 10, marginBottom: 10 }}
            type="primary"
            onClick={addDetails}
          >
            新增
          </XlbButton>
          <XlbButton type="primary" onClick={delDetails} disabled={!selectedRowKeys?.length}>
            删除
          </XlbButton>
        </XlbButton.Group>
        <XlbTable
          ref={tableRef}
          style={{ flex: 1, minHeight: 285, maxHeight: 285, overflowY: 'auto' }}
          columns={[
            { name: '序号', code: '_index', width: 50, align: 'center' },
            {
              name: '批发共享中心',
              code: 'share_center_store_id',
              width: 200,
              render: (text, record, index) => {
                return (
                  <div onClick={(e) => e.stopPropagation()}>
                    <XlbSelect
                      style={{ width: 180 }}
                      options={shareStore ?? []}
                      showSearch
                      allowClear={false}
                      value={text}
                      onChange={async (value, option: any) => {
                        if (type == 'add' && !record?.wholesale_price_val) {
                          record.wholesale_price_val = 'SHARING_CENTER_WHOLESALE_PRICE'
                        }
                        rowData[index.index].share_center_store_id = value
                        rowData[index.index].share_center_store_name = option?.label
                        rowData[index.index].cooperate_store_id = ''
                        rowData[index.index].cooperate_store_name = ''
                        // const resC = await Api.getCoperateStore({ share_center_store_id: value })
                        rowData[index.index].cooperations = await queryCooperations(value)
                        setRowData([...rowData])
                      }}
                    />
                  </div>
                )
              }
            },
            // {
            //   name: '是否默认',
            //   code: 'default_flag',
            //   width: 112,
            //   render: (text, record, index) => {
            //     return (
            //       <div onClick={(e) => e.stopPropagation()}>
            //         <XlbSelect
            //           style={{ width: 95 }}
            //           options={[
            //             { label: '是', value: true },
            //             { label: '否', value: false }
            //           ]}
            //           allowClear={false}
            //           value={text}
            //           onChange={(val) => changeFlag(val, index.index)}
            //         />
            //       </div>
            //     )
            //   }
            // },
            {
              name: '合作门店',
              code: 'cooperate_store_id',
              width: 200,
              render: (text, record, index) => {
                return (
                  <div onClick={(e) => e.stopPropagation()}>
                    <XlbSelect
                      style={{ width: 180 }}
                      options={rowData[index.index].cooperations ?? []}
                      showSearch
                      // filterOption={(input, option) => {
                      //   return (
                      //     (`${option!.label ? option!.label.toString() : ''}` as unknown as string)
                      //       .toLowerCase()
                      //       .includes(input.toLowerCase()) ||
                      //     (`${option!.value ? option!.value.toString() : ''}` as unknown as string)
                      //       .toLowerCase()
                      //       .includes(input.toLowerCase())
                      //   )
                      // }}
                      // disabled={type === 'view'}
                      allowClear={true}
                      // onClear={() => {
                      //   handleClear('SHARE_DELIVERY_PRICE', index)
                      // }}
                      value={text}
                      onChange={(value, option: any) => {
                        rowData[index.index].cooperate_store_id = value
                        rowData[index.index].cooperate_store_name = option?.label
                        setRowData([...rowData])
                      }}
                    />
                  </div>
                )
              }
            },
            {
              name: '批发价取值',
              code: 'wholesale_price_val',
              width: 200,
              render: (text, record, index) => {
                return (
                  <div onClick={(e) => e.stopPropagation()}>
                    <XlbSelect
                      style={{ width: 180 }}
                      options={wholesalePriceValues}
                      showSearch
                      // disabled={type === 'view'}
                      allowClear={false}
                      value={text}
                      onChange={(value, option: any) => {
                        rowData[index.index].wholesale_price_val = value
                        setRowData([...rowData])
                      }}
                    />
                  </div>
                )
              }
            }
          ]}
          dataSource={rowData}
          showSizeChanger={false}
          total={rowData?.length ?? 0}
          selectMode="multiple"
          // disabled={type === 'view'}
          selectedRowKeys={selectedRowKeys}
          onSelectRow={setSelectedRowKeys}
        />
      </div>
    </XlbModal>
  )
}

.providerWrap {
  height: calc(100vh - 78px);
  padding: 12px;
  background-color: #f2f3f5;
  :global {
    .xlb-pageContainer-toolBtn {
      margin-top: 0 !important;
    }
    .xlbPageContainer .xlb-table-pagination{
      margin-right: 16px !important;
    }
  }
  .innerProvider {
    height: 100%;
    overflow: hidden;
    background-color: #fff;
    border-radius: 4px;
  }
}
.button_box {
  border-bottom: 1px solid @color_line2;
}

.contractTab {
  position: relative;
  :global .ant-tabs-top > .ant-tabs-nav {
    margin: 0 0 4px 0px;
  }
}

.tabs {
  :global .xlb-ant-tabs-nav {
    margin: 0px 0 0px 16px;
  }
}
.forceDelivery {
  background-color: #F2F3F5 !important;
}

.sku_box {
  display: inline-block;
  color: @color_danger;
  line-height: 36px;
  margin-left: 75px;
  span {
    font-size: 16px;
    margin-left: 20px;
  }
}

.storeName {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.demolition {
  display: inline-block;
  width: 14px;
  height: 14px;
  font-size: 12px;
  background-color: #f53f3f;
  text-align: center;
  color: aliceblue;
  border-radius: 2px;
  line-height: 14px;
}

.modalDivider {
  border-top: 1px solid #E5E6EA;
}

.modalItem {
  margin: 10px 0;
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  justify-content: space-evenly;
  gap: 10px;
  div {
    width: 46%;
    color: #86909C;
    span {
      color: #1D2129;
    }
  }
}

.modalTable {
  font-weight: 500 !important;
  :global thead th:nth-child(1) {
    border-right: 0px;
  }

  :global thead tr th {
    background-color: #F7F8FA !important;
    padding: 8px !important;
    font-weight: normal !important;
    color: #4E5969 !important;
  }

  :global thead th:nth-child(1)>.resize-handle{
    display: none;
  }

  :global tbody tr td {
    padding: 4px !important;
  }

  :global tbody tr td:nth-child(1){
    background-color: #fafafa !important;
    border-right: 1px solid #e5e6ea;
    font-weight: normal !important;
    color: #4E5969 !important;
  }

  :global tbody tr:nth-child(1) td:nth-child(2),
  :global tbody tr:nth-child(3) td:nth-child(2) {
    background-color: #fafafa;
    border-right: 1px solid #e5e6ea;
    font-weight: normal !important;
    color: #4E5969 !important;
  }

  :global tbody tr:nth-child(2), :global tbody tr:nth-child(4) {
    background-color: #fff !important;
  }
}
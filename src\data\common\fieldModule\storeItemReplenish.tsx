export const StoreItemReplenishKeyMap = {
  /**查询最近 天销量 */
  erpIssaleDays: 'erpIssaleDays',
  // 补货门店
  erpStoreIdsMultipletrue: 'erpStoreIdsMultipletrue',
  // 显示单位
  erpUnitType: 'erpUnitType'
}

export const storeItemReplenishConfig: any[] = [
  {
    tag: 'ERP',
    id: 'erpIssaleDays',
    componentType: 'group',
    fieldProps: {
      formList: [
        {
          componentType: 'checkbox',
          name: 'delivery_out_order',
          id: 'erpIssaleDaysGroup1',
          tag: 'ERP',
          group: false,
          colon: false,
          fieldProps: {
            options: [
              {
                label: '调出单',
                value: 'delivery_out_order'
              },
              {
                label: '批发销售单',
                value: 'wholesale_order'
              },
              {
                label: '前台销售',
                value: 'pos_order'
              }
            ]
          }
        },
        {
          componentType: 'inputNumber',
          id: 'erpIssaleDaysGroup2',
          tag: 'ERP',
          textAfter: '天销量',
          textBefore: '最近',
          name: 'sale_day',
          formItemProps: {
            name: 'sale_day',
            rules: [
              { pattern: /^(0|[1-9]\d*)$/, max: 999, message: '最近【】天销量支持录入>=0的整数' },
              { required: true, message: '请输入最近【】天销量' }
            ]
          },
          async request() {
            return {}
          },
          fieldProps: {
            min: 0,
            max: 999,
            step: 1,
            width: 70,
            controls: false
          }
        }
      ]
    }
  },
  {
    tag: 'ERP',
    id: StoreItemReplenishKeyMap?.erpStoreIdsMultipletrue,
    name: 'store_ids',
    dependencies: ['org_ids'],
    fieldProps: (form) => {
      const data = {
        ...form.getFieldsValue(['org_ids'])
      }
      if (!form?.getFieldsValue(true).org_ids) {
        delete data?.org_ids
      }
      return {
        dialogParams: {
          type: 'store',
          dataType: 'lists',
          isMultiple: true,
          data: {
            ...data,
            status: true
          }
        },
        fieldNames: {
          idKey: 'id',
          nameKey: 'store_name'
        }
      }
    },
    formItemProps: {
      label: '门店'
    },
    componentType: 'inputDialog'
  },
  {
    tag: 'ERP',
    label: '显示单位',
    id: StoreItemReplenishKeyMap.erpUnitType,
    name: 'unit_type',
    componentType: 'select',
    fieldProps: {
      options: [
        {
          label: '采购单位',
          value: 'PURCHASE'
        },
        {
          label: '库存单位',
          value: 'STOCK'
        },
        {
          label: '配送单位',
          value: 'DELIVERY'
        },
        {
          label: '批发单位',
          value: 'WHOLESALE'
        },
        {
          label: '基本单位',
          value: 'BASIC'
        }
      ]
    }
  }
]

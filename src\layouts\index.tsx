/*
 * @Author: PengKang <EMAIL>
 * @Date: 2024-11-04 17:03:50
 * @LastEditors: PengKang <EMAIL>
 * @LastEditTime: 2024-12-03 17:10:52
 * @FilePath: \xlb_wujie_template\src\layouts\index.tsx
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { appName, isWUJIE } from '@/wujie/utils';
import { KeepAlive, Outlet, useLocation } from '@umijs/max';
import { XlbErrorBoundary } from '@xlb/components';
import { useSubWujie } from '@xlb/max';
import { ConfigProvider } from 'antd';
import classNames from 'classnames';
import { useState } from 'react';
import XlbMenus from './component/xlbMenus';
import style from './index.less';

const HomePage: React.FC = () => {
  const { pathname } = useLocation();
  const [collapsed, setCollapsed] = useState(false);
  const [menuType] = useState<{ Menu: string; subMenu: string }>({
    Menu: appName,
    subMenu: '',
  });

  useSubWujie({
    appName,
  });
  const wujieClassName = isWUJIE() ? style['erp-is-wujie'] : '';

  return (
    // <ConfigProvider
    //   theme={{
    //     token: {
    //       screenXLMin: 1280, // for grid (row/col)
    //       screenXL: 1280, // default is 1600, for List
    //     },
    //   }}
    // >
      <div className={classNames(style['xlb-sub-container'], wujieClassName)}>
        {!isWUJIE() && (
          <XlbMenus
            menuType={menuType}
            setCollapsed={setCollapsed}
            collapsed={collapsed}
          />
        )}

        <div
          className={style.xlb_main}
          style={{
            width: collapsed ? 'calc(100vw - 60px)' : 'calc(100vw - 120px)',
          }}
        >
          {/* <SelectLang />
        <FormattedMessage id="welcome" /> */}

          <XlbErrorBoundary>
            <KeepAlive
              when={true}
              name={pathname}
              id={pathname}
              autoFreeze={false}
              cacheKey={pathname}
            >
              <Outlet />
            </KeepAlive>
          </XlbErrorBoundary>
        </div>
      </div>
    // </ConfigProvider>
  );
};

export default HomePage;

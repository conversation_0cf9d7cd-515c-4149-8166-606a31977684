import { SearchFormType, XlbTableColumnProps } from '@xlb/components';
import { useNavigation } from '@xlb/max';

export const formList: SearchFormType[] = [
  {
    label: '门店',
    name: 'store_ids',
    type: 'inputDialog',
    allowClear: true,
    dialogParams: {
      type: 'store',
      dataType: 'lists',
      isLeftColumn: true,
      isMultiple: true,
      data: {
        status: true,
      },
    },

    fieldNames: {
      idKey: 'id',
      nameKey: 'store_name',
    } as any,
  },
  {
    label: '是否强配',
    name: 'is_force',
    type: 'select',
    options: [
      {
        label: '是',
        value: true,
      },
      {
        label: '否',
        value: false,
      },
    ],
  },
  {
    label: '是否统配',
    name: 'has_force',
    type: 'select',
    options: [
      {
        label: '是',
        value: true,
      },
      {
        label: '否',
        value: false,
      },
    ],
  },
];

export const tableColumn: XlbTableColumnProps<any>[] = [
  {
    name: '序号',
    code: '_index',
    width: 100,
    align: 'center',
  },
  {
    name: '单据号',
    code: 'fid',
    width: 180,
    lock: true,
    hidden: false,
    render: (text, record: any) =>
      text ? (
        <div
          className="xlb-table-clickBtn"
          onClick={(e) => {
            e.stopPropagation();
            const { navigate } = useNavigation();
            navigate(
              '/xlb_erp/storeSupplyOrder/item',
              {
                ...record,
              },
              'xlb_erp',
              true,
            );
          }}
        >
          {text}
        </div>
      ) : (
        '-'
      ),
  },
  {
    name: '发货门店',
    code: 'out_store_name',
    width: 140,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '发货仓库',
    code: 'storehouse_name',
    width: 180,
    align: 'left',
  },
  {
    name: '是否强配',
    code: 'is_force',
    width: 180,
    align: 'left',
  },
  {
    name: '是否统配',
    code: 'has_force',
    width: 180,
    align: 'left',
  },
  {
    name: '强配门店',
    code: 'store_name',
    width: 140,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '商品代码',
    code: 'item_code',
    width: 180,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '商品名称',
    code: 'item_name',
    width: 160,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '商品数量',
    code: 'actual_quantity',
    width: 160,
    features: { sortable: true },
    align: 'center',
  },
];

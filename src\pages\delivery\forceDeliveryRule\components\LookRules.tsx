import {
  XlbBasicForm,
  XlbButton,
  XlbIcon,
  XlbInputDialog,
  XlbSelect,
  XlbTable,
  XlbTabs,
} from '@xlb/components';
import { useEffect, useState } from 'react';
import api from '../server';

const LookRules = (props: any) => {
  const [form] = XlbBasicForm.useForm<any>();
  const [dataSource, setDataSource] = useState<any>({
    no_force_delivery_items: [],
    force_delivery_items: [],
  });
  const [loading, setLoading] = useState(false);
  const [options, setOptions] = useState([]);
  const [tab, setTab] = useState('1');

  const fetchData = async () => {
    form.validateFields().then(async () => {
      setLoading(true);
      const res = await api.ruleItem({
        store_id: form.getFieldValue('store_id')?.[0],
        rule_id: form.getFieldValue('rule_id'),
      });
      if (res.code === 0) {
        setDataSource(
          res.data || {
            no_force_delivery_items: [],
            force_delivery_items: [],
          },
        );
      }
      setLoading(false);
    });
  };
  const getRuleList = async () => {
    const res = await api.rulePage({
      page: 1,
      limit: 999999,
    });
    if (res.code === 0) {
      const list = res.data.content.map((item: any) => ({
        label: item.rule_name,
        value: item.id,
      }));
      setOptions(list);
    }
  };
  useEffect(() => {
    getRuleList();
  }, []);

  return (
    <>
      <XlbBasicForm form={form} layout="inline">
        <XlbBasicForm.Item
          name="store_id"
          label="应用门店"
          rules={[{ required: true, message: '请选择应用门店' }]}
        >
          <XlbInputDialog
            dialogParams={{
              type: 'store',
              isMultiple: false,
              dataType: 'lists',
              placeholder: '请选择',
              data: {
                status: true,
              },
            }}
            fieldNames={{
              idKey: 'id',
              nameKey: 'store_name',
            }}
            width={256}
          />
        </XlbBasicForm.Item>
        <XlbBasicForm.Item name="rule_id" label="命中规则">
          <XlbSelect options={options} width={256} />
        </XlbBasicForm.Item>
        <XlbBasicForm.Item>
          <XlbButton
            type="primary"
            icon={<XlbIcon name="sousuo" />}
            onClick={fetchData}
            style={{ marginLeft: 16 }}
          >
            查询
          </XlbButton>
        </XlbBasicForm.Item>
      </XlbBasicForm>
      <XlbTabs
        items={[
          {
            label:
              '待统配商品(' +
              (dataSource?.no_force_delivery_items?.length || 0) +
              ')',
            key: '1',
          },
          {
            label:
              '已统配商品(' +
              (dataSource?.force_delivery_items?.length || 0) +
              ')',
            key: '2',
          },
        ]}
        onChange={(key) => setTab(key)}
      />
      <XlbTable
        columns={[
          { name: '序号', code: '_index', width: 80, align: 'center' },
          {
            name: '代码',
            code: 'item_code',
            features: { sortable: true },
            width: 180,
          },
          {
            name: '名称',
            code: 'item_name',
            features: { sortable: true },
            width: 250,
          },
          {
            name: '命中规则名称',
            code: 'force_delivery_rule_name',
            features: { sortable: true },
            width: 180,
          },
          {
            name: '命中规则',
            code: 'rule',
            features: { sortable: true },
            width: 160,
          },
        ]}
        dataSource={
          tab === '1'
            ? dataSource?.no_force_delivery_items
            : dataSource?.force_delivery_items
        }
        isLoading={loading}
        pageSize={200}
        total={
          tab === '1'
            ? dataSource?.no_force_delivery_items?.length
            : dataSource?.force_delivery_items?.length
        }
      />
    </>
  );
};

export default LookRules;

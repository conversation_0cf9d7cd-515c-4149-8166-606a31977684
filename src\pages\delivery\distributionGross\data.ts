import { SearchFormType } from '@xlb/components';
import {
  // type BasicDataConfig,
  // type SelectTreeType,
  type SelectType,
} from '@xlb/components';
import type { XlbTableColumnProps } from '@xlb/components'


export const purchase_type = [
  {
    label: '集采品',
    value: 'COLLECTIVE_PURCHASE'
  },
  {
    label: '集售品',
    value: 'COLLECTIVE_SALE'
  },
  {
    label: '地采品',
    value: 'GROUND_PURCHASE'
  },
  {
    label: '店采品',
    value: 'SHOP_PURCHASE'
  }
]

const columnWidthEnum = {
  tel : 120,
  fid : 160,
  TIME : 120,
  DATE : 100,
  ITEM_CODE : 124,
  ITEM_BAR_CODE : 124,
  SHORTHAND_CODE : 110,
  STORE_NAME : 140,
  MEMO : 140,
  STOP_SALE : 90,
  ORDER_STATE : 90,
  INDEX : 50,
  ITEM_SPEC : 110,
  BY : 110,
  ORDER_FID : 140
}

export const queryUnit = [
  {
    label: '单据单位',
    value: 'ORDER'
  },
  {
    label: '配送单位',
    value: 'DELIVERY'
  },
  {
    label: '库存单位',
    value: 'STOCK'
  },
  {
    label: '采购单位',
    value: 'PURCHASE'
  },
  {
    label: '基本单位',
    value: 'BASIC'
  },
  {
    label: '批发单位',
    value: 'WHOLESALE'
  }
]


export const checkOptions = [
  {
    label: '仅查询在途数量>0的数据',
    value: 'normal'
  }
]

export const basicTable: XlbTableColumnProps<any>[] = [
  {
    name: '序号',
    code: '_index',
    width: columnWidthEnum.INDEX,
    align: 'center',
  },
  {
    name: '调出数量',
    code: 'out_quantity',
    width: 110,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '调出基本数量',
    code: 'basic_out_quantity',
    width: 120,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '调出金额',
    code: 'out_money',
    width: 100,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '调出金额（去税）',
    code: 'no_tax_out_money',
    width: 150,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '调出成本',
    code: 'out_cost',
    width: 100,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '调出成本（去税）',
    code: 'no_tax_out_cost',
    width: 150,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '调入数量',
    code: 'in_quantity',
    width: 100,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '调入基本数量',
    code: 'basic_in_quantity',
    width: 120,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '调入金额',
    code: 'in_money',
    width: 100,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '调入金额（去税）',
    code: 'no_tax_in_money',
    width: 150,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '调入成本',
    code: 'in_cost',
    width: 100,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '调入成本（去税）',
    code: 'no_tax_in_cost',
    width: 150,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '配送数量',
    code: 'out_quantity_total',
    width: 100,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '配送金额',
    code: 'out_money_total',
    width: 150,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '配送金额（税额）',
    code: 'out_tax_money_total',
    width: 180,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '配送金额（去税）',
    code: 'no_tax_out_money_total',
    width: 180,
    features: { sortable: true },
    align: 'right',
  },

  {
    name: '配送成本',
    code: 'out_cost_total',
    width: 120,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '配送成本（税额）',
    code: 'out_cost_tax_money_total',
    width: 180,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '配送成本（去税）',
    code: 'no_tax_out_cost_total',
    width: 180,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '配送毛利',
    code: 'delivery_gross_profit',
    width: 100,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '配送毛利（税额）',
    code: 'delivery_gross_profit_tax_money',
    width: 180,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '配送毛利（去税）',
    code: 'no_tax_delivery_gross_profit',
    width: 180,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '配送毛利率',
    code: 'delivery_gross_profit_rate',
    width: 120,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '配送毛利率（去税）',
    code: 'no_tax_delivery_gross_profit_rate',
    width: 180,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '零售金额',
    code: 'sale_money',
    width: 150,
    features: { sortable: true },
    align: 'right',
  },

  {
    name: '零售金额（去税）',
    code: 'no_tax_sale_money',
    width: 150,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '零售毛利',
    code: 'sale_gross_profit',
    width: 150,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '零售毛利（去税）',
    code: 'no_tax_sale_gross_profit',
    width: 150,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '零售毛利率',
    code: 'sale_gross_profit_rate',
    width: 150,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '零售毛利率（去税）',
    code: 'no_tax_sale_gross_profit_rate',
    width: 180,
    features: { sortable: true },
    align: 'right',
  },
];

export const supplier: XlbTableColumnProps<any>[] = [
  {
    name: '供应商',
    code: 'supplier_name',
    width: 250
    // features: { sortable: true }
  }
]

export const deliveryDate: XlbTableColumnProps<any>[] = [
  {
    name: '配送日期',
    code: 'date',
    width: 100,
    features: { sortable: true }
  }
]

export const outStore: XlbTableColumnProps<any>[] = [
  {
    name: '调出组织',
    code: 'out_org_name',
    width: columnWidthEnum.STORE_NAME,
    features: { sortable: true }
  },
  {
    name: '调出门店',
    code: 'out_store_name',
    width: columnWidthEnum.STORE_NAME,
    features: { sortable: true }
  }
]

export const inStore: XlbTableColumnProps<any>[] = [
  {
    name: '调入组织',
    code: 'in_org_name',
    width: columnWidthEnum.STORE_NAME,
    features: { sortable: true }
  },
  {
    name: '调入门店',
    code: 'in_store_name',
    width: columnWidthEnum.STORE_NAME,
    features: { sortable: true }
  },
  {
    name: '调入门店代码',
    code: 'in_store_code',
    width: 115,
    features: { sortable: true }
  }
]

export const categoryName: XlbTableColumnProps<any>[] = [
  {
    name: '商品类别',
    code: 'item_category_name',
    width: 150,
    features: { sortable: true }
  }
]

export const financeCode = [
  {
    name: '业财核算分类',
    code: 'finance_name',
    width: 144,
    features: { sortable: true }
  }
]

export const itemTable: XlbTableColumnProps<any>[] = [
  {
    name: '采购类型',
    code: 'purchase_type',
    width: 110,
    features: { sortable: true },
    align: 'right'
  },
  {
    name: '商品代码',
    code: 'item_code',
    width: columnWidthEnum.ITEM_CODE,
    features: { sortable: true }
  },
  {
    name: '商品条码',
    code: 'item_bar_code',
    width: columnWidthEnum.ITEM_BAR_CODE,
    features: { sortable: true }
  },
  {
    name: '商品名称',
    code: 'item_name',
    width: 150,
    features: { sortable: true }
  },
  {
    name: '商品规格',
    code: 'item_spec',
    width: columnWidthEnum.ITEM_SPEC,
    features: { sortable: true }
  },
  {
    name: '配送门店数',
    code: 'delivery_count',
    width: 150,
    features: { sortable: true },
    align: 'right'
  },
  {
    name: '税率',
    code: 'tax_rate',
    width: 100,
    features: { sortable: true },
    align: 'right'
  }
]

export const categoryArr: XlbTableColumnProps<any>[] = [
  {
    name: '一级类别',
    code: 'one_item_category_name',
    width: 120,
    features: { sortable: true },
    align: 'left'
  },
  {
    name: '二级类别',
    code: 'two_item_category_name',
    width: 120,
    features: { sortable: true },
    align: 'left'
  },
  {
    name: '三级类别',
    code: 'three_item_category_name',
    width: 120,
    features: { sortable: true },
    align: 'left'
  }
]

export const businessArr = [
  {
    name: '配送类型',
    code: 'delivery_type_name',
    width: 100,
    features: { sortable: true }
  }
]
import {
  XlbButton,
  XlbInput,
  XlbModal,
  XlbTable,
  XlbTableColumnProps,
} from '@xlb/components';
import { XlbFetch } from '@xlb/utils';
import { Form, message } from 'antd';
import { useEffect, useState } from 'react';
import { tableList } from '../../data';
import style from './TemplateModel.less';
const TemplateModel = (props: any) => {
  const { visible, handleCancel, handleOk } = props;
  const [chooseList, setChooseList] = useState<any[]>([]);
  const [rowData, setRowData] = useState<any[]>([]);
  const [selectRow, setSelectRow] = useState<any>([]);
  const [loading, setloading] = useState<any>(false);
  const [keyword, setKeyword] = useState<string>('');
  const [tablelist, settableList] = useState<XlbTableColumnProps<any>[]>(
    JSON.parse(JSON.stringify(tableList)),
  );
  const [form] = Form.useForm();
  const [pagin, setPagin] = useState({
    pageSize: 200,
    pageNum: 1,
    total: 0,
  });
  const [pageSize, setPageSize] = useState<any>(200);
  // 获取数据
  const getRecord = async () => {
    setChooseList([]);
    setloading(true);
    const data = {
      keyword: keyword,
    };
    const res = await XlbFetch.post(
      '/erp/hxl.erp.retailpricetemplate.page',
      data,
    );
    setloading(false);
    if (res.code === 0) {
      setRowData(res.data.content || []);
      setPagin({
        ...pagin,
        total: res.data.total_elements,
      });
    }
  };
  const onCancel = () => {
    handleCancel();
    setChooseList([]);
    form.resetFields();
  };
  useEffect(() => {
    visible && getRecord();
  }, [visible]);
  const onSelectRow = (keys, row) => {
    setChooseList(keys);
    setSelectRow(row);
  };

  return (
    <div>
      <XlbModal
        title={'选择模板'}
        style={{ top: 150 }}
        open={visible}
        maskClosable={false}
        onOk={() => {
          if (chooseList.length == 0) {
            message.error('选择模板');
            return;
          }
          handleOk(chooseList, selectRow);
          onCancel();
        }}
        onCancel={onCancel}
        bodyStyle={{ padding: '5px' }}
        width={700}
        confirmLoading={loading}
      >
        <div style={{ height: 342, overflow: 'hidden' }}>
          <div className={style.header}>
            <div style={{ display: 'flex', alignItems: 'center', gap: 10 }}>
              <span>关键字：</span>
              <XlbInput
                onChange={(e) => setKeyword(e.target.value)}
                allowClear
                size="small"
                style={{ width: 180 }}
              />
            </div>

            <XlbButton onClick={() => getRecord()} type={'primary'}>
              查询
            </XlbButton>
          </div>
          <div style={{ height: 300 }}>
            <XlbTable
              columns={tablelist}
              dataSource={rowData}
              style={{ height: '90%', overflow: 'auto' }}
              total={pagin.total}
              pageSize={pageSize}
              onPaginChange={(page, pageSize) => {
                setPageSize(pageSize);
              }}
              selectMode="multiple"
              selectedRowKeys={chooseList}
              onSelectRow={(selectedRowKeys, selectedRows) => {
                onSelectRow(selectedRowKeys, selectedRows);
              }}
            ></XlbTable>
          </div>
        </div>
      </XlbModal>
    </div>
  );
};

export default TemplateModel;

.form_container {
    padding-bottom: 0 !important;
    :global form {
    //   width: 800px !important;
    }
    :global .ant-form{
        min-width: unset;
    }
    :global .ant-input-number-disabled{
        color: #86908c !important;
        background-color: #f2f3f5;
    }
    :global .ant-form-item {
        .ant-input {
            // width: 260px !important;
        }
        .ant-select {
            width: 260px !important;
        }
        .ant-input-affix-wrapper {
            // width: 260px !important;
        }
        .ant-input-number{
            width: 260px !important;
        }
        .ant-picker {
            width: 260px !important;
        }
    }
    .memo {
        :global .ant-form-item {
            .ant-input-affix-wrapper {
                width: 620px !important;
            }
        }
    }
}
.contractTab {
    :global .ant-tabs-top > .ant-tabs-nav {
      margin-bottom: 5px;
    }
}
.badge {
    z-index: 10;
  }
import { XlbTableColumnProps } from '@xlb/components'

export enum DataType {
  LISTS = 'lists',
  TREE = 'tree'
}


//商品明细
export const itemTableList: XlbTableColumnProps<any>[] = [
  {
    name: '序号',
    code: '_index',
    width: 60,
    align: 'center'
  },
  {
    name: '门店代码',
    code: 'store_code',
    width: 120,
    features: { sortable: true }
  },
  {
    name: '组织',
    code: 'org_name',
    width: 140,
    hidden: true,
    features: { sortable: true }
  },
  {
    name: '门店名称',
    code: 'store_name',
    width: 160,
    features: { sortable: true }
  },
  {
    name: '门店分组',
    code: 'store_group_name',
    width: 100,
    features: { sortable: true }
  },
  {
    name: '',
    width: 500
  }
]
export const ruleOptions = [
  {
    label: '按周',
    value: 'WEEK'
  },
  {
    label: '按月',
    value: 'MONTH'
  }
]

export const orderingCircle = {
  WEEK: [
    { label: '星期一', value: 1 },
    { label: '星期二', value: 2 },
    { label: '星期三', value: 3 },
    { label: '星期四', value: 4 },
    { label: '星期五', value: 5 },
    { label: '星期六', value: 6 },
    { label: '星期日', value: 7 }
  ],
  MONTH: [
    { label: '1号', value: 1 },
    { label: '2号', value: 2 },
    { label: '3号', value: 3 },
    { label: '4号', value: 4 },
    { label: '5号', value: 5 },
    { label: '6号', value: 6 },
    { label: '7号', value: 7 },
    { label: '8号', value: 8 },
    { label: '9号', value: 9 },
    { label: '10号', value: 10 },
    { label: '11号', value: 11 },
    { label: '12号', value: 12 },
    { label: '13号', value: 13 },
    { label: '14号', value: 14 },
    { label: '15号', value: 15 },
    { label: '16号', value: 16 },
    { label: '17号', value: 17 },
    { label: '18号', value: 18 },
    { label: '19号', value: 19 },
    { label: '20号', value: 20 },
    { label: '21号', value: 21 },
    { label: '22号', value: 22 },
    { label: '23号', value: 23 },
    { label: '24号', value: 24 },
    { label: '25号', value: 25 },
    { label: '26号', value: 26 },
    { label: '27号', value: 27 },
    { label: '28号', value: 28 },
    { label: '29号', value: 29 },
    { label: '30号', value: 30 },
    { label: '31号', value: 31 }
  ]
}

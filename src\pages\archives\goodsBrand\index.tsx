import { ErpFieldKeyMap } from '@/data/common/fieldListConfig';
import { hasAuth } from '@/utils/kit';
import { XlbProPageContainer } from '@xlb/components';
import { type FC } from 'react';
import { tableColumn } from './data';

const ProForm: FC<{ title: string }> = () => {
  return (
    <XlbProPageContainer // 查询
      searchFieldProps={{
        formList: [
          { id: 'keyword', name: 'keyword', label: '关键字' },
          { id: ErpFieldKeyMap.brandLevel, label: '品牌等级' },
        ],
      }}
      tableFieldProps={{
        url: '/erp-mdm/hxl.erp.brand.find',
        tableColumn: tableColumn,
        selectMode: 'single',
        keepDataSource: false,
        showColumnsSetting: false,
        immediatePost: true,
      }}
      deleteFieldProps={{
        name: '删除',
        showField: 'name',
        url: hasAuth(['商品品牌', '删除']) ? '/erp-mdm/hxl.erp.brand.delete' : '',
      }}
      addFieldProps={{
        name: '新增',
        url: hasAuth(['商品品牌', '编辑']) ? '/erp-mdm/hxl.erp.brand.save' : '',
      }}
      details={{
        mode: 'modal',
        isCancel: true,
        width: 350,
        title: (obj) => {
          return <div>{obj?.id ? '编辑' : '新增'}</div>;
        },
        hiddenSaveBtn: true,
        primaryKey: 'id',
        formList: [
          {
            componentType: 'form',
            fieldProps: {
              formList: [
                {
                  id: ErpFieldKeyMap.otherIncomeExpensesName,
                  itemSpan: 24,
                  label: '商品品牌名称',
                  rules: [{ required: true, message: '商品品牌名称不能为空' }],
                  fieldProps: { maxLength: 20, width: '100%' },
                  // width:180
                },
                {
                  id: ErpFieldKeyMap.brandLevel,
                  label: '品牌等级',
                  rules: [{ required: true, message: '品牌等级不能为空' }],
                  itemSpan: 24,
                },
              ],
            },
          },
        ],
        updateFieldProps: {
          url: '/erp-mdm/hxl.erp.brand.update',
        },
      }}
    />
  );
};

export default ProForm;
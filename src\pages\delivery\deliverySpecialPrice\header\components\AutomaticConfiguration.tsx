import { columnWidthEnum } from '@/data/common/constant';
import * as fusion from '@alifd/next';
import {
  DeleteOutlined,
  PlusOutlined,
  QuestionCircleOutlined,
} from '@ant-design/icons';
import {
  XlbBasicForm,
  XlbButton,
  XlbIcon,
  XlbInputDialog,
  XlbInputNumber,
  XlbModal,
  XlbTableColumnProps,
  XlbTabs,
  XlbTipsModal,
} from '@xlb/components';
import { useDebounceFn, useUpdateEffect } from 'ahooks';
import { BaseTable, features, useTablePipeline } from 'ali-react-table';
import { message, Space, Tooltip } from 'antd';
import { isBoolean } from 'lodash';
import { useEffect, useState } from 'react';
import { v4 as uuidv4 } from 'uuid';
import Api from '../../api';
import { tableStyle } from '../../data';
import styles from './index.less';

const AutomaticConfiguration = (props: any) => {
  const { visible, setVisible } = props;
  const [form] = XlbBasicForm.useForm();
  const uniqueStrings = new Set();

  const [dataSource, setDataSource] = useState<any>([]);
  // const { details } = useStore()
  const [levelTwoOrgId, setLevelTwoOrgId] = useState<any>();
  const [orgList, setOrgList] = useState<any[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [tableLoading, setTableLoading] = useState<boolean>(false);
  const [okLoading, setOkLoading] = useState<boolean>(false);
  const [selectedList, setSelectedList] = useState<any>([]);

  const getTableInfo = async () => {
    setTableLoading(true);
    const res = await Api?.autodeliveryspecialpriceconf({
      level_two_org_id: levelTwoOrgId,
    });
    setTableLoading(false);
    if (res?.code === 0) {
      setDataSource(
        addKeyToAdjacentItems(
          Array.isArray(res?.data?.detail_list) ? res?.data?.detail_list : [],
        ),
      );
      form.setFieldValue('store_ids', res?.data?.supply_store_id_list || []);
    }
  };

  const generateUniqueString = () => {
    let uniqueStr;
    do {
      uniqueStr = `${performance.now().toString(36)}-${Math.random().toString(36).substring(2, 12)}`;
    } while (uniqueStrings.has(uniqueStr));
    uniqueStrings.add(uniqueStr);
    return uniqueStr;
  };
  const addKeyToAdjacentItems = (arr: any[]) => {
    if (arr.length === 0) return [];
    let startIndex = 0;
    arr[0]._mergeSign = generateUniqueString();
    arr[0]._id = uuidv4();
    for (let i = 1; i < arr.length; i++) {
      arr[i]._mergeSign = generateUniqueString();
      arr[i]._id = uuidv4();
      if (
        arr[i].shelf_life_start === arr[startIndex].shelf_life_start &&
        arr[i].shelf_life_end === arr[startIndex].shelf_life_end
      ) {
        arr[i]._mergeSign = arr[startIndex]._mergeSign;
      } else {
        startIndex = i;
      }
    }
    return arr;
  };

  const getTwoOrgList = async () => {
    setLoading(true);
    const res = await Api?.findbylevel({ level: 2 });
    setLoading(false);
    if (res.code == 0) {
      setOrgList([...res.data]);
      setLevelTwoOrgId(`${res.data[0].id}`);
    }
  };

  useUpdateEffect(() => {
    if (levelTwoOrgId) {
      getTableInfo();
    }
  }, [levelTwoOrgId]);

  useEffect(() => {
    if (visible) {
      getTwoOrgList();
    } else {
      setDataSource([]);
      setSelectedList([]);
      setLevelTwoOrgId('');
      uniqueStrings.clear();
      form.resetFields();
    }
  }, [visible]);

  const saveOptions = async () => {
    setOkLoading(true);
    const dt = dataSource.filter(
      (item: any) =>
        item.shelf_life_start ||
        item.shelf_life_end ||
        item.to_expire_percent ||
        item.discount_percent,
    );
    const ed = dt.filter(
      (item) =>
        !item.shelf_life_start ||
        !item.shelf_life_end ||
        !item.discount_percent ||
        !item.to_expire_percent,
    );
    if (ed.length) {
      message.error('请完整填写配置');
      return;
    }
    dt.forEach((item: any, index: number) => {
      validateInput(index, false);
    });
    const errlist = dt.filter(
      (item: any) => isBoolean(item._validate) && !item._validate,
    );
    if (errlist.length) {
      const mesAry: any = [];
      errlist.forEach((item: any) => {
        mesAry.push(...item._validateMessage);
      });
      // NiceModal.show(XlbNewTipsModal, {
      //   tips_list: mesAry,
      //   isCancel: true,
      //   width: 500,
      // });
      XlbTipsModal({
        tipsList: mesAry,
        isCancel: true,
        width: 500,
      });
      return;
    }
    const res = await Api?.autodeliveryspecialpriceconfSave({
      level_two_org_id: levelTwoOrgId,
      supply_store_id_list: form.getFieldValue('store_ids'),
      detail_list: dt,
    });
    setOkLoading(false);
    if (res?.code === 0) {
      setVisible(false);
    }
  };

  const deleteItem = () => {
    if (selectedList.length) {
      // NiceModal.show(XlbNewTipsModal, {
      //   tips: '确定删除选中的行吗？',
      //   isCancel: true,
      // }).then((res) => {
      //   if (res) {
      //     const newDataSource = dataSource.filter(
      //       (item: any) => !selectedList.includes(item._id),
      //     );
      //     setDataSource(newDataSource);
      //     setSelectedList([]);
      //   }
      // });

      XlbTipsModal({
        tips: '确定删除选中的行吗？',
        isCancel: true,
        onOkBeforeFunction: async () => {
          const newDataSource = dataSource.filter(
            (item: any) => !selectedList.includes(item._id),
          );
          setDataSource(newDataSource);
          setSelectedList([]);

          return true;
        },
      });
    }
  };

  const inputChangeDebounce = useDebounceFn(
    (e: any, index: any, key: any) => {
      inputChange(e, index, key);
    },
    {
      wait: 200,
    },
  );
  const inputChange = (e: any, index: any, key: any) => {
    if (key === 'discount_percent' || key === 'to_expire_percent') {
      if (e === 0 || e === 100) {
        message.warning('输入值0~100之间！');
        return;
      }
    }
    const row = dataSource[index];
    row[key] = e;
    setDataSource([...dataSource]);
  };
  const onPressEnter = (code: any, index: any, e?: any) => {
    // if (code === 'to_expire_percent') {
    //   inputBlur(e.target.value, index, 'to_expire_percent')
    // }
    Promise.resolve()
      .then(() => {
        dataSource[index]._edit = false;
        index + 1 == dataSource.length
          ? (dataSource[0]._edit = true)
          : (dataSource[index + 1]._edit = true);
        setDataSource(JSON.parse(JSON.stringify(dataSource)));
      })
      .then(() => {
        const inputBox =
          index + 1 == dataSource.length
            ? document.getElementById(code + '-' + (0).toString())
            : document.getElementById(code + '-' + (index + 1).toString());
        inputBox?.focus();
      });
  };

  const isValidFraction = (fraction: string) => {
    const regex = /^([1-9]\d*)\/([1-9]\d*)$/;
    const match = fraction.match(regex);

    if (match) {
      const numerator = parseInt(match[1], 10); // 分子
      const denominator = parseInt(match[2], 10); // 分母
      return numerator < denominator; // 只返回分子小于分母的情况
    }
    return false;
  };

  const findPreviousMax = (i: number, current: any) => {
    while (i >= 0) {
      if (
        (dataSource[i].shelf_life_end || dataSource[i].shelf_life_end == 0) &&
        dataSource[i]._mergeSign !== current._mergeSign
      ) {
        return dataSource[i].shelf_life_end;
      }
      i--;
    }
    return null;
  };
  const findNextMin = (i: number, current: any) => {
    while (i < dataSource.length) {
      if (
        (dataSource[i].shelf_life_start ||
          dataSource[i].shelf_life_start == 0) &&
        dataSource[i]._mergeSign !== current._mergeSign
      ) {
        return dataSource[i].shelf_life_start;
      }
      i++;
    }
    return null;
  };
  const validateInput = (index: number, showError = true): boolean => {
    const current = dataSource[index];
    const prevMax = findPreviousMax(index - 1, current);
    const nextMin = findNextMin(index + 1, current);
    let msg = '';
    current._validate = true;
    current._validateMessage = [];
    if (
      current.shelf_life_start !== '' &&
      current.shelf_life_end !== '' &&
      current.shelf_life_start > current.shelf_life_end
    ) {
      msg = `第【${index + 1}】行: 保质期天数区间, 起始时间小于等于终止时间`;
      current._validate = false;
      current._validateMessage.push(msg);
    }
    if (
      current.shelf_life_start !== '' &&
      prevMax !== null &&
      current.shelf_life_start <= prevMax
    ) {
      msg = `第【${index + 1}】行: 保质期天数区间, 起始时间需大于上一条的终止时间`;
      current._validate = false;
      current._validateMessage.push(msg);
    }
    if (
      current.shelf_life_end !== '' &&
      nextMin !== null &&
      current.shelf_life_end >= nextMin
    ) {
      msg = `第【${index + 1}】行: 保质期天数区间, 终止时间需小于下一条的起始时间`;
      current._validate = false;
      current._validateMessage.push(msg);
    }
    if (!current._validate && showError) {
      // NiceModal.show(XlbNewTipsModal, {
      //   tips_list: current._validateMessage,
      //   isCancel: true,
      //   width: 500,
      // });
      XlbTipsModal({
        tipsList: current._validateMessage,
        isCancel: true,
        width: 500,
      });
    }
    setDataSource([...dataSource]);
    return current._validate;
  };

  const tableList: XlbTableColumnProps<any>[] = [
    {
      name: '序号',
      code: '_index',
      width: columnWidthEnum.INDEX,
      align: 'center',
    },
    {
      name: '保质期天数区间',
      code: '_mergeSign',
      width: 200,
      align: 'center',
      features: { autoRowSpan: true },
      render: (value: any, record: any, index: any) => {
        return record._edit ? (
          <div className={`${styles.inputBox} info overwidth`}>
            <XlbInputNumber
              min={1}
              max={999999999}
              precision={0}
              className={styles.inputNumber}
              value={record?.shelf_life_start?.toFixed(0)}
              // onChange={(e) => inputChangeDebounce.run(e, index, 'shelf_life_start')}
              onBlur={(e) => {
                if (!e.target.value) return;

                inputChange(Number(e.target.value), index, 'shelf_life_start');
                validateInput(index);
                dataSource.forEach((item, i) => {
                  if (item._mergeSign === record._mergeSign) {
                    item.shelf_life_start = Number(e.target.value);
                  }
                });
                setDataSource([...dataSource]);
              }}
              onClick={(e) => {
                e.stopPropagation();
              }}
              onPressEnter={(e) => onPressEnter('shelf_life_start', index)}
            />
            <span>~</span>
            <XlbInputNumber
              min={0}
              max={999999999}
              precision={0}
              className={styles.inputNumber}
              value={record?.shelf_life_end?.toFixed(0)}
              // onChange={(e) => inputChangeDebounce.run(e, index, 'shelf_life_end')}
              onBlur={(e) => {
                if (!e.target.value) return;
                inputChange(Number(e.target.value), index, 'shelf_life_end');
                validateInput(index);
                dataSource.forEach((item, i) => {
                  if (item._mergeSign === record._mergeSign) {
                    item.shelf_life_end = Number(e.target.value);
                  }
                });
                setDataSource([...dataSource]);
              }}
              onClick={(e) => {
                e.stopPropagation();
              }}
              onPressEnter={(e) => onPressEnter('shelf_life_end', index)}
            />
          </div>
        ) : (
          <div
            className="info overwidth"
            style={
              isBoolean(record._validate) && !record._validate
                ? { color: 'red' }
                : {}
            }
          >
            {record?.shelf_life_start && record?.shelf_life_end
              ? `${record?.shelf_life_start ? record?.shelf_life_start?.toFixed(0) : ''} ~ ${
                  record?.shelf_life_end
                    ? record?.shelf_life_end?.toFixed(0)
                    : ''
                }`
              : ''}
          </div>
        );
      },
    },
    {
      name: '距到期比例',
      code: 'to_expire_percent',
      width: 120,
      align: 'center',
      render: (value: any, record: any, index: any) => {
        return record._edit ? (
          <div className="info overwidth">
            <XlbInputNumber
              min={0}
              max={100}
              value={value}
              suffix="%"
              onChange={(e) =>
                inputChangeDebounce.run(e, index, 'to_expire_percent')
              }
              onClick={(e) => e.stopPropagation()}
              onPressEnter={(e) => onPressEnter('to_expire_percent', index)}
            />
          </div>
        ) : (
          <div className="info overwidth">{value ? `${value} %` : ''}</div>
        );
      },
    },
    {
      name: '折扣',
      code: 'discount_percent',
      width: 120,
      align: 'center',
      render: (value: any, record: any, index: any) => {
        return record._edit ? (
          <div className="info overwidth">
            <XlbInputNumber
              min={0}
              max={100}
              value={value}
              suffix="%"
              onChange={(e) =>
                inputChangeDebounce.run(e, index, 'discount_percent')
              }
              onClick={(e) => e.stopPropagation()}
              onPressEnter={(e) => onPressEnter('discount_percent', index)}
            />
          </div>
        ) : (
          <div className="info overwidth">{value ? `${value} %` : ''}</div>
        );
      },
    },
    {
      name: '特价时长',
      code: 'duration',
      width: 120,
      align: 'center',
      render: (value: any) => {
        return <div className="info overwidth">{`${value} 天`}</div>;
      },
    },
    {
      name: '操作',
      code: 'operation',
      align: 'center',
      width: 80,
      render: (value: any, record: any, index: any) => {
        return record.index === '合计' ? null : (
          <div className="info overwidth">
            <PlusOutlined
              className="link cursors"
              style={{ fontSize: '16px', marginRight: '5px' }}
              onClick={(e) => {
                e.stopPropagation();
                // if (dataSource[dataSource.length - 1]._newRow === true) return
                dataSource.splice(index + 1, 0, {
                  _mergeSign: record._mergeSign,
                  shelf_life_end: record.shelf_life_end || '',
                  shelf_life_start: record.shelf_life_start || '',
                  _id: uuidv4(),
                  duration: 1,
                  _newRow: true,
                });
                setDataSource([...dataSource]);
              }}
            />
            <DeleteOutlined
              className="link cursors"
              style={{ fontSize: '16px' }}
              onClick={(e) => {
                e.stopPropagation();
                setSelectedList([]);
                record.delete = true;
                dataSource.splice(
                  dataSource.findIndex((v: any) => v.delete),
                  1,
                );
                setDataSource([...dataSource]);
              }}
            />
          </div>
        );
      },
    },
  ];

  const pipeline = useTablePipeline({ components: fusion })
    .input({ dataSource: dataSource, columns: tableList })
    .primaryKey('_id')
    .use(
      features.columnResize({
        fallbackSize: 100,
        onChangeSizes: (nextSizes: number[]) => {},
      }),
    )
    .use(
      features.multiSelect({
        value: selectedList,
        highlightRowWhenSelected: false,
        clickArea: 'row',
        onChange: (value: any) => {
          setSelectedList(value);
        },
      }),
    )
    .use(features.autoRowSpan())
    .use(
      features.singleSelect({
        highlightRowWhenSelected: true,
        clickArea: 'row',
        radioColumn: { hidden: true },
        onChange: (value: any) => {
          pipeline.getDataSource().map((v, index) => {
            v._edit = value == v._id;
          });
        },
      }),
    );

  return (
    <XlbModal
      wrapClassName={'xlbDialog'}
      title={
        <div>
          自动特价配置{' '}
          <Tooltip
            placement="right"
            title={
              '配置后,系统会每天凌晨1点自动产生配送特价单和仓库的移库任务。'
            }
          >
            <QuestionCircleOutlined />
          </Tooltip>
        </div>
      }
      keyboard={false}
      okButtonProps={{
        loading: okLoading,
      }}
      visible={visible}
      isCancel={true}
      loading={loading}
      maskClosable={false}
      onOk={() => saveOptions()}
      onCancel={() => setVisible(false)}
      width={900}
      centered
    >
      <div>
        <XlbTabs
          activeKey={levelTwoOrgId}
          className={styles.contractTab}
          onChange={(e) => setLevelTwoOrgId(e)}
          items={orgList.map((t) => ({ label: t.name, key: `${t.id}` }))}
        />
        <XlbBasicForm form={form}>
          <XlbBasicForm.Item label="配送门店" name="store_ids">
            <XlbInputDialog
              dialogParams={{
                type: 'store',
                dataType: 'lists',
                isMultiple: true,
                data: {
                  center_flag: true,
                },
              }}
              fieldNames={{
                idKey: 'id',
                nameKey: 'store_name',
              }}
              width={210}
            />
          </XlbBasicForm.Item>
        </XlbBasicForm>
        <Space style={{ marginBottom: 10 }}>
          <XlbButton
            type="primary"
            label="新增"
            icon={<XlbIcon size={16} name="jia" />}
            onClick={() => {
              setDataSource([
                ...dataSource,
                {
                  _mergeSign: generateUniqueString(),
                  _id: uuidv4(),
                  duration: 1,
                  _newRow: true,
                },
              ]);
            }}
          />
          <XlbButton
            type="primary"
            label="删除"
            disabled={!selectedList?.length}
            onClick={() => deleteItem()}
            icon={<XlbIcon name="shanchu" />}
          />
        </Space>
        <BaseTable
          className="jMuPhs"
          isLoading={tableLoading}
          style={{
            ...tableStyle,
            overflow: 'auto',
            height: 360,
          }}
          emptyCellHeight={210}
          {...pipeline.getProps()}
        />
      </div>
    </XlbModal>
  );
};

export default AutomaticConfiguration;

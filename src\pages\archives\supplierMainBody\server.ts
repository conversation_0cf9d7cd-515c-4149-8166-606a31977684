import {XlbFetch as ErpRequest } from '@xlb/utils'
import { KeywordReqDTO, IdReqDTO, MainBodyUpdateReqDTO, MainBodySaveReqDTO } from './type'

export default {
  //获取数据
  getData: async (data: KeywordReqDTO) => {
    return await ErpRequest.post('/erp-mdm/hxl.erp.suppliermainbody.find', { data })
  },

  //新增
  add: async (data: MainBodySaveReqDTO) => {
    return await ErpRequest.post('/erp-mdm/hxl.erp.suppliermainbody.save', { data })
  },
  //更新
  update: async (data: MainBodyUpdateReqDTO) => {
    return await ErpRequest.post('/erp-mdm/hxl.erp.suppliermainbody.update', { data })
  },
  //删除
  delete: async (data: IdReqDTO) => {
    return await ErpRequest.post('/erp-mdm/hxl.erp.suppliermainbody.delete', { data })
  }
}
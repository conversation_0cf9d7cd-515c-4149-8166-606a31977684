function generateChineseNumberMap() {
  const map = {};
  const chineseUnits = ['', '十', '百', '千', '万'];
  const chineseDigits = ['零', '一', '二', '三', '四', '五', '六', '七', '八', '九'];

  // 辅助函数：转换 0-9999 的数字
  function convertNumber(num: number) {
    if (num === 0) return chineseDigits[0];
    let result = '';
    let digitIndex = 0;
    let lastNonZero = false;

    while (num > 0) {
      const digit = num % 10;
      if (digit !== 0) {
        result = chineseDigits[digit] + chineseUnits[digitIndex] + result;
        lastNonZero = true;
      } else if (lastNonZero) {
        // 避免连续的零
        result = chineseDigits[digit] + result;
        lastNonZero = false;
      }
      num = Math.floor(num / 10);
      digitIndex++;
    }

    // 处理特殊情况：一十 → 十
    if (result.startsWith('一十')) {
      result = result.substring(1);
    }

    // 移除末尾的零
    return result.replace(/零+$/, '');
  }

  // 生成 0-10000 的映射
  for (let i = 0; i <= 10000; i++) {
    if (i === 10000) {
      map[i] = '一万';
    } else {
      map[i] = convertNumber(i);
    }
  }

  return map;
}
export default generateChineseNumberMap
// // 使用示例
// const chineseNumberMap = generateChineseNumberMap();
// console.log(chineseNumberMap[0]);  // 输出: "零"
// console.log(chineseNumberMap[10]); // 输出: "十"
// console.log(chineseNumberMap[110]); // 输出: "一百一十"
// console.log(chineseNumberMap[10000]); // 输出: "一万"
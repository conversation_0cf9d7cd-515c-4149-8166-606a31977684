.box {
  position: relative;
  width: 100%;
  padding: 22px 0 10px 0;
  margin: 10px 0 24px 0;
  // padding: 10px;
  border: 1px solid #D8D8D8;
  border-radius: 5px;
  .title {
    position: absolute;
    top: -20px;
    left: 20px;
    padding: 0 13px;
    background: white;
  }
  :global .ant-form-item {
    display: inline-block;
    margin: 0px 30px 0 10px;
  }
  :global .ant-modal-content .ant-modal-body {
    padding:5px 15px  0;
  }
  :global label.ant-checkbox-wrapper.ant-checkbox-wrapper-in-form-item{
    margin-top: 5px;
    width: 156px;
  }
  :global .ant-radio-wrapper {
    line-height: 26px;
  }
  :global .ant-input-affix-wrapper > .ant-input {
    height: 26px;
  }
  :global div.ant-input-affix-wrapper {
    margin: 0 10px;
  }
  :global .ant-select {
    display: inline-block;
    width: 156px;
  }
  :global .ant-select-single.ant-select-sm:not(.ant-select-customize-input) .ant-select-selector{
    height: 26px;
  }
  :global .ant-input-suffix{
    height: 26px;
  }
  // :global div#checkValue.ant-checkbox-group{
  //   margin-top: 15px;
  //   margin-bottom: -10px;
  // }
}
import NiceModal from '@ebay/nice-modal-react';
import {
  type BaseModalProps,
  XlbButton,
  XlbModal,
  XlbTable,
} from '@xlb/components';
import { FC, useEffect, useState } from 'react';
import styles from '../index.less';

interface Props extends Pick<BaseModalProps, 'title'> {
  item: any;
}

const ViewDeliveryUnit: FC<Props> = ({ item }) => {
  const modal = NiceModal.useModal();
  const [rowData, setRowData] = useState<any[]>([]);

  useEffect(() => {
    if (modal?.visible) {
      setRowData(item?.detail_units);
    }
  }, [modal.visible]);

  return (
    <div>
      <XlbModal
        title={'查看配送单位'}
        open={modal.visible}
        width={750}
        onCancel={() => modal.hide()}
        isConfirm={false}
        footerExtroContent={[
          <XlbButton onClick={() => modal.hide()} key={1}>
            确定
          </XlbButton>,
        ]}
      >
        <div className={rowData?.length ? styles.table_box : ''}>
          <XlbTable
            style={{
              height: 288,
            }}
            dataSource={rowData}
            columns={[
              {
                name: '配送中心门店',
                code: 'store_name',
                width: 160,
                align: 'center',
              },
              {
                name: '配送单位',
                code: 'distribution_unit',
                width: 160,
                align: 'left',
                render: (text, r) => {
                  return (
                    <div style={{ color: item.item_delivery_unit !== text ? 'red' : '' }}>
                      {text}
                    </div>
                  );
                },
              },
              {
                name: '基本单位',
                code: 'unit',
                width: 120,
                align: 'left',
              },
              {
                name: '配送换算率',
                code: 'distribution_ratio',
                width: 120,
                align: 'left',
              },
              {
                name: '是否拆零',
                code: 'demolition',
                width: 120,
                align: 'left',
                render: (text) => (text ? '是' : '否'),
              },
            ]}
            keepDataSource={true}
            pageSize={100}
            total={rowData?.length}
          ></XlbTable>
        </div>
      </XlbModal>
    </div>
  );
};
export default NiceModal.create(ViewDeliveryUnit);
import type { XlbTableColumnProps } from '@xlb/components'

export const tableColumn: XlbTableColumnProps<any>[] = [
  {
    name: '序号',
    code: '_index',
    width: 60,
    align: 'center'
  },
  {
    name: '退货申请原因名称',
    code: 'name',
    width: 160,
    features: { sortable: true, details: true }
  },
  {
    name: '附件必传',
    code: 'file_required',
    width: 120,
    render: (value) => {
      return (
        <div>
          <span>{value ? '是' : '否'}</span>
        </div>
      )
    }
  }
]

export interface ReasonFindResDTO {
  /**
   * @name id
   */
  id?: number

  /**
   * @name 原因名字
   */
  name?: string
}

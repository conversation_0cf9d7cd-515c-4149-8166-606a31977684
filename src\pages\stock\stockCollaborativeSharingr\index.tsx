import { hasAuth } from '@/utils';
import { wujieBus } from '@/wujie/utils';
import NiceModal from '@ebay/nice-modal-react';
import {
  XlbBasicForm,
  XlbButton,
  XlbForm,
  XlbIcon,
  XlbImportModal,
  XlbMessage,
  XlbTipsModal,
} from '@xlb/components';
import XlbPageContainer from '@xlb/components/dist/lowcodes/XlbPageContainer';
import addModel from './components/addModel/index';
import { searchFormList, tableColumn } from './data';
import Api from './server';
const { Table, ToolBtn, SearchForm } = XlbPageContainer;

const stockCollaborativeSharingr = () => {
  const [form] = XlbBasicForm.useForm();

  const prevPost = () => {
    const formData = form.getFieldsValue();
    return {
      ...formData,
    };
  };

  const handleDelete = async (selectedRowKeys: any, fetchData: any) => {
    await XlbTipsModal({
      isConfirm: true,
      isCancel: true,
      tips: `已选择${selectedRowKeys?.length}条数据，确定要删除吗？`,
      onOk: async () => {
        const res = await Api.deleteData({ ids: selectedRowKeys });
        if (res?.code === 0) {
          fetchData();
          XlbMessage.success('删除成功');
        }
      },
    });
  };
  const exportItem = async (setIsLoading: any, e: any) => {
    setIsLoading(true);
    const res = await Api.exportDetail({
      ...prevPost(),
    });
    if (res.code == 0) {
      wujieBus?.$emit('xlb_erp-event', {
        code: 'downloadEnd',
        target: e,
      });
      XlbMessage.success('导出受理成功，请前往下载中心查看');
    }
    setIsLoading(false);
  };
  // 导入门店
  const handleImport = async (fetchData: any) => {
    const res = await XlbImportModal({
      importUrl: `${process.env.BASE_URL}/erp/hxl.erp.stockcargoownershares.import`,
      templateUrl: `${process.env.BASE_URL}/erp/hxl.erp.stockcargoownershares.template.download`,
      templateName: '库存货主共享导入模版',
      callback: (res: any) => {
        if (res.code !== 0) return;
        fetchData();
      },
    });
  };
  return (
    <XlbPageContainer
      url={'/erp/hxl.erp.stockcargoownershares.page'}
      tableColumn={tableColumn}
      immediatePost={true}
      prevPost={prevPost}
    >
      <SearchForm>
        <XlbForm form={form} formList={searchFormList} isHideDate />
      </SearchForm>
      <ToolBtn>
        {({ fetchData, dataSource, loading, setLoading, selectRowKeys }) => {
          return (
            <XlbButton.Group>
              <XlbButton
                type="primary"
                onClick={() => fetchData()}
                icon={<XlbIcon name="sousuo" />}
              >
                查询
              </XlbButton>
              {hasAuth(['库存协同共享', '编辑']) && (
                <XlbButton
                  type="primary"
                  onClick={() => {
                    NiceModal.show(NiceModal.create(addModel), {
                      fetchData: fetchData,
                    });
                  }}
                  icon={<XlbIcon name="jia" />}
                >
                  新增
                </XlbButton>
              )}
              {hasAuth(['库存协同共享', '删除']) && (
                <XlbButton
                  type="primary"
                  onClick={() => {
                    handleDelete(selectRowKeys, fetchData);
                  }}
                  disabled={!selectRowKeys?.length || loading}
                  icon={<XlbIcon name="shanchu" />}
                >
                  删除
                </XlbButton>
              )}
              {hasAuth(['库存协同共享', '导入']) && (
                <XlbButton
                  type="primary"
                  onClick={() => handleImport(fetchData)}
                  icon={<XlbIcon name="daoru" />}
                >
                  导入
                </XlbButton>
              )}
              {hasAuth(['库存协同共享', '导出']) && (
                <XlbButton
                  type="primary"
                  label="导出"
                  disabled={!dataSource?.length || loading}
                  onClick={(e) => exportItem(setLoading, e)}
                  icon={<XlbIcon name="daochu" />}
                >
                  导出
                </XlbButton>
              )}
            </XlbButton.Group>
          );
        }}
      </ToolBtn>
      <Table primaryKey="id" selectMode="multiple" />
    </XlbPageContainer>
  );
};

export default stockCollaborativeSharingr;

import { useBaseParams } from '@/hooks/useBaseParams';
import { hasAuth, normalizeToCommaSeparated } from '@/utils/kit';
import { LStorage } from '@/utils/storage';
import toFixed from '@/utils/toFixed';
import { wujieBus } from '@/wujie/utils';
import { history } from '@@/core/history';
import {
  SearchFormType,
  XlbBasicForm,
  XlbButton,
  XlbForm,
  XlbIcon,
  XlbMessage,
} from '@xlb/components';
import XlbPageContainer, {
  XlbPageContainerRef,
} from '@xlb/components/dist/lowcodes/XlbPageContainer';
import { Tabs, Tooltip } from 'antd';
import dayjs from 'dayjs';
import { useEffect, useRef, useState } from 'react';
import { v4 as uuidv4 } from 'uuid';
import {
  ALL_ORDER_STATE_LIST,
  dddhl,
  ddsphz,
  ddzt,
  djhddmx,
  formListData,
  gqdd,
  ORDER_FILTER_LIST,
  ORDER_STATE_FILTER_LIST,
  receive_Type,
} from './data';
import styles from './index.less';
import Api from './server';

const { Table, ToolBtn, SearchForm } = XlbPageContainer;

const OrderWatch = () => {
  const userInfo = LStorage.get('userInfo');
  const pageRef = useRef<XlbPageContainerRef>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  // searchFormList
  const { TabPane } = Tabs;
  const [form] = XlbBasicForm.useForm();
  const [formList, setFormList] = useState<SearchFormType[]>(formListData);
  const { enable_organization, enable_cargo_owner } = useBaseParams(
    (state) => state,
  );
  // Table
  const [itemArr, setItemArr] = useState(ddzt);
  const [itemArrDdzt, setItemArrDdzt] = useState();
  const [itemArrDddhl, setItemArrDddhl] = useState();
  const [itemArrGqdd, setItemArrGqdd] = useState();
  const [itemArrDdsphz, setItemArrDdsphz] = useState();
  const [itemArrDjhddmx, setItemArrDjhddmx] = useState();
  const [itemArrFooter, setItemArrFooter] = useState<any[]>([]);
  const [itemArrDdztFooter, setItemArrDdztFooter] = useState<any[]>([]);
  const [itemArrDdsphzFooter, setItemArrDdsphzFooter] = useState<any[]>([]);
  // Tab
  const record = history.location.state as any;
  const [tabsKey, setTabsKey] = useState<string>(
    record?.key ? record?.key : 'ddzt',
  );

  const calculateTotal = (data: any, field: string, type = 'QUANTITY') => {
    return (
      toFixed(
        data.reduce((sum: any, v: any) => sum + Number(v[field]), 0),
        type == 'QUANTITY' ? type : 'MONEY',
      ) || 0
    );
  };

  const onValuesChange = (changeValues: any, allValues: any) => {
    if (changeValues?.org_ids?.length) {
      form.setFieldsValue({
        store_ids: [],
      });
    }
  };

  // 切换表格数据
  const onChangeKey = (key: any) => {
    setTabsKey(key);
    // 根据tab切换formList
    formList.find((v) => v.label === '过滤')!.hidden =
      key == 'gqdd' || key == 'djhddmx' ? true : false;
    formList.find((v) => v.label === '查询单位')!.hidden = !!(
      key == 'gqdd' ||
      key == 'ddsphz' ||
      key == 'djhddmx'
    );
    formList.find((v) => v.label === '商品部门')!.hidden =
      key == 'ddsphz' ? false : true;
    formList.find((v) => v.label === '时间类型')!.hidden =
      key !== 'ddzt' && key !== 'djhddmx';
    formList.find((v) => v.label === '仓库')!.hidden = key !== 'djhddmx';
    formList.find((v) => v.label === '是否过期')!.hidden = key == 'djhddmx';
    formList.find((v) => v.label === '订单状态')!.hidden = key !== 'ddzt';
    formList.find((v) => v.label === '过滤')!.options =
      key === 'ddzt' ? ORDER_STATE_FILTER_LIST : ORDER_FILTER_LIST;
    setFormList([...formList]);
    //根据tab切换列表
    let tableArr: any = [];
    switch (key) {
      case 'ddzt':
        tableArr = ddzt;
        pageRef.current?.setDataSource(itemArrDdzt as any);
        setItemArrFooter([...itemArrDdztFooter]);
        break;
      case 'dddhl':
        tableArr = dddhl;
        pageRef.current?.setDataSource(itemArrDddhl as any);
        setItemArrFooter([]);
        break;
      case 'gqdd':
        tableArr = gqdd;
        pageRef.current?.setDataSource(itemArrGqdd as any);
        setItemArrFooter([]);
        break;
      case 'ddsphz':
        tableArr = ddsphz;
        pageRef.current?.setDataSource(itemArrDdsphz as any);
        setItemArrFooter([...itemArrDdsphzFooter]);
        break;
      case 'djhddmx':
        tableArr = djhddmx;
        pageRef.current?.setDataSource(itemArrDjhddmx as any);
        setItemArrFooter([]);
        break;
      default:
        tableArr = ddzt;
        break;
    }
    tableArr.map((v: any) => itemArrTotalRender(v));
    setItemArr([...tableArr]);
  };
  // 处理表格
  const itemArrTotalRender = (item: any) => {
    switch (item.code) {
      case 'item_code':
        item.render = (value: any, record: any) => {
          return record.item_code ? (
            <div className="info overwidth">{value}</div>
          ) : (
            <div className="info overwidth">{record.fid}</div>
          );
        };
        break;
      case 'receive_state':
        item.render = (value: any) => {
          const item = receive_Type.find((v) => v.value === value);
          return <div className="info overwidth">{item ? item.label : ''}</div>;
        };
        break;
      case 'out_quantity':
      case 'in_quantity':
      case 'quantity':
      case 'receive_quantity':
      case 'un_receive_quantity':
      case 'present_quantity':
      case 'present_receive_quantity':
        return (item.render = (value: any) => (
          <div className="info overwidth">
            {value ? Number(value).toFixed(3) : '0.000'}
          </div>
        ));
      case 'receive_rate':
        return (item.render = (value: any, record: any) => (
          <div className="info overwidth">
            {tabsKey === 'ddsphz' && record?.index === '合计'
              ? ''
              : value
                ? Number(value).toFixed(2) + '%'
                : '0.00%'}
          </div>
        ));
      case 'money':
      case 'receive_money':
      case 'un_receive_money':
        return (item.render = (value: any) => (
          <div className="info overwidth">
            {hasAuth(['订单监控/采购价', '查询'])
              ? value
                ? Number(value).toFixed(2)
                : '0.00'
              : '****'}
          </div>
        ));
      case 'out_money':
      case 'out_cost':
      case 'in_money':
      case 'in_cost':
      case 'out_money_total':
      case 'out_cost_total':
      case 'delivery_gross_profit':
        item.render = (value: any) => (
          <div className="info overwidth">
            {value ? Number(value).toFixed(2) : '0.00'}
          </div>
        );
      case 'delivery_gross_profit_rate':
        item.render = (value: any) => (
          <div className="info overwidth">
            {value ? Number(value).toFixed(2) + '%' : '0.00%'}
          </div>
        );
      case 'receive_present_rate':
      case 'receive_money_rate':
        item.render = (value: any, record: any) => (
          <div className="info overwidth">
            {record?.index === '合计'
              ? ''
              : value
                ? Number(value).toFixed(2) + '%'
                : '0.00%'}
          </div>
        );
      case 'appoint_time':
        item.render = (value: any, record: any) => (
          <Tooltip title={value}>{value}</Tooltip>
        );
    }
    return item;
  };
  // 处理数据
  const prevPost = () => {
    let storehouse_ids = form.getFieldValue('storehouse_ids');
    const hasWarehouse = formList.some((v) => v.label === '仓库' && !v.hidden);
    let storehouseIds: any = '';
    if (Array.isArray(storehouse_ids)) {
      storehouseIds = storehouse_ids;
    } else if (storehouse_ids) {
      storehouseIds = [storehouse_ids];
    } else {
      storehouseIds = [];
    }
    const { ...rest } = form.getFieldsValue(true);
    const sTime =
      dayjs(form.getFieldValue('compactDatePicker')?.[0]).format('YYYY-MM-DD') +
      ' 00:00:00';
    const eTime =
      dayjs(form.getFieldValue('compactDatePicker')?.[1]).format('YYYY-MM-DD') +
      ' 23:59:59';
    const data = {
      ...rest,
      create_date:
        (tabsKey === 'ddzt' || tabsKey === 'djhddmx') &&
        form.getFieldValue('time_type') === 'create_date'
          ? [sTime, eTime]
          : null,
      audit_date:
        tabsKey === 'ddzt' || tabsKey === 'djhddmx'
          ? form.getFieldValue('time_type') === 'audit_date'
            ? [sTime, eTime]
            : null
          : [sTime, eTime],
      storehouse_ids:
        storehouseIds?.length && hasWarehouse ? storehouseIds : null,
      unit_type:
        tabsKey === 'ddsphz' ? 'PURCHASE' : form.getFieldValue('unit_type'),
      item_dept_ids:
        tabsKey === 'ddsphz' && form.getFieldValue('item_dept_ids')
          ? form.getFieldValue('item_dept_ids')
          : null,
      only_show_un_receive_item:
        tabsKey != 'gqdd' && tabsKey != 'djhddmx'
          ? form
              .getFieldValue('check_filter')
              ?.includes('only_show_un_receive_item') || null
          : null,
      states:
        tabsKey === 'ddzt' && form.getFieldValue('order_state')
          ? [form.getFieldValue('order_state')]
          : null,
      invalid_equal_audit_date:
        tabsKey === 'ddzt'
          ? form
              .getFieldValue('check_filter')
              ?.includes('invalid_equal_audit_date') || null
          : null,
      fid: normalizeToCommaSeparated(form.getFieldValue('fid')),
    };
    return data;
  };
  const afterPost = (data: any) => {
    setFooterDataTmp(data);
    // 处理 groupTitle
    function calculateTotalAndFixed(items: any[], key: string) {
      return toFixed(
        items.reduce((sum: any, v: any) => sum + Number(v[key]), 0),
        'MONEY',
      );
    }

    const res: any = data.content.map((v: any, index: number) => {
      if (tabsKey == 'ddzt') {
        v.id = v.order_id;
        v.groupTitle = `单据号:${v.order_id} | 订单门店：${v.store_name} | 供应商：${
          v.supplier_name
        } | 收货仓库：${v.storehouse_name}|${enable_cargo_owner ? `货主：${v.cargo_owner_name || '--'}` : null} | 收货状态：${
          receive_Type.find((item) => item.value === v.state)?.label
        } | 下单日期：${v.payment_time || ''}
            | 到货日期：${v.arrival_time?.substring(0, 10) || ''}
            | 实际到货日期：${v.actual_receive_date || ''}
            | 组织名称：${v.org_name || ''}
            | 创建人：${v.create_by || ''}
            | 订单状态：${
              ALL_ORDER_STATE_LIST.find(
                (item) => item.value === v.purchase_state,
              )?.label || ''
            }
            | 审核日期：${v.audit_date || ''}
            | 作废日期：${v.invalid_date || ''}
            | 订单失效日：${v.delivery_deadline?.substring(0, 10) || ''}
            | 交货日期：${v.purchase_deadline?.substring(0, 10) || ''}`;
      }
      if (tabsKey == 'dddhl') {
        v.id = uuidv4();
        v.groupTitle = `供应商：${v.supplier_name} | 平均到货率：${v.average_arrival_rate}% `;
      }
      v.children = v.items || [];
      return v;
    });
    // 处理小计
    const list = res.map((item: any) => ({
      ...item,
      ...(item.children.length > 0 && {
        children: [
          ...item.children,
          {
            item_code: '小计',
            money: toFixed(
              calculateTotalAndFixed(item.items, 'money'),
              'MONEY',
            ),
            receive_money: toFixed(
              calculateTotalAndFixed(item.items, 'receive_money'),
              'MONEY',
            ),
            un_receive_money: toFixed(
              calculateTotalAndFixed(item.items, 'un_receive_money'),
              'MONEY',
            ),
            quantity: calculateTotal(item.items, 'quantity'),
            receive_quantity: calculateTotal(item.items, 'receive_quantity'),
            un_receive_quantity: calculateTotal(
              item.items,
              'un_receive_quantity',
            ),
            receive_rate:
              calculateTotal(item.items, 'receive_rate') / item.items.length,
            receive_count: calculateTotal(item.items, 'receive_count'),
            present_quantity: calculateTotal(item.items, 'present_quantity'),
            present_receive_quantity: calculateTotal(
              item.items,
              'present_receive_quantity',
            ),
          },
        ],
      }),
    }));
    // 不同tab的table缓存
    if (tabsKey == 'ddzt') {
      setItemArrDdzt([...list]);
      return list;
    } else {
      switch (tabsKey) {
        case 'dddhl':
          setItemArrDddhl([...res]);
          break;
        case 'gqdd':
          setItemArrGqdd([...res]);
          break;
        case 'ddsphz':
          setItemArrDdsphz([...res]);
          break;
        case 'djhddmx':
          setItemArrDjhddmx([...res]);
          break;
        default:
          break;
      }
      return res;
    }
  };
  // 获取数据
  const pageContainerUrl = () => {
    let res: any;
    switch (tabsKey) {
      case 'ddzt':
        res = Api.urlInfoList.getorderstate;
        break;
      case 'dddhl':
        res = Api.urlInfoList.getOrderarrivalrate;
        break;
      case 'gqdd':
        res = Api.urlInfoList.getExpireorder;
        break;
      case 'ddsphz':
        res = Api.urlInfoList.getOrderdetail;
        break;
      case 'djhddmx':
        res = Api.urlInfoList.getPrepareOrder;
        break;
      default:
        break;
    }
    return res;
  };
  const queryData = () => {
    setIsLoading(true);
    pageRef.current?.fetchData();
    setIsLoading(false);
  };
  // 合计
  const setFooterDataTmp = (data: any): Record<string, any>[] => {
    let _footerData: Record<string, any> = {};
    if (tabsKey == 'ddzt') {
      const rowAll = data.content.map((item: any) => item.items).flat();
      _footerData = {
        item_code: '总计',
        money: toFixed(
          rowAll.reduce((sum: any, v: any) => sum + Number(v.money), 0),
          'MONEY',
        ),
        receive_money: toFixed(
          rowAll.reduce((sum: any, v: any) => sum + Number(v.receive_money), 0),
          'MONEY',
        ),
        un_receive_money: toFixed(
          rowAll.reduce(
            (sum: any, v: any) => sum + Number(v.un_receive_money),
            0,
          ),
          'MONEY',
        ),
        quantity: calculateTotal(rowAll, 'quantity'),
        receive_quantity: calculateTotal(rowAll, 'receive_quantity'),
        un_receive_quantity: calculateTotal(rowAll, 'un_receive_quantity'),
        receive_rate: '0.00%',
        receive_count: calculateTotal(rowAll, 'receive_count'),
        present_quantity: calculateTotal(rowAll, 'present_quantity'),
        present_receive_quantity: calculateTotal(
          rowAll,
          'present_receive_quantity',
        ),
      };
      // 计算接收率
      const receiveRate = (
        (Number(_footerData.receive_quantity) / Number(_footerData.quantity)) *
        100
      ).toFixed(2);
      _footerData.receive_rate = receiveRate;
      setItemArrDdztFooter([_footerData]);
    } else if (tabsKey === 'ddsphz') {
      _footerData = {
        _index: '合计',
        money: toFixed(data.money_total, 'MONEY'),
        receive_money: toFixed(data.receive_money_total, 'MONEY'),
        un_receive_money: toFixed(data.un_receive_money_total, 'MONEY'),
        quantity: toFixed(data.quantity_total, 'QUANTITY'),
        purchase_present_quantity: toFixed(
          data.purchase_present_quantity_total,
          'QUANTITY',
        ),
        receive_present_quantity: toFixed(
          data.receive_present_quantity_total,
          'QUANTITY',
        ),
        receive_quantity: toFixed(data.receive_quantity_total, 'QUANTITY'),
        un_receive_present_quantity: toFixed(
          data.un_receive_present_quantity_total,
          'QUANTITY',
        ),
        un_receive_quantity: toFixed(
          data.un_receive_quantity_total,
          'QUANTITY',
        ),
      };
      setItemArrDdsphzFooter([_footerData]);
    }
    if (Object.keys(_footerData).length) {
      setItemArrFooter([_footerData]);
    }
  };
  // 导出
  const exportItem = async (e: any) => {
    setIsLoading(true);
    const Parameter = prevPost();
    // ddsphz走下载中心，其余前端自行下载
    if (tabsKey == 'ddsphz') {
      await Api.orderdetailExport({ ...Parameter });
      wujieBus?.$emit('xlb_erp-event', { code: 'downloadEnd', target: e });
      XlbMessage.success('导出受理成功，请前往下载中心查看');
    } else {
      let url: string = '';
      switch (tabsKey) {
        case 'ddzt':
          url = '/erp/hxl.erp.purchasereport.purchasemonitor.orderstate.export';
          break;
        case 'dddhl':
          url =
            '/erp/hxl.erp.purchasereport.purchasemonitor.orderarrivalrate.export';
          break;
        case 'gqdd':
          url =
            '/erp/hxl.erp.purchasereport.purchasemonitor.expireorder.export';
          break;
        case 'djhddmx':
          url = '/scm/hxl.scm.prepareorder.purchaseandshippingorder.export';
          break;
        default:
          break;
      }

      let res = await Api.commonExport(url, { ...Parameter });

      if (res.code == 0) {
        wujieBus?.$emit('xlb_erp-event', { code: 'downloadEnd', target: e });
        XlbMessage.success('导出受理成功，请前往下载中心查看');
      }
    }
    setIsLoading(false);
  };

  itemArr.map((v) => itemArrTotalRender(v));
  // 跳转页面进入处理
  useEffect(() => {
    if (record && record.condition && record.key) {
      form.setFieldsValue({
        ...record.condition,
        compactDatePicker: [
          record.condition.audit_date
            ? dayjs(record.condition.audit_date[0])
            : dayjs(),
          record.condition.audit_date
            ? dayjs(record.condition.audit_date[1])
            : dayjs(),
        ],
        supplier_ids: record.condition.supplier_ids,
        fids: record.condition.fids || null,
        only_show_receive_mismatch_item:
          record.condition.only_show_receive_mismatch_item,
      });
      setTabsKey(record.key);
    }
  }, [record]);

  return (
    <XlbPageContainer
      ref={pageRef}
      url={pageContainerUrl()}
      immediatePost={false}
      prevPost={prevPost}
      changeColumnAndResetDataSource={false}
      tableColumn={itemArr}
      afterPost={(data: any) => {
        return afterPost(data);
      }}
    >
      <ToolBtn showColumnsSetting>
        {(context) => {
          return (
            <XlbButton.Group>
              {hasAuth(['订单监控', '查询']) && (
                <XlbButton
                  label={'查询'}
                  type="primary"
                  disabled={isLoading}
                  onClick={() => queryData()}
                  icon={<XlbIcon name="sousuo" />}
                />
              )}
              {hasAuth(['订单监控', '导出']) && (
                <XlbButton
                  label={'导出'}
                  type="primary"
                  disabled={isLoading}
                  onClick={(e: any) => exportItem(e)}
                  icon={<XlbIcon name="daochu" />}
                />
              )}
            </XlbButton.Group>
          );
        }}
      </ToolBtn>

      <SearchForm>
        <XlbForm
          form={form}
          formList={formList.filter((v) => {
            if (!enable_organization && v.name == 'org_ids') {
              return false;
            } else if (!enable_cargo_owner && v.name == 'cargo_owner_ids') {
              return false;
            } else {
              return true;
            }
          })}
          initialValues={{
            time_desc: 0,
            time_type:
              tabsKey == 'ddzt' || tabsKey == 'djhddmx' ? 'create_date' : null,
            compactDatePicker: [dayjs(), dayjs()],
            unit_type: 'PURCHASE',
            category_level: 0,
          }}
          isHideDate={true}
          onValuesChange={onValuesChange}
          className={styles.check_filter_box_row}
        />
      </SearchForm>

      <div>
        <Tabs
          activeKey={tabsKey}
          className="contractTab"
          style={{ paddingLeft: '16px' }}
          onChange={(key: any) => onChangeKey(key)}
        >
          <TabPane tab="订单状态" key={'ddzt'}></TabPane>
          <TabPane tab="订单到货率" key={'dddhl'}></TabPane>
          <TabPane tab="过期订单" key={'gqdd'}></TabPane>
          <TabPane tab="订单商品汇总" key={'ddsphz'}></TabPane>
          <TabPane tab="待交货订单明细" key={'djhddmx'}></TabPane>
        </Tabs>
      </div>

      <Table
        footerDataSource={itemArrFooter}
        primaryKey={tabsKey == 'ddzt' || tabsKey == 'dddhl' ? 'id' : '_index'}
        rowGrouping={tabsKey == 'ddzt' || tabsKey == 'dddhl'}
      />
    </XlbPageContainer>
  );
};
export default OrderWatch;

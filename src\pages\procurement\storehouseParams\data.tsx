import { columnWidthEnum } from '@/data/common/constant';
import {
  XlbBasicForm,
  XlbSelect,
  XlbTooltip,
  type SearchFormType,
  type XlbTableColumnProps,
} from '@xlb/components';

export const timesType = [
  {
    label: '每日',
    value: 'DAILY',
  },
  {
    label: '每周',
    value: 'WEEKLY',
  },
  {
    label: '每月',
    value: 'MONTHLY',
  },
  {
    label: '单双号',
    value: 'ODD_EVEN',
  },
];

export const weekArrs = [
  { label: '星期一', value: 1 },
  { label: '星期二', value: 2 },
  { label: '星期三', value: 3 },
  { label: '星期四', value: 4 },
  { label: '星期五', value: 5 },
  { label: '星期六', value: 6 },
  { label: '星期天', value: 7 },
];

export const oddEvenArrs = [
  { label: '单号', value: 'odd' },
  { label: '双号', value: 'even' },
];

export const formList: SearchFormType[] = [
  {
    label: '供应商',
    name: 'supplier_ids',
    width: 270,
    type: 'inputDialog',
    dialogParams: {
      type: 'supplier',
      dataType: 'lists',
      isMultiple: true,
    },
  },
  {
    label: '配送中心',
    name: 'store_ids',
    type: 'inputDialog',
    dialogParams: {
      type: 'store',
      dataType: 'lists',
      isLeftColumn: true,
      isMultiple: true,
      primaryKey: 'id',
      data: {
        center_flag: true,
        virtual_house: false,
        enable_organization: false,
      },
    },
    fieldNames: {
      idKey: 'id',
      nameKey: 'store_name',
    },
    width: 200,
  },
  {
    label: '频次类型',
    name: 'frequency_type',
    type: 'select',
    options: timesType,
  },
  {
    label: '频次强校验',
    name: 'is_frequency_strict',
    type: 'select',
    options: [
      {
        label: '是',
        value: 1,
      },
      {
        label: '否',
        value: 0,
      },
    ],
  },
];

export const editFormList: (param: boolean) => SearchFormType[] = (
  isAdd: boolean,
) => [
  {
    label: '供应商',
    name: 'supplier_id',
    width: 260,
    disabled: !isAdd ? true : false,
    type: 'inputDialog',
    rules: [{ required: true, message: '供应商不能为空' }],
    dialogParams: {
      type: 'supplier',
      dataType: 'lists',
      isMultiple: false,
    },
  },
  {
    label: '配送中心',
    name: 'store_id',
    type: 'inputDialog',
    rules: [{ required: true, message: '配送中心不能为空' }],
    disabled: !isAdd ? true : false,
    dialogParams: {
      type: 'store',
      dataType: 'lists',
      isLeftColumn: true,
      isMultiple: false,
      primaryKey: 'id',
      data: {
        center_flag: true,
        virtual_house: false,
        enable_organization: false,
      },
    },
    fieldNames: {
      idKey: 'id',
      nameKey: 'store_name',
    },
    width: 260,
  },
  {
    label: '频次类型',
    name: 'frequency_type',
    type: 'select',
    rules: [{ required: true, message: '频次类型不能为空！' }],
    allowClear: true,
    options: timesType,
    width: 260,
    onChange: (e, form: any) => {
      form?.setFieldsValue({
        purchase_frequency: undefined,
      });
    },
  },
  {
    label: '采购频次',
    name: 'purchase_frequency',
    type: 'custom',
    render: (itemData, form: any) => {
      return (
        <XlbBasicForm.Item
          name={'purchase_frequency'}
          label="采购频次"
          dependencies={['frequency_type']}
          rules={[
            ({ getFieldValue }) => {
              return {
                required:
                  getFieldValue('frequency_type') !== 'DAILY' ? true : false,
                message: '采购频次不能为空！',
              };
            },
          ]}
        >
          <XlbSelect
            width={260}
            mode={
              form.getFieldValue('frequency_type') === 'ODD_EVEN'
                ? ''
                : 'multiple'
            }
            disabled={
              form.getFieldValue('frequency_type') === 'DAILY' ||
              !form.getFieldValue('frequency_type')
                ? true
                : false
            }
            options={
              form.getFieldValue('frequency_type') === 'WEEKLY'
                ? weekArrs
                : form.getFieldValue('frequency_type') === 'MONTHLY'
                  ? Array.from({ length: 31 }, (_, i) => ({
                      label: String(i + 1),
                      value: String(i + 1),
                    }))
                  : form.getFieldValue('frequency_type') === 'ODD_EVEN'
                    ? oddEvenArrs
                    : []
            }
          />
        </XlbBasicForm.Item>
      );
    },
  },
  {
    label: '频次强校验',
    name: 'is_frequency_strict',
    type: 'select',
    options: [
      {
        label: '是',
        value: 1,
      },
      {
        label: '否',
        value: 0,
      },
    ],
    rules: [{ required: true, message: '频次强校验不能为空' }],
    allowClear: false,
    width: 260,
  },
  {
    label: '预约白名单',
    name: 'reservation_whitelist',
    type: 'select',
    options: [
      {
        label: '是',
        value: 1,
      },
      {
        label: '否',
        value: 0,
      },
    ],
    rules: [{ required: true, message: '预约白名单不能为空' }],
    allowClear: false,
    width: 260,
  },
  {
    label: '下单白名单',
    name: 'order_whitelist',
    type: 'select',
    options: [
      {
        label: '是',
        value: 1,
      },
      {
        label: '否',
        value: 0,
      },
    ],
    rules: [{ required: true, message: '下单白名单不能为空' }],
    allowClear: false,
    width: 260,
  },
];

export const tableList: XlbTableColumnProps<any>[] = [
  {
    name: '序号',
    code: '_index',
    width: columnWidthEnum.INDEX,
    align: 'center',
  },
  {
    name: '配送中心',
    code: 'store_name',
    width: 160,
    align: 'center',
  },
  {
    name: '供应商代码',
    code: 'supplier_code',
    width: 120,
    align: 'center',
  },
  {
    name: '供应商',
    code: 'supplier_name',
    width: 120,
    align: 'center',
  },
  {
    name: '频次类型',
    code: 'frequency_type',
    width: 120,
    align: 'center',
    render: (text) => {
      const item = timesType?.find((v) => v?.value === text);
      return <span>{item?.label}</span>;
    },
  },
  {
    name: '采购频次',
    code: 'purchase_frequency',
    width: 160,
    align: 'center',
    render: (text, record) => {
      const frequency_type = record?.frequency_type;
      let content: any = null;
      if (frequency_type === 'WEEKLY') {
        const labels = JSON.parse(text || '[]')
          ?.map((val) => {
            const item = weekArrs.find((w) => w.value === val);
            return item ? '每' + item.label : val;
          })
          .join(',');
        // return <span>{labels}</span>;
        content = labels;
      } else if (frequency_type === 'MONTHLY') {
        const labels = JSON.parse(text || '[]')?.join(',');
        content = `每月${labels}号`;
        // return <span>每月{labels}号</span>;
      } else if (frequency_type === 'ODD_EVEN') {
        const item = oddEvenArrs?.find((v) => v?.value === text);
        content = item?.label;
        // return <span>{item?.label}</span>;
      }
      return (
        <XlbTooltip title={content}>
          <span>{content}</span>
        </XlbTooltip>
      );
    },
  },
  {
    name: '频次强校验',
    code: 'is_frequency_strict',
    width: 100,
    align: 'center',
    render: (text) => {
      return <span>{text ? '是' : '否'}</span>;
    },
  },
  {
    name: '预约白名单',
    code: 'reservation_whitelist',
    width: 100,
    align: 'center',
    render: (text) => {
      return <span>{text ? '是' : '否'}</span>;
    },
  },
  {
    name: '下单白名单',
    code: 'order_whitelist',
    width: 100,
    align: 'center',
    render: (text) => {
      return <span>{text ? '是' : '否'}</span>;
    },
  },
  {
    name: '最近更新人',
    code: 'update_by',
    width: 120,
    align: 'center',
  },
  {
    name: '最近更新时间',
    code: 'update_time',
    width: 120,
    align: 'center',
  },
];

import React, { useState, useEffect, Fragment } from 'react'
import { Input, Select, Form, FormInstance } from 'antd'
import { XlbModal, BaseModalProps, XlbInputDialog, XlbSelect, XlbInput } from '@xlb/components'
import { SearchOutlined } from '@ant-design/icons'
import styles from '../index.less'

interface prosType extends Pick<BaseModalProps, 'visible'> {
  fid: string
  disabled: boolean
  handleCancel: (values: any) => void
  handleOk: (values: any) => void
  noticeTemplateList: any[]
  infoForm: FormInstance<any>
}

const BatchChange = (props: prosType) => {
  const { fid, disabled, visible, handleCancel, handleOk, noticeTemplateList, infoForm } = props
  const [form] = Form.useForm()
  const [storeModal, setStoreModal] = useState<any>({
    modalVisible: false, // 弹窗是否展示
    storeItem: {}, // 接收弹窗选中的值
    modalState: {
      isMultiple: true,
      modalType: 'item',
      value: 1
    }
  })

  // 弹窗确认
  const goodsSubmit = (list: any) => {
    form.setFieldsValue({
      notice_send_store_ids: list.map((v: any) => v.id),
      notice_send_store_names: list.map((v: any) => v.store_name).join(',')
    })
    setStoreModal({ ...storeModal, modalVisible: false })
  }

  const handleInitialValues = () => {
    const values = infoForm.getFieldsValue(true)
    const params = {
      notice_send_store_ids: [],
      notice_send_store_names: '',
      notice_template_id: values?.notice_template_id,
      notice_template_name: values?.notice_template_name,
      notice_title: values?.notice_title
    }
    if (!values?.notice_send_store_ids?.length) {
      params.notice_send_store_ids = values?.store_ids
      params.notice_send_store_names = values?.store_names
    } else {
      params.notice_send_store_ids = values?.notice_send_store_ids
      params.notice_send_store_names = values?.notice_send_store_names
    }
    form.setFieldsValue(params)
  }

  useEffect(() => {
    if (visible) {
      handleInitialValues()
    }
  }, [visible])

  return (
    <XlbModal
      title={'选择公告模板'}
      open={visible}
      onOk={() => form.submit()}
      onCancel={() => {
        handleCancel(
          !fid
            ? {
                notice_template_id: '',
                notice_template_name: '',
                notice_title: '',
                notice_send_store_ids: [],
                notice_send_store_names: ''
              }
            : {}
        )
      }}
      width={355}
      bodyStyle={{ padding: '15px 15px 0 15px' }}
      className={styles.checkTemplate}
    >
      <Form
        form={form}
        onFinish={() => {
          const values = form.getFieldsValue(true)
          handleOk(values)
        }}
        disabled={disabled}
      >
        <Form.Item
          name="notice_template_id"
          style={{ display: 'inline-block', marginLeft: '32px', marginBottom: 12 }}
          label="公告模版"
          rules={[{ required: true, message: '公告模板不能为空' }]}
        >
          <XlbSelect
            showSearch
            filterOption={(input, option) =>
              (`${option!.children ? option!.children.toString() : ''}` as unknown as string)
                .toLowerCase()
                .includes(input.toLowerCase())
            }
            style={{ width: '188px' }}
            onChange={(_val: any, option: any) => {
              form.setFieldsValue({
                notice_template_name: option?.children ?? '',
                notice_title: option?.children ?? ''
              })
            }}
            allowClear
          >
            {noticeTemplateList.map((item: any) => (
              <XlbSelect.Option key={item.id} value={item.id}>
                {item.label}
              </XlbSelect.Option>
            ))}
          </XlbSelect>
        </Form.Item>
        <Form.Item
          name="notice_title"
          style={{ display: 'inline-block', marginLeft: '32px', marginBottom: 12 }}
          label="公告名称"
          rules={[{ required: true, message: '公告名称不能为空' }]}
        >
          <XlbInput style={{ width: '188px' }} size="small" />
        </Form.Item>
        <Form.Item
          name="notice_send_store_ids"
          style={{ display: 'inline-block', marginLeft: '32px', marginBottom: 12 }}
          label="发送门店"
          rules={[{ required: true, message: '发送门店不能为空' }]}
        >
          {/* <Input
            readOnly
            suffix={<SearchOutlined style={{ color: 'rgb(102, 102, 102)' }} />}
            onClick={() => handleDialogClick(1)}
            size="small"
          /> */}
          <XlbInputDialog
            style={{ width: '156px' }}
            disabled={disabled}
            dialogParams={{
              type: 'store',
              isMultiple: true,
              showDialogByDisabled: true
            }}
            fieldNames={{
              idKey: 'id',
              nameKey: 'store_name'
            }}
          />
        </Form.Item>
      </Form>
    </XlbModal>
  )
}
export default BatchChange

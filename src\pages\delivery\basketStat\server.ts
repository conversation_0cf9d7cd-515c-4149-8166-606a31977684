// import { XlbFetch } from '@/utils/request'

import { XlbFetch } from '@xlb/utils';

// 获取汇总查询数据
export const getsummary = async (data: any) => {
  return await XlbFetch.post(
    '/erp/hxl.erp.deliveryreport.basketorder.summary.page',
    { ...data },
  );
};

// 获取按明细查询数据
export const getdetail = async (data: any) => {
  return await XlbFetch.post(
    '/erp/hxl.erp.deliveryreport.basketorder.detail.page',
    { ...data },
  );
};

// 获取中心筐合计
export const getbasket = async (data: any) => {
  return await XlbFetch.post(
    '/erp/hxl.erp.deliveryreport.basketorder.basket.page',
    { ...data },
  );
};

// 手动出入库
export const updateStock = async (data: any) => {
  return await XlbFetch.post('/erp/hxl.erp.basket.stock.update', { ...data });
};

// 手动出入库记录
export const updateStockLog = async (data: any) => {
  return await XlbFetch.post('/erp/hxl.erp.basket.stock.log', { ...data });
};
// 读取
export const readBasket = async (data: any) => {
  return await XlbFetch.post('/erp/hxl.erp.basketorder.read', { ...data });
};

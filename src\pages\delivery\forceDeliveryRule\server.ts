import { XlbFetch } from '@xlb/utils';

export default {
  // 获取规则详情
  read: async (data: any) => {
    return await XlbFetch.post('/erp/hxl.erp.forcedelivery.rule.read', data);
  },

  // 保存规则
  save: async (data: any) => {
    return await XlbFetch.post('/erp/hxl.erp.forcedelivery.rule.save', data);
  },

  // 更新规则
  update: async (data: any) => {
    return await XlbFetch.post('/erp/hxl.erp.forcedelivery.rule.update', data);
  },
  // 删除规则
  delete: async (data: any) => {
    return await XlbFetch.post('/erp/hxl.erp.forcedelivery.rule.delete', data);
  },

  // 查询规则/erp/hxl.erp.forcedelivery.rule.page
  rulePage: async (data: any) => {
    return await XlbFetch.post('/erp/hxl.erp.forcedelivery.rule.page', data);
  },

  // 排除新增
  saveExclude: async (data: any) => {
    return await XlbFetch.post('/erp/hxl.erp.forcedelivery.exclude.save', data);
  },

  // 排除删除
  deleteExclude: async (data: any) => {
    return await XlbFetch.post(
      '/erp/hxl.erp.forcedelivery.exclude.delete',
      data,
    );
  },

  // 排除查询
  excludePage: async (data: any) => {
    return await XlbFetch.post('/erp/hxl.erp.forcedelivery.exclude.page', data);
  },

  // 排除详情
  excludeRead: async (data: any) => {
    return await XlbFetch.post('/erp/hxl.erp.forcedelivery.exclude.read', data);
  },

  // 排除更新
  updateExclude: async (data: any) => {
    return await XlbFetch.post(
      '/erp/hxl.erp.forcedelivery.exclude.update',
      data,
    );
  },

  // scm新品删除
  scmDelete: async (data: any) => {
    return await XlbFetch.post(
      '/erp/hxl.erp.forcedelivery.scm.item.delete',
      data,
    );
  },

  // scm查询
  scmPage: async (data: any) => {
    return await XlbFetch.post(
      '/erp/hxl.erp.forcedelivery.scm.item.page',
      data,
    );
  },

  // scm更新
  scmUpdate: async (data: any) => {
    return await XlbFetch.post(
      '/erp/hxl.erp.forcedelivery.scm.item.update',
      data,
    );
  },

  // scm详情
  scmRead: async (data: any) => {
    return await XlbFetch.post(
      '/erp/hxl.erp.forcedelivery.scm.item.read',
      data,
    );
  },

  // 查询自动统配规则效果
  ruleItem: async (data: any) => {
    return await XlbFetch.post(
      '/erp/hxl.erp.forcedelivery.rule.item.find',
      data,
    );
  },

  // 查询自动统配规则适用门店
  ruleStore: async (data: any) => {
    return await XlbFetch.post(
      '/erp/hxl.erp.forcedelivery.rule.store.find',
      data,
    );
  },

  // 导出自动统配规则适用门店
  ruleStoreExport: async (data: any) => {
    return await XlbFetch.post(
      '/erp/hxl.erp.forcedelivery.rule.store.export',
      data,
    );
  },
};

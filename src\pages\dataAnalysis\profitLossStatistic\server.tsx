import { XlbFetch } from '@xlb/utils';

//  账号管理仓库查询
// export const getStock = async (data: any) => {
//   return await XlbFetch.post('/erp/hxl.erp.storehouse.store.find', { ...data });
// };

// 根据门店ids获取仓库信息
export const getStoreHouseList = async (data: any) => {
  return await XlbFetch.post('/erp/hxl.erp.storehouse.store.find', { ...data });
};

//  查询
export const getProfitLossData = async (data: any) => {
  return await XlbFetch.post(
    '/erp/hxl.erp.stockprofitlossreport.stocksummary.page',
    { ...data },
  );
};

// 导出
export const exportProfitLossData = async (data: any) => {
  return await XlbFetch.post(
    '/erp/hxl.erp.stockprofitlossreport.stocksummary.export',
    { ...data },
  );
};

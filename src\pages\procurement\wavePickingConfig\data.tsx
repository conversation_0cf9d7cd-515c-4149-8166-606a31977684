import { SearchFormType } from '@xlb/components';

export const ENABLE_LIST = [
  { label: '启用', value: 1 },
  { label: '禁用', value: 0 },
];

type GetForm = (customData: any) => SearchFormType[];
export const getFormList: GetForm = ({ storeList }) => [
  {
    label: '配送中心',
    name: 'store_ids',
    type: 'select',
    clear: true,
    multiple: true,
    check: true,
    options: storeList,
  },
  {
    label: '状态',
    name: 'enable',
    type: 'select',
    clear: true,
    check: true,
    options: ENABLE_LIST,
  },
  {
    label: '供应商',
    name: 'supplier_ids',
    type: 'inputDialog',
    allowClear: true,
    dialogParams: {
      type: 'supplier',
      dataType: 'lists',
      isLeftColumn: true,
      isMultiple: true,
      primaryKey: 'id',
      data: {
        enabled: true,
      },
    },
  },
];

export const itemFormlist: SearchFormType[] = [
  {
    label: '供应商',
    name: 'supplier_ids',
    type: 'inputDialog',
    allowClear: true,
    dialogParams: {
      type: 'supplier',
      dataType: 'lists',
      isLeftColumn: true,
      isMultiple: true,
      primaryKey: 'id',
      data: {
        enabled: true,
      },
    },
  },
  {
    label: '状态',
    name: 'enable',
    type: 'select',
    clear: true,
    check: true,
    options: ENABLE_LIST,
  },
];

import { type FC } from 'react'
import { tableColumn } from './data'
import { hasAuth } from '@/utils/kit'
import { ErpFieldKeyMap } from '@/data/common/fieldListConfig'
import { XlbProPageContainer } from '@xlb/components'

const ProForm: FC<{ title: string }> = () => {
  return (
    <XlbProPageContainer // 查询
      searchFieldProps={{
        formList: ['keyword']
      }}
      tableFieldProps={{
        url: '/erp-mdm/hxl.erp.userdept.find',
        tableColumn: tableColumn,
        selectMode: 'single',
        keepDataSource: false,
        showColumnsSetting: true,
        immediatePost: true
      }}
      deleteFieldProps={{
        name: '删除',
        url: hasAuth(['用户部门', '删除']) ? '/erp/hxl.erp.userdept.delete' : ''
      }}
      addFieldProps={{
        name: '新增',
        url: hasAuth(['用户部门', '编辑']) ? '/erp/hxl.erp.userdept.save' : '',
        beforePost: (data) => {
          return {
            ...data,
            leader: data?.leader && data?.leader[0]
          }
        }
      }}
      details={{
        primaryKey: 'id',
        mode: 'modal',
        width: 350,
        isCancel: true,
        title: (obj) => {
          return <div>{obj?.id ? '编辑' : '新增'}</div>
        },
        itemSpan: 24,
        formList: [
          {
            componentType: 'form',
            fieldProps: {
              formList: [
                {
                  id: ErpFieldKeyMap?.otherIncomeExpensesName,
                  label: '用户部门',
                  rules: [{ required: true, message: '用户部门不能为空' }],
                  fieldProps: {
                    width: '100%',
                    maxLength: 10
                  }
                },
                {
                  id: ErpFieldKeyMap?.erpLeader,
                  label: '部门负责人',
                  rules: [{ required: true, message: '部门负责人不能为空' }],
                  fieldProps: {
                    // width: 180
                  }
                }
              ]
            }
          }
        ],
        queryFieldProps: {
          url: '',
          params: (row: any) => {
            return { ...row }
          }
          // afterPost(data) {
          //   console.log(data,'data after')
          //   return {
          //     ...data,
          //     // leader: '111'
          //   }
          // }
        },
        saveFieldProps: {
          hidden: () => true
          // url: '/erp/hxl.erp.userdept.save',
          // beforePost: (data) => {
          //   return {
          //     ...data,
          //     leader: data?.leader && data?.leader[0]
          //   }
          // }
        },
        updateFieldProps: {
          url: '/erp/hxl.erp.userdept.update',
          beforePost: (data) => {
            return {
              ...data,
              leader: data?.leader && data?.leader[0]
            }
          }
        }
      }}
    />
  )
}

export default ProForm
import { useBaseParams } from '@/hooks/useBaseParams';
import { hasAuth } from '@/utils';
import { LStorage } from '@/utils/storage';
import { toFixed } from '@/utils/toFixed';
import { wujieBus } from '@/wujie/utils';
import NiceModal from '@ebay/nice-modal-react';
import {
  ContextState,
  SearchFormType,
  XlbBasicData,
  XlbBasicForm,
  XlbButton,
  XlbCheckbox,
  XlbDatePicker,
  XlbForm,
  XlbIcon,
  XlbInputNumber,
  XlbPageContainer,
  XlbRadio,
  XlbSelect,
  XlbTipsModal,
  type XlbTableColumnProps,
} from '@xlb/components';
import { useNavigation } from '@xlb/max';
import { safeMath } from '@xlb/utils';
import { message } from 'antd';
import dayjs from 'dayjs';
import { cloneDeep } from 'lodash-es';
import { FC, useEffect, useRef, useState } from 'react';
import ItemDetailModal from './components/itemDetailModal';
import PurchaseQuantityModal from './components/purchaseQuantityModal';
import StoreDetail from './components/storeDetail';
import {
  COMPARE_OPTIONS,
  CREATE_PRDER_TYPE,
  FILTER_CHECKBOX,
  FORM_LIST,
  IS_SHOW_OPTIONS,
  RESTOCK_GOODS_FILTER,
  SUGGEST_TYPE,
  SYMBOL_OPTIONS,
  TABLE_COLUMN,
} from './data';
import style from './index.less';
import {
  batchCreateItem,
  batchCreatePurcahseOrder,
  createPurcahseOrder,
  exportPurchaseReplenishAnalysis,
  purchaseParams,
  purchasereplenishanalysis,
  supplierrebatepresent,
} from './server';

const Index: FC = () => {
  // form
  const [form] = XlbBasicForm.useForm();
  const [isWarnFlag, setIsWarnFlag] = useState(false);
  const { enable_organization } = useBaseParams((state) => state);
  const [formList, setFormList] = useState<SearchFormType[]>(
    cloneDeep(FORM_LIST),
  );
  // 采购参数
  const [inQuantifyType, setInQuantifyType] = useState<number | null>(null);
  // 采购计划详情
  const getPurchasereplenishanalysis = async (record: any) => {
    const { store_id, storehouse_id, supplier_id } = record;
    if (!store_id) {
      message.error('门店不能为空');
      return;
    }
    if (!storehouse_id) {
      message.error('仓库不能为空');
      return;
    }
    const start_time = dayjs(form.getFieldValue('start_time')).format(
      'YYYY-MM-DD',
    );
    const end_time = dayjs(form.getFieldValue('end_time')).format('YYYY-MM-DD');
    const res = await purchasereplenishanalysis({
      item_id: record.item_id,
      store_id: store_id,
      storehouse_id,
      supplier_id,
      unit_type: form.getFieldValue('unit_type'),
      sale_date: [start_time, end_time],
    });
    if (res.code === 0) {
      NiceModal.show(ItemDetailModal, {
        // eslint-disable-next-line @typescript-eslint/no-use-before-define
        formatTableColumn,
        record,
        purchasere: {
          ...record,
          ...res.data,
        },
      });
    }
  };
  //赠品数据计算
  const formatQuantity = (record: any, result?: any) => {
    const res = result ? result?.data : record;
    const data = {
      ...form.getFieldsValue(true),
    };
    form.getFieldValue('common_filter')?.forEach((item: any) => {
      data[item] = true;
    });
    if (
      !data.enable_valid_days_add_purchase_quantity &&
      data.enable_valid_days_add_quantity
    ) {
      record.valid_days = Math.floor(
        safeMath.divide(
          safeMath.add(
            record.stock_quantity,
            safeMath.add(record.suggest_quantity, res.data.present_quantity),
          ),
          record.avg_sale_quantity,
        ),
      );
    }
    if (
      data.enable_valid_days_add_purchase_quantity &&
      data.enable_valid_days_add_quantity
    ) {
      record.valid_days = Math.floor(
        safeMath.divide(
          safeMath.add(
            record.stock_quantity,
            safeMath.add(
              record.purchase_quantity,
              safeMath.add(record.suggest_quantity, res.data.present_quantity),
            ),
          ),
          record.avg_sale_quantity,
        ),
      );
    }
  };
  const getSupplierrebatepresent = async (record: any) => {
    const { ratio, unit, item_id, supplier_id, org_id, quantity } = record;
    const res = await supplierrebatepresent({
      quantity,
      ratio,
      item_id,
      org_id,
      supplier_id,
      unit,
    });
    if (res.code === 0) {
      record.present_quantity = res.data.present_quantity || 0;
      if (res.data?.present_quantity > 0) {
        formatQuantity(record, res);
        message.success(`自动搭赠数量为${res.data.present_quantity}`);
      }
    }
  };

  // table format
  const paginRef = useRef<any>();
  const [tableColumn, setTableColumn] = useState<XlbTableColumnProps<any>[]>(
    cloneDeep(TABLE_COLUMN),
  );
  //表格数据
  const { navigate } = useNavigation();
  const tableDataRender = (item: any) => {
    switch (item.code) {
      case 'index':
        return {
          ...item,
          render: (value: any, _record: any, index: any) => {
            const { pageNum, pageSize } = paginRef.current;
            return value ? value : (pageNum - 1) * pageSize + index.index + 1;
          },
        };
      case 'item_name':
        return {
          ...item,
          render: (value: any, record: any) => (
            <div className="overwidth">
              <span
                className="link cursors"
                onClick={(e) => {
                  e.stopPropagation();
                  getPurchasereplenishanalysis(record);
                }}
              >
                {value}
              </span>
            </div>
          ),
        };
      case 'supplier_name':
        return {
          ...item,
          render: (_value: any, record: any) => {
            return record._click ? (
              <XlbSelect
                width={200}
                defaultValue={record.supplier_id}
                onChange={(id: any) => {
                  const selectItem = record.supplier_details.find(
                    (item: any) => item.supplier_id === id,
                  );
                  record.supplier_id = selectItem?.supplier_id;
                  record.supplier_name = selectItem?.supplier_name;
                  record.purchase_period = selectItem?.purchase_period;
                  record.safe_days = selectItem?.safe_days;
                }}
                onClick={(e) => e.stopPropagation()}
              >
                {record.supplier_details.map((v: any) => (
                  <XlbSelect.Option key={v.supplier_id} value={v.supplier_id}>
                    {v.supplier_name}
                  </XlbSelect.Option>
                ))}
              </XlbSelect>
            ) : (
              record.supplier_name
            );
          },
        };
      case 'present_quantity':
      case 'quantity':
        return {
          ...item,
          render: (value: any, record: any) => {
            const defaultValue = value || 0;
            return record._click ? (
              <XlbInputNumber
                controls={false}
                precision={3}
                min={0}
                max={999999999.999}
                style={{ width: '100%' }}
                onClick={(e) => e.stopPropagation()}
                size="small"
                defaultValue={defaultValue}
                onFocus={(e) => e.target.select()}
                onBlur={(e) => {
                  const value: number = Number(e?.target?.value) || 0;
                  if (
                    value > 0 &&
                    record?.supplier_name &&
                    item.code === 'quantity'
                  ) {
                    getSupplierrebatepresent(record);
                  } else {
                    formatQuantity(record);
                  }
                }}
                onChange={(e) => (record[item.code] = e)}
              />
            ) : (
              Number(defaultValue).toFixed(3)
            );
          },
        };

      case 'point':
      case 'suggest_quantity':
      case 'last_sale_quantity':
      case 'force_transfer_quantity':
      case 'avg_sale_quantity':
      case 'month_ago_sale_quantity':
      case 'two_month_ago_sale_quantity':
      case 'three_month_ago_sale_quantity':
      case 'week_ago_sale_quantity':
      case 'two_week_ago_sale_quantity':
      case 'three_week_ago_sale_quantity':
      case 'stock_quantity':
        return {
          ...item,
          render: (value: any) =>
            toFixed(value, 'QUANTITY', item.code === 'stock_quantity'),
        };
      case 'purchase_price':
        return {
          ...item,
          render: (value: any, record: any) =>
            record?.index === '勾选合计'
              ? null
              : hasAuth(['采购补货分析/采购价', '查询'])
                ? toFixed(Number(value), 'PRICE')
                : '****',
        };
      case 'item_sale_price':
        return {
          ...item,
          render: (value: any, record: any) =>
            record?.index === '勾选合计'
              ? null
              : hasAuth(['采购补货分析/零售价', '查询'])
                ? toFixed(Number(value), 'PRICE')
                : '****',
        };
      case 'purchase_quantity':
        return {
          ...item,
          render: (value: any, record: any) =>
            record?.index === '勾选合计' ? null : (
              <div
                className="link overwidth"
                onClick={async (e) => {
                  e.stopPropagation();
                  if (inQuantifyType === 1) {
                    navigate('/xlb_erp/purchaseOrder/index', {
                      create_date: [dayjs(), dayjs()],
                      store_names: form.getFieldValue('store_names'),
                      store_ids: form.getFieldValue('store_ids'),
                      storehouse_id: form.getFieldValue('storehouse_id'),
                      item_ids: [record.item_id],
                      item_names: record.item_name,
                      over_deadline: true,
                      supplier_names: record.supplier_name,
                      time_type: 'audit_date',
                      time_desc: 0,
                      supplier_ids: [record.supplier_id],
                      checkValue: ['over_deadline'],
                    });
                  } else {
                    NiceModal.show(PurchaseQuantityModal, { ...record });
                  }
                }}
              >
                {toFixed(Number(value), 'QUANTITY')}
              </div>
            ),
        };

      case 'stop_purchase':
      case 'stop_sale':
      case 'must_sell':
      case 'stop_request':
        return {
          ...item,
          render: (value: any, record: any) =>
            record?.index === '勾选合计' ? null : (
              <div className={value ? 'success' : 'danger'}>
                {value ? '是' : '否'}
              </div>
            ),
        };
      case 'purchase_period':
        return {
          ...item,
          render: (value: any) => <span key={value}>{value}</span>,
        };
      case 'new_store_quantity':
        return {
          ...item,
          render: (value: any, record: any) =>
            record?.index === '勾选合计' ? null : (
              <span key={value}>{value || 0}</span>
            ),
        };
      case 'out_stock_store_count':
        return {
          ...item,
          render: (value: any, record: any) =>
            record?.index === '勾选合计' ? null : (
              <div>
                <span
                  className="link cursors"
                  onClick={() => {
                    const start_time = dayjs(
                      form.getFieldValue('start_time'),
                    ).format('YYYY-MM-DD');
                    const end_time = dayjs(
                      form.getFieldValue('end_time'),
                    ).format('YYYY-MM-DD');
                    XlbTipsModal({
                      title: '采购补货分析门店详情',
                      width: 900,
                      height: 450,
                      tips: (
                        <StoreDetail
                          data={{
                            sale_date: [start_time, end_time],
                            item_id: record.item_id,
                            store_id:
                              record?.store_id ||
                              form.getFieldValue('store_ids')?.[0],
                            click_value: 'out_stock_store',
                          }}
                        />
                      ),
                      isCancel: true,
                    });
                  }}
                >
                  {value}
                </span>
                <span>({record?.out_stock_store_count_percent})</span>
              </div>
            ),
        };
      case 'order_store_count':
        return {
          ...item,
          render: (value: any, record: any) =>
            record?.index === '勾选合计' ? null : (
              <div>
                <span
                  className="link cursors"
                  onClick={() => {
                    const start_time = dayjs(
                      form.getFieldValue('start_time'),
                    ).format('YYYY-MM-DD');
                    const end_time = dayjs(
                      form.getFieldValue('end_time'),
                    ).format('YYYY-MM-DD');
                    XlbTipsModal({
                      title: '采购补货分析门店详情',
                      width: 900,
                      height: 450,
                      tips: (
                        <StoreDetail
                          data={{
                            sale_date: [start_time, end_time],
                            item_id: record.item_id,
                            store_id:
                              record?.store_id ||
                              form.getFieldValue('store_ids')?.[0],
                            click_value: 'order_store',
                          }}
                        />
                      ),
                      isCancel: true,
                    });
                  }}
                >
                  {value}
                </span>
                <span>({record?.order_store_count_percent})</span>
              </div>
            ),
        };
      case 'sale_store_count':
        return {
          ...item,
          render: (value: any, record: any) =>
            record?.index === '勾选合计' ? null : (
              <div>
                <span
                  className="link cursors"
                  onClick={() => {
                    const start_time = dayjs(
                      form.getFieldValue('start_time'),
                    ).format('YYYY-MM-DD');
                    const end_time = dayjs(
                      form.getFieldValue('end_time'),
                    ).format('YYYY-MM-DD');
                    XlbTipsModal({
                      title: '采购补货分析门店详情',
                      width: 900,
                      height: 450,
                      tips: (
                        <StoreDetail
                          data={{
                            sale_date: [start_time, end_time],
                            item_id: record.item_id,
                            store_id:
                              record?.store_id ||
                              form.getFieldValue('store_ids')?.[0],
                            click_value: 'sale_store',
                          }}
                        />
                      ),
                      isCancel: true,
                    });
                  }}
                >
                  {value}
                </span>
                <span>({record?.sale_store_count_percent})</span>
              </div>
            ),
        };
      case 'new_store_count':
        return {
          ...item,
          render: (value: any, record: any) =>
            record?.index === '勾选合计' ? null : (
              <span
                className="link cursors"
                onClick={() => {
                  const start_time = dayjs(
                    form.getFieldValue('start_time'),
                  ).format('YYYY-MM-DD');
                  const end_time = dayjs(form.getFieldValue('end_time')).format(
                    'YYYY-MM-DD',
                  );
                  XlbTipsModal({
                    title: '采购补货分析门店详情',
                    width: 900,
                    height: 450,
                    tips: (
                      <StoreDetail
                        data={{
                          sale_date: [start_time, end_time],
                          item_id: record.item_id,
                          store_id:
                            record?.store_id ||
                            form.getFieldValue('store_ids')?.[0],
                          click_value: 'new_store',
                          new_store: true,
                        }}
                      />
                    ),
                    isCancel: true,
                  });
                }}
              >
                {value}
              </span>
            ),
        };
      case 'business_store_count':
        return {
          ...item,
          render: (value: any, record: any) =>
            record?.index === '勾选合计' ? null : (
              <span
                className="link cursors"
                onClick={() => {
                  const start_time = dayjs(
                    form.getFieldValue('start_time'),
                  ).format('YYYY-MM-DD');
                  const end_time = dayjs(form.getFieldValue('end_time')).format(
                    'YYYY-MM-DD',
                  );
                  XlbTipsModal({
                    title: '采购补货分析门店详情',
                    width: 900,
                    height: 450,
                    tips: (
                      <StoreDetail
                        data={{
                          sale_date: [start_time, end_time],
                          item_id: record.item_id,
                          store_id:
                            record?.store_id ||
                            form.getFieldValue('store_ids')?.[0],
                          click_value: 'business_store',
                        }}
                      />
                    ),
                    isCancel: true,
                  });
                }}
              >
                {value}
              </span>
            ),
        };
      default:
        return item;
    }
  };
  const formatTableColumn = (level: number) => {
    const chineseNumbers = [
      '一',
      '二',
      '三',
      '四',
      '五',
      '六',
      '七',
      '八',
      '九',
      '十',
    ];
    const codeNumbers = ['one', 'two', 'three', 'four', 'top'];
    const monthList = ['前1月月销量', '前2月月销量', '前3月月销量'];
    const weekList = ['前1周周销量', '前2周周销量', '前3周周销量'];
    const updateList = tableColumn
      .map((item) => tableDataRender(item))
      .map((item: any) => {
        if (monthList.includes(item.name)) {
          return {
            ...item,
            hidden: !form.getFieldValue('time_range')?.includes(0),
          };
        }
        if (weekList.includes(item.name)) {
          return {
            ...item,
            hidden: !form.getFieldValue('time_range')?.includes(1),
          };
        }
        return {
          ...item,
          name: item.name.includes('级类别')
            ? `${chineseNumbers[(level ?? 2) - 1]}级类别`
            : item.name,
          code: item.name.includes('级类别')
            ? `item_${codeNumbers[(level ?? 2) - 1]}_category_name`
            : item.code,
        };
      });
    setTableColumn(updateList);
  };
  const prevPost = (data: any) => {
    formatTableColumn(form.getFieldValue('category_level') ?? 2);
    const formatTime = (time: any) => dayjs(time).format('YYYY-MM-DD');
    const params: any = {
      sale_date: [
        formatTime(form.getFieldValue('start_time')),
        formatTime(form.getFieldValue('end_time')),
      ],
      category_level: form.getFieldValue('category_level') ?? 2,
      // enable_valid_days
      valid_days_compare: form.getFieldValue('enable_valid_days')?.length
        ? form.getFieldValue('valid_days_compare')
        : void 0,
      valid_days_ratio: form.getFieldValue('enable_valid_days')?.length
        ? form.getFieldValue('valid_days_ratio')
        : void 0,
      valid_days_symbol: form.getFieldValue('enable_valid_days')?.length
        ? form.getFieldValue('valid_days_symbol')
        : void 0,
      //TODO: 门店处理（只传store_ids接口识别为多个门店，待接口重构后修复）
      store_id:
        form.getFieldValue('store_ids')?.length === 1
          ? form.getFieldValue('store_ids')[0]
          : void 0,
      store_ids:
        form.getFieldValue('store_ids')?.length > 1
          ? form.getFieldValue('store_ids')
          : void 0,
      stop_purchase: void 0,
      stop_sale: void 0,
      stop_request: void 0,
    };
    form.getFieldValue('judge_filter').forEach((item: string) => {
      params[item] = !!form.getFieldValue('is_show');
    });
    const filterFormat = (chooseList: string[]) => {
      chooseList?.forEach((item: any) => {
        params[item] = true;
      });
    };
    filterFormat(form.getFieldValue('sale_filter'));
    filterFormat(form.getFieldValue('common_filter'));
    filterFormat(form.getFieldValue('sale_count_filter'));
    filterFormat(form.getFieldValue('suggest_filter'));

    return {
      ...data,
      ...form.getFieldsValue(true),
      ...params,
      erp_purchase_analysis_find_type: 1,
    };
  };
  const afterPost = (data: any) =>
    data.map((item: any, index: number) => ({
      ...item,
      new_store_count: item?.new_store_count || 0,
      new_store_quantity: item?.new_store_quantity || 0,
      fidKey: `${item?.store_id}_${item?.item_id}`,
      itemIndex: index + 1,
    }));
  // footer
  const needShowSumColumn = [
    'quantity',
    'present_quantity',
    'suggest_quantity',
    'purchase_quantity',
    'seven_day_ago_delivery_quantity',
    'month_ago_delivery_quantity',
    'last_sale_quantity',
    'force_transfer_quantity',
    'avg_sale_quantity',
    'stock_quantity',
    'month_ago_sale_quantity',
    'two_month_ago_sale_quantity',
    'three_month_ago_sale_quantity',
    'week_ago_sale_quantity',
    'two_week_ago_sale_quantity',
    'three_week_ago_sale_quantity',
  ];
  const [footerDataSource, setFooterDataSource] = useState<any[]>([]);
  const getFooterDataSource = (selectedRows?: any[]) => {
    const footerDefaultData = { index: '勾选合计' };
    needShowSumColumn.forEach((item) => {
      Object.assign(footerDefaultData, {
        [item]: (
          selectedRows?.reduce(
            (sum, v) => safeMath.add(sum, Number(v[item])),
            0,
          ) || 0
        ).toFixed(3),
      });
    });
    setFooterDataSource([{ ...footerDefaultData }]);
  };

  // init
  const initForm = (recordData?: any) => {
    const formData = LStorage.get('purchaseReplenishAnalysis');
    if (formData) {
      form.setFieldsValue({
        ...formData,
        start_time: dayjs(formData.start_time),
        end_time: dayjs(formData.end_time),
      });
    } else {
      form.setFieldsValue({
        unit_type: 'PURCHASE',
        category_level: 2,
        time_range: [0],
        common_filter: ['main_supplier'],
        is_show: 0,
        judge_filter: FILTER_CHECKBOX.judge.map((item) => item.value),
        valid_days_compare: 1,
        valid_days_symbol: '+',
        valid_days_ratio: 0,
        item_rule: null,
        sale_count_filter: ['delivery_out_order', 'wholesale_order'],
        start_time: dayjs().subtract(1, 'day').subtract(1, 'month'),
        end_time: dayjs().subtract(1, 'day'),
        ...recordData,
      });
    }

    const updateList = formList.map((item) => {
      if (item.name === 'org_ids') {
        return { ...item, hidden: !enable_organization };
      }
      return item;
    });
    setFormList(updateList);
  };
  // 获取采购参数
  const getPurchaseParams = async () => {
    const { code, data } = await purchaseParams({});
    if (code === 0) {
      setInQuantifyType(data?.in_quantify_type); //1-采购订单 2-发货单+备货单
    }
  };
  useEffect(() => {
    initForm();
    getPurchaseParams();
    getFooterDataSource();
  }, []);

  // operate
  const [exportLoading, setExportLoading] = useState(false);
  const exportItem = async (e: any, dataSource: any[]) => {
    wujieBus?.$emit('xlb_erp-event', { code: 'downloadEnd', target: e });
    setExportLoading(true);
    const res = await exportPurchaseReplenishAnalysis({
      details: dataSource.map((v: any) => ({
        ...v,
        purchase_price: v.purchase_price === '****' ? 0 : v.purchase_price,
        item_sale_price: v.item_sale_price === '****' ? 0 : v.item_sale_price,
        present_quantity: v.present_quantity ?? 0,
      })),
      erp_purchase_analysis_find_type: 1,
    });
    setExportLoading(false);
    if (res.code === 0) message.success(res.data);
  };
  // 生成采购订单
  const [purchaseOrderFrom] = XlbBasicForm.useForm();
  const createPuchaseOrder = async (selectRow: any[]) => {
    const { store_ids, storehouse_id } = form.getFieldsValue(true);
    if (selectRow?.some((v: any) => !v.supplier_id)) {
      XlbTipsModal({
        tips: '供应商为空，不能生成采购订单',
        isConfirm: true,
        isCancel: false,
      });
      return;
    }
    XlbTipsModal({
      title: '生成采购订单',
      tips: (
        <XlbBasicForm form={purchaseOrderFrom}>
          <XlbBasicForm.Item name="create_type">
            <XlbRadio.Group defaultValue={false}>
              {CREATE_PRDER_TYPE.map((item) => (
                <XlbRadio
                  style={{ width: '100%', margin: '4px 0' }}
                  key={`${item.value}`}
                  value={item.value}
                >
                  {item.label}
                </XlbRadio>
              ))}
            </XlbRadio.Group>
          </XlbBasicForm.Item>
        </XlbBasicForm>
      ),
      onOkBeforeFunction: async () => {
        const createType = purchaseOrderFrom.getFieldValue('create_type');
        const errorRows = selectRow
          ?.filter((v) => !v.quantity && !createType && !v?.present_quantity)
          .map((item) => ({
            ...item,
            errorMessage: `第${item.itemIndex}行,【${item.item_name}】实际订购量与搭赠数量都为0`,
          }));
        const successRows = selectRow?.filter(
          (v) => v.quantity || createType || v.present_quantity,
        );

        if (errorRows?.length === selectRow?.length) {
          XlbTipsModal({
            isCancel: false,
            tips: (
              <div className="danger">
                已生成0张采购订单，以下商品系统已自动过滤！
              </div>
            ),
            tipsList: errorRows.map((item) => item.errorMessage),
          });
          return true;
        }
        const data = {
          details: successRows.map((v: any) => {
            return {
              item_id: v.item_id,
              quantity: v.quantity,
              ratio: v.ratio,
              supplier_id: v.supplier_id,
              unit: v.unit,
              store_id: v.store_id,
              present_quantity: v.present_quantity,
              store_item_replenish_info: {
                avg_sale_quantity: v.avg_sale_quantity,
                business_scopes: v.business_scopes,
                business_store_count: v.business_store_count,
                force_transfer_quantity: v.force_transfer_quantity,
                item_code: v.item_code,
                item_name: v.item_name,
                last_sale_quantity: v.last_sale_quantity,
                month_ago_sale_quantity: v.month_ago_sale_quantity,
                purchase_period: v.purchase_period,
                quantity: v.quantity,
                present_quantity: v.present_quantity,
                stock_quantity: v.stock_quantity,
                suggest_quantity: v.suggest_quantity,
                supplier_details: v.supplier_details,
                three_month_ago_sale_quantity: v.three_month_ago_sale_quantity,
                three_week_ago_sale_quantity: v.three_week_ago_sale_quantity,
                two_month_ago_sale_quantity: v.two_month_ago_sale_quantity,
                two_week_ago_sale_quantity: v.two_week_ago_sale_quantity,
                valid_days: v.valid_days,
                week_ago_sale_quantity: v.week_ago_sale_quantity,
                supplier_prepare: v.supplier_prepare,
              },
              supplier_prepare: v.supplier_prepare,
            };
          }),
          store_id: store_ids.length > 1 ? '' : store_ids[0],
          storehouse_id: store_ids.length > 1 ? '' : storehouse_id,
          store_ids: store_ids.length > 1 ? store_ids : '',
        };
        let res: any;
        if (store_ids?.length > 1) {
          res = await batchCreatePurcahseOrder(data);
        } else {
          res = await createPurcahseOrder(data);
        }
        if (res.code === 0) {
          XlbTipsModal({
            isCancel: false,
            tips: (
              <div className={errorRows?.length ? 'danger' : ''}>
                已生成{res.data.count}张采购订单
                {!!errorRows?.length && '，以下商品系统已自动过滤！'}
              </div>
            ),
            tipsList: errorRows.map((item) => item.errorMessage),
          });
          return true;
        }
        return false;
      },
    });
  };
  // 批量添加
  interface BatchCreateItemParams {
    dataSource: any[];
    setSelectRow: any;
    setDataSource: any;
  }
  const batchCreate = async ({
    dataSource,
    setSelectRow,
    setDataSource,
  }: BatchCreateItemParams) => {
    const params = form.getFieldsValue(true);
    await XlbBasicData({
      type: 'purchaseReplenishGoods',
      dataType: 'lists',
      isLeftColumn: true,
      isMultiple: true,
      data: {
        openNormalProduct: true,
        store_ids: params?.store_ids,
        category_ids: params?.item_category_ids,
        item_dept_ids: params?.item_dept_ids,
      },
      onOkBeforeFunction: async (ids) => {
        const res = await batchCreateItem({
          delivery_out_order: params.delivery_out_order,
          item_ids: ids,
          pos_order: params.pos_order,
          sale_date: [
            dayjs(params.start_time).format('YYYY-MM-DD'),
            dayjs(params.end_time).format('YYYY-MM-DD'),
          ],
          subtract_purchase_quantity: params.subtract_purchase_quantity,
          suggest_type: params.suggest_type,
          wholesale_order:
            params.sale_count_filter?.includes('wholesale_order'),
          store_id: params.store_ids?.length === 1 ? params.store_ids[0] : null,
          store_ids: params.store_ids?.length > 1 ? params.store_ids : null,
          storehouse_id: params.storehouse_id,
          unit_type: params.unit_type,
        });
        if (res.code === 0) {
          const ids = dataSource.map((v: any) => v.item_id);
          let repeatArr = res.data.filter((v: any) => ids.includes(v.item_id));
          let newArr = res.data
            .filter((item: any) => !ids.includes(item.item_id))
            ?.map((v: any) => ({
              ...v,
              fidKey: v?.store_id + '_' + v?.item_id,
            }));
          const repeatName = [
            repeatArr.map((v: any) => `【${v.item_name}】`).join('、'),
          ];
          if (repeatArr.length) {
            XlbTipsModal({
              tips: '以下商品已存在，不允许重复添加，系统已自动过滤！',
              isCancel: false,
              isConfirm: true,
              tipsList: repeatName,
            });
          }
          setDataSource(dataSource.concat(newArr));
          setSelectRow([]);
          return true;
        }
        return false;
      },
    });
  };

  return (
    <XlbPageContainer
      className={style.pagePurchaseContainer}
      prevPost={prevPost}
      url="/erp/hxl.erp.purchasereplenishanalysis.find"
      tableColumn={tableColumn}
      afterPost={afterPost}
    >
      <XlbPageContainer.ToolBtn showColumnsSetting>
        {({
          fetchData,
          loading,
          dataSource,
          selectRow,
          setSelectRow,
          setDataSource,
          pagin,
        }: ContextState<any>) => {
          paginRef.current = pagin;
          return (
            <XlbButton.Group>
              {hasAuth(['采购补货分析', '查询']) && (
                <XlbButton
                  label="查询"
                  type="primary"
                  loading={loading}
                  onClick={() => {
                    fetchData();
                  }}
                  icon={<XlbIcon name="sousuo" />}
                />
              )}
              {hasAuth(['采购补货分析', '编辑']) && (
                <XlbButton
                  label="批量添加"
                  type="primary"
                  icon={<XlbIcon name="baocunxinzeng" />}
                  onClick={() =>
                    batchCreate({
                      dataSource: dataSource || [],
                      setSelectRow,
                      setDataSource,
                    })
                  }
                />
              )}
              {hasAuth(['采购补货分析', '导出']) && (
                <XlbButton
                  label="导出"
                  type="primary"
                  loading={exportLoading}
                  disabled={!dataSource?.length}
                  icon={<XlbIcon name="daochu" />}
                  onClick={(e) => exportItem(e, dataSource!)}
                />
              )}
              {hasAuth(['采购订单', '编辑']) && (
                <XlbButton
                  label="生成采购订单"
                  disabled={!selectRow?.length}
                  type="primary"
                  icon={<XlbIcon name="xiugai" />}
                  onClick={() => createPuchaseOrder(selectRow!)}
                />
              )}
            </XlbButton.Group>
          );
        }}
      </XlbPageContainer.ToolBtn>

      <XlbPageContainer.SearchForm>
        <XlbForm form={form} formList={formList} isHideDate />
        <XlbBasicForm form={form} layout="horizontal">
          <XlbBasicForm.Item name="store_names" noStyle />
          {/* 通用筛选 */}
          <XlbBasicForm.Item name="common_filter">
            <XlbCheckbox.Group
              options={FILTER_CHECKBOX.common}
              onChange={(value) => {
                setIsWarnFlag(value?.includes('warn_flag'));
                if (value?.includes('warn_flag')) {
                  form.setFieldsValue({
                    sale_count_filter: [
                      'delivery_out_order',
                      'wholesale_order',
                    ],
                    start_time: dayjs().subtract(6, 'day'),
                    end_time: dayjs(),
                  });
                }
              }}
            />
          </XlbBasicForm.Item>
          {/* 条件筛选 */}
          <XlbBasicForm.Item className={style.formItemJudgeFilter}>
            <XlbBasicForm.Item name="is_show" noStyle>
              <XlbSelect
                width={86}
                options={IS_SHOW_OPTIONS}
                style={{ marginRight: 16 }}
                popupMatchSelectWidth={false}
              />
            </XlbBasicForm.Item>
            <XlbBasicForm.Item name="judge_filter" noStyle>
              <XlbCheckbox.Group options={FILTER_CHECKBOX.judge} />
            </XlbBasicForm.Item>
            {/* 销量筛选 */}
            <XlbBasicForm.Item name="sale_filter" noStyle>
              <XlbCheckbox.Group
                style={{ marginLeft: 30 }}
                options={FILTER_CHECKBOX.sale}
              />
            </XlbBasicForm.Item>
            <div className="v-flex">
              <XlbBasicForm.Item name="enable_valid_days" noStyle>
                <XlbCheckbox.Group>
                  <XlbCheckbox value="enable_valid_days" />
                </XlbCheckbox.Group>
              </XlbBasicForm.Item>
              <span className={style.formItemWord}>预计可用天数</span>
              <XlbBasicForm.Item name="valid_days_compare" noStyle>
                <XlbSelect
                  width={52}
                  options={COMPARE_OPTIONS}
                  popupMatchSelectWidth={false}
                />
              </XlbBasicForm.Item>
              <span className={style.formItemWord}>交货周期</span>
              <XlbBasicForm.Item name="valid_days_symbol" noStyle>
                <XlbSelect
                  width={52}
                  options={SYMBOL_OPTIONS}
                  popupMatchSelectWidth={false}
                />
              </XlbBasicForm.Item>
              <XlbBasicForm.Item name="valid_days_ratio" noStyle>
                <XlbInputNumber
                  controls={false}
                  step={0.1}
                  precision={1}
                  min={0}
                  max={99999999}
                  width={60}
                  onFocus={(e) => e.target.select()}
                />
              </XlbBasicForm.Item>
            </div>
          </XlbBasicForm.Item>

          <div className="flex">
            <fieldset className={style.fieldset}>
              <legend className={style.legend}>补货商品筛选</legend>
              <XlbBasicForm.Item name="item_rule">
                <XlbRadio.Group>
                  {RESTOCK_GOODS_FILTER.map((v) => (
                    <XlbRadio value={v.value} key={`${v.value}`}>
                      {v.lable}
                    </XlbRadio>
                  ))}
                </XlbRadio.Group>
              </XlbBasicForm.Item>
            </fieldset>
            <fieldset className={style.fieldset}>
              <legend className={style.legend}>销量统计</legend>
              <XlbBasicForm.Item
                label="开始时间"
                name="start_time"
                className="custom-form-item"
              >
                <XlbDatePicker
                  disabled={isWarnFlag}
                  width={136}
                  format="YYYY-MM-DD"
                  allowClear={false}
                />
              </XlbBasicForm.Item>
              <XlbBasicForm.Item
                label="结束时间"
                name="end_time"
                className="custom-form-item"
              >
                <XlbDatePicker
                  width={136}
                  disabled={isWarnFlag}
                  format={'YYYY-MM-DD'}
                  allowClear={false}
                />
              </XlbBasicForm.Item>
              <XlbBasicForm.Item name="sale_count_filter">
                <XlbCheckbox.Group
                  disabled={isWarnFlag}
                  options={FILTER_CHECKBOX.saleCount}
                  onChange={(value) => {
                    if (value.includes('store_in') && value.length > 1) {
                      message.warning('门店调入不能同时与其他勾选!');
                      form.setFieldValue('sale_count_filter', []);
                    }
                  }}
                />
              </XlbBasicForm.Item>
            </fieldset>
            <fieldset className={style.fieldset}>
              <legend className={style.legend}>建议补货量</legend>
              <XlbBasicForm.Item
                label="补货规则"
                name="suggest_type"
                className="custom-form-item"
              >
                <XlbSelect width={136} popupMatchSelectWidth={false}>
                  {SUGGEST_TYPE.map((item) => (
                    <XlbSelect.Option value={item.value} key={item.value}>
                      <div style={{ minHeight: 22 }}>{item.label}</div>
                    </XlbSelect.Option>
                  ))}
                </XlbSelect>
              </XlbBasicForm.Item>
              <XlbBasicForm.Item name="suggest_filter">
                <XlbCheckbox.Group options={FILTER_CHECKBOX.suggest} />
              </XlbBasicForm.Item>
            </fieldset>
          </div>
        </XlbBasicForm>
      </XlbPageContainer.SearchForm>

      <XlbPageContainer.Table
        key="fidKey"
        primaryKey="fidKey"
        selectMode="multiple"
        //TODO: 待处理： 勾选合计用PageContainer中的footerDataSource时，数据改变合计不更新
        footerDataSource={footerDataSource}
        onSelectRow={(_selectedRowKeys: string[], selectedRows: any[]) =>
          getFooterDataSource(selectedRows)
        }
        keepDataSource={false}
      />
    </XlbPageContainer>
  );
};
export default Index;

import { useRef, FC, useState } from 'react';
import { Space } from 'antd';
import {
  XlbTable,
  XlbTableColumnProps,
  XlbInput,
  XlbButton,
  XlbIcon,
} from "@xlb/components";


const TableModal: FC<any> = (params: any) => {
  console.log(params, "params");
  const { data } = params;
  const inputRef = useRef<any>();
  const [rowData, setRowData] = useState<any[]>(data);

  const columns: XlbTableColumnProps<any>[] = [
    {
      name: '代码',
      code: 'store_code',
      width: 80
    },
    {
      name: '名称',
      code: 'store_name',
      width: 150
    },
    {
      name: '营业执照名称',
      code: 'license_name',
      width: 240
    },
    {
      name: '执照类型',
      code: 'license_type',
      width: 100,
      render(text) {
        const obj: any = {
          COMPANY: '公司',
          PERSONAL: '个人'
        }
        return obj[text]
      }
    },
    {
      name: '门店分组',
      code: 'store_group',
      width: 100,
      render(text) {
        return text?.name || ''
      }
    },
    {
      name: '配送类型',
      code: 'delivery_type',
      width: 80,
      render(text) {
        const obj: any = {
          JOIN: '加盟',
          DIRECT: '直营'
        }
        return obj[text]
      }
    },
    {
      name: '经营类型',
      code: 'management_type',
      width: 80,
      render(text) {
        const obj: any = {
          0: '直营',
          1: '加盟'
        }
        return obj[text]
      }
    }
  ];

  const search = () => {
    const value = RegExp(inputRef.current.input.value, 'i');
    setRowData(data.filter((item: any) => value.test(item.store_name)))
  }

  return (
    <div>
      <Space style={{ marginBottom: 12 }}>
        <XlbInput ref={inputRef} id='deliverPriceMangeShowStoreKeyword' placeholder="请输入门店名称查询" width={220} />
        <XlbButton
          style={{ width: 80 }}
          onClick={search}
          icon={<XlbIcon name='sousuo' />}
        >
          查询
        </XlbButton>
      </Space>
      <XlbTable
        style={{ height: 400 }}
        dataSource={rowData}
        columns={columns}
        total={rowData.length}
        pageSize={200}
      />
    </div>
  )
}
export default TableModal

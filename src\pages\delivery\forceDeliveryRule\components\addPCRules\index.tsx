import { columnWidthEnum } from '@/data/common/constant';
import { LStorage } from '@/utils/storage';
import { useModal } from '@ebay/nice-modal-react';
import type { BaseModalProps } from '@xlb/components';
import {
  XlbBasicData,
  XlbBasicForm,
  XlbButton,
  XlbIcon,
  XlbInput,
  XlbInputDialog,
  XlbModal,
  XlbRadio,
  XlbTable,
  XlbTabs,
  XlbTreeModal,
} from '@xlb/components';
import { message } from 'antd';
import { useEffect, useState, type FC } from 'react';
import api from '../../server';
interface Props extends BaseModalProps {
  fetchData?: any;
  id?: number;
}

const AddItem: FC<Props> = ({ fetchData, id = -1 }) => {
  const [form] = XlbBasicForm.useForm();
  const modal = useModal();
  const [rowData, setRowData] = useState<any[]>([]);
  const [goodsExcludeRowData, setGoodsExcludeRowData] = useState<any[]>([]);
  const [isLoading, setisLoading] = useState<boolean>(false);
  const [pageSize, setPageSize] = useState(200);
  const [excludeGoodsPageSize, setExcludeGoodsPageSize] = useState(200);
  const [selectRow, setSelectRow] = useState<any>([]); // 已选择的
  const [excludeGoodsSelectRow, setExcludeGoodsSelectRow] = useState<any>([]); // 已选择的

  //补货范围增加
  const addItem = async () => {
    const list = await XlbTreeModal({
      width: 320,
      multiple: true,
      title: '商品分类',
      dataType: 'lists',
      checkable: true,
      url: '/erp-mdm/hxl.erp.category.find',
    });
    if (list) {
      const obj = list?.filter((i: any) => i?.id);
      if (rowData && rowData?.length > 0) {
        const Arr3 = rowData.concat(obj);
        const newObj: any = [];
        //并集
        const result = Arr3.reduce(
          (prev: any, cur: any, index: number, array: any) => {
            newObj[cur.id] ? '' : (newObj[cur.id] = true && prev.push(cur));
            return prev;
          },
          [],
        );
        setRowData(result);
      } else {
        setRowData(obj);
      }
    }
  };
  //补货范围删除
  const deleteItem = () => {
    const deleteItem = rowData?.filter(
      (item: any) => !selectRow.includes(item.id),
    );
    setRowData(deleteItem);
    setSelectRow([]);
  };

  // 排除商品
  const excludedGoods = async () => {
    const data = await XlbBasicData({
      type: 'goods',
      isMultiple: true,
      isLeftColumn: true,
      dataType: 'lists',
      primaryKey: 'id',
      resetForm: true,
      selectedList: goodsExcludeRowData || [],
      data: {
        company_id: LStorage.get('userInfo')?.company_id,
        operator_store_id: LStorage.get('userInfo').store_id,
      },
    });
    if (data) {
      const exclude_goods: any = [];
      data?.forEach((item: any, index: number) => {
        exclude_goods.push({
          id: item.id,
          code: item.code,
          item_category_name: item.item_category_name,
          name: item.name,
        });
      });
      setGoodsExcludeRowData(exclude_goods);
      setExcludeGoodsSelectRow([]);
    }
  };
  // 排除商品删除
  const deleteGoodsExcludeItem = () => {
    const deleteItem = goodsExcludeRowData.filter(
      (item: any) => !excludeGoodsSelectRow.includes(item.id),
    );
    setGoodsExcludeRowData(deleteItem);
    setExcludeGoodsSelectRow([]);
  };

  useEffect(() => {
    if (id !== -1) {
      setisLoading(true)
      api.excludeRead({ id: id }).then((res: any) => {
        form.setFieldsValue({
          ...res?.data,
          exclude_store_ids: res?.data?.stores?.map((item: any) => item.id),
        });
        setRowData(res?.data?.item_categories);
        setGoodsExcludeRowData(res?.data?.items);
        setisLoading(false)
      });
    }
  }, []);

  const handleOk = async (values: any) => {
    setisLoading(true)
    const res =
      id == -1
        ? await api.saveExclude({
            ...values,
            item_category_ids: rowData?.map((item: any) => item.id),
            item_ids: goodsExcludeRowData?.map((item: any) => item.id),
          })
        : await api.updateExclude({
            ...values,
            id: id,
            item_category_ids: rowData?.map((item: any) => item.id),
            item_ids: goodsExcludeRowData?.map((item: any) => item.id),
          });
    setisLoading(false)
    if (res?.code === 0) {
      modal.hide();
      modal.resolve(false);
      fetchData?.();
      message.success('操作成功');
    }
  };
  return (
    <XlbModal
      width={1000}
      open={modal.visible}
      title={
        <>
          设置排除{' '}
          <span style={{ color: '#999', fontSize: '12px', fontWeight: 400 }}>
            为部分门店设置排除商品,如A门店排除酒水类商品,则不会给门店统配酒水分类的商品
          </span>
        </>
      }
      isCancel={true}
      onOk={async () => {
        form.submit();
      }}
      onCancel={() => {
        modal.resolve(false);
        modal.hide();
      }}
    >
      <div style={{ marginLeft: '12px' }}>
        <XlbBasicForm
          form={form}
          disabled={isLoading}
          style={{ margin: '20px 0' }}
          layout={'horizontal'}
          onFinish={() => {
            const values = form.getFieldsValue(true);
            handleOk(values);
          }}
        >
          {/* <XlbBlueBar title="设置排除" subTitle='为部分门店设置排除商品,如A门店排除酒水类商品,则不会给门店统配酒水分类的商品'  /> */}
          <XlbBasicForm.Item
            label="规则名称"
            rules={[{ required: true, message: '规则名称不能为空' }]}
            name="rule_name"
          >
            <XlbInput width={400} placeholder="请输入" />
          </XlbBasicForm.Item>
          <XlbBasicForm.Item
            label="是否启用"
            name="enabled"
            rules={[{ required: true, message: '是否启用不能为空' }]}
          >
            {/* <XlbCheckbox
              checked={enabled}
              onChange={(value) => {
                setEnabled(value.target.checked);
              }}
            /> */}
            <XlbRadio.Group>
              <XlbRadio value={true}>启用</XlbRadio>
              <XlbRadio value={false}>停用</XlbRadio>
            </XlbRadio.Group>
          </XlbBasicForm.Item>
          <XlbBasicForm.Item label="应用门店" name="exclude_store_ids">
            <XlbInputDialog
              dialogParams={{
                type: 'store',
                isMultiple: true,
                dataType: 'lists',
                placeholder: '请选择',
                data: {
                  status: true,
                  center_flag: false,
                },
              }}
              fieldNames={{
                idKey: 'id',
                nameKey: 'store_name',
              }}
              width={400}
            />
          </XlbBasicForm.Item>
          <XlbBasicForm.Item label="排除商品" name="force_delivery_stores">
            <XlbTabs
              style={{ paddingLeft: 20 }}
              items={[
                {
                  label: '按商品分类排除',
                  key: '2',
                  children: (
                    <div
                      style={{
                        display: 'flex',
                        flexDirection: 'column',
                        width: '100%',
                      }}
                    >
                      <div style={{ marginBottom: 10 }}>
                        <XlbButton.Group>
                          <XlbButton
                            label="添加商品分类"
                            type="primary"
                            onClick={addItem}
                            icon={<XlbIcon size={16} name="jia" />}
                          />
                          <XlbButton
                            label="删除"
                            type="primary"
                            onClick={deleteItem}
                            disabled={selectRow?.length === 0}
                            icon={<XlbIcon size={16} name="shanchu" />}
                          />
                        </XlbButton.Group>
                      </div>
                      <XlbTable
                        columns={[
                          {
                            name: '序号',
                            code: '_index',
                            width: columnWidthEnum.INDEX,
                            align: 'center',
                          },
                          {
                            name: '类别代码',
                            code: 'id',
                            width: 140,
                            features: { sortable: true },
                          },
                          {
                            name: '类别名称',
                            code: 'name',
                            width: 160,
                            features: { sortable: true },
                          },
                          {
                            name: '速记码',
                            code: 'shorthand_code',
                            width: columnWidthEnum.SHORTHAND_CODE,
                            features: { sortable: true },
                          },
                        ]}
                        dataSource={rowData}
                        total={rowData?.length}
                        onPaginChange={(page, pageSize) => {
                          setPageSize(pageSize);
                        }}
                        pageSize={pageSize}
                        selectMode="multiple"
                        style={{ maxHeight: 400, height: 400 }}
                        isLoading={isLoading}
                        emptyCellHeight={400}
                        selectedRowKeys={selectRow}
                        onSelectRow={(record) => setSelectRow(record)}
                      />
                    </div>
                  ),
                },
                {
                  label: '按商品排除',
                  key: '4',
                  children: (
                    <div
                      style={{
                        display: 'flex',
                        flexDirection: 'column',
                        width: '100%',
                      }}
                    >
                      <div style={{ marginBottom: 10 }}>
                        <XlbButton.Group>
                          <XlbButton
                            label="排除商品"
                            type="primary"
                            onClick={excludedGoods}
                            icon={<XlbIcon size={16} name="jia" />}
                          />
                          <XlbButton
                            label="删除"
                            type="primary"
                            onClick={deleteGoodsExcludeItem}
                            disabled={excludeGoodsSelectRow?.length === 0}
                            icon={<XlbIcon size={16} name="shanchu" />}
                          />
                        </XlbButton.Group>
                      </div>
                      <XlbTable
                        columns={[
                          {
                            name: '序号',
                            code: '_index',
                            width: columnWidthEnum.INDEX,
                            align: 'center',
                          },
                          {
                            name: '商品代码',
                            code: 'code',
                            width: 140,
                            features: { sortable: true },
                          },
                          {
                            name: '商品分类',
                            code: 'item_category_name',
                            width: 100,
                            features: { sortable: true },
                          },
                          {
                            name: '商品名称',
                            code: 'name',
                            width: 160,
                            features: { sortable: true },
                          },
                        ]}
                        dataSource={goodsExcludeRowData}
                        total={goodsExcludeRowData?.length}
                        pageSize={excludeGoodsPageSize}
                        onPaginChange={(page, pageSize) => {
                          setExcludeGoodsPageSize(pageSize);
                        }}
                        selectMode="multiple"
                        selectedRowKeys={excludeGoodsSelectRow}
                        style={{ maxHeight: 400, height: 400 }}
                        emptyCellHeight={400}
                        isLoading={isLoading}
                        onSelectRow={(record) =>
                          setExcludeGoodsSelectRow(record)
                        }
                      />
                    </div>
                  ),
                },
              ]}
            />
          </XlbBasicForm.Item>
        </XlbBasicForm>
      </div>
    </XlbModal>
  );
};
export default AddItem
export interface PurchasePlanPageReqDTO {
  /**
   * @name 制单时间 yyyy-MM-dd格式
   */
  create_date: Array<string | number>

  /**
   * @name 状态：true：启用；false: 停用
   */
  enable?: boolean

  /**
   * @name 计划名称
   */
  name?: string

  /**
   * @name 所属组织
   */
  org_ids?: Array<string | number>

  /**
   * @name 页号
   */
  page_number?: number

  /**
   * @name 每页条数
   */
  page_size?: number
}

export interface PurchasePlanResDTO {
  /**
   * @name 开始时间
   */
  begin_time?: string

  /**
   * @name 创建人
   */
  create_by?: string

  /**
   * @name 创建时间
   */
  create_time?: string

  /**
   * @name 状态
   */
  enable?: boolean

  /**
   * @name 结束时间
   */
  end_time?: string

  /**
   * @name 采购计划id
   */
  id?: number

  /**
   * @name 金额
   */
  money?: number

  /**
   * @name 计划名称
   */
  name?: string

  /**
   * @name
   */
  org_id?: number

  /**
   * @name 所属组织
   */
  org_name?: string

  /**
   * @name 金额
   */
  quantity?: number
}

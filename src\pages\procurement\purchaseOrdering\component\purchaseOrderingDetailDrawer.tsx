import NiceModal from '@ebay/nice-modal-react';
import { XlbBlueBar, XlbDrawer, XlbTooltip } from '@xlb/components';
import classnames from 'classnames';
import { useEffect, useState } from 'react';
import Api from '../server';
import { activeModal } from './activeModal';
import AnalysisCharts from './analysisCharts';
import { ChartsUnits } from './data';
import './purchaseOrderingDetailDrawer.less';
import styles from './purchaseOrderingDetailDrawer.less';

interface IProps {
  record: any;
  visible: boolean;
  setVisible: (visible: boolean) => void;
}
const PurchaseOrderingDetailDrawer = (props: IProps) => {
  const { record, visible, setVisible } = props;
  // 标题
  const title = () => (
    <div className="v-flex">
      {record?.item_name}
      {record?.abc_class && (
        <div className={styles['modal-subtitle']}>{record?.abc_class}级</div>
      )}
    </div>
  );
  // 基本信息处理
  const BASE_INFO_LIST = [
    {
      label: '可用库存',
      value: record?.avl_stock_qty,
      key: 'avl_stock_qty',
    },
    {
      label: '在订库存',
      value: record?.on_order_qty,
      key: 'on_order_qty',
    },
    {
      label: '调拨在途库存',
      value: record?.transfer_in_transit_qty,
      key: 'transfer_in_transit_qty',
    },
    {
      label: '可用库存DIFC',
      value: record?.avl_stock_qty_difc,
      key: 'avl_stock_qty_difc',
    },
    {
      label: '总库存DIFC',
      value: record?.total_avl_stock_qty,
      key: 'total_avl_stock_qty',
    },
    {
      label: '安全库存天数',
      value: record?.safe_days,
      key: 'safe_days',
    },
    {
      label: '交货周期',
      value: record?.supplier_lead_time,
      key: 'supplier_lead_time',
    },
    {
      label: '订单频次',
      value: record?.order_freq,
      key: 'order_freq',
    },
    {
      label: '起订量',
      value: record?.min_order_qty_desc?.join('，'),
      key: 'min_order_qty_desc',
    },
  ];
  // chars data
  const [charsData, setCharsData] = useState<any>();
  const [loading, setLoading] = useState<boolean>(false);
  const getCharsData = async (unit: ChartsUnits) => {
    setLoading(true);
    const res = await Api.getAnalysisForecast({
      type: unit,
      store_id: record?.store_id,
      supplier_id: record?.supplier_id,
      item_id: record?.item_id,
    });
    if (res.code === 0) {
      setCharsData(res.data);
    }
    setLoading(false);
  };

  useEffect(() => {
    if (visible) {
      getCharsData(ChartsUnits.WEEK);
    } else {
      setCharsData({});
    }
  }, [visible]);

  return (
    <XlbDrawer
      width={620}
      placement="right"
      title={title()}
      maskClosable
      isCancel
      onClose={() => setVisible(false)}
      open={visible}
      footer={null}
    >
      <div className="purchase-order-container">
        <div>
          <XlbBlueBar
            title={`${record?.store_name}(${record?.storehouse_name})`}
          />
          <div className="detail-container">
            {BASE_INFO_LIST.map((item) => (
              <div
                className={classnames('detail-item', {
                  [item.key]: item.key === 'min_order_qty_desc',
                })}
                key={item.key}
              >
                <div className="detail-item__label">{item.label}</div>
                <div className="detail-item__value">{item.value}</div>
              </div>
            ))}
          </div>
          {!!record?.promotions?.length && (
            <div className="common-border active-container">
              <div className="active-title">
                <div className="active-title__name">促销活动</div>
                {record?.promotions?.length > 2 && (
                  <div
                    className="active-title__view"
                    onClick={() => {
                      NiceModal.show(activeModal, {
                        activeList: record?.promotions,
                      });
                    }}
                  >
                    查看全部活动
                  </div>
                )}
              </div>
              {record?.promotions
                ?.slice(0, 2)
                .map((item: string, index: number) => (
                  <div className="mt8" key={index}>
                    {item}
                  </div>
                ))}
            </div>
          )}
        </div>

        <div className="mt16">
          {Boolean(
            charsData?.future_weeks?.length ||
              charsData?.previous_weeks?.length,
          ) && (
            <>
              <XlbBlueBar title="仓出库量预测" />
              <div className="mt20">
                <AnalysisCharts
                  // width={600}
                  height={340}
                  data={charsData}
                  key={String(visible)}
                  getCharsData={getCharsData}
                  loading={loading}
                />
              </div>
            </>
          )}
        </div>

        <div className="analysis-container">
          <XlbBlueBar title="建议订货量" />
          <div className="mt12 analysis-title">1、初始需求计算</div>
          <div className="mt8 common-border analysis-group">
            <div className="text-center analysis-item">
              <div className="analysis-item__label">目标库存天数</div>
              <div className="analysis-item__value">
                {record?.target_inventory_days || 0}
              </div>
            </div>
            <div className="text-center analysis-item symbol-add">
              <div className="analysis-item__label">到货周期</div>
              <div className="analysis-item__value">
                {record?.supplier_lead_time || 0}
              </div>
            </div>
            <div className="text-center analysis-item symbol-add">
              <div className="analysis-item__label">订货频次</div>
              <div className="analysis-item__value">
                {record?.order_freq || 0}
              </div>
            </div>
            <div className="text-center analysis-item">
              <div className="analysis-item__label">安全库存天数</div>
              <div className="analysis-item__value">
                {record?.safe_days || 0}
              </div>
            </div>
          </div>

          <div className="mt12 common-border analysis-group">
            <div className="text-center analysis-item">
              <div className="analysis-item__label">不能补缺天数</div>
              <div className="analysis-item__value">
                {record?.stockout_days || 0}
              </div>
            </div>
            <div className="v-flex analysis-child-group">
              <div className="analysis-item analysis-symbol symbol-none">
                <div className="analysis-item__value">{'max ('}</div>
              </div>
              <div className="analysis-child-group__item">
                <div className="text-center analysis-item symbol-subtract">
                  <div className="analysis-item__label">到货周期</div>
                  <div className="analysis-item__value">
                    {record?.supplier_lead_time || 0}
                  </div>
                </div>
                <div className="text-center analysis-item symbol-comma">
                  <div className="analysis-item__label">总库存DIFC</div>
                  <div className="analysis-item__value">
                    {record?.total_avl_stock_qty || 0}
                  </div>
                </div>
                <div className="flex-center analysis-item">
                  <div className="analysis-item__value">0 )</div>
                </div>
              </div>
            </div>
          </div>

          <div className="mt12 common-border analysis-group">
            <div className="text-center analysis-item fixed-position">
              <div className="analysis-item__label">初始需求建议量</div>
              <div className="analysis-item__value">
                {record?.source_advise_qty || 0}
              </div>
            </div>
            <div className="analysis-child-group">
              <div className="flex">
                <div className="analysis-item w-72px symbol-none">
                  <div className="analysis-item__value">{'{max ('}</div>
                </div>
                <div className="flex-1 analysis-item symbol-subtract">
                  <div className="analysis-item__label">目标库存天数</div>
                  <div className="analysis-item__value">
                    {record?.target_inventory_days || 0}
                  </div>
                </div>
                <div className="flex-1 analysis-item symbol-subtract">
                  <div className="analysis-item__label">总库存DIFC</div>
                  <div className="analysis-item__value">
                    {record?.total_avl_stock_qty || 0}
                  </div>
                </div>
                <div className="flex-1 analysis-item symbol-comma">
                  <div className="analysis-item__label">不能补缺天数</div>
                  <div className="analysis-item__value">
                    {record?.stockout_days || 0}
                  </div>
                </div>
                <div className="analysis-item w-52px">
                  <div className="analysis-item__value">{'0 )}'}</div>
                </div>
              </div>
              <div className="v-flex analysis-item__introduce">
                <div className="analysis-item__label">范围内预测之和</div>
                <div className="analysis-item__value">
                  {record?.forecast_qty?.length > 7 ? (
                    <XlbTooltip title={record?.forecast_qty?.join(',')}>
                      sum({record?.forecast_qty?.slice(0, 5).join(',')}...
                      {record.forecast_qty?.[record.forecast_qty.length - 1]})
                    </XlbTooltip>
                  ) : record?.forecast_qty?.length ? (
                    `sum(${record?.forecast_qty?.join(',')})`
                  ) : (
                    0
                  )}
                </div>
              </div>
            </div>
          </div>
          {!!record?.process_desc?.length && (
            <>
              <div className="mt20 analysis-title">2、调节补货</div>
              <div className="mt8 analysis-introduce">
                {record?.process_desc?.map((item: string) => (
                  <>
                    {item}
                    <br />
                  </>
                ))}
              </div>
            </>
          )}
        </div>
      </div>
    </XlbDrawer>
  );
};

export default PurchaseOrderingDetailDrawer;

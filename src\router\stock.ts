interface IRoute {
  component?: any;
  exact?: boolean;
  path?: string;
  routes?: IRoute[];
  wrappers?: string[];
  title?: string;
  __toMerge?: boolean;
  __isDynamic?: boolean;
  [key: string]: any;
}

export const routeList: IRoute[] = [
  {
    path: '/xlb_erp/stockSearch/index',
    component: '@/pages/stock/stockSearch/index',
    title: '库存查询',
    subTitle: '数据查询',
    subMenu: 'stock',
    tabClass: 'stockSearch'
  },
  {
    path: '/xlb_erp/stockDyingPeriod/index',
    component: '@/pages/stock/stockDyingPeriod/index',
    title: '库存临期检查',
    subTitle: '数据查询',
    subMenu: 'stock',
    tabClass: 'stockDyingPeriod',
  },
  {
    path: '/xlb_erp/unsalableItem/index',
    component: '@/pages/stock/unsalableItem/index',
    title: '滞销商品',
    subTitle: '数据查询',
    subMenu: 'stock',
    tabClass: 'unsalableItem',
  },
  {
    path: '/xlb_erp/stockLog/index',
    component: '@/pages/stock/stockLog/index',
    title: '库存进出记录',
    subTitle: '数据查询',
    subMenu: 'stock',
    tabClass: 'stockLog'
  },
  {
    path: '/xlb_erp/stockCollaborativeSharingr/index',
    component: '@/pages/stock/stockCollaborativeSharingr/index',
    title: '库存协同共享',
    subTitle: '业务设置',
    subMenu: 'stock',
    tabClass: 'stockCollaborativeSharingr'
  },
  {
    path: '/xlb_erp/inventoryReason/index',
    component: '@/pages/stock/inventoryReason/header/index',
    title: '库存调整原因',
    subTitle: '业务设置',
    subMenu: 'stock',
    tabClass: 'inventoryReason',
  },

];

export { routeList as stockRouteList };
import { XlbTableColumnProps } from "@xlb/components"

export const checkOptions = [
  {
    label: '停购',
    value: 'stop_purchase'
  },
  {
    label: '停售',
    value: 'stop_sale'
  },
  {
    label: '停止要货',
    value: 'stop_request'
  }
]
export const ShopArr: any[] = [
  {
    name: '序号',
    code: '_index',
    width: 60,
    align: 'center'
  },
  {
    name: '组织',
    code: 'org_name',
    width: 140,
    hidden: true
  },
  {
    name: '门店编码',
    code: 'store_number',
    width: 100,
    features: { sortable: true }
  },
  {
    name: '门店名称',
    code: 'store_name',
    width: 180,
    features: { sortable: true }
  },
  {
    name: '门店分组',
    code: 'store_group_name',
    width: 100,
    features: { sortable: true }
  },
  {
    name: '经营类型',
    code: 'management_type',
    width: 100,
    features: { sortable: true },
    render: (Text: any, record: any) => {
      return record.management_type == '1' ? '加盟店' : '直营店'
    }
  },
  {
    name: '配送类型',
    code: 'delivery_type',
    width: 100,
    render: (Text: any, record: any) => {
      return record.delivery_type == 'JOIN' ? '加盟' : '直营'
    },
    features: { sortable: true }
  },
  {
    name: ''
  }
]
export const goodsType = [
  {
    label: '主规格商品',
    value: 'MAINSPEC'
  },
  {
    label: '多规格商品',
    value: 'MULTIPLESPEC'
  },
  {
    label: '标准商品',
    value: 'STANDARD'
  },
  {
    label: '组合商品',
    value: 'COMBINATION'
  },
  {
    label: '成分商品',
    value: 'COMPONENT'
  },
  {
    label: '制单组合',
    value: 'MAKEBILL'
  },
  {
    label: '分级商品',
    value: 'GRADING'
  }
]
export const batch = [
  {
    label: '零售价',
    value: 'sale_price'
  },
  {
    label: '零售价2',
    value: 'sale_price_s'
  },
  {
    label: '零售价3',
    value: 'sale_price_t'
  },
  {
    label: '零售价4',
    value: 'sale_price_f'
  },
  {
    label: '最低售价',
    value: 'sale_min_price'
  },
  {
    label: '最高售价',
    value: 'sale_max_price'
  }
]
export const tableList: XlbTableColumnProps<any>[] = [
  {
    name: '序号',
    code: '_index',
    width: 80,
    align: 'center'
  },
  {
    name: '零售价模板代码',
    code: 'code',
    width: 130,
    features: { sortable: true }
  },
  {
    name: '零售价模板名称',
    code: 'name',
    width: 200,
    features: { sortable: true }
  },
  {
    name: '组织',
    code: 'org_name',
    width: 140,
    hidden: true
  },
  {
    name: ''
  }
]

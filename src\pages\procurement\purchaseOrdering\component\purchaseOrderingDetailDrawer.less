.modal-subtitle {
  border-radius: 2px;
  background: #00b341;
  color: #ffffff;
  font-size: 11px;
  width: 23px;
  height: 16px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-left: 8px;
  padding: 1px 2px;
}
.purchase-order-container {
  padding: 6px 0;
  font-size: 14px;
  line-height: 20px;
  .detail-container {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    row-gap: 12px;
    column-gap: 8px;
    margin-top: 14px;
    .detail-item {
      display: flex;
      align-items: center;
      &__label {
        color: #86909c;
        width: 88px;
      }
      &__value {
        margin-left: 8px;
        color: #1d2129;
      }
      &.min_order_qty_desc {
        grid-column: 1 / 4;
      }
    }
  }
  .common-border {
    padding: 8px 12px;
    border-radius: 4px;
    border: 1px solid rgba(30, 33, 38, 0.15);
  }
  .active-container {
    margin-top: 14px;
    .active-title {
      display: flex;
      justify-content: space-between;
      &__name {
        font-weight: 500;
        color: #26272a;
      }
      &__view {
        color: #1a6aff;
        cursor: pointer;
        user-select: none;
      }
    }
  }
  .analysis-container {
    .active-title {
      color: #26272a;
      font-weight: 500;
    }
    .analysis-group.common-border {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      padding: 0;
      .analysis-item {
        padding: 8px 0;
        border-right: 1px solid #e5e6eb;
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
        row-gap: 6px;
        &:last-child {
          border: none;
          &::after {
            display: none;
          }
        }
        // 图标
        &::after {
          position: absolute;
          content: '';
          width: 20px;
          height: 20px;
          inset: 0 0 0 auto;
          margin: auto;
          background: url('../images/symbol-equal-to.png') no-repeat
            center/cover;
          transform: translateX(50%);
        }
        &.symbol-subtract::after {
          background-image: url('../images/symbol-subtract.png');
        }
        &.symbol-comma::after {
          background-image: url('../images/symbol-comma.png');
        }
        &.symbol-add::after {
          background-image: url('../images/symbol-add.png');
        }
        &.symbol-none::after {
          display: none;
        }
        // 内容
        &__label {
          font-size: 14px;
          color: #4e5969;
        }
        &__value {
          font-size: 16px;
          font-weight: bold;
          color: #1f2126;
        }
        // 特殊处理盒子
        &.analysis-symbol {
          width: 84px;
          height: 100%;
          padding: 8px 16px;
          align-items: flex-end;
        }
        &.w-52px {
          width: 52px;
        }
        &.w-72px {
          width: 72px;
        }
        &__introduce {
          border-top: 1px solid #e5e6eb;
          padding: 8px 16px;
          column-gap: 6px;
          .analysis-item__value,
          .analysis-item__label {
            line-height: normal;
          }
        }
        &.fixed-position::after {
          inset: 21px 0 auto auto;
        }
      }
      .analysis-child-group {
        grid-column: 2 / 5;
        &__item {
          flex: 1;
          display: grid;
          grid-template-columns: repeat(3, 1fr);
        }
      }
      .analysis-introduce {
        color: #1f2126;
        font-size: 14px;
        line-height: 24px;
      }
    }
  }
}

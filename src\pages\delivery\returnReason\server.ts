import {XlbFetch as ErpRequest } from '@xlb/utils'


// 新增统配
export  const addstrongReason = async (data: any) => {
    return await ErpRequest.post('/erp/hxl.erp.reason.save', { data })
}

// 删除统配
export  const deletestrongReason = async (data: any) => {
    return await ErpRequest.post('/erp/hxl.erp.reason.delete', { data })
}

// 修改统配
export  const editstrongReason = async (data: any) => {
    return await ErpRequest.post('/erp/hxl.erp.reason.update', { data })
}
  

import { XlbModal, XlbTable } from '@xlb/components';
import { useEffect, useState } from 'react';
import { historyArr } from '../data';
import { gethistory } from '../server';
import styles from './history';

export default function History(props: any) {
  const { visible, onCancel, id, unitType } = props;
  const [rowData, setRowData] = useState<any[]>([]);
  const [itemArr, setItemArr] = useState(
    JSON.parse(JSON.stringify(historyArr)),
  );
  const [pagin, setPagin] = useState({
    pageSize: 200,
    pageNum: 1,
    total: 0,
  });

  const getHistory = async (page_number: any) => {
    const data = {
      company_id: 1000,
      operator: JSON.parse(localStorage.userInfo).value.account,
      store_id: id[0].store_id,
      unit_type: unitType,
      item_id: id[0].item_id,
    };
    const res = await gethistory(data);
    if (res.code === 0) {
      setRowData(res.data);
      setPagin({
        ...pagin,
        pageNum: page_number,
        total: res.data?.total_elements,
      });
    }
  };
  // const tableRender = (item: any) => {
  //   switch (item.code) {
  //     case 'index':
  //       item.render = (value: any, record: any, index: number) => {
  //         return <div className="info overwidth">{(pagin.pageNum - 1) * pagin.pageSize + 1 + index}</div>
  //       }
  //       break
  //     case 'memo':
  //       item.render = (value: any, record: any, index: number) => {
  //         return <Tooltip placement="topRight" title={value} mouseEnterDelay={1}>
  //           <div className="info overwidth">{value}</div>
  //         </Tooltip>

  //       }
  //       break
  //     default:
  //       item.render = (value: any) => {
  //         return <div className="info overwidth">{value}</div>
  //       }
  //       break
  //   }
  // }
  useEffect(() => {
    // itemArr.map((v: any) => tableRender(v))
    visible && getHistory(1);
  }, [visible]);

  // const pageChange = (p: number) => {
  //   setPagin({
  //     ...pagin,
  //     pageNum: p
  //   })
  //   getHistory(p).then()
  // }

  // const pipeline = useTablePipeline({ components: fusion })
  //   .input({ dataSource: rowData, columns: itemArr })
  //   .primaryKey('id')
  //   .use(
  //     features.sort({
  //       mode: 'single',
  //       highlightColumnWhenActive: true
  //     })
  //   )
  //   .use(
  //     features.columnResize({
  //       fallbackSize: 100,
  //       onChangeSizes: (nextSizes: number[]) => {
  //         itemArr.map((v, i) => {
  //           v.width = nextSizes[i]
  //         })
  //       }
  //     })
  //   )

  return (
    <div>
      <XlbModal
        title={'修改记录'}
        visible={visible}
        width={750}
        maskClosable={true}
        onCancel={onCancel}
        onOk={onCancel}
        footer={null}
      >
        <div className={rowData?.length ? styles.table_box : ''}>
          <XlbTable
            dataSource={rowData}
            columns={itemArr}
            total={rowData?.length}
            pageSize={100}
            style={{
              overflow: 'auto',
              height: 360,
            }}
          ></XlbTable>
        </div>
        {/* <XlbPagin
          style={{ paddingBottom: 10 }}
          total={rowData?.length}
          pageNum={pagin.pageNum}
          pageSize={pagin.pageSize}
          pageChange={pageChange}
          list={rowData}
          selectMode="single"
        /> */}
      </XlbModal>
    </div>
  );
}

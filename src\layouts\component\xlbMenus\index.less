.menu_container {
  height: 100%;
  border-right: solid 1px #e5e6ea;
  transition: 0.4s;
  :global .ant-popover {
    .ant-popover-inner {
      border-radius: 4px;
    }
  }

  .menu_item {
    position: relative;
    display: flex;
    align-items: center;
    width: 100%;
    height: 40px;
    margin-top: 10px;
    padding: 10px;
    color: #1d2129;
    cursor: pointer;
    :nth-child(1) {
      color: #c9cdd4;
      font-size: 20px;
    }
    :nth-child(2) {
      margin-left: 8px;
      color: #1d2129;
      font-size: 14px;
    }
    &:hover {
      // background: linear-gradient(to right, #fff, #ced8fa);
      // border-right: 3px solid @color_theme;
      span {
        color: @color_theme !important;
      }
    }
  }
  .menu_item_active {
    .menu_item;
    span {
      color: @color_theme !important;
    }
    background: linear-gradient(to right, #fff, #ced8fa);
    border-right: 3px solid @color_theme;
  }
  .menu_items {
    display: flex;
    align-items: center;
    width: 100%;
    padding: 16px 10px 2px;
    font-weight: 500;
    font-size: 14px;
    cursor: pointer;
  }
}

.menu_detail_container {
  width: 400px;
  max-height: calc(100vh - 100px);
  padding: 0 0 20px 15px;
  overflow-x: hidden;
  overflow-y: auto;
}
.menu_detail_item {
  padding: 0 0 16px 0;
  border-bottom: 1px solid #f0f0f0;
  &:last-child {
    border-bottom: none;
  }
}
.menu_detail_item_title {
  color: #999999;
  font-size: 12px;
}
.menu_detail_item_span {
  color: #000000;
  font-size: 14px;
  //margin-left: 58px;
  cursor: pointer;
  &:hover {
    color: @color_theme!important;
  }
}

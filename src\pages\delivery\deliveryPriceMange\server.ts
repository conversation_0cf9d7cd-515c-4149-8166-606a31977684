import { XlbFetch as ErpRequest } from '@xlb/utils';
export default class Api {
  //获取数据
  static getAllList = async (data: any) => {
    return await ErpRequest.post('/erp/hxl.erp.deliverypriceadjust.page', data);
  };
  //新增
  static addInfo = async (data: any) => {
    return await ErpRequest.post('/erp/hxl.erp.deliverypriceadjust.save', data);
  };
  //删除
  static deleteInfo = async (data: any) => {
    return await ErpRequest.post(
      '/erp/hxl.erp.deliverypriceadjust.batchdelete',
      data,
    );
  };
  //读取
  static readInfo = async (data: any) => {
    return await ErpRequest.post('/erp/hxl.erp.deliverypriceadjust.read', data);
  };
  //审核
  static auditInfo = async (data: any) => {
    return await ErpRequest.post(
      '/erp/hxl.erp.deliverypriceadjust.audit',
      data,
    );
  };
  //反审核
  static unAuditInfo = async (data: any) => {
    return await ErpRequest.post(
      '/erp/hxl.erp.deliverypriceadjust.reaudit',
      data,
    );
  };
  //更新
  static updateInfo = async (data: any) => {
    return await ErpRequest.post(
      '/erp/hxl.erp.deliverypriceadjust.update',
      data,
    );
  };
  //通过
  static passInfo = async (data: any) => {
    return await ErpRequest.post(
      '/erp/hxl.erp.deliverypriceadjust.handle',
      data,
    );
  };
  //拒绝
  static refuseInfo = async (data: any) => {
    return await ErpRequest.post(
      '/erp/hxl.erp.deliverypriceadjust.handlerefuse',
      data,
    );
  };
  //终止
  static abortInfo = async (data: any) => {
    return await ErpRequest.post(
      '/erp/hxl.erp.deliverypriceadjust.handleabort',
      data,
    );
  };
  //查询中心配送门店
  static getCenterStore = async () => {
    return await ErpRequest.post('/erp-mdm/hxl.erp.store.center.find');
  };
  // 获取配送价
  static getGoodsPrice = async (data: any) => {
    return await ErpRequest.post(
      '/erp/hxl.erp.deliverypriceadjust.deliveryprice.page',
      data,
    );
  };

  // 复制
  static copyInfo = async (data: any) => {
    return await ErpRequest.post('/erp/hxl.erp.deliverypriceadjust.copy', data);
  };

  // 获取模板数据
  static getNotice = async (data: any) => {
    return await ErpRequest.post('/erp/hxl.erp.noticetemplate.find', data);
  };

  // 获取模板内容
  static getNoticeContent = async (data: any) => {
    return await ErpRequest.post('/erp/hxl.erp.noticetemplate.read', data);
  };
}
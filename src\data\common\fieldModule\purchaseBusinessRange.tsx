export const PurchaseBusinessRangeKeyMap = {
  erpRangeCategoryIds: 'erpRangeCategoryIds'
}

export const purchaseBusinessRangeConfig: any[] = [
  {
    tag: 'ERP',
    label: '类别',
    id: PurchaseBusinessRangeKeyMap.erpRangeCategoryIds,
    name: 'category_ids',
    fieldProps: {
      treeModalConfig: {
        // @ts-ignore
        title: '经营范围类别',
        url: '/erp-mdm/hxl.erp.businessscopecategory.find',
        multiple: true,
        checkable: true,
        dataType: 'lists',
        params: {
          business_type: 0
        }
      }
    },
    componentType: 'inputDialog'
  }
]
import { defineConfig } from '@umijs/max';
import { routeList } from './src/router';
export default defineConfig({
  codeSplitting: {
    jsStrategy: 'granularChunks',
  },
  mfsu: false, // @xlb/max
  antd: {},
  cssLoaderModules: {
    // 配置驼峰式使用
    exportLocalsConvention: 'camelCase',
  },
  plugins: ['@umijs/max-plugin-openapi', 'umi-plugin-keep-alive'],
  model: {},
  initialState: {},
  request: {},
  layout: {
    title: 'ERP连锁管理',
  },

  title: 'ERP连锁管理',
  extraPostCSSPlugins: [
    require('tailwindcss')({ config: './tailwind.config.js' }),
  ],
  locale: {
    // 默认使用 src/locales/zh-CN.ts 作为多语言文件
    default: 'zh-CN',
    baseSeparator: '-',
  },
  hash: true,
  proxy: {
    '/erp': {
      target: 'https://react-web.react-web.ali-test.xlbsoft.com/',
      changeOrigin: true,
      // pathRewrite: { '^/erp': '' },
      onProxyReq: (proxyReq) => {
        proxyReq.setHeader('origin', 'http://localhost:8000/');
      },
    },
    '/scm': {
      target: 'https://react-web.react-web.ali-test.xlbsoft.com/',
      changeOrigin: true,
      // pathRewrite: { '^/erp': '' },
      onProxyReq: (proxyReq) => {
        proxyReq.setHeader('origin', 'http://localhost:7788/');
      },
    },
  },
  theme: {
    '@color_fff': '#FFFFFF',
    '@color_header': '#2069D1',
    '@color_tab': '#F4F7FA',
    '@color_main': '#F4F7FA',
    '@color_home': '#EFF2F7',
    '@color_link': '#3D66FE',
    '@color_disabled': '#F2F3F5',

    '@page_bg': '#F3F4F7',

    // 主色
    '@color_theme': '#3D66FE',
    '@color_theme_bg': '#E8F1FF',

    // 按钮，辅助图形，图标
    '@color_init': '#1D2129',
    '@color_default': '#3D66FE',
    '@color_danger': '#FF0000',
    '@color_invalid': '#86909C',
    '@color_warning': '#FF7D01',
    '@color_success': '#00B42B',
    '@color_purple': '#CC66CC',
    // 边框、分栏
    '@color_line1': '#393D49',
    '@color_line2': '#E5E6EA',
    '@color_line3': '#F5F7FA',
    '@color_line4': '#DCDFE6',
    '@color_line5': '#ECEDF0',
    // 文字
    color_black1: '#000',
    color_black2: '#333',
    color_black3: '#666',
    color_black4: '#999',

    '@size_5': '5px',
    '@size_10': '10px',
    '@size_12': '12px',
    '@size_14': '14px',
    '@size_18': '18px',
    '@size_20': '20px',
  },
  routes: routeList,
  npmClient: 'npm',
});

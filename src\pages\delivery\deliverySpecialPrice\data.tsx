import approvalPass from '@/assets/images/xlb_erp/approvalPass.png';
import approvalReject from '@/assets/images/xlb_erp/approvalReject.png';
import cancel from '@/assets/images/xlb_erp/cancel.png';
import checking from '@/assets/images/xlb_erp/checking.png';
import end from '@/assets/images/xlb_erp/end.png';
import endState from '@/assets/images/xlb_erp/endState.png';
import finish from '@/assets/images/xlb_erp/finish.png';
import handling from '@/assets/images/xlb_erp/handling.png';
import init from '@/assets/images/xlb_erp/init.png';
import partCancel from '@/assets/images/xlb_erp/partCancel.png';
import pass from '@/assets/images/xlb_erp/pass.png';
import reject from '@/assets/images/xlb_erp/reject.png';
import unPay from '@/assets/images/xlb_erp/unPay.png';
import { columnWidthEnum } from '@/data/common/constant';
import { hasAuth } from '@/utils';
import { XlbTableColumnProps } from '@xlb/components';

export const tableStyle = {
  '--header-row-height': '28px',
  '--row-height': '24px',
  '--header-bgcolor': '#fafafa',
  '--header-color': '#666',
  '--row-color': '#000',
  '--cell-padding': '0',
  '--highlight-bgcolor': '#E8F1FF',
  fontSize: '14px',
};

//单据状态
export const Options1 = [
  {
    label: '制单',
    value: 'INIT',
    type: 'info',
  },
  {
    label: '审核',
    value: 'AUDIT',
    type: 'warning',
  },
  {
    label: '处理通过',
    value: 'HANDLE',
    type: 'success',
  },
  {
    label: '处理拒绝',
    value: 'HANDLE_REFUSE',
    type: 'danger',
  },
  {
    label: '作废',
    value: 'INVALID',
    type: 'danger',
  },
];
// 时间类型
export const Options3 = [
  {
    label: '制单时间',
    value: 0,
  },
  {
    label: '审核时间',
    value: 1,
  },
  {
    label: '处理时间',
    value: 2,
  },
  {
    label: '作废时间',
    value: 3,
  },
];
export const formList: any[] = [
  {
    label: '单据状态',
    value: 'state',
    type: 'select',
    clear: true,
    check: true,
    options: Options1,
  },
  {
    label: '单据号',
    value: 'fid',
    type: 'input',
    clear: true,
    check: true,
  },
  {
    label: '时间类型',
    value: 'time_type',
    type: 'select',
    clear: false,
    check: true,
    options: Options3,
  },
  {
    label: '配送组织',
    value: 'org_names',
    type: 'dialog',
    clear: true,
    check: true,
    hidden: true,
  },
  {
    label: '配送门店',
    value: 'store_names',
    type: 'dialog',
    clear: false,
    check: true,
  },
  {
    label: '应用组织',
    value: 'supply_org_names',
    type: 'dialog',
    clear: true,
    check: true,
    hidden: true,
  },
  {
    label: '应用门店',
    value: 'supply_store_names',
    type: 'dialog',
    clear: false,
    check: true,
  },
  {
    label: '商品档案',
    value: 'item_names',
    type: 'dialog',
    clear: true,
    check: true,
  },
  {
    label: '特价模式',
    value: 'type',
    type: 'select',
    clear: true,
    check: true,
    options: [
      {
        label: '补货特价',
        value: 'REQUEST',
      },
      {
        label: '调出特价',
        value: 'DELIVERY_OUT',
      },
    ],
  },
  {
    label: '制单人',
    value: 'create_by',
    type: 'input',
    check: true,
    clear: true,
  },
  {
    label: '审核人',
    value: 'audit_by',
    type: 'input',
    check: true,
    clear: true,
  },
  {
    label: '生成费用单',
    value: 'supplier_fee_status',
    type: 'select',
    clear: true,
    check: true,
    options: [
      {
        label: '是',
        value: 'true',
      },
      {
        label: '否',
        value: 'false',
      },
    ],
  },
  {
    label: ' ',
    value: 'checkValue',
    type: 'checkGroup',
    // colon: false,
    options: [
      {
        label: '仅查询有效期内单据',
        value: 'flag',
      },
      {
        label: '仅查询剩余商品大于0的单据',
        value: 'have_remain_quantity',
      },
    ],
  },
];

export const tableList: XlbTableColumnProps<any>[] = [
  {
    name: '序号',
    code: '_index',
    width: columnWidthEnum.INDEX,
    align: 'center',
  },
  {
    name: '单据号',
    code: 'fid',
    width: columnWidthEnum.fid,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '配送门店',
    code: 'store_name',
    width: columnWidthEnum.STORE_NAME,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '应用门店数',
    code: 'store_count',
    width: 120,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '特价模式',
    code: 'type',
    width: 120,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '单据状态',
    code: 'state',
    width: columnWidthEnum.ORDER_STATE,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '生成费用单',
    code: 'supplier_fee_status',
    width: 100,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '开始时间',
    code: 'start_time',
    width: 180,
    features: { sortable: true, format: 'TIME' },
    align: 'left',
  },
  {
    name: '结束时间',
    code: 'end_time',
    width: 180,
    features: { sortable: true, format: 'TIME' },
    align: 'left',
  },
  {
    name: '制单人',
    code: 'create_by',
    width: columnWidthEnum.BY,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '审核人',
    code: 'audit_by',
    width: columnWidthEnum.BY,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '处理人',
    code: 'handle_by',
    width: columnWidthEnum.BY,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '作废人',
    code: 'invalid_by',
    width: 90,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '制单时间',
    code: 'create_time',
    width: 180,
    features: { sortable: true, format: 'TIME' },
    align: 'left',
  },
  {
    name: '审核时间',
    code: 'audit_time',
    width: 180,
    features: { sortable: true, format: 'TIME' },
    align: 'left',
  },
  {
    name: '处理时间',
    code: 'handle_time',
    width: 180,
    features: { sortable: true, format: 'TIME' },
    align: 'left',
  },
  {
    name: '作废时间',
    code: 'invalid_time',
    width: 180,
    features: { sortable: true, format: 'TIME' },
    align: 'left',
  },
  {
    name: '留言备注',
    code: 'memo',
    width: columnWidthEnum.MEMO,
    features: { sortable: true },
    align: 'left',
  },
];

//商品明细
export const itemTableListDetail: XlbTableColumnProps<any>[] = [
  {
    name: '序号',
    code: '_index',
    width: columnWidthEnum.INDEX,
    align: 'center',
  },
  {
    name: '操作',
    code: '_operator',
    align: 'center',
    width: 80,
  },
  {
    name: '供应商',
    code: 'supplier_name',
    width: 160,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '商品代码',
    code: 'item_code',
    width: columnWidthEnum.ITEM_CODE,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '商品条码',
    code: 'item_bar_code',
    width: columnWidthEnum.ITEM_BAR_CODE,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '商品名称',
    code: 'item_name',
    width: 280,
    features: { sortable: true, showShort: true },
    align: 'left',
  },
  {
    name: '采购规格',
    code: 'item_spec',
    width: columnWidthEnum.ITEM_SPEC,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '配送单位',
    code: 'unit',
    width: 110,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '起始数量',
    code: 'initial_quantity',
    width: 110,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '活动数量',
    code: 'activity_quantity',
    width: 110,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '剩余数量',
    code: 'remain_quantity',
    width: 110,
    features: { sortable: true },
    align: 'right',
    render: (value) => {
      return <div className="info">{value?.toFixed(3)}</div>;
    },
  },
  {
    name: '批次',
    code: 'producing_date',
    width: 180,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '批次库存',
    code: 'batch_stock',
    width: 110,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '特价类型',
    code: 'special_price_type',
    width: 160,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '数值',
    code: 'value',
    width: 110,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '组织配送价',
    code: 'price',
    width: 110,
    features: { sortable: true },
    align: 'right',
    render: (value) => {
      return (
        <span>
          {hasAuth(['配送特价/配送价', '查询']) ? value?.toFixed(4) : '****'}
        </span>
      );
    },
  },
  {
    name: '配送特价',
    code: 'special_price',
    width: 110,
    features: { sortable: true },
    align: 'right',
    render: (value) => {
      return (
        <span>
          {hasAuth(['配送特价/配送价', '查询']) ? value?.toFixed(4) : '****'}
        </span>
      );
    },
  },
  {
    name: '备注',
    code: 'memo',
    width: columnWidthEnum.MEMO,
    features: { sortable: true },
    align: 'left',
  },
];

// 单据状态对应图标
export const orderStatusIcons: any = {
  INIT: init,
  AUDIT: checking,
  APPROVE: approvalPass,
  APPROVE_REFUSE: approvalReject,
  HANDLE: pass,
  HANDLE_REFUSE: reject,
  HANDLE_ING: handling,
  REFUSE: reject,
  INVALID: cancel,
  HANDLE_ABORT: end,
  ABORT: endState,
  PART_INVALID: partCancel,
  PAID: unPay,
  FINISH: finish,
};

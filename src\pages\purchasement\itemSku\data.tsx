import { XlbTableColumnProps } from '@xlb/components';
// import { XlbFetch as ErpRequest } from '@xlb/utils';
// const userInfo = LStorage('userInfo')
// import { ArtColumn } from 'ali-react-table'
import { columnWidthEnum } from '@/data/common/constant';

export const goodsType = [
  {
    label: '主规格商品',
    value: 'MAINSPEC',
  },
  {
    label: '多规格商品',
    value: 'MULTIPLESPEC',
  },
  {
    label: '标准商品',
    value: 'STANDARD',
  },
  {
    label: '组合商品',
    value: 'COMBINATION',
  },
  {
    label: '成分商品',
    value: 'COMPONENT',
  },
  {
    label: '制单组合',
    value: 'MAKEBILL',
  },
  {
    label: '分级商品',
    value: 'GRADING',
  },
];

//  {
//               label: '组织',
//               name: 'org_ids',
//               id: ErpFieldKeyMap?.erpOrgIdsLevel2,
//               hidden: !enable_organization,
//             },
//             {
//               id: ErpFieldKeyMap?.erpStoreIds,
//               name: 'store_id_list',
//             },

//
//  {
//                               id: 'inputPanel',
//                               label: '其他条件',
//                               name: 'checkValue',
//                               fieldProps: {
//                                 initialValue: 'false',
//                                 allowClear: true,
//                                 options: checkOptions,
//                                 items: [
//                                   {
//                                     label: '仅显示',
//                                     key: true,
//                                   },
//                                   {
//                                     label: '不显示',
//                                     key: false,
//                                   },
//                                 ],
//                                 style: {
//                                   width: 150,
//                                 },
//                                 width: 150,
//                               },
//                               itemSpan: 4,
//                             },

export const tableList: XlbTableColumnProps<any>[] = [
  // {
  //   name: '序号',
  //   code: 'index',
  //   width: 60,
  //   align: 'center'
  // },
  {
    name: '商品分类',
    code: 'category_name',
    width: 200,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: 'sku数量',
    code: 'skuQuantity',
    width: 100,
    features: { sortable: true },
    align: 'center',
    children: [
      {
        name: '中心理论初始值',
        code: 'theory_business_sku_num',
        width: 144,
        features: { sortable: true },
        align: 'left',
      },
      {
        name: '门店理论初始值',
        code: 'store_theory_initial_sku_num',
        width: 144,
        align: 'left',
      },
      {
        name: '正常',
        code: 'normal_quantity',
        width: 100,
        align: 'center',
        render: (text, record, index) => {
          return Number(text) > 0 && record.category_name !== '合计' ? (
            <span style={{ color: '#1989fa', cursor: 'pointer' }}>{text}</span>
          ) : (
            text
          );
        },
      },
      {
        name: '停购',
        code: 'stop_purchase_quantity',
        align: 'center',
        width: 100,
        render: (text) => {
          return Number(text) > 0 ? (
            <span style={{ color: '#1989fa', cursor: 'pointer' }}>{text}</span>
          ) : (
            text
          );
        },
      },
      {
        name: '停售',
        code: 'stop_sale_quantity',
        align: 'center',
        width: 100,
        render: (text) => {
          return Number(text) > 0 ? (
            <span style={{ color: '#1989fa', cursor: 'pointer' }}>{text}</span>
          ) : (
            text
          );
        },
      },
      {
        name: '停止要货',
        code: 'stop_request_quantity',
        align: 'center',
        width: 100,
        render: (text) => {
          return Number(text) > 0 ? (
            <span style={{ color: '#1989fa', cursor: 'pointer' }}>{text}</span>
          ) : (
            text
          );
        },
      },
      {
        name: '称重',
        code: 'weigh_quantity',
        align: 'center',
        width: 100,
        render: (text) => {
          return Number(text) > 0 ? (
            <span style={{ color: '#1989fa', cursor: 'pointer' }}>{text}</span>
          ) : (
            text
          );
        },
      },
    ],
  },
  {
    name: '',
    code: '',
    align: 'right',
  },
];

export const stockTableList: ArtColumn[] = [
  {
    name: '序号',
    code: 'index',
    width: columnWidthEnum.INDEX,
    align: 'center',
  },
  {
    name: '商品代码',
    code: 'code',
    width: 110,
    features: { sortable: true },
  },
  {
    name: '商品条码',
    code: 'bar_code',
    width: 130,
    features: { sortable: true },
  },
  {
    name: '商品名称',
    code: 'name',
    width: 180,
    features: { sortable: true },
  },
  {
    name: '分类名称',
    code: 'category_name',
    width: 120,
    features: { sortable: true },
  },
  {
    name: '商品类型',
    code: 'item_type',
    width: 110,
    features: { sortable: true },
  },
  {
    name: '采购规格',
    code: 'purchase_spec',
    width: 110,
    features: { sortable: true },
  },
];

import { LStorage } from '@/utils/storage';
import NiceModal from '@ebay/nice-modal-react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { XlbConfigProvider } from '@xlb/components';
import { XlbFetch } from '@xlb/utils';
import { FC, PropsWithChildren } from 'react';
import { config } from './constants/baseDataConfig';
import { fieldListConfig } from './data/common/fieldListConfig';

const Provider: FC<PropsWithChildren> = ({ children }) => {
  const queryClient = new QueryClient();

  XlbFetch.interceptors.request.use((config) => {
    const userInfo = LStorage.get('userInfo');
    const companyId = userInfo?.company_id;
    const userId = userInfo?.id;
    const orgIds = userInfo?.org_ids;
    config.headers = {
      ...config.headers,
      'Api-Version': '1.5.0',
    };
    if (companyId) {
      config.headers['Company-Id'] = companyId;
    }
    if (userId) {
      config.headers['User-Id'] = userId;
    }
    if (Array.isArray(orgIds) && orgIds.length) {
      config.headers['Org-Ids'] = orgIds.join(',');
    }
    return config;
  });
  return (
    <XlbConfigProvider.Provider
      value={{
        baseURL: process.env.BASE_URL as string,
        config: config,
        isOldBtn: true,
        fieldList: fieldListConfig,
        globalFetch: XlbFetch,
        columns: {
          query: '/erp/hxl.erp.usercolumn.get',
          update: '/erp/hxl.erp.usercolumn.update',
        },
      }}
    >
      <QueryClientProvider client={queryClient}>
        <NiceModal.Provider>{children}</NiceModal.Provider>
      </QueryClientProvider>
    </XlbConfigProvider.Provider>
  );
};

export default Provider;
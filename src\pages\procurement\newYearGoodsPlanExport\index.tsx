import { columnWidthEnum } from '@/data/common/constant';
import { ErpFieldKeyMap } from '@/data/common/fieldListConfig';
import { hasAuth } from '@/utils';
import type { XlbTableColumnProps } from '@xlb/components';
import { XlbProPageContainer } from '@xlb/components';
import { toFixed } from '@xlb/utils';
import dayjs from 'dayjs';
import { omit } from 'lodash';
import type { FC } from 'react';
import { baseColumns } from './data';
import type {
  PurchasePlanReportResDTO,
  PurchasePlanReportTotalResDTO,
} from './type';

const NewYearGoodsPlanExport: FC = () => {
  // 按商品
  const coverItemColumn =
    (): XlbTableColumnProps<PurchasePlanReportResDTO>[] => {
      const columns: XlbTableColumnProps<PurchasePlanReportResDTO>[] = [
        {
          code: 'item_name',
          name: '商品名称',
          width: columnWidthEnum.STORE_NAME,
          features: { sortable: true },
          align: 'center',
        },
        {
          code: 'item_code',
          name: '商品代码',
          width: columnWidthEnum.ITEM_CODE,
          features: { sortable: true },
          align: 'center',
        },
        {
          code: 'item_barcode',
          name: '商品条码',
          width: columnWidthEnum.ITEM_BAR_CODE,
          features: { sortable: true },
          align: 'center',
        },
      ];
      return columns;
    };

  // 按计划
  const coverPlanColumn =
    (): XlbTableColumnProps<PurchasePlanReportResDTO>[] => {
      const columns: XlbTableColumnProps<PurchasePlanReportResDTO>[] = [
        {
          code: 'purchase_plan_name',
          name: '计划名称',
          align: 'center',
          features: { sortable: true },
        },
      ];
      return columns;
    };

  // 按门店
  const coverStoreColumn =
    (): XlbTableColumnProps<PurchasePlanReportResDTO>[] => {
      const columns: XlbTableColumnProps<PurchasePlanReportResDTO>[] = [
        {
          code: 'store_name',
          name: '门店',
          features: { sortable: true },
          align: 'center',
        },
      ];
      return columns;
    };

  // 按供应商
  const coverSupplierColumn =
    (): XlbTableColumnProps<PurchasePlanReportResDTO>[] => {
      const columns: XlbTableColumnProps<PurchasePlanReportResDTO>[] = [
        {
          code: 'supplier_name',
          name: '供应商',
          features: { sortable: true },
          align: 'center',
        },
      ];

      return columns;
    };

  // 按订单员
  const coverPurchaseColumn =
    (): XlbTableColumnProps<PurchasePlanReportResDTO>[] => {
      const columns: XlbTableColumnProps<PurchasePlanReportResDTO>[] = [
        {
          code: 'purchaser',
          name: '订单员',
          features: { sortable: true },
          align: 'center',
        },
      ];

      return columns;
    };

  return (
    <XlbProPageContainer
      searchFieldProps={{
        initialValues: {
          summary_types: ['ITEM'],
          operate_date: [
            dayjs().subtract(1, 'month').format('YYYY-MM-DD'),
            dayjs().format('YYYY-MM-DD'),
          ],
        },
        formList: [
          {
            id: 'dateCommon',
            name: 'operate_date',
          },
          {
            id: 'commonSelect',
            name: 'summary_types',
            label: '汇总条件',
            rules: [{ message: '汇总条件不能为空', required: true }],
            fieldProps: {
              mode: 'multiple',
            },
            options: [
              {
                label: '按商品',
                value: 'ITEM',
              },
              {
                label: '按计划',
                value: 'PLAN',
              },
              {
                label: '按门店',
                value: 'DELIVERY_STORE',
              },
              {
                label: '按供应商',
                value: 'SUPPLIER',
              },
              {
                label: '按订单员',
                value: 'PURCHASER',
              },
            ],
          },
          {
            id: ErpFieldKeyMap.businessOrgIds,
            label: '所属组织',
          },
          {
            id: ErpFieldKeyMap.purchasePlans,
            name: 'purchase_plan_ids',
            label: '采购计划',
            hidden: (obj) => !obj.summary_types?.includes('PLAN'),
          },
          {
            id: ErpFieldKeyMap.erpStoreIds,
            name: 'store_ids',
            label: '门店',
            hidden: (obj) => !obj.summary_types?.includes('DELIVERY_STORE'),
          },
          {
            id: ErpFieldKeyMap.erpitemIds,
            label: '商品',
            hidden: (obj) => !obj.summary_types?.includes('ITEM'),
          },
          {
            id: ErpFieldKeyMap.erpSupplierIds,
            name: 'supplier_ids',
            label: '供应商',
            hidden: (obj) => !obj.summary_types?.includes('SUPPLIER'),
          },
          {
            id: ErpFieldKeyMap.purchaseManagerName,
            name: 'purchaser',
            label: '订单员',
            hidden: (obj) => !obj.summary_types?.includes('PURCHASER'),
          },
          {
            id: 'commonInputNumber',
            name: 'complete_rate',
            label: '计划达成率',
            fieldProps: {
              min: 0,
              max: 100,
              precision: 0,
            },
          },
          {
            id: 'commonInputNumber',
            name: 'receive_rate',
            label: '到货率',
            fieldProps: {
              min: 0,
              max: 100,
              precision: 0,
            },
          },
        ],
      }}
      exportFieldProps={{
        url: hasAuth(['采购计划报表', '导出'])
          ? '/erp/hxl.erp.purchaseplan.report.export'
          : '',
        fileName: '采购计划报表.xlsx',
      }}
      tableFieldProps={{
        url: '/erp/hxl.erp.purchaseplan.report',
        changeColumnAndResetDataSource: false,
        footerDataSource: (data: unknown) => {
          const { content } = (data as PurchasePlanReportTotalResDTO) || {};
          if (Array.isArray(content)) {
            const totalMapper = omit(
              data as PurchasePlanReportTotalResDTO,
              'content',
            );
            return [
              {
                _index: '合计',
                ...totalMapper,
              },
            ];
          }

          return [];
        },
        tableColumn(formValues, data: PurchasePlanReportTotalResDTO) {
          const indexColumns: XlbTableColumnProps<PurchasePlanReportResDTO>[] =
            [
              {
                code: '_index',
                name: '序号',
                align: 'center',
              },
            ];

          let columns: XlbTableColumnProps<PurchasePlanReportResDTO>[] = [];
          const { summary_types } = (formValues as any) || {};

          if (Array.isArray(summary_types)) {
            if (summary_types.includes('ITEM')) {
              columns = columns.concat(coverItemColumn());
            }

            if (summary_types.includes('PLAN')) {
              columns = columns.concat(coverPlanColumn());
            }

            if (summary_types.includes('DELIVERY_STORE')) {
              columns = columns.concat(coverStoreColumn());
            }

            if (summary_types.includes('SUPPLIER')) {
              columns = columns.concat(coverSupplierColumn());
            }

            if (summary_types.includes('PURCHASER')) {
              columns = columns.concat(coverPurchaseColumn());
            }

            const purchaser = baseColumns.find((i) => i.code == 'purchaser');
            purchaser?.children?.map((i: any) => {
              if (i.code == 'over_stock_quantity') {
                i.render = (text: any) =>
                  !summary_types.some((_i: any) =>
                    ['DELIVERY_STORE', 'PLAN', 'ITEM'].includes(_i),
                  )
                    ? '-'
                    : toFixed(text, 'QUANTITY');
              }
              return i;
            });

            // 如果多列，做一次聚合
            if (columns.length > 1) {
              columns = [
                {
                  code: 'base',
                  name: '基本信息',
                  align: 'center',
                  id: Math.random(),
                  children: columns,
                },
              ];
            }
            return indexColumns.concat(columns).concat(baseColumns);
          }
          return indexColumns.concat(baseColumns);
        },
      }}
    />
  );
};

export default NewYearGoodsPlanExport;

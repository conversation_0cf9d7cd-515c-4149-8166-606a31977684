import { routeList } from './index';


// export const MenuList = {
//   board: {
//     label: '看板',
//     key: 'board',
//     iconUrl: 'iconfont icon-kanban',
//   },
//   archives: {
//     label: '档案',
//     key: 'archives',
//     iconUrl: 'iconfont icon-dangan',
//     list: [{ label: '基础资料', children: [] }],
//   },
// };
// erp菜单栏配置
export const MenuList = {
  Board: {
    label: '看板',
    iconUrl: 'iconfont icon-kanban',
    key: 'erpBoard'
  },
  archives: {
    label: '档案',
    iconUrl: 'iconfont icon-dangan',
    key: 'archives',
    list: [
      { label: '基础资料', children: [] },
      { label: '基础项目', children: [] },
      { label: '业务模块', children: [] },
      { label: '业务参数', children: [] }
    ]
  },
  distribution: {
    label: '配送',
    iconUrl: 'iconfont icon-peisong',
    key: 'distribution',
    list: [
      { label: '业务设置', children: [] },
      { label: '业务操作', children: [] },
      { label: '物资管理', children: [] },
      { label: '档案配置', children: [] },
      { label: '数据查询', children: [] }
    ]
  },
  purchase: {
    label: '采购',
    iconUrl: 'iconfont icon-caigou',
    key: 'purchase',
    list: [
      { label: '基础配置', children: [] },
      { label: '业务操作', children: [] },
      // { label: '采购', children: [] },
      { label: '数据查询', children: [] },
      { label: '质量管理', children: [] }
    ]
  },
  stock: {
    label: '库存',
    iconUrl: 'iconfont icon-kucun',
    key: 'stock',
    list: [
      { label: '业务设置', children: [] },
      { label: '业务操作', children: [] },
      { label: '数据查询', children: [] }
    ]
  },
  wholesale: {
    label: '批发',
    iconUrl: 'iconfont icon-pifa',
    key: 'wholesale',
    list: [
      { label: '业务设置', children: [] },
      { label: '业务操作', children: [] },
      { label: '数据查询', children: [] }
    ]
  },
  retail: {
    label: '零售',
    iconUrl: 'iconfont icon-lingshou',
    key: 'retail',
    list: [
      { label: '基础设置', children: [] },
      { label: '业务操作', children: [] },
      { label: '数据查询', children: [] }
    ]
  },
  junction: {
    label: '结算',
    iconUrl: 'iconfont icon-jiesuan',
    key: 'junction',
    list: [
      { label: '门店', children: [] },
      { label: '供应商', children: [] },
      { label: '批发客户', children: [] },
      { label: '劳务商', children: [] },
      { label: '收付款管理', children: [] }
    ]
  },
  finance: {
    label: '财务',
    iconUrl: 'iconfont icon-caiwu',
    key: 'finance',
    list: [
      { label: '业务操作', children: [] },
      { label: '数据查询', children: [] }
    ]
  },
  dataqueries: {
    label: '数据',
    iconUrl: 'iconfont icon-shuju',
    key: 'dataqueries',
    list: [
      { label: '门店分析', children: [] },
      { label: '营业分析', children: [] },
      { label: '采购分析', children: [] },
      { label: '库存分析', children: [] },
      { label: '支付宝分析', children: [] }
    ]
  },
  manage: {
    label: '经营',
    iconUrl: 'iconfont icon-jingying',
    key: 'manage',
    list: [
      { label: '基础配置', children: [] },
      { label: '业务管理', children: [] },
      { label: '数据查询', children: [] }
    ]
  }
}
export const RouteSetting = {
  ['xlb_erp']: {
    MenuList: MenuList,
    RouteList: routeList,
  },
};

import { columnWidthEnum } from '@/data/common/constant';
import { hasAuth } from '@/utils';
import type { XlbTableColumnProps } from '@xlb/components';
import { XlbSwitch } from '@xlb/components';
import { message } from 'antd';
import { updateMinDeliveryConfig } from './server';

export const summaryType = [
  {
    label: '组织',
    value: 'ORGANIZATION',
    weight: 10,
  },
  {
    label: '门店类型',
    value: 'MANAGEMENTTYPE',
    weight: 20,
  },
  {
    label: '业务区域',
    value: 'BUSINESSAREA',
    weight: 30,
  },
  {
    label: '省',
    value: 'PROVINCE',
    weight: 40,
  },
  {
    label: '市',
    value: 'CITY',
    weight: 50,
  },
  {
    label: '区',
    value: 'DISTRICT',
    weight: 60,
  },
  {
    label: '门店',
    value: 'STORE',
    weight: 70,
  },
  // {
  //   label: '设备',
  //   value: 'DEVICE'
  // }
];

export const typeOptions = [
  {
    label: '单仓单品',
    value: 'single.item',
  },
  {
    label: '单仓多品',
    value: 'multiple.item',
  },
  {
    label: '供应商单仓',
    value: 'single.warehouse',
  },
];
// const [dataSource, setDataSource] = useState<any[]>([])

export const tableColumn: XlbTableColumnProps<any>[] = [
  {
    name: '序号',
    code: '_index',
    width: 50,
  },
  // TODO: 待后端完成后添加
  // {
  //   name: '组织',
  //   code: 'org_id',
  //   width: 140,
  //   features: { sortable: true },
  //   render(_, record) {
  //     return record?.org_name;
  //   },
  // },
  {
    name: '配送中心',
    code: 'center_store_id',
    width: columnWidthEnum.STORE_NAME,
    features: { sortable: true },
    render(text, record) {
      return record?.center_store_name;
    },
  },
  {
    name: '供应商代码',
    code: 'supplier_code',
    width: columnWidthEnum.fid,
    features: { sortable: true },
  },
  {
    name: '供应商',
    code: 'supplier_id',
    width: columnWidthEnum.STORE_NAME,
    features: { sortable: true, details: true },
    render(text, record) {
      return record?.supplier_name;
    },
  },
  {
    name: '起订量',
    code: 'quantity',
    features: { sortable: true },
  },
  {
    name: '起送金额',
    code: 'money',
    features: { sortable: true },
  },
  {
    name: '是否强校验',
    code: 'strong_validation',
    width: 160,
    render(text, record, operate) {
      return (
        <div className="v-flex">
          <XlbSwitch
            value={Boolean(text)}
            loading={record?._loading}
            disabled={!hasAuth(['起送量配置', '编辑'])}
            onChange={async (e: boolean) => {
              record._loading = true;
              const res = await updateMinDeliveryConfig({
                ...record,
                strong_validation: e,
              });
              if (res.code === 0) {
                message.success('更新成功');
                record.strong_validation = e;
              }
              operate?.fetchData?.();
            }}
          />
          <span style={{ marginLeft: 8 }}>{text ? '是' : '否'}</span>
        </div>
      );
    },
  },
  {
    name: '起订量类型',
    code: 'type',
    width: 120,
    features: { sortable: true },
    render: (text) => {
      const item = typeOptions?.find((v) => v?.value === text);
      return <span>{item?.label}</span>;
    },
  },
];

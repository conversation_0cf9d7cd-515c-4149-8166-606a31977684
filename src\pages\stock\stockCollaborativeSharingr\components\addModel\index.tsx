import { columnWidthEnum } from '@/data/common/constant';
import { useModal } from '@ebay/nice-modal-react';
import type { BaseModalProps } from '@xlb/components';
import {
  XlbBasicForm,
  XlbButton,
  XlbIcon,
  XlbInputDialog,
  XlbMessage,
  XlbModal,
  XlbTable,
} from '@xlb/components';
import dayjs from 'dayjs';
import { useState, type FC } from 'react';
import api from '../../server';
import styles from './index.less';
interface Props extends BaseModalProps {
  fetchData?: any;
  id?: number;
}

const AddItem: FC<Props> = ({ fetchData, id = -1 }) => {
  const [form] = XlbBasicForm.useForm();
  const modal = useModal();
  const [rowData, setRowData] = useState<any[]>([
    {
      fid: 0,
      cargo_owner_id: '',
      cargo_owner_name: '',
      share_cargo_owner_id: '',
      share_cargo_owner_name: '',
      sort: '',
    },
  ]);
  const [isLoading, setisLoading] = useState<boolean>(false);
  const [pageSize, setPageSize] = useState(200);
  const [selectRow, setSelectRow] = useState<any>([]); // 已选择的

  //补货范围增加
  const addItem = async () => {
    // 检查是否配送中心是否为空
    const storeId = form.getFieldValue('store_id');
    if (!storeId) {
      XlbMessage.warning('请选择配送中心');
      return;
    }
    // 检查是否单据货主为空
    const cargoOwnerId = form.getFieldValue('cargo_owner_id');
    if (!cargoOwnerId) {
      XlbMessage.warning('请选择单据货主');
      return;
    }
    setRowData((prev) => [
      ...prev,
      {
        fid: dayjs().format('YYYYMMDDHHmmss') + (prev?.length + 1)?.toString(),
        cargo_owner_id: '',
        cargo_owner_name: '',
        share_cargo_owner_id: '',
        share_cargo_owner_name: '',
        sort: '',
        _click: true,
      },
    ]);
  };
  //补货范围删除
  const deleteItem = () => {
    // 检查是否选中了第一条记录
    const firstItem = rowData[0];
    const hasFirstItem = selectRow.includes(firstItem?.fid);

    if (hasFirstItem) {
      XlbMessage.warning('优先级为0的货主不能删除');
      return;
    }

    const deleteItem = rowData?.filter(
      (item: any) => !selectRow.includes(item.fid),
    );
    setRowData(deleteItem);
    setSelectRow([]);
  };

  const handleOk = async (values: any) => {
    // 校验rowData里面是否有cargo_owner_id或者share_cargo_owner_id为空的
    const hasEmpty = rowData?.some((item: any) => !item?.share_cargo_owner_id);
    if (hasEmpty) {
      XlbMessage.warning('请选择共享货主');
      return;
    }

    // return;
    setisLoading(true);
    const res = await api.save({
      store_id: values?.store_id?.[0],
      item_ids: values?.item_ids,
      cargo_owner_id: values?.cargo_owner_id,
      stock_cargo_owner_shares: rowData?.map((item: any, index: any) => {
        return {
          share_cargo_owner_id: item?.share_cargo_owner_id,
          sort: index,
        };
      }),
    });
    setisLoading(false);
    if (res?.code === 0) {
      modal.hide();
      modal.resolve(false);
      fetchData?.();
      XlbMessage.success('操作成功');
    }
  };
  const handleDragSortEnd = (dataSource: any, oldIndex: any, newIndex: any) => {
    if (oldIndex == 0 || newIndex == 0) {
      XlbMessage.warning('优先级为0的货主不能拖动');
      return;
    }
    setRowData(dataSource?.map((item: any) => ({ ...item, _edit: false })));
  };
  return (
    <XlbModal
      width={1000}
      open={modal.visible}
      title={'新增'}
      isCancel={true}
      onOk={async () => {
        form.submit();
      }}
      onCancel={() => {
        modal.resolve(false);
        modal.hide();
      }}
    >
      <div
        style={{ marginLeft: '12px' }}
        className={styles.stockCollaborativeSharingraddmodel}
      >
        <XlbBasicForm
          form={form}
          disabled={isLoading}
          layout="inline"
          autoComplete="off"
          style={{ margin: '10px 0px' }}
          onFinish={() => {
            const values = form.getFieldsValue(true);
            handleOk(values);
          }}
        >
          <XlbBasicForm.Item
            label="配送中心"
            name="store_id"
            rules={[{ required: true, message: '请选择配送中心' }]}
          >
            <XlbInputDialog
              dialogParams={{
                type: 'store',
                isMultiple: false,
                dataType: 'lists',
                url: '/erp/hxl.erp.store.cargoownerdelivery.short.page',
                placeholder: '请选择',
                // data: {
                //   status: true,
                //   center_flag: true,
                // },
              }}
              disabled={rowData?.length > 1}
              handleValueChange={(e: any, option: any) => {
                form.setFieldsValue({
                  cargo_owner_id: undefined,
                  cargo_owner_name: undefined,
                });
              }}
              fieldNames={{
                idKey: 'id',
                nameKey: 'store_name',
              }}
              width={180}
            />
          </XlbBasicForm.Item>
          <XlbBasicForm.Item
            label="商品"
            name="item_ids"
            rules={[{ required: true, message: '请选择商品' }]}
          >
            <XlbInputDialog
              dialogParams={{
                type: 'goods',
                isMultiple: true,
                dataType: 'lists',
                placeholder: '请选择',
                // data: {
                //   status: true,
                // },
              }}
              width={180}
            />
          </XlbBasicForm.Item>

          <XlbBasicForm.Item noStyle dependencies={['store_id']}>
            {({ getFieldValue }) => (
              <>
                <XlbBasicForm.Item
                  label="单据货主"
                  name="cargo_owner_id"
                  rules={[{ required: true, message: '请选择单据货主' }]}
                >
                  <XlbInputDialog
                    dialogParams={{
                      type: 'cargoOwner',
                      isMultiple: false,
                      isLeftColumn: false,
                      data: {
                        store_ids: getFieldValue('store_id'),
                      },
                    }}
                    fieldNames={{
                      idKey: 'id',
                      nameKey: 'source_name',
                    }}
                    width={180}
                    disabled={
                      rowData?.length > 1 ||
                      isLoading ||
                      !form?.getFieldValue('store_id')
                    }
                    placeholder="请选择"
                    handleValueChange={(e: any, option: any) => {
                      if (option?.length > 0) {
                        form.setFieldsValue({
                          cargo_owner_id: option?.[0]?.id,
                          cargo_owner_name: option?.[0]?.source_name,
                        });
                        setRowData((prev: any) => [
                          {
                            ...prev?.[0],
                            share_cargo_owner_id: option?.[0]?.id,
                            share_cargo_owner_name: option?.[0]?.source_name,
                          },
                        ]);
                      }
                    }}
                  />
                </XlbBasicForm.Item>
              </>
            )}
          </XlbBasicForm.Item>
          <div>
            <div style={{ marginBottom: '10px' }}>
              <XlbButton.Group>
                <XlbButton
                  label="新增"
                  type="primary"
                  onClick={addItem}
                  icon={<XlbIcon size={16} name="jia" />}
                />
                <XlbButton
                  label="删除"
                  type="primary"
                  onClick={deleteItem}
                  disabled={selectRow?.length === 0 || isLoading}
                  icon={<XlbIcon size={16} name="shanchu" />}
                />
              </XlbButton.Group>
            </div>
            <XlbTable
              columns={[
                {
                  name: '序号',
                  code: '_index',
                  width: columnWidthEnum.INDEX,
                  align: 'center',
                },
                {
                  name: '单据货主',
                  code: 'cargo_owner_id',
                  width: 320,
                  render: (text: any, record: any, index) => {
                    return (
                      <span>{form?.getFieldValue('cargo_owner_name')}</span>
                    );
                  },
                },
                {
                  name: '共享货主',
                  code: 'share_cargo_owner_id',
                  width: 320,
                  render: (text: any, record: any, index: any) => {
                    return record?._click && record?.fid !== 0 ? (
                      <div
                        // onClick={(e: any) => {
                        //   e.stopPropagation();
                        // }}
                      >
                        <XlbInputDialog
                          dialogParams={{
                            type: 'cargoOwner',
                            isMultiple: false,
                            isLeftColumn: false,
                            data: {
                              store_ids: form?.getFieldValue('store_id'),
                            },
                          }}
                          fieldNames={{
                            idKey: 'id',
                            nameKey: 'source_name',
                          }}
                          disabled={index?.index === 0}
                          style={{ width: '256px' }}
                          value={text}
                          placeholder="请选择"
                          handleValueChange={(e: any, option: any) => {
                            // 判断e是否存在与rowData里面
                            const hasE = rowData?.some(
                              (item: any) =>
                                item?.share_cargo_owner_id === option?.[0]?.id,
                            );
                            // 存在则不添加
                            if (hasE) {
                              XlbMessage.warning('货主已存在');
                              // 所有的_edit设置为false
                              setRowData((prev: any) =>
                                prev?.map((item: any) => ({
                                  ...item,
                                  _click: false,
                                })),
                              );
                              return;
                            }
                            // 不存在则修改对应index的share_cargo_owner_id和share_cargo_owner_name
                            setRowData((prev: any) =>
                              prev?.map((item: any, i: any) =>
                                i === index?.index
                                  ? {
                                      ...item,
                                      share_cargo_owner_id: option?.[0]?.id,
                                      share_cargo_owner_name:
                                        option?.[0]?.source_name,
                                    }
                                  : item,
                              ),
                            );
                          }}
                        />
                      </div>
                    ) : (
                      <span>{record?.share_cargo_owner_name}</span>
                    );
                  },
                },
                {
                  name: '优先级',
                  code: 'sort',
                  width: 75,
                  render: (text: any, record: any, index) => {
                    return <span>{index?.index}</span>;
                  },
                },
              ]}
              dataSource={rowData}
              total={rowData?.length}
              onPaginChange={(page, pageSize) => {
                setPageSize(pageSize);
              }}
              pageSize={pageSize}
              clickArea={'checkbox'}
              selectMode="multiple"
              onClickRow={(row, index) => {
                setRowData((prev: any) =>
                  prev?.map((item: any) => ({
                    ...item,
                    _click: row?.fid === item?.fid,
                  })),
                );
              }}
              style={{ height: 'calc(100vh - 475px)', maxHeight: 400 }}
              primaryKey={'fid'}
              isLoading={isLoading}
              dragSort={true}
              onDragSortEnd={(dataSource, oldIndex, newIndex) =>
                handleDragSortEnd(dataSource, oldIndex, newIndex)
              }
              selectedRowKeys={selectRow}
              onSelectRow={(record) => setSelectRow(record)}
            />
          </div>
        </XlbBasicForm>
      </div>
    </XlbModal>
  );
};
export default AddItem;

export const StoreHouseKeyMap = {
  // 仓库管理
  erpWarehouseType: 'erpWarehouseType'
}

export const storeHouseConfig: any[] = [
  // 仓库管理
  {
    tag: 'ERP',
    label: '仓库类型',
    id: StoreHouseKeyMap.erpWarehouseType,
    name: 'type',
    componentType: 'select',
    fieldProps: {
      options: [
        {
          label: '中心仓',
          value: 'CENTER'
        },
        {
          label: '退货仓',
          value: 'RETURN'
        }
      ]
    }
  }
]

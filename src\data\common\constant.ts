export enum columnWidthEnum {
    tel = 120,
    fid = 160,
    TIME = 120,
    DATE = 100,
    ITEM_CODE = 96,
    ITEM_BAR_CODE = 124,
    SHORTHAND_CODE = 110,
    STORE_NAME = 140,
    MEMO = 140,
    STOP_SALE = 90,
    ORDER_STATE = 90,
    INDEX = 50,
    ITEM_SPEC = 110,
    BY = 110,
    ORDER_FID = 140
  }
  
  export const EMPTY_CODE = '_empty_code'
  
  
  //获取系统信息  可扩展封装其他weight值
  const getPosType= ()=> {
    const agent = navigator.userAgent.toLowerCase();
    const isMac = /macintosh|mac os x/i.test(navigator.userAgent);
    if (agent.indexOf("win32") >= 0 || agent.indexOf("wow32") >= 0) {
      return 600
    }
    if (agent.indexOf("win64") >= 0 || agent.indexOf("wow64") >= 0) {
      return 600
    }
    if (isMac) {
      return 500
    }
    return 600
  }
  
  export enum fontWeight {
    BOLD = getPosType()
  }
  
import { hasAuth } from '@/utils';
import {
  XlbBasicForm,
  XlbButton,
  XlbIcon,
  XlbInputNumber,
  XlbMessage,
  XlbRadio,
  XlbTable,
  XlbTableColumnProps,
  XlbTabs,
  XlbTipsModal,
} from '@xlb/components';
import { Tooltip } from 'antd';
import { useEffect, useState } from 'react';
import { getStoreOrderConfig, saveStoreOrderConfig } from '../server';
import styles from './index.less';

const storeOrderSetting = () => {
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [DataStore, setDataStore] = useState<any>([]);
  const [form] = XlbBasicForm.useForm();
  // RECEIVE_ORDER(采购收货单), PURCHASE_ORDER(采购订单), DELIVERY_IN_ORDER(调入单), DELIVERY_OUT_ORDER(调出单), REQUEST_ORDER(门店补货单)
  const downstreamTypesOption = [
    { label: '采购收货单', value: 'RECEIVE_ORDER' },
    { label: '采购订单', value: 'PURCHASE_ORDER' },
    { label: '调入单', value: 'DELIVERY_IN_ORDER' },
    { label: '调出单', value: 'DELIVERY_OUT_ORDER' },
    { label: '门店补货单', value: 'REQUEST_ORDER' },
  ];
  // DIRECT(直供),ORDER(订购),STOREHOUSE(仓配),CROSS(越库),CONSIGNMENT(寄售)
  const typeOption = [
    { label: '直供', value: 'DIRECT' },
    { label: '订购', value: 'ORDER' },
    { label: '仓配', value: 'STOREHOUSE' },
    { label: '越库', value: 'CROSS' },
    { label: '寄售', value: 'CONSIGNMENT' },
  ];
  const Columns: XlbTableColumnProps<any>[] = [
    {
      name: '订单类型',
      code: 'type',
      width: 130,
      render(text, record, index) {
        return (
          <div>{typeOption.find((item) => item.value === text)?.label}</div>
        );
      },
    },
    {
      name: 'ERP下游单据',
      code: 'content.downstream_types',
      width: 180,
      render(text, record, index) {
        // record.content.downstream_types数组通过downstreamTypesOption转换
        return (
          <div>
            {record.content.downstream_types
              .map(
                (name: any) =>
                  downstreamTypesOption.find((item: any) => item.value === name)
                    ?.label,
              )
              .join(',')}
          </div>
        );
      },
    },
    {
      name: '冻结货款流程',
      code: 'content.freeze_flag',
      width: 150,
      render(text, record, index) {
        return <div>{record.content.freeze_flag ? '审核冻结' : '不冻结'}</div>;
      },
    },
    {
      name: '确认前时效',
      code: 'content.confirm_days',
      width: 150,
      render(text, record, index) {
        return (
          <div>
            {record.content.confirm_days === 0
              ? '当天有效'
              : record.content.confirm_days == null &&
                  record.content.delivery_cycle
                ? '按供应商交货周期'
                : `${record.content.confirm_days}天`}
          </div>
        );
      },
    },
    {
      name: '单据有效期(超时未处理作废单据)',
      code: 'content.valid_days',
      width: 230,
      render(text, record, index) {
        return (
          <div>
            {record.content.valid_days ? `${record.content.valid_days}天` : ''}
          </div>
        );
      },
    },
    {
      name: '操作时间',
      code: 'update_time',
      width: 180,
      features: { format: 'TIME' },
    },
    {
      name: '操作人',
      code: 'update_by',
      width: 180,
    },
    ...(hasAuth(['门店订单配置', '编辑'])
      ? [
          {
            name: '操作',
            code: 'operation',
            width: 100,
            lock: true,
            render(text: any, record: any, index: any) {
              return (
                <XlbButton
                  type="link"
                  onClick={() => {
                    console.log(record, 'record????????');

                    const obj = {
                      freeze_flag: record?.content?.freeze_flag,
                      confirmBeforeTime:
                        record?.content?.confirm_days == '0'
                          ? '0'
                          : record?.content?.confirm_days == null &&
                              record?.content?.delivery_cycle
                            ? 'delivery_cycle'
                            : '1',
                      confirm_days:
                        record?.content?.confirm_days == '0'
                          ? '0'
                          : record?.content?.confirm_days,
                      valid_days: record?.content?.valid_days,
                      auto_confirm_days: record?.content?.auto_confirm_days,
                    };
                    form.setFieldsValue(obj);
                    XlbTipsModal({
                      title: '订单类型参数设置',
                      isCancel: true,
                      onOkBeforeFunction: async () => {
                        try {
                          await form.validateFields();
                        } catch (err: any) {
                          return false;
                        }
                        console.log(form.getFieldValue('confirm_days'));
                        console.log(form.getFieldValue('confirmBeforeTime'));

                        const res = await saveStoreOrderConfig({
                          id: record.id,
                          content: {
                            freeze_flag: form.getFieldValue('freeze_flag'),
                            valid_days: form.getFieldValue('valid_days'),
                            auto_confirm_days:
                              form.getFieldValue('auto_confirm_days'),
                            confirm_days:
                              form.getFieldValue('confirmBeforeTime') === '1'
                                ? form.getFieldValue('confirm_days')
                                : form.getFieldValue('confirmBeforeTime') ==
                                    'delivery_cycle'
                                  ? null
                                  : form.getFieldValue('confirmBeforeTime'),
                            delivery_cycle:
                              form.getFieldValue('confirmBeforeTime') ==
                              'delivery_cycle'
                                ? true
                                : null,
                            downstream_types: record.content.downstream_types,
                          },
                        });
                        if (res.code === 0) {
                          XlbMessage.success('保存成功');
                          getStoreOrderSetting();
                        }
                        return true;
                      },
                      onCancel: () => form.resetFields(),
                      tips: (
                        <XlbBasicForm
                          form={form}
                          initialValues={obj}
                          labelCol={{ span: 7 }}
                          layout={'horizontal'}
                          style={{ margin: '16px 0' }}
                        >
                          <XlbBasicForm.Item label="订单类型">
                            {
                              typeOption.find(
                                (item) => item.value === record?.type,
                              )?.label
                            }
                          </XlbBasicForm.Item>
                          <XlbBasicForm.Item
                            label="冻结货款"
                            name="freeze_flag"
                            rules={[
                              { required: true, message: '请选择冻结货款' },
                            ]}
                          >
                            <XlbRadio.Group style={{ display: 'inline-flex' }}>
                              <XlbRadio value={false}>不冻结</XlbRadio>
                              <XlbRadio value={true}>审核冻结</XlbRadio>
                              {record.type == 'DIRECT' && (
                                <Tooltip
                                  placement="right"
                                  autoAdjustOverflow
                                  title="【直供加点模式】默认冻结,不受此配置影响"
                                >
                                  <XlbIcon
                                    name="bangzhu"
                                    style={{
                                      display: 'inline-flex',
                                      margin: 'auto',
                                      color: '#86909C',
                                    }}
                                  />
                                </Tooltip>
                              )}
                            </XlbRadio.Group>
                          </XlbBasicForm.Item>
                          <XlbBasicForm.Item
                            label="确认时效"
                            name="confirmBeforeTime"
                            rules={[
                              { required: true, message: '请选择确认时效' },
                            ]}
                          >
                            <XlbRadio.Group
                              style={{
                                display: 'flex',
                                alignItems: 'center',
                                flexWrap: 'wrap',
                              }}
                            >
                              <XlbRadio value="0">当天有效</XlbRadio>
                              <XlbRadio value="1">自定义时间</XlbRadio>
                              <XlbBasicForm.Item
                                noStyle
                                shouldUpdate={(prev, cur) =>
                                  prev.confirmBeforeTime !==
                                  cur.confirmBeforeTime
                                }
                              >
                                {({ getFieldValue }) => {
                                  return getFieldValue('confirmBeforeTime') ===
                                    '1' ? (
                                    <XlbBasicForm.Item
                                      name="confirm_days"
                                      noStyle
                                      rules={[
                                        {
                                          validator: (_, value) => {
                                            if (
                                              !value &&
                                              form.getFieldValue(
                                                'confirmBeforeTime',
                                              ) === '1'
                                            ) {
                                              return Promise.reject(
                                                '请输入自定义时间',
                                              );
                                            }
                                            return Promise.resolve();
                                          },
                                        },
                                      ]}
                                    >
                                      <XlbInputNumber
                                        min={0}
                                        max={99}
                                        style={{ width: 100 }}
                                        precision={0}
                                        addonAfter={'天'}
                                      />
                                    </XlbBasicForm.Item>
                                  ) : (
                                    <div
                                      style={{ width: 100, height: 28 }}
                                    ></div>
                                  );
                                }}
                              </XlbBasicForm.Item>
                              {/* {form?.getFieldValue('confirmBeforeTime') ==
                              '1' ? (
                                <XlbBasicForm.Item
                                  label=""
                                  noStyle
                                  name="confirm_days"
                              
                                >
                                  <XlbInputNumber
                                    min={0}
                                    max={99}
                                    style={{ width: 100 }}
                                    precision={0}
                                    addonAfter={'天'}
                                  ></XlbInputNumber>
                                </XlbBasicForm.Item>
                              ) : null} */}

                              <XlbRadio
                                style={{ flex: 1 }}
                                value="delivery_cycle"
                              >
                                按供应商交货周期
                              </XlbRadio>
                            </XlbRadio.Group>
                          </XlbBasicForm.Item>
                          <div
                            style={{
                              color: '#86909C',
                              margin: '-12px 0 16px 112px',
                            }}
                          >
                            超时未确认,会自动作废门店订单和下游单据
                            <br />
                            自定义1天=隔日未确认自动作废
                          </div>
                          <XlbBasicForm.Item
                            label="单据有效期"
                            name="valid_days"
                            rules={[
                              { required: true, message: '请输入单据有效期' },
                            ]}
                          >
                            <XlbInputNumber
                              placeholder="请输入1-99"
                              min={1}
                              max={99}
                              precision={0}
                              addonAfter={'天'}
                            ></XlbInputNumber>
                          </XlbBasicForm.Item>
                          {record?.type == 'DIRECT' && (
                            <>
                              <XlbBasicForm.Item
                                label="订单自动确认收货"
                                name="auto_confirm_days"
                                rules={[
                                  {
                                    required: true,
                                    message: '请输入订单自动确认收货',
                                  },
                                ]}
                              >
                                <XlbInputNumber
                                  placeholder="请输入0-99"
                                  min={0}
                                  max={99}
                                  precision={0}
                                  addonAfter={'天'}
                                ></XlbInputNumber>
                              </XlbBasicForm.Item>
                              <div
                                style={{
                                  color: '#86909C',
                                  margin: '-12px 0 16px 112px',
                                }}
                              >
                                自定义1天=供应商已确认送货，但是门店隔日未确认自动确认　更新时间：每天23:00
                              </div>
                            </>
                          )}
                        </XlbBasicForm>
                      ),
                    });
                  }}
                >
                  编辑
                </XlbButton>
              );
            },
          },
        ]
      : []),
  ];

  const getStoreOrderSetting = async () => {
    setIsLoading(true);
    const res = await getStoreOrderConfig({});
    if (res.code == 0) {
      setDataStore(res.data);
    }
    setIsLoading(false);
  };
  useEffect(() => {
    getStoreOrderSetting();
  }, []);
  return (
    <div className={styles.innerProvider}>
      <XlbTabs
        items={DataStore.map((item: any) => ({
          label: item.org_name,
          key: item.org_id.toString(),
          children: (
            <XlbTable
              columns={Columns}
              style={{ height: 'calc(100vh - 176px)' }}
              dataSource={item?.children}
              primaryKey="orderType"
              isLoading={isLoading}
              hideOnSinglePage={true}
            />
          ),
        }))}
      ></XlbTabs>
    </div>
  );
};
export default storeOrderSetting;


import { SearchFormType, XlbTableColumnProps } from '@xlb/components';

export const formList: SearchFormType[] = [
  {
    label: '门店名称',
    name: 'keyword',
    type: 'input',
    clear: true,
  },
]

export const tableColumn: XlbTableColumnProps<any>[] = [
  {
    name: '序号',
    code: '_index',
    width: 50,
    align: 'center'
  },
  {
    name: '门店名称',
    code: 'store_name',
    width: 200,
    features: { sortable: true },
  },
  {
    name: '门店分组',
    code: 'store_group_name',
    align: 'left',
    width: 102,
    features: { sortable: true }
  }
]

export const itemColumn: XlbTableColumnProps<any>[]  = [
  {
    name: '商品分类',
    code: 'name',
    width: 240,
    align: 'center'
  },
  {
    name: '预测系数',
    code: 'coefficient',
    width: 120,
    align: 'left',
    features: { sortable: true },
  },
]
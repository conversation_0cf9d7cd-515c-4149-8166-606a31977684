import {XlbFetch as ErpRequest } from '@xlb/utils'
import { ReasonFindReqDTO, ReasonSaveReqDTO, ReasonUpdateReqDTO } from './type'

export default {
  // 获取数据
  getData: async (data: ReasonFindReqDTO) => {
    return await ErpRequest.post('/erp/hxl.erp.reason.find', { data })
  },
  // 新增
  add: async (data: ReasonSaveReqDTO) => {
    return await ErpRequest.post('/erp/hxl.erp.reason.save', { data })
  },

  // 删除 
  delete: async (data: { id: number }) => {
    return await ErpRequest.post('/erp/hxl.erp.reason.delete', { data })
  },

  // 修改
  update: async (data: ReasonUpdateReqDTO) => {
    return await ErpRequest.post('/erp/hxl.erp.reason.update', { data })
  }

}
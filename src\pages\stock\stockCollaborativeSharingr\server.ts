import { XlbFetch } from '@xlb/utils';

// 导出
const exportDetail = async (data: any) => {
  return await XlbFetch.post('/erp/hxl.erp.stockcargoownershares.export', data);
};

// 删除
const deleteData = async (data: any) => {
  return await XlbFetch.post(
    '/erp/hxl.erp.stockcargoownershares.batchdelete',
    data,
  );
};

// 新增
const save = async (data: any) => {
  return await XlbFetch.post('/erp/hxl.erp.stockcargoownershares.save', data);
};

export default {
  exportDetail,
  deleteData,
  save,
};

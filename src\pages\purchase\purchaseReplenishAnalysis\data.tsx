import { LStorage } from '@/utils/storage';
import { SearchFormType, type XlbTableColumnProps } from '@xlb/components';

export const QUERY_UNITS = [
  {
    label: '采购单位',
    value: 'PURCHASE',
  },
  {
    label: '基本单位',
    value: 'BASIC',
  },
  {
    label: '配送单位',
    value: 'DELIVERY',
  },
  {
    label: '库存单位',
    value: 'STOCK',
  },
  {
    label: '批发单位',
    value: 'WHOLESALE',
  },
];
export const PURCHASE_SCOPE = [
  {
    label: '不限',
    value: '不限',
  },
  {
    label: '总部购配',
    value: '总部购配',
  },
  {
    label: '门店采购',
    value: '门店采购',
  },
];

export const PURCHASE_TYPES = [
  {
    label: '集采品',
    value: 'COLLECTIVE_PURCHASE',
  },
  {
    label: '集售品',
    value: 'COLLECTIVE_SALE',
  },
  {
    label: '地采品',
    value: 'GROUND_PURCHASE',
  },
  {
    label: '店采品',
    value: 'SHOP_PURCHASE',
  },
];

export const CREATE_PRDER_TYPE = [
  {
    label: '默认订购量=0的不生成采购订单',
    value: false,
  },
  {
    label: '全部商品',
    value: true,
  },
];

export const FORM_LIST: SearchFormType[] = [
  {
    label: '组织',
    name: 'org_ids',
    type: 'select',
    multiple: true,
    check: true,
    hidden: true,
    // @ts-ignore
    onChange: (_e: any, form: any) => {
      form?.setFieldsValue({ store_ids: [] });
    },
    selectRequestParams: {
      url: '/erp-mdm/hxl.erp.org.find',
      responseTrans(response: any) {
        return (
          response?.map((item: any) => ({
            ...item,
            label: item.name,
            value: item.id,
          })) || []
        );
      },
    },
  },
  {
    label: '补货门店',
    name: 'store_ids',
    type: 'inputDialog',
    allowClear: false,
    dependencies: ['org_ids'],
    dialogParams: (data) => {
      const formatData = { ...data };
      // 左侧树和查询条件同时存在时，若查询条件为空删除查询条件(即时为undefined/null)
      if (!formatData.org_ids) {
        delete formatData.org_ids;
      }
      return {
        type: 'store',
        dataType: 'lists',
        isLeftColumn: true,
        isMultiple: true,
        data: { org_ids: formatData.org_ids, status: true },
      };
    },
    fieldNames: {
      idKey: 'id',
      nameKey: 'store_name',
    } as any,
    // @ts-ignore
    onChange: (_ids: number[], list: any, form: any) => {
      form?.setFieldsValues?.({
        store_names: list?.map((i: any) => i.store_name),
      });
    },
  },
  {
    label: '补货仓库',
    name: 'storehouse_id',
    type: 'select',
    allowClear: false,
    check: true,
    dependencies: ['store_ids'],
    // @ts-ignore
    disabled: (form: any) => form.getFieldValue('store_ids')?.length !== 1,
    options: [],
    handleDefaultValue: (data: any) => {
      if (data?.length === 0) {
        return null;
      }
      const defaultStoreHouse =
        data.find((item: any) => item.default_flag) || data[0];
      return defaultStoreHouse?.value;
    },
    // @ts-ignore
    selectRequestParams: (params: any, form: any) => {
      if (params?.store_ids?.length === 1) {
        return {
          url: '/erp/hxl.erp.storehouse.store.find',
          postParams: {
            store_id:
              params?.store_ids?.[0] || LStorage.get('userInfo').store_id,
          },
          responseTrans(response: any) {
            form?.setFieldValue(
              'storehouse_id',
              response?.find((item: any) => item.default_flag)?.id,
            );
            return (
              response?.map((item: any) => ({
                label: item.name,
                value: item.id,
                default_flag: item.default_flag,
              })) || []
            );
          },
        };
      } else {
        form?.setFieldValue('storehouse_id', null);
      }
    },
  },
  {
    label: '商品档案',
    name: 'item_ids',
    type: 'inputDialog',
    allowClear: true,
    dependencies: ['store_ids', 'item_category_ids', 'item_dept_ids'],
    dialogParams: (params) => ({
      type: 'purchaseReplenishGoods',
      dataType: 'lists',
      isLeftColumn: true,
      isMultiple: true,
      data: {
        openNormalProduct: true,
        store_ids: params?.store_ids,
        category_ids: params?.item_category_ids,
        item_dept_ids: params?.item_dept_ids,
      },
    }),
  },
  {
    label: '商品类别',
    name: 'item_category_ids',
    type: 'inputDialog',
    check: true,
    treeModalConfig: {
      // @ts-ignore
      title: '选择商品分类', // 标题
      url: '/erp/hxl.erp.category.find', // 请求地址
      dataType: 'lists',
      checkable: true, // 是否多选
      primaryKey: 'id',
      data: {
        enabled: true,
      },
      width: 360, // 模态框宽度
    },
  },
  {
    label: '供应商',
    name: 'supplier_ids',
    type: 'inputDialog',
    check: true,
    dialogParams: {
      type: 'supplier',
      dataType: 'lists',
      isLeftColumn: true,
      isMultiple: true,
      primaryKey: 'id',
      data: {
        isNotShowParentSupplier: true,
        mainBody: true,
      },
    },
  },
  {
    label: '商品部门',
    name: 'item_dept_ids',
    type: 'inputDialog',
    check: true,
    dialogParams: {
      type: 'productDept',
      dataType: 'lists',
      isLeftColumn: true,
      isMultiple: true,
    },
  },
  {
    label: '查询单位',
    name: 'unit_type',
    type: 'select',
    allowClear: false,
    check: true,
    options: QUERY_UNITS,
  },
  {
    label: '采购范围',
    name: 'purchase_scope',
    type: 'select',
    check: true,
    options: PURCHASE_SCOPE,
  },
  {
    label: '类别等级',
    name: 'category_level',
    type: 'select',
    check: true,
    selectRequestParams: {
      url: '/erp/hxl.erp.category.findmaxlevel',
      responseTrans(response: any) {
        const options = [];
        for (let i = 1; i <= response; i++) {
          options.push({
            label: `等级${i}`,
            value: i,
          });
        }
        return options;
      },
    },
  },
  {
    label: '销量趋势范围',
    name: 'time_range',
    type: 'select',
    allowClear: false,
    check: true,
    multiple: true,
    options: [
      {
        label: '前三月',
        value: 0,
      },
      {
        label: '前三周',
        value: 1,
      },
    ],
  },
  {
    label: '采购类型',
    name: 'purchase_types',
    type: 'select',
    check: true,
    multiple: true,
    options: PURCHASE_TYPES,
  },
];

export const FILTER_CHECKBOX = {
  common: [
    {
      label: '主供应商',
      value: 'main_supplier',
    },
    {
      label: '库存数量＞0',
      value: 'stock_gt_zero',
    },
    {
      label: '周转天数预警',
      value: 'warn_flag',
    },
    {
      label: '库存数量按可用库存统计',
      value: 'available_stock_quantity_statistics',
    },
    {
      label: '预计可用天数计算统计在订量',
      value: 'enable_valid_days_add_purchase_quantity',
    },
    {
      label: '预计可用天数计算统计本次订购量',
      value: 'enable_valid_days_add_quantity',
    },
    {
      label: '日均销量按实际天数',
      value: 'avg_sale_quantity_actual_day',
    },
  ],
  judge: [
    {
      label: '停购',
      value: 'stop_purchase',
    },
    {
      label: '停售',
      value: 'stop_sale',
    },
    {
      label: '停止要货',
      value: 'stop_request',
    },
  ],
  sale: [
    {
      label: '仅显示必卖品',
      value: 'must_sell',
    },
    {
      label: '在订量>0',
      value: 'purchase_quantity_gt_zero',
    },
    {
      label: '上期销量=0',
      value: 'last_sale_quantity_eq_zero',
    },
  ],
  saleCount: [
    {
      label: '调出单',
      value: 'delivery_out_order',
    },
    {
      label: '批发销售单',
      value: 'wholesale_order',
    },
    {
      label: '前台销售',
      value: 'pos_order',
    },
    {
      label: '门店调入',
      value: 'store_in',
    },
  ],
  suggest: [
    {
      label: '扣减在订量',
      value: 'subtract_purchase_quantity',
    },
    {
      label: '扣减库存数量',
      value: 'subtract_stock_quantity',
    },
  ],
};

export const IS_SHOW_OPTIONS = [
  {
    label: '仅显示',
    value: 1,
  },
  {
    label: '不显示',
    value: 0,
  },
];

export const COMPARE_OPTIONS = [
  {
    label: '>',
    value: 1,
  },
  {
    label: '≤',
    value: 0,
  },
];

export const SYMBOL_OPTIONS = [
  {
    label: '+',
    value: '+',
  },
  {
    label: 'x',
    value: '*',
  },
];

export const RESTOCK_GOODS_FILTER = [
  { lable: '不限', value: null },
  { lable: '库存量＜补货订购点', value: 1 },
  { lable: '库存量+在订量＜补货订购点', value: 2 },
  { lable: '库存量＜基础库存', value: 3 },
  { lable: '库存量+在订量＜基础库存', value: 4 },
  { lable: '库存量＜库存上限', value: 5 },
];

export const SUGGEST_TYPE = [
  {
    label: '',
    value: null,
  },
  {
    label: '根据库存上限',
    value: 1,
  },
  {
    label: '根据补货订购量',
    value: 2,
  },
  {
    label: '日均销量*交货周期',
    value: 3,
  },
  {
    label: '上期销量-库存量',
    value: 4,
  },
  {
    label: '日均销量*安全天数',
    value: 5,
  },
];

export const TABLE_COLUMN: XlbTableColumnProps<any>[] = [
  {
    name: '序号',
    code: 'index',
    width: 80,
    align: 'center',
    lock: true,
  },
  {
    name: '组织',
    code: 'org_name',
    width: 140,
    lock: true,
    hidden: true,
  },
  {
    name: '门店',
    code: 'store_name',
    width: 140,
    features: { sortable: true },
    lock: true,
  },
  {
    name: '补货仓库',
    code: 'storehouse_name',
    width: 140,
    features: { sortable: true },
    lock: true,
  },
  {
    name: '商品代码',
    code: 'item_code',
    width: 96,
    features: { sortable: true },
    lock: true,
  },
  {
    name: '商品条码',
    code: 'item_bar_code',
    width: 124,
    features: { sortable: true },
    lock: true,
  },
  {
    name: '商品名称',
    code: 'item_name',
    width: 180,
    features: { sortable: true },
    lock: true,
  },
  {
    name: 'ABC评级',
    code: 'item_level',
    width: 100,
    features: { sortable: true },
    lock: true,
  },
  {
    name: '采购规格',
    code: 'item_spec',
    width: 110,
    features: { sortable: true },
    lock: true,
  },
  {
    name: '单位',
    code: 'unit',
    width: 70,
    features: { sortable: true },
    lock: true,
  },
  {
    name: '商品类别',
    code: 'item_category_name',
    width: 120,
    features: { sortable: true },
    lock: true,
  },
  {
    name: '品牌',
    code: 'item_brand_name',
    width: 100,
    features: { sortable: true },
  },
  {
    name: '二级类别',
    code: 'item_two_category_name',
    width: 120,
    features: { sortable: true },
  },
  {
    name: '采购价',
    code: 'purchase_price',
    width: 120,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '零售价',
    code: 'item_sale_price',
    width: 120,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '补货订购点',
    code: 'point',
    width: 110,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '供应商',
    code: 'supplier_name',
    width: 220,
    features: { sortable: true },
  },
  {
    name: '采购员',
    code: 'purchase_by',
    width: 140,
    features: { sortable: true },
  },
  {
    name: '采购组',
    code: 'purchase_group',
    width: 140,
    features: { sortable: true },
  },
  {
    name: '保质期',
    code: 'period',
    width: 90,
    features: { sortable: true },
  },
  {
    name: '实际订购量',
    code: 'quantity',
    width: 110,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '搭赠数量',
    code: 'present_quantity',
    width: 110,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '建议订购量',
    code: 'suggest_quantity',
    width: 110,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '在订量',
    code: 'purchase_quantity',
    width: 90,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '前7天调出量',
    code: 'seven_day_ago_delivery_quantity',
    width: 120,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '前1月调出量',
    code: 'month_ago_delivery_quantity',
    width: 120,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '停购',
    code: 'stop_purchase',
    width: 70,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '停售',
    code: 'stop_sale',
    width: 70,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '停止要货',
    code: 'stop_request',
    width: 100,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '必卖品',
    code: 'must_sell',
    width: 80,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '交货周期',
    code: 'purchase_period',
    width: 90,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '安全天数',
    code: 'safe_days',
    width: 90,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '上期销量',
    code: 'last_sale_quantity',
    width: 100,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '统配数量',
    code: 'force_transfer_quantity',
    width: 100,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '采购类型',
    code: 'purchase_type',
    width: 100,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '日均销量',
    code: 'avg_sale_quantity',
    width: 100,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '上期前台',
    code: 'last_store_sale_quantity',
    width: 100,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '日均前台',
    code: 'avg_store_sale_quantity',
    width: 100,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '库存数量',
    code: 'stock_quantity',
    width: 100,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '预计可用天数',
    code: 'valid_days',
    width: 114,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '缺货次数',
    code: 'out_count',
    width: 114,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '缺货天数',
    code: 'out_days',
    width: 114,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '前1月月销量',
    code: 'month_ago_sale_quantity',
    width: 110,
    align: 'right',
    hidden: true,
    features: { sortable: true },
  },
  {
    name: '前2月月销量',
    code: 'two_month_ago_sale_quantity',
    width: 110,
    align: 'right',
    hidden: true,
    features: { sortable: true },
  },
  {
    name: '前3月月销量',
    code: 'three_month_ago_sale_quantity',
    width: 110,
    align: 'right',
    hidden: true,
    features: { sortable: true },
  },
  {
    name: '前1周周销量',
    code: 'week_ago_sale_quantity',
    width: 110,
    align: 'right',
    hidden: true,
    features: { sortable: true },
  },
  {
    name: '前2周周销量',
    code: 'two_week_ago_sale_quantity',
    width: 110,
    align: 'right',
    hidden: true,
    features: { sortable: true },
  },
  {
    name: '前3周周销量',
    code: 'three_week_ago_sale_quantity',
    width: 110,
    align: 'right',
    hidden: true,
    features: { sortable: true },
  },
  {
    name: '经营范围',
    code: 'business_scopes',
    width: 200,
    align: 'left',
    features: { sortable: true },
  },
  {
    name: '经营门店数',
    code: 'business_store_count',
    width: 110,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '新店门店数',
    code: 'new_store_count',
    width: 110,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '新店铺货量',
    code: 'new_store_quantity',
    width: 110,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '缺货门店数',
    code: 'out_stock_store_count',
    width: 110,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '订货门店数',
    code: 'order_store_count',
    width: 110,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '在售门店数',
    code: 'sale_store_count',
    width: 110,
    align: 'right',
    features: { sortable: true },
  },
  // TODO: 待定日期处理方式
  {
    name: '最近收货日期',
    code: 'latest_in_date',
    width: 160,
    features: { format: 'TIME' },
  },
];

export const JUDGE_OPTIONS = [
  {
    label: <div style={{ minHeight: 22 }}> </div>,
    value: null,
  },
  {
    label: '是',
    value: 1,
  },
  {
    label: '否',
    value: 0,
  },
];
export const storeDetailFormList: SearchFormType[] = [
  {
    label: '经营门店',
    name: 'business_store',
    type: 'select',
    allowClear: false,
    check: true,
    initialValue: null,
    options: JUDGE_OPTIONS,
  },
  {
    label: '缺货门店',
    name: 'out_stock_store',
    type: 'select',
    allowClear: false,
    check: true,
    initialValue: null,
    options: JUDGE_OPTIONS,
  },
  {
    label: '订货门店',
    name: 'order_store',
    type: 'select',
    allowClear: false,
    check: true,
    initialValue: null,
    options: JUDGE_OPTIONS,
  },
  {
    label: '在售门店',
    name: 'sale_store',
    type: 'select',
    allowClear: false,
    check: true,
    initialValue: null,
    options: JUDGE_OPTIONS,
  },
];
export const storeDetailTableColumn: XlbTableColumnProps<any>[] = [
  {
    name: '序号',
    code: '_index',
    width: 60,
    align: 'center',
  },
  {
    name: '门店名称',
    code: 'store_name',
    width: 120,
    features: { sortable: true },
  },
  {
    name: '门店分组',
    code: 'store_group_name',
    width: 120,
    features: { sortable: true },
  },
  {
    name: '经营门店',
    code: 'business_store',
    width: 120,
    features: { sortable: true },
  },
  {
    name: '缺货门店',
    code: 'out_stock_store',
    width: 120,
    features: { sortable: true },
  },
  {
    name: '订货门店',
    code: 'order_store',
    width: 120,
    features: { sortable: true },
  },
  {
    name: '在售门店',
    code: 'sale_store',
    width: 120,
    features: { sortable: true },
  },
  {
    name: '是否新店',
    code: 'new_store',
    width: 120,
    features: { sortable: true },
  },
];

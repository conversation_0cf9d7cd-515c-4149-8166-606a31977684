import { useBaseParams } from '@/hooks/useBaseParams';
import { hasAuth } from '@/utils';
import { wujieBus } from '@/wujie/utils';
import NiceModal from '@ebay/nice-modal-react';
import {
  XlbBasicForm,
  XlbButton,
  XlbColumns,
  XlbForm,
  XlbIcon,
  XlbMessage,
  XlbPageContainer,
  XlbTable,
  XlbTableColumnProps,
  XlbTabs,
  XlbTipsModal,
  XlbTooltip,
} from '@xlb/components';
import { XlbFetch as ErpRequest, LStorage } from '@xlb/utils';
import classnames from 'classnames';
import dayjs from 'dayjs';
import { cloneDeep } from 'lodash-es';
import { useEffect, useState } from 'react';
import DeliveryOrderItem from './components/deliveryOrder';
import { formList, GoodsArr, StoreArr, StoreGoodsArr } from './data';
import styles from './index.less';
import { getgoods, getstoregoods, getstoretotal } from './server';
const Goodsway = () => {
  const [isFold, setIsFold] = useState<boolean>(false);
  const [tabList, setTabList] = useState<XlbTableColumnProps<any>[]>(
    cloneDeep(StoreGoodsArr),
  );
  const center = LStorage.get('userInfo').store.enable_delivery_center;
  const { enable_organization } = useBaseParams((state) => state);
  const [form] = XlbBasicForm.useForm();
  const [tabKey, setTabKey] = useState('mx');
  const [rowData, setRowData] = useState<any[]>([]);
  const [footerData, setFooterData] = useState<any[]>([]);
  const [sortType, setSortType] = useState<{ order: string; code: string }>({
    order: '',
    code: '',
  });
  const [useGoodsTotalRowData, setUseGoodsTotalRowData] = useState<[]>([]);
  const [useStoreTotalArrRowData, setUseStoreTotalArrRowData] = useState<[]>(
    [],
  );
  const [useStoregoodsTotalRowData, setUseStoregoodsTotalRowData] = useState<
    []
  >([]);

  const [useGoodsTotalFooterData, setUseGoodsTotalFooterData] = useState<[]>(
    [],
  );
  const [useStoreTotalFooterData, setUseStoreTotalFooterData] = useState<[]>(
    [],
  );
  const [useStoregoodsTotalFooterData, setUseStoregoodsTotalFooterData] =
    useState<[]>([]);
  const [pagin, setPagin] = useState({
    pageSize: 200,
    pageNum: 1,
    total: 0,
  });
  const [useGoodsTotalPagin, setUseGoodsTotalPagin] = useState({
    pageSize: 200,
    pageNum: 1,
    total: 0,
  });
  const [useStoreTotalPagin, setUseStoreTotalPagin] = useState({
    pageSize: 200,
    pageNum: 1,
    total: 0,
  });
  const [useStoregoodsTotalPagin, setUseStoregoodsTotalPagin] = useState({
    pageSize: 200,
    pageNum: 1,
    total: 0,
  });
  const [isLoading, setisLoading] = useState<boolean>(false);

  const fIdClick = (data: any) => {
    NiceModal.show(NiceModal.create(DeliveryOrderItem), data);
  };
  const [formLists, setSearchFormLists] = useState(cloneDeep(formList));
  const { enable_cargo_owner } = useBaseParams((state) => state);

  const changeKey = (key: string) => {
    let tableArr: any = [];
    switch (key) {
      case 'mx':
        tableArr = StoreGoodsArr.filter((v) => {
          if (!enable_cargo_owner) {
            return v.code !== 'out_org_name';
          }
          return v;
        });
        setRowData([...useGoodsTotalRowData]);
        setFooterData([...useGoodsTotalFooterData]);
        setPagin(useGoodsTotalPagin);
        break;
      case 'sphj':
        tableArr = GoodsArr.filter((v) => {
          if (!enable_cargo_owner) {
            return v.code !== 'out_org_name';
          }
          return v;
        });
        setRowData([...useStoreTotalArrRowData]);
        setFooterData([...useStoreTotalFooterData]);
        setPagin(useStoreTotalPagin);
        break;
      case 'mdhj':
        tableArr = StoreArr.filter((v) => {
          if (!enable_cargo_owner) {
            return v.code !== 'out_org_name';
          }
          return v;
        });
        setRowData([...useStoregoodsTotalRowData]);
        setFooterData([...useStoregoodsTotalFooterData]);
        setPagin(useStoregoodsTotalPagin);
        break;
    }
    setTabList([...tableArr]);
  };
  const checkData = (page_number: number = 1, page_size: number = 200) => {
    const data = {
      page_number: page_number - 1,
      page_size: page_size,
      ...form.getFieldsValue(),
      audit_date: [
        form.getFieldValue('compactDatePicker')?.[0] + ' 00:00:00',
        form.getFieldValue('compactDatePicker')?.[1] + ' 23:59:59',
      ],
      out_store_ids: form.getFieldValue('out_store_ids')
        ? form.getFieldValue('out_store_ids')
        : undefined,
      in_store_ids: form.getFieldValue('in_store_ids')
        ? form.getFieldValue('in_store_ids')
        : undefined,
      item_ids: form.getFieldValue('item_ids')
        ? form.getFieldValue('item_ids')
        : undefined,
      item_category_ids: form.getFieldValue('item_category_ids')
        ? form.getFieldValue('item_category_ids')
        : undefined,
      filter_not_out_store_request_order: form
        .getFieldValue('checkValue')
        ?.includes('filter_not_out_store_request_order')
        ? true
        : undefined,
      filter_not_actual_hundred_rate: form
        .getFieldValue('checkValue')
        ?.includes('filter_not_actual_hundred_rate')
        ? true
        : undefined,
    };
    data.orders = sortType.code
      ? [
          {
            direction: sortType.order.toUpperCase(),
            property: sortType.code,
          },
        ]
      : undefined;
    return data;
  };
  const handleUid = () => {
    let uid: any = {
      key: '',
      name: '',
    };
    switch (tabKey) {
      case 'mx':
        uid.key =
          'hxl.erp.deliveryreport.itemonway.itemsummary.mx.page-columns';
        uid.name =
          'hxl.erp.deliveryreport.itemonway.itemsummary.mx.page-columns-name';
        break;
      case 'sphj':
        uid.key =
          'hxl.erp.deliveryreport.itemonway.itemsummary.sphj.page-columns';
        uid.name =
          'hxl.erp.deliveryreport.itemonway.itemsummary.sphj.page-columns-name';
        break;
      case 'mdhj':
        uid.key =
          'hxl.erp.deliveryreport.itemonway.itemsummary.mdhj.page-columns';
        uid.name =
          'hxl.erp.deliveryreport.itemonway.itemsummary.mdhj.page-columns-name';
        break;

      default:
        uid.key =
          'hxl.erp.deliveryreport.itemonway.itemsummary.mx.page-columns';
        uid.name =
          'hxl.erp.deliveryreport.itemonway.itemsummary.mx.page-columns-name';
    }
    return uid;
  };
  const oldArr = () => {
    let tableArr: any = [];
    switch (tabKey) {
      case 'mx':
        tableArr = cloneDeep(
          StoreGoodsArr.filter((v) => {
            if (!enable_cargo_owner) {
              return v.code !== 'out_org_name';
            }
            return v;
          }),
        );
        break;
      case 'sphj':
        tableArr = cloneDeep(
          GoodsArr.filter((v) => {
            if (!enable_cargo_owner) {
              return v.code !== 'out_org_name';
            }
            return v;
          }),
        );
        break;
      case 'mdhj':
        tableArr = cloneDeep(
          StoreArr.filter((v) => {
            if (!enable_cargo_owner) {
              return v.code !== 'out_org_name';
            }
            return v;
          }),
        );
        break;
      default:
        tableArr = cloneDeep(
          StoreGoodsArr.filter((v) => {
            if (!enable_cargo_owner) {
              return v.code !== 'out_org_name';
            }
            return v;
          }),
        );
    }
    return tableArr;
  };
  // 获取数据
  const getData = async (
    page_number: number = 1,
    page_size: number = pagin?.pageSize || 200,
  ) => {
    // 如果out_store_ids为空，则不查询
    if (form.getFieldValue('out_store_ids')?.length === 0) {
      XlbTipsModal({
        tips: '请选择调出门店',
      });
      return;
    }
    const data = checkData(page_number, page_size);
    setisLoading(true);
    let res = null;
    switch (tabKey) {
      case 'mx':
        res = await getstoregoods(data);
        break;
      case 'sphj':
        res = await getgoods(data);
        break;
      case 'mdhj':
        res = await getstoretotal(data);
        break;
    }
    setisLoading(false);
    if (res?.code == '0') {
      setRowData(res.data.content || []);
      //合计行
      const footDatas: any = res.data;
      const data: any = [
        {
          _index: '合计',
          delivery_quantity:
            footDatas.delivery_quantity_total?.toFixed(3) || '0.000', // 实发数量合计
          basic_delivery_quantity:
            footDatas.basic_delivery_quantity_total?.toFixed(3) || '0.000',
          delivery_shortage_quantity:
            footDatas.delivery_shortage_quantity_total?.toFixed(3) || '0.000', //实发缺货量合计
          basic_delivery_shortage_quantity:
            footDatas.basic_delivery_shortage_quantity_total?.toFixed(3) ||
            '0.000',
          pre_delivery_quantity:
            footDatas.pre_delivery_quantity_total?.toFixed(3) || '0.000', //预发数量合计
          basic_pre_delivery_quantity:
            footDatas.basic_pre_delivery_quantity_total?.toFixed(3) || '0.000',
          request_quantity:
            footDatas.request_quantity_total?.toFixed(3) || '0.000', //要货数量合计
          basic_request_quantity:
            footDatas.basic_request_quantity_total?.toFixed(3) || '0.000',
          shortage_quantity:
            footDatas.shortage_quantity_total?.toFixed(3) || '0.000', //缺货数量合计
          basic_shortage_quantity:
            footDatas.basic_shortage_quantity_total?.toFixed(3) || '0.000',
          pre_delivery_rate:
            footDatas.pre_delivery_rate_total?.toFixed(2) || '0.00', //预发率合计
          delivery_rate: footDatas.delivery_rate_total?.toFixed(2) || '0.00', //实发率合计
        },
      ];
      setFooterData(data);
      const paginData = {
        ...pagin,
        pageNum: page_number,
        pageSize: page_size,
        total: res.data.total_elements,
      };
      setPagin(paginData);
      switch (tabKey) {
        case 'mx':
          setUseGoodsTotalRowData(res.data.content || []);
          setUseGoodsTotalFooterData(data);
          setUseGoodsTotalPagin(paginData);
          break;
        case 'sphj':
          setUseStoreTotalArrRowData(res.data.content || []);
          setUseStoreTotalFooterData(data);
          setUseStoreTotalPagin(paginData);
          break;
        case 'mdhj':
          setUseStoregoodsTotalRowData(res.data.content || []);
          setUseStoregoodsTotalFooterData(data);
          setUseStoregoodsTotalPagin(paginData);
          break;
      }
    }
  };
  // 导出
  const exportItem = async (e: any) => {
    // 如果out_store_ids为空，则不查询
    if (form.getFieldValue('out_store_ids')?.length === 0) {
      XlbTipsModal({
        tips: '请选择调出门店',
      });
      return;
    }
    setisLoading(true);
    const data = checkData();
    data.page_size = 10000;
    let res = null;
    switch (tabKey) {
      case 'mx':
        res = await ErpRequest.post(
          '/erp/hxl.erp.deliveryreport.receiverateanalysis.detail.export',
          data,
        );
        break;
      case 'sphj':
        res = await ErpRequest.post(
          '/erp/hxl.erp.deliveryreport.receiverateanalysis.item.export',
          data,
        );
        break;
      case 'mdhj':
        res = await ErpRequest.post(
          '/erp/hxl.erp.deliveryreport.receiverateanalysis.store.export',
          data,
        );
        break;
    }
    setisLoading(false);
    if (res.code == 0) {
      wujieBus?.$emit('xlb_erp-event', { code: 'downloadEnd', target: e });
      XlbMessage.success('导出受理成功，请前往下载中心查看');
    }
  };

  useEffect(() => {
    getData(1);
  }, [sortType]);
  return (
    <XlbPageContainer
      isShowTemplate={false}
      className={styles.deliveryanalysis_container}
    >
      <div
        className={'button_box row-flex'}
        style={{ marginBottom: '10px', padding: '0 16px' }}
      >
        <div style={{ width: '90%' }} className="row-flex">
          <XlbButton.Group>
            {hasAuth(['到货率分析', '查询']) && (
              <XlbButton
                label={'查询'}
                disabled={isLoading}
                type="primary"
                onClick={() => getData()}
                icon={<XlbIcon name="sousuo" />}
              />
            )}
            {hasAuth(['到货率分析', '导出']) && (
              <XlbButton
                type="primary"
                label={'导出'}
                disabled={!rowData.length || isLoading}
                onClick={(e: any) => exportItem(e)}
                icon={<XlbIcon name="daochu" />}
              />
            )}
          </XlbButton.Group>
        </div>
        <div
          style={{
            width: '10%',
            height: '28px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'flex-end',
            columnGap: '8px',
          }}
        >
          <XlbTooltip title={isFold ? '收起' : '展开'}>
            <XlbIcon
              data-type={'顶层展开收起'}
              onClick={() => setIsFold(!isFold)}
              name="shouqi"
              size={20}
              className={classnames('xlb-columns-main-btn', {
                'xlb-columns-fold': !isFold,
                'xlb-columns-expand-btn-dev': true,
              })}
            />
          </XlbTooltip>
          <XlbColumns
            isFold={isFold}
            isFoldChange={setIsFold}
            url={handleUid()?.key}
            originColumns={oldArr()}
            value={tabList}
            onChange={setTabList}
            name={handleUid()?.name}
          />
        </div>
      </div>

      <div
        style={{ display: isFold ? 'none' : 'block' }}
        className={'form_header_box'}
      >
        <XlbForm
          formList={formLists?.filter((v) => {
            if (
              !enable_organization &&
              ['out_org_ids', 'in_org_ids'].includes(v.name)
            ) {
              return false;
            }
            if (tabKey === 'mx' && v.name === 'summary') {
              return false;
            }
            if (tabKey !== 'mx' && v.name === 'request_order_fid') {
              return false;
            }
            return true;
          })}
          form={form}
          isHideDate={true}
          initialValues={{
            in_store_ids: center ? '' : [LStorage.get('userInfo').store_id],
            out_store_ids: center ? [LStorage.get('userInfo').store_id] : '',
            compactDatePicker: [
              dayjs().format('YYYY-MM-DD'),
              dayjs().format('YYYY-MM-DD'),
            ],
          }}
        />
      </div>
      <div>
        <XlbTabs
          defaultActiveKey={tabKey}
          style={{ paddingLeft: '16px' }}
          activeKey={tabKey}
          onTabClick={(e) => {
            setTabKey(e);
            changeKey(e);
          }}
          items={[
            {
              label: '到货率分析-明细',
              key: 'mx',
            },
            {
              label: '到货率分析-商品汇总',
              key: 'sphj',
            },
            {
              label: '到货率分析-门店汇总',
              key: 'mdhj',
            },
          ]}
        />
      </div>
      <XlbTable
        key={tabKey + '_' + isFold}
        columns={
          // 先根据form的summary是否等于AUDIT_TIME过滤audit_time
          tabList
            .filter((v) => {
              if (
                form?.getFieldValue('summary') != 'AUDIT_TIME' &&
                tabKey != 'mx' &&
                v.code === 'audit_time'
              ) {
                return false;
              }
              return true;
            })
            .map((v) => {
              if (v.code === 'request_order_fid') {
                return {
                  ...v,
                  render: (value: any) => {
                    return (
                      <div
                        className="link overwidth"
                        onClick={() => fIdClick({ orderId: value })}
                      >
                        {value}
                      </div>
                    );
                  },
                };
              }
              return v;
            })
        }
        isLoading={isLoading}
        style={{ flex: 1, margin: '0 16px' }}
        pagin={pagin}
        pageNum={pagin?.pageNum}
        pageSize={pagin?.pageSize}
        total={pagin?.total}
        dataSource={rowData}
        isFold={isFold}
        onPaginChange={(page_number: number, page_size: number) => {
          getData(page_number, page_size);
        }}
        onChangeSorts={(e) => {
          setSortType(e);
        }}
        footerDataSource={footerData}
      />
    </XlbPageContainer>
  );
};
export default Goodsway;

import safeMath from '@/utils/safeMath';
import NiceModal from '@ebay/nice-modal-react';
import {
  type BaseModalProps,
  XlbBasicForm,
  XlbInputDialog,
  XlbModal,
  XlbTipsModal,
} from '@xlb/components';
import { message } from 'antd';
import { type FC, useState } from 'react';
import { copyInfo } from '../../server';
import XlbProgress from './progress';

interface Props extends Pick<BaseModalProps, 'title'> {
  getData?: any;
  client_ids?: string | number[];
}

const CustomerGoodsAttributesCopy: FC<Props> = ({ getData, client_ids }) => {
  const [form] = XlbBasicForm.useForm();
  const modal = NiceModal.useModal();

  const [loading, setLoading] = useState<boolean>(false);
  const [progress, setProgress] = useState<any>({
    open: false,
    tips: '',
    percentNum: 0,
  });

  const handleOk = async () => {
    console.log(form.getFieldsValue(true));
    if (!form.getFieldValue('client_ids')) {
      XlbTipsModal({
        tips: `请先选择修改客户！`,
      });
      return;
    } else if (!form.getFieldValue('client_id')) {
      XlbTipsModal({
        tips: `请先选择参照客户！`,
      });
      return;
    }
    setLoading(true);
    await handleCopyPost(0);
  };

  const handleCopyPost = async (apiCalls: number) => {
    const apiCallsCount = apiCalls + 1;
    const len = form.getFieldValue('client_ids')?.length || 0;
    const client_names = form.getFieldValue('client_names');
    setProgress({
      ...progress,
      tips: client_names[apiCalls] || '',
      percentNum:
        safeMath.divide(Number(apiCallsCount) || 1, Number(len)) * 100,
      open: true,
    });
    const res = await copyInfo({
      target_client_ids: form.getFieldValue('client_ids'),
      source_client_id: form.getFieldValue('client_id')[0],
    });
    if (res?.code === 0) {
      if (apiCallsCount <= len - 1) {
        await handleCopyPost(apiCallsCount);
      } else {
        setProgress({
          ...progress,
          open: false,
        });
        setLoading(false);
        message.success('操作成功');
        modal.hide();
        form.resetFields();
        if (client_ids && client_ids.length != 0) {
          getData();
        }
      }
    } else {
      setLoading(false);
      message.success('操作失败');
      setProgress({
        ...progress,
        open: false,
      });
      form.resetFields();
      if (client_ids && client_ids.length != 0) {
        getData();
      }
    }
  };

  return (
    <XlbModal
      title={'复制'}
      centered
      open={modal.visible}
      maskClosable={false}
      isCancel={true}
      onOk={() => handleOk()}
      wrapClassName="xlbDialog"
      onCancel={() => {
        form.resetFields();
        modal.resolve(false);
        modal.hide();
      }}
      width={460}
      confirmLoading={loading}
    >
      <XlbBasicForm
        form={form}
        style={{ margin: '30px 0 30px 20px' }}
        labelCol={{ style: { width: 100 } }}
      >
        <XlbBasicForm.Item
          label="修改客户"
          name="client_ids"
          rules={[{ required: true, message: '请选择修改客户' }]}
        >
          <XlbInputDialog
            fieldNames={{
              idKey: 'id',
              nameKey: 'name',
            }}
            dialogParams={{
              type: 'wholesaler',
              dataType: 'lists',
              isMultiple: false,
              primaryKey: 'id',
              data: {
                enabled: true,
              },
              onOkBeforeFunction: (_val: any, data: any[]) => {
                form.setFieldsValue({
                  client_ids: _val,
                  client_names: data.map((v) => v.name),
                });
                return true;
              },
            }}
            width={240}
          />
        </XlbBasicForm.Item>
        <XlbBasicForm.Item
          label="参照客户"
          name="client_id"
          rules={[{ required: true, message: '请选择参照客户' }]}
        >
          <XlbInputDialog
            fieldNames={{
              idKey: 'id',
              nameKey: 'name',
            }}
            dialogParams={{
              type: 'wholesaler',
              dataType: 'lists',
              isMultiple: false,
              primaryKey: 'id',
              data: {
                enabled: true,
              },
            }}
            width={240}
          />
        </XlbBasicForm.Item>
      </XlbBasicForm>
      <XlbProgress progress={progress} onCancel={setProgress} />
    </XlbModal>
  );
};
export default NiceModal.create(CustomerGoodsAttributesCopy);

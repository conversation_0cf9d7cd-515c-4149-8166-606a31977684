import { XlbTableColumnProps } from '@xlb/components';

export enum DataType {
  LISTS = 'lists',
  TREE = 'tree',
}
export const cycleDay = [
  {
    label: 'D+1',
    value: 'D+1',
  },
  {
    label: 'D+2',
    value: 'D+2',
  },
  {
    label: 'D+3',
    value: 'D+3',
  },
  {
    label: 'D+4',
    value: 'D+4',
  },
  {
    label: 'D+5',
    value: 'D+5',
  },
  {
    label: 'D+6',
    value: 'D+6',
  },
  {
    label: 'D+7',
    value: 'D+7',
  },
  {
    label: 'D+8',
    value: 'D+8',
  },
  {
    label: 'D+9',
    value: 'D+9',
  },
  {
    label: 'D+10',
    value: 'D+10',
  },
];

//商品明细
export const itemTableList: XlbTableColumnProps<any>[] = [
  {
    name: '序号',
    code: '_index',
    width: 50,
    align: 'center',
  },
  {
    name: '门店代码',
    code: 'store_code',
    width: 110,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '组织',
    code: 'org_name',
    width: 140,
    hidden: true,
    features: { sortable: true },
  },
  {
    name: '门店名称',
    code: 'store_name',
    width: 180,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '门店分组',
    code: 'store_group_name',
    width: 160,
    features: { sortable: true },
    align: 'left',
  },
];
export const ruleOptions = [
  { label: '按日期周期', value: 'CYCLE' },
  { label: '按日期奇/偶数', value: 'NUMBER' },
];

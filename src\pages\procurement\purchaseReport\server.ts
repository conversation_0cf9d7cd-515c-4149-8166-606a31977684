import { XlbFetch as ErpRequest } from '@xlb/utils';

//采购汇总明细-采购收货明细
export const receiveOrderDetail = async (data: any) => {
  return await ErpRequest.post(
    '/erp/hxl.erp.purchasereport.purchasesummary.receiveorderdetail.page',
    { ...data },
  );
};

//采购汇总明细-采购退货明细
export const returnOrderDetail = async (data: any) => {
  return await ErpRequest.post(
    '/erp/hxl.erp.purchasereport.purchasesummary.returnorderdetail.page',
    { ...data },
  );
};

//采购汇总明细-采购类别汇总
export const storeCategoryDetail = async (data: any) => {
  return await ErpRequest.post(
    '/erp/hxl.erp.purchasereport.purchasesummary.storecategory.page',
    { ...data },
  );
};

//采购汇总明细-采购商品汇总
export const storeItemDetail = async (data: any) => {
  return await ErpRequest.post(
    '/erp/hxl.erp.purchasereport.purchasesummary.storeitem.page',
    { ...data },
  );
};

//采购汇总明细-采购供应商汇总
export const supplierDetail = async (data: any) => {
  return await ErpRequest.post(
    '/erp/hxl.erp.purchasereport.purchasesummary.supplier.page',
    { ...data },
  );
};

//采购汇总明细-供应商-商品汇总
export const supplierItemDetail = async (data: any) => {
  return await ErpRequest.post(
    '/erp/hxl.erp.purchasereport.purchasesummary.supplieritem.page',
    { ...data },
  );
};

//采购汇总明细-供应商-门店-商品汇总
export const supplierStoreItemDetail = async (data: any) => {
  return await ErpRequest.post(
    '/erp/hxl.erp.purchasereport.purchasesummary.supplierstoreitem.page',
    { ...data },
  );
};

//采购汇总明细-供应商-门店汇总
export const supplierStoreDetail = async (data: any) => {
  return await ErpRequest.post(
    '/erp/hxl.erp.purchasereport.purchasesummary.supplierstore.page',
    { ...data },
  );
};

/**
 * @description: 查询业财结算分类枚举
 * @param {any} data
 * @return {*}
 */
export const queryFinanceCode = async (data: any) => {
  return await ErpRequest.post('/erp-mdm/hxl.erp.settlementcategory.center.find', {
    ...data,
  });
};

export const getStoreHouse = async (data: any) => {
  return await ErpRequest.post('/erp/hxl.erp.storehouse.store.find', {
    ...data,
  });
};
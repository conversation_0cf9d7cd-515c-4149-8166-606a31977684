import { columnWidthEnum } from '@/data/common/constant';
import { XlbTableColumnProps } from '@xlb/components';
//单据状态
export const Options1 = [
  {
    label: '制单',
    value: 'INIT',
    type: 'info',
  },
  {
    label: '审核',
    value: 'AUDIT',
    type: 'warning',
  },
  {
    label: '处理通过',
    value: 'HANDLE',
    type: 'theme',
  },
  {
    label: '批复通过',
    value: 'APPROVE',
    type: 'success',
  },
];
// 时间类型
export const Options3 = [
  {
    label: '制单时间',
    value: 'create_date',
  },
  {
    label: '审核时间',
    value: 'audit_date',
  },
  {
    label: '处理时间',
    value: 'handle_date',
  },
  {
    label: '批复时间',
    value: 'approve_date',
  },
];
//进出方向
export const InOut = [
  {
    label: '发货',
    value: false,
  },
  {
    label: '退货',
    value: true,
  },
];

export const tableList: XlbTableColumnProps<any>[] = [
  {
    name: '序号',
    code: '_index',
    width: columnWidthEnum.INDEX,
    align: 'center',
  },
  {
    name: '单据号',
    code: 'fid',
    width: columnWidthEnum.fid,
    features: { sortable: true },
  },
  {
    name: '进出方向',
    code: 'flag',
    width: 100,
    features: { sortable: true },
  },
  {
    name: '发货组织',
    code: 'out_org_id',
    hiddenInXlbColumns: true,
    width: 100,
    features: { sortable: true },
  },
  {
    name: '发货门店',
    code: 'out_store_id',
    width: columnWidthEnum.STORE_NAME,
    features: { sortable: true },
  },
  {
    name: '金额',
    code: 'money',
    width: 120,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '收货门店',
    code: 'store_id',
    width: columnWidthEnum.STORE_NAME,
    features: { sortable: true },
  },
  {
    name: '单据状态',
    code: 'state',
    width: columnWidthEnum.ORDER_STATE,
    features: { sortable: true },
  },
  {
    name: '制单人',
    code: 'create_by',
    width: columnWidthEnum.BY,
    features: { sortable: true },
  },
  {
    name: '制单时间',
    code: 'create_time',
    width: columnWidthEnum.TIME,
    features: { sortable: true, format: 'TIME' },
  },
  {
    name: '审核人',
    code: 'audit_by',
    width: columnWidthEnum.BY,
    features: { sortable: true },
  },
  {
    name: '审核时间',
    code: 'audit_time',
    width: columnWidthEnum.TIME,
    features: { sortable: true, format: 'TIME' },
  },
  {
    name: '处理人',
    code: 'handle_by',
    width: columnWidthEnum.BY,
    features: { sortable: true },
  },
  {
    name: '处理时间',
    code: 'handle_time',
    width: columnWidthEnum.TIME,
    features: { sortable: true, format: 'TIME' },
  },
  {
    name: '批复人',
    code: 'approve_by',
    width: 90,
    features: { sortable: true },
  },
  {
    name: '批复时间',
    code: 'approve_time',
    width: 160,
    features: { sortable: true, format: 'TIME' },
  },
  {
    name: '留言备注',
    code: 'memo',
    width: columnWidthEnum.MEMO,
    features: { sortable: true },
  },
  {
    name: '关联门店费用单',
    code: 'store_fee_fid',
    width: 180,
    features: { sortable: true },
  },
];
export const itemTableList: XlbTableColumnProps<any>[] = [
  {
    name: '序号',
    code: '_index',
    width: columnWidthEnum.INDEX,
    align: 'center',
  },
  {
    name: '操作',
    code: 'operation',
    align: 'center',
    width: 60,
  },
  {
    name: '载具名称',
    code: 'basket_name',
    width: 140,
    features: { sortable: true },
  },
  {
    name: '单价',
    code: 'price',
    width: 110,
    align: 'right',
    features: { sortable: true },
  },
];
export const inItemTableList: XlbTableColumnProps<any>[] = [
  {
    name: '司机取件数量',
    code: 'driver_confirm_quantity',
    width: 120,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '仓库确认数量',
    code: 'quantity',
    width: 120,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '留店数量',
    code: 'out_quantity',
    width: 110,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '仓库确认金额',
    code: 'money',
    width: 120,
    align: 'right',
    features: { sortable: true, format: 'MONEY' },
  },
  {
    name: '门店确认金额',
    code: 'out_money',
    width: 120,
    align: 'right',
    features: { sortable: true, format: 'MONEY' },
  },
];

export const outItemTableList: XlbTableColumnProps<any>[] = [
  {
    name: '门店确认数量',
    code: 'quantity',
    width: 120,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '司机取件数量',
    code: 'driver_confirm_quantity',
    width: 120,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '实收数量',
    code: 'out_quantity',
    width: 110,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '仓库确认金额',
    code: 'out_money',
    width: 120,
    align: 'right',
    features: { sortable: true, format: 'MONEY' },
  },
  {
    name: '门店确认金额',
    code: 'money',
    width: 120,
    align: 'right',
    features: { sortable: true, format: 'MONEY' },
  },
];

import type { CreateMapItem } from '@xlb/components/dist/lowcodes/XlbProForm/type'
import dayjs from 'dayjs'

export const DeliverySpecialPriceKeyMap = {
  /**单据状态 */
  SingleStatus: 'SingleStatus',
  // 单据类型
  erpTimeType: 'erpTimeType',
  // 特价模式
  ErpSpecialPriceMode: 'ErpSpecialPriceMode',
  // 详情-应用门店
  deliverySpecialPriceApplyStores: 'deliverySpecialPriceApplyStores'
}

export const deliverySpecialPriceConfig: CreateMapItem[] = [
  {
    tag: 'ERP',
    componentType: 'select',
    label: '',
    id: DeliverySpecialPriceKeyMap?.SingleStatus,
    fieldProps: {
      options: [
        {
          label: '制单',
          value: 'INIT'
        },
        {
          label: '审核',
          value: 'AUDIT'
        },
        {
          label: '处理通过',
          value: 'HANDLE'
        },
        {
          label: '处理拒绝',
          value: 'HANDLE_REFUSE'
        },
        {
          label: '作废',
          value: 'INVALID'
        }
      ]
    }
  },
  {
    tag: 'ERP',
    componentType: 'select',
    label: 'type',
    id: DeliverySpecialPriceKeyMap?.ErpSpecialPriceMode,
    fieldProps: {
      options: [
        {
          label: '补货特价',
          value: 'REQUEST'
        },
        {
          label: '调出特价',
          value: 'DELIVERY_OUT'
        }
      ]
    }
  }
]

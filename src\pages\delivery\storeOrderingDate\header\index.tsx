import { ErpFieldKeyMap } from '@/data/common/fieldListConfig';
import { useBaseParams } from '@/hooks/useBaseParams';
import { hasAuth } from '@/utils';
import type { ContextState, XlbProPageContainerRef, XlbProPageModalRef } from '@xlb/components';
import {
  XlbButton,
  XlbIcon,
  XlbProPageContainer,
  XlbProPageModal,
} from '@xlb/components';
import { useRef, useState } from 'react';
import { DataType } from '../data';
import Item from '../item/index';
const StoreDeliveryDay = () => {
  const { enable_organization } = useBaseParams((state) => state);
  let ref: ContextState;
  const [record, setRecord] = useState<any>({});
  const pageConatainerRef = useRef<XlbProPageContainerRef>(null);
  const pageModalRef = useRef<XlbProPageModalRef>(null);

  return (
    <>
      <XlbProPageModal
        ref={pageModalRef}
        Content={({ onClose }) => {
          return (
            <div>
              <Item
                onBack={(back) => {
                  pageConatainerRef?.current?.pageContainerRef?.current?.fetchData?.();
                  pageModalRef.current?.setOpen(false);
                }}
                record={record}
              ></Item>
            </div>
          );
        }}
      >
        <div className="xlb-table-clickBtn"></div>
      </XlbProPageModal>
      <XlbProPageContainer
        ref={pageConatainerRef}
        searchFieldProps={{
          formList: [
            { id: 'commonInput', label: '关键字', name: 'keyword' },
            { id: ErpFieldKeyMap?.erpOrderTimeruleStoreIds },
          ],
        }}
        treeFieldProps={
          enable_organization
            ? {
                leftUrl: '/erp-mdm/hxl.erp.org.tree',
                dataType: DataType.LISTS,
                leftKey: 'org_id',
              }
            : ''
        }
        extra={(content: ContextState) => {
          ref = content;
          return (
            <>
              {hasAuth(['门店下单日', '编辑']) ? (
                <XlbButton
                  type="primary"
                  disabled={content?.loading}
                  onClick={() => {
                    setRecord({ id: -1 });
                    pageModalRef.current?.setOpen(true);
                  }}
                  icon={<XlbIcon name="jia" />}
                >
                  新增
                </XlbButton>
              ) : null}
            </>
          );
        }}
        tableFieldProps={{
          url: '/erp/hxl.erp.ordertimerule.page',
          tableColumn: [
            {
              name: '序号',
              code: '_index',
              width: 60,
              align: 'center',
            },
            {
              name: '下单日名称',
              code: 'name',
              width: 280,
              features: { sortable: true },
              render: (text: any, record: any, index: any) => {
                return (
                  <span
                    className="link cursors"
                    onClick={() => {
                      // go('/xlb_erp/storeOrderingDate/item', {
                      //   id: record.id,
                      //   enable_organization: enable_organization,
                      // });
                      setRecord(record);
                      pageModalRef.current?.setOpen(true);
                    }}
                  >
                    {text}
                  </span>
                );
              },
            },
            {
              name: '创建人',
              code: 'create_by',
              width: 200,
              features: { sortable: true },
            },
            ...(enable_organization
              ? [
                  {
                    name: '组织',
                    code: 'org_name',
                    width: 140,
                    features: { sortable: true },
                  },
                ]
              : []),
            {
              name: '创建时间',
              code: 'create_time',
              width: 200,
              features: { sortable: true, format: 'TIME' },
            },
          ],
          selectMode: 'single',
          showColumnsSetting: true,
          immediatePost: true,
        }}
        deleteFieldProps={{
          order: 2,
          url: hasAuth(['门店下单日', '删除'])
            ? '/erp/hxl.erp.ordertimerule.batchdelete'
            : '',
          params: (data: any, v: any) => {
            return { ids: data };
          },
        }}
      />
    </>
  );
};

export default StoreDeliveryDay;
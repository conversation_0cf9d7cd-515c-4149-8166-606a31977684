import { LStorage } from '@/utils';
import { XlbFetch } from '@xlb/utils';

// 商品品牌查询
const getBrand = async () => {
  const hadUpdate = LStorage.get('CACHE_BRAND_UPDATE');
  if (!hadUpdate && LStorage.get('CACHE_BRAND_DATA')) {
    return new Promise((resolve) => {
      const res = JSON.parse(LStorage.get('CACHE_BRAND_DATA') || '{}');
      resolve(res);
    });
  } else {
    const res = await XlbFetch.post('/erp/hxl.erp.brand.find');
    LStorage.set('CACHE_BRAND_DATA', JSON.stringify(res));
    LStorage.set('CACHE_BRAND_UPDATE', '');
    return res;
  }
};

// 删除
const deleteItems = async (data: any) => {
  return await XlbFetch.post('/erp/hxl.erp.priceorder.batchdelete', {
    ...data,
  });
};
// 导出
const exportItems = async (data: any) => {
  return await XlbFetch.post(
    '/erp/hxl.erp.priceorder.export',
    { data },
    { responseType: 'blob' },
  );
};
// 明细
const getItem = async (data: any) => {
  return await XlbFetch.post('/erp/hxl.erp.priceorder.read', data);
};
// 通过
const passItem = async (data: any) => {
  return await XlbFetch.post('/erp/hxl.erp.priceorder.approve', data);
};
// 拒绝
const refuseItem = async (data: any) => {
  return await XlbFetch.post('/erp/hxl.erp.priceorder.refuse', data);
};

export default {
  getBrand,
  deleteItems,
  exportItems,
  getItem,
  passItem,
  refuseItem,
};

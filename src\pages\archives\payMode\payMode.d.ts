// 查询
export interface PaymentMethodFindReqDTO {
  /**
   * @name 查询关键字
   */
  keyword?: string

  /**
   * @name 是否应用于POS业务
   */
  pos?: boolean

  /**
   * @name 是否应用于门店结算
   */
  store_settlement?: boolean

  /**
   * @name 是否应用于供应商结算
   */
  supplier_settlement?: boolean

  /**
   * @name 是否应用于批发客户结算
   */
  wholesale_settlement?: boolean
}

// 删除
export interface PaymentMethodDeleteReqDTO {
  /**
   * @name ID
   */
  id: number
}

// 新增

export interface PaymentMethodSaveReqDTO {
  /**
   * @name 备注
   */
  memo?: string

  /**
   * @name 支付方式
   */
  name: string

  /**
   * @name 是否应用于POS业务,默认false
   */
  pos?: boolean

  /**
   * @name 是否应用于门店结算,默认false
   */
  store_settlement?: boolean

  /**
   * @name 是否应用于供应商结算,默认false
   */
  supplier_settlement?: boolean

  /**
   * @name 是否应用于批发客户结算,默认false
   */
  wholesale_settlement?: boolean
}

// 更新

export interface PaymentMethodUpdateReqDTO {
  /**
   * @name ID
   */
  id?: number

  /**
   * @name 备注
   */
  memo?: string

  /**
   * @name 支付方式
   */
  name: string

  /**
   * @name 是否应用于POS业务,默认false
   */
  pos?: boolean

  /**
   * @name 是否应用于门店结算,默认false
   */
  store_settlement?: boolean

  /**
   * @name 是否应用于供应商结算,默认false
   */
  supplier_settlement?: boolean

  /**
   * @name 是否应用于批发客户结算,默认false
   */
  wholesale_settlement?: boolean
  /**
   * @name 应用于pos的门店和组织
   */
  store_ids?: any[]
  org_ids?: any[]
  stores?: any[]
  organizations?: any[]
}

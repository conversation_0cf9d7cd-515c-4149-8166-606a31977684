import PromiseModal from '@/components/promiseModal/PromiseModal';
import { useBaseParams } from '@/hooks/useBaseParams';
import { hasAuth } from '@/utils';
import { wujieBus } from '@/wujie/utils';
import NiceModal from '@ebay/nice-modal-react';
import {
  XlbBasicForm,
  XlbButton,
  XlbColumns,
  XlbForm,
  XlbIcon,
  XlbMessage,
  XlbPageContainer,
  XlbTable,
  XlbTableColumnProps,
} from '@xlb/components';
import { XlbFetch as ErpRequest } from '@xlb/utils';
import { Tabs } from 'antd';
import dayjs from 'dayjs';
import { cloneDeep } from 'lodash-es';
import { useEffect, useState } from 'react';
import { detailTableList, formList, tableList } from './data';
import { basketonway, detailData } from './server';
const TransferWay = () => {
  const { TabPane } = Tabs;
  const [flag, setFlag] = useState<any>(null);
  const [visible, setVisible] = useState<any>(false);
  const [form] = XlbBasicForm.useForm<any>();
  const [formModel] = XlbBasicForm.useForm<any>();
  const [tabKey, setTabKey] = useState<any>('TOTAL');
  const [tableLoading, setTableLoading] = useState<any>(false);
  const [fidDataList, setFidDataList] = useState<any>([]);
  const { enable_cargo_owner } = useBaseParams((state) => state);

  const [isFold, setIsFold] = useState<boolean>(false);
  const [tabList, setTabList] = useState<XlbTableColumnProps<any>[]>(
    cloneDeep(tableList),
  );
  const [formLists, setSearchFormLists] = useState(
    cloneDeep(
      formList?.filter((v) => {
        if (!enable_cargo_owner) {
          return v.name !== 'out_org_ids';
        }
        return v;
      }),
    ),
  );
  const [rowData, setRowData] = useState<any[]>([]);
  const [sortType, setSortType] = useState<{ order: string; code: string }>({
    order: '',
    code: '',
  });
  const [useTotalRowData, setUseTotalRowData] = useState<[]>([]);
  const [useDetailRowData, setUseDetailRowData] = useState<[]>([]);

  const [pagin, setPagin] = useState({
    pageSize: 200,
    pageNum: 1,
    total: 0,
  });
  const [useTotalPagin, setUseTotalPagin] = useState({
    pageSize: 200,
    pageNum: 1,
    total: 0,
  });
  const [useDetailPagin, setUseDetailPagin] = useState({
    pageSize: 200,
    pageNum: 1,
    total: 0,
  });
  const [isLoading, setisLoading] = useState<boolean>(false);

  // 获取数据
  const checkData = (page_number: number = 1, page_size: number = 200) => {
    const data = {
      page_number: page_number - 1,
      page_size: page_size,
      ...form.getFieldsValue(),
      audit_date: [
        form.getFieldValue('compactDatePicker')?.[0] + ' 00:00:00',
        form.getFieldValue('compactDatePicker')?.[1] + ' 23:59:59',
      ],
      out_store_ids: form.getFieldValue('out_store_ids')
        ? form.getFieldValue('out_store_ids')
        : undefined,
      store_ids: form.getFieldValue('store_ids')
        ? form.getFieldValue('store_ids')
        : undefined,
      basket_ids: form.getFieldValue('basket_ids')
        ? form.getFieldValue('basket_ids')
        : undefined,
    };
    data.orders = sortType.code
      ? [
          {
            direction: sortType.order.toUpperCase(),
            property: sortType.code,
          },
        ]
      : undefined;
    return data;
  };
  const getData = async (
    page_number: number = 1,
    page_size: number = pagin?.pageSize || 200,
  ) => {
    const data = checkData(page_number, page_size);
    setisLoading(true);
    let res = null;
    switch (tabKey) {
      case 'TOTAL':
        res = await basketonway(data);
        break;
      case 'DETAIL':
        res = await detailData(data);
        break;
    }
    setisLoading(false);
    if (res.code == '0') {
      const content =
        res.data?.content?.map((v: any) => {
          v.rate = Number(v?.rate) * 100;
          v.out_rate = Number(v?.out_rate) * 100;
          return v;
        }) || [];
      setRowData(content);
      const paginData = {
        ...pagin,
        pageNum: page_number,
        pageSize: page_size,
        total: res.data.total_elements,
      };
      setPagin(paginData);
      switch (tabKey) {
        case 'TOTAL':
          setUseTotalRowData(res.data.content || []);
          setUseTotalPagin(paginData);
          break;
        case 'DETAIL':
          setUseDetailRowData(res.data.content || []);
          setUseDetailPagin(paginData);
          break;
      }
    }
  };
  // 导出
  const exportItem = async (e: any) => {
    setisLoading(true);
    const data = checkData();
    data.page_size = 10000;
    let res = null;
    switch (tabKey) {
      case 'TOTAL':
        res = await ErpRequest.post(
          '/erp/hxl.erp.deliveryreport.basketonway.summary.export',
          data,
        );
        break;
      case 'DETAIL':
        res = await ErpRequest.post(
          '/erp/hxl.erp.deliveryreport.basketonway.detail.export',
          data,
        );
        break;
    }
    setisLoading(false);
    if (res.code == 0) {
      wujieBus?.$emit('xlb_erp-event', { code: 'downloadEnd', target: e });
      XlbMessage.success('导出受理成功，请前往下载中心查看');
    }
  };
  const changeKey = (key: string) => {
    let tableArr: any = [];
    switch (key) {
      case 'TOTAL':
        tableArr = tableList.filter((v) => {
          if (!enable_cargo_owner) {
            return v.code !== 'out_org_name';
          }
          return v;
        });
        setRowData([...useTotalRowData]);
        setPagin(useTotalPagin);
        break;
      case 'DETAIL':
        tableArr = detailTableList.filter((v) => {
          if (!enable_cargo_owner) {
            return v.code !== 'out_org_name';
          }
          return v;
        });
        setRowData([...useDetailRowData]);
        setPagin(useDetailPagin);
        break;
    }
    setTabList([...tableArr]);
  };

  const oldArr = () => {
    let tableArr: any = [];
    switch (tabKey) {
      case 'TOTAL':
        tableArr = cloneDeep(
          tableList.filter((v) => {
            if (!enable_cargo_owner) {
              return v.code !== 'out_org_name';
            }
            return v;
          }),
        );
        break;
      case 'DETAIL':
        tableArr = cloneDeep(
          detailTableList.filter((v) => {
            if (!enable_cargo_owner) {
              return v.code !== 'out_org_name';
            }
            return v;
          }),
        );
        break;
      default:
        tableArr = cloneDeep(detailTableList);
    }
    return tableArr;
  };

  useEffect(() => {
    getData(1);
  }, [sortType]);

  return (
    <>
      <XlbPageContainer>
        <div
          className={'button_box row-flex'}
          style={{ marginBottom: '10px', padding: '0 16px' }}
        >
          <div style={{ width: '90%' }} className="row-flex">
            <XlbButton.Group>
              {hasAuth(['物资在途', '查询']) && (
                <XlbButton
                  label={'查询'}
                  disabled={isLoading}
                  type="primary"
                  onClick={() => getData()}
                  icon={<XlbIcon name="sousuo" />}
                />
              )}
              {hasAuth(['物资在途', '导出']) && (
                <XlbButton
                  type="primary"
                  label={'导出'}
                  disabled={!rowData.length || isLoading}
                  onClick={(e: any) => exportItem(e)}
                  icon={<XlbIcon name="daochu" />}
                />
              )}
            </XlbButton.Group>
          </div>
          <div
            style={{
              width: '10%',
              height: '28px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'flex-end',
            }}
          >
            <XlbColumns
              isFold={isFold}
              isFoldChange={setIsFold}
              url={
                tabKey === 'TOTAL'
                  ? '/erp/hxl.erp.deliveryreport.basketonway.summary.page'
                  : '/erp/hxl.erp.deliveryreport.basketonway.detail.page'
              }
              originColumns={oldArr()}
              originFormList={formList.filter((v) => {
                if (!enable_cargo_owner) {
                  return v.name !== 'out_org_ids';
                }
                return v;
              })}
              value={tabList}
              onChange={setTabList}
              name={
                tabKey === 'TOTAL'
                  ? '/erp/hxl.erp.deliveryreport.basketonway.summary.page-TOTAL'
                  : '/erp/hxl.erp.deliveryreport.basketonway.detail.page-DETAIL'
              }
              formList={formLists}
              onFormChange={setSearchFormLists}
            />
          </div>
        </div>

        {isFold ? null : (
          <div className={'form_header_box'}>
            <XlbForm
              formList={formLists}
              form={form}
              isHideDate={true}
              initialValues={{
                compactDatePicker: [
                  dayjs().format('YYYY-MM-DD'),
                  dayjs().format('YYYY-MM-DD'),
                ],
              }}
            />
          </div>
        )}
        <div>
          <Tabs
            defaultActiveKey={tabKey}
            style={{ paddingLeft: '16px' }}
            activeKey={tabKey}
            onTabClick={(e) => {
              setTabKey(e);
              changeKey(e);
            }}
          >
            <TabPane tab="物资在途-汇总" key={'TOTAL'}></TabPane>
            <TabPane tab="物资在途-明细" key={'DETAIL'}></TabPane>
          </Tabs>
        </div>
        <XlbTable
          tableKey={tabKey}
          columns={tabList
            .filter((v) => {
              if (!enable_cargo_owner) {
                return v.code !== 'out_org_name';
              }
              return v;
            })
            .map((v) => {
              if (v.code === 'fid') {
                return {
                  ...v,
                  render(text: any, record: any, index: any) {
                    return (
                      <div
                        className="link"
                        onClick={() => {
                          NiceModal.show(PromiseModal, {
                            order_type: '物资进出单',
                            order_fid: record.fid,
                          });
                        }}
                      >
                        {text}
                      </div>
                    );
                  },
                };
              }
              return v;
            })}
          isLoading={isLoading}
          style={{ flex: 1, margin: '0 16px' }}
          pagin={pagin}
          pageNum={pagin?.pageNum}
          pageSize={pagin?.pageSize}
          total={pagin?.total}
          dataSource={rowData}
          isFold={isFold}
          onPaginChange={(page_number: number, page_size: number) => {
            getData(page_number, page_size);
          }}
          onChangeSorts={(e) => {
            setSortType(e);
          }}
        />
      </XlbPageContainer>
    </>
  );
};
export default TransferWay;

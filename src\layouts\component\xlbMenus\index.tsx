import { RouteSetting } from '@/router/data';
import { hasOldAuth } from '@/utils/kit';
import { LStorage } from '@/utils/storage';
import { appName, auth, useIRouter } from '@/wujie/utils';
import { LockOutlined } from '@ant-design/icons';
import { Popover, message } from 'antd';
import { useEffect, useState } from 'react';
import styles from './index.less';

const XlbMenus = (props: any) => {
  const { menuType, collapsed } = props;
  const [menuList, setMenuList] = useState<any[]>([]);
  const [routeList, setRouteList] = useState<any[]>([]);

  const tabList: any = [];

  let userInfo: any = LStorage.get('userInfo');

  const { navigate } = useIRouter();

  useEffect(() => {
    if (menuType.Menu === 'xlb_home') return;
    let MenuList = Object.values(RouteSetting[menuType.Menu].MenuList) as any[];

    const RouteList = RouteSetting[menuType.Menu].RouteList as any[];
    let l: any[] = [];
    let m: any[] = [];
    /*
     * !   注意：修改oldSystem需要跟后端和前端张福旭沟通，确保线上系统新系统权限已经开启
     */
    const oldSystem = ['xlb_wms', 'xlb_dms'];
    const AppType = {
      [appName]: auth,
    };

    const appType = AppType[menuType.Menu];
    if (!oldSystem.includes(menuType.Menu)) {
      if (userInfo) {
        if (userInfo.authorities) {
          l = RouteList.filter((v) => {
            const item = userInfo.authorities.find(
              (s: any) =>
                s.action === '查询' &&
                s.name === v.title &&
                appType === s.app_type,
            );
            // 路由增加noAuth参数，设置为true显示
            return v?.noAuth || !!item;
          });
        } else {
          l = [];
          MenuList = [];
        }
      }
      m = MenuList.filter((v) => {
        if (v.label === '看板') return true;
        const item = l.find((s: any) => s.subMenu === v.key);
        return !!item;
      });
    } else {
      l = RouteList.filter((v) => {
        if (userInfo.admin) {
          return true;
        } else {
          return v.auths ? hasOldAuth(v.auths) : false;
        }
      });
      m = MenuList.filter((v) => {
        if (v.label === '看板') return true;
        const item = l.find((s: any) => s.subMenu === v.key);
        return !!item;
      });
    }

    setMenuList(MenuList);
    setRouteList(l);
  }, [menuType]);

  const content = (item: any) => {
    if (item.list) {
      item.list.map((a: any) => (a.children = []));
      routeList
        .filter((v) => !v.hidden)
        .map((s) => {
          if (s.subMenu === item.key && !!s.subTitle) {
            // 路由页有subMenu和subTitle才在菜单页显示
            const index = item.list.findIndex(
              (a: any) => a.label === s.subTitle,
            );
            if (index !== -1) {
              s.actived = false;
              if (tabList) {
                s.actived = tabList.some(
                  (b: any) => b.path == s.path || b.tabClass == s.tabClass,
                );
              }
              item.list[index].children.push(s);
            }
          }
        });
      item.list.forEach((v: any) => {
        v.width = v.children.some((s: any) => s.title.length > 7);
      });
    } else if (item.children) {
      item.children = [];
      routeList
        .filter((v) => !v.hidden)
        .map((s) => {
          if (s.subMenu === item.key && !!s.subTitle) {
            // 路由页有subMenu和subTitle才在菜单页显示
            s.actived = false;
            if (tabList) {
              tabList.forEach((b: any) => {
                if (b.path === s.path) {
                  s.actived = true;
                }
              });
            }
            item.children.push(s);
          }
        });
    }
    return (
      <>
        {item.list ? (
          <div className={styles.menu_detail_container}>
            {item.list.map((v: any, i: number) => {
              return (
                <div className={styles.menu_detail_item} key={i}>
                  <div style={{ margin: '16px 0 0 0' }}>
                    <span className={styles.menu_detail_item_title}>
                      {v.label}
                    </span>
                  </div>
                  <div className={'row-flex'} style={{ flexWrap: 'wrap' }}>
                    {v.children.map((s: any) => {
                      return (
                        <div
                          style={{
                            width: v.width ? '50%' : '33%',
                            margin: '16px 0 0 0',
                          }}
                          key={s.path}
                        >
                          {s?.noAuth ? (
                            <span
                              style={{
                                color: '#86909C',
                                fontSize: '14px',
                                cursor: 'pointer',
                              }}
                              onClick={() => {
                                if (s?.noAuth) return message.error('暂未开放');
                              }}
                            >
                              {s.showTitle ? s.showTitle : s.title}&nbsp;
                              <LockOutlined />
                            </span>
                          ) : (
                            <span
                              className={styles.menu_detail_item_span}
                              style={{ color: s.actived ? '#3D66FE' : '#000' }}
                              onClick={() => {
                                navigate(s.path);
                                if (
                                  s.path &&
                                  typeof s.path === 'string' &&
                                  s.path.indexOf('http') !== -1
                                ) {
                                  window.open(
                                    `${s.path}?store_id=${userInfo.store_id}&opreator=${userInfo.name}&token=${userInfo.access_token}`,
                                  );
                                  return;
                                }
                              }}
                            >
                              {s.showTitle ? s.showTitle : s.title}
                            </span>
                          )}
                        </div>
                      );
                    })}
                  </div>
                </div>
              );
            })}
          </div>
        ) : item.children ? (
          <div className={styles.menu_detail_container}>
            {item.children.map((v: any) => {
              return (
                <div className={styles.menu_detail_item} key={v.path}>
                  <div style={{ margin: '16px 0 0 0' }}>
                    <span className={styles.menu_detail_item_title}>
                      {v.label}
                    </span>
                  </div>
                  <div className={'row-flex'} style={{ flexWrap: 'wrap' }}>
                    <div style={{ width: '33%', margin: '16px 0 0 0' }}>
                      {v?.noAuth ? (
                        <span
                          style={{
                            color: '#86909C',
                            fontSize: '14px',
                            cursor: 'pointer',
                          }}
                          onClick={() => {
                            if (v?.noAuth) return message.error('暂未开放');
                          }}
                        >
                          {v.showTitle ? v.showTitle : v.title}&nbsp;
                          <LockOutlined />
                        </span>
                      ) : (
                        <span
                          className={styles.menu_detail_item_span}
                          style={{ color: v.actived ? '#3D66FE' : '#000' }}
                          onClick={() => {}}
                        >
                          {v.showTitle ? v.showTitle : v.title}
                        </span>
                      )}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        ) : null}
      </>
    );
  };

  return (
    <div
      className={styles.menu_container}
      style={{ width: collapsed ? '60px' : '120px' }}
    >
      <div className={styles.menu_items + ' c-flex'}>菜单管理</div>
      {menuList.map((v, i) => {
        return v.list || v.children ? (
          <Popover placement={'rightTop'} content={content(v)} key={i}>
            {/* 当前tab对应对面菜单高亮 */}
            <div
              className={
                v.key === tabList?.find((v: any) => v.active)?.subMenu
                  ? styles.menu_item_active
                  : styles.menu_item
              }
            >
              <span
                className={v.iconUrl}
                style={{ marginLeft: collapsed ? '10px' : '12px' }}
              ></span>
              <span
                style={{
                  opacity: collapsed ? 0 : 1,
                  transition: collapsed
                    ? 'opacity 0.1s ease'
                    : 'opacity 1s ease',
                }}
              >
                {v.label}
              </span>
            </div>
          </Popover>
        ) : (
          <div
            className={
              v.key === tabList?.find((v: any) => v.active)?.subMenu
                ? styles.menu_item_active
                : styles.menu_item
            }
            key={v.key}
            onClick={() => navigate(`/${v.key}`)}
          >
            <span
              className={v.iconUrl}
              style={{ marginLeft: collapsed ? '10px' : '12px' }}
            ></span>
            <span
              style={{
                marginLeft: '8px',
                opacity: collapsed ? 0 : 1,
                transition: collapsed ? 'opacity 0.1s ease' : 'opacity 1s ease',
              }}
            >
              {v.label}
            </span>
          </div>
        );
      })}
    </div>
  );
};

export default XlbMenus;

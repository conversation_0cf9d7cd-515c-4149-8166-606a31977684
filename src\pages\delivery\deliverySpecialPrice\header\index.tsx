import { useBaseParams } from '@/hooks/useBaseParams';
import { hasAuth } from '@/utils/kit';

import { message, Tooltip } from 'antd';

import { ErpFieldKeyMap } from '@/data/common/fieldListConfig';
// import { useKeepAliveRefresh } from '@/hooks';
import { useLocation } from '@umijs/max';
import {
  XlbButton,
  XlbIcon,
  XlbProPageContainer,
  XlbProPageContainerProps,
  XlbProPageModal,
  XlbProPageModalRef,
  XlbTipsModal,
} from '@xlb/components';
import dayjs from 'dayjs';
import { useEffect, useRef, useState } from 'react';
import Api from '../api';
import { Options1, Options3, tableList } from '../data';
import Item from '../item';
import AutomaticConfiguration from './components/AutomaticConfiguration';

const DeliverySpecialPrice = () => {
  // const { go } = useKeepAliveRefresh();
  const [autoConfig, setAutoConfig] = useState<boolean>(false);
  const { enable_organization } = useBaseParams((state) => state);
  const pageModalRef = useRef<XlbProPageModalRef>(null);
  const pageConatainerRef = useRef<XlbProPageContainerProps>(null);
  const [record, setRecord] = useState<any>({});
  const { search, state } = useLocation()

  const tableRender = (item: any) => {
    switch (item.code) {
      case 'fid':
        item.render = (value: any, record: any, index: number) => {
          return (
            <div className="cursors">
              <span
                className="link cursors"
                onClick={(e) => {
                  e.stopPropagation();
                  setRecord({ fid: record.fid, index: index });
                  pageModalRef.current?.setOpen(true);
                }}
              >
                {value}
              </span>
            </div>
          );
        };
        break;
      case 'state':
        item.render = (value: any) => {
          const item = Options1.find((v) => v.value === value);
          return (
            <div className={` ${item ? item.type : ''}`}>
              {item ? item.label : ''}
            </div>
          );
        };
        break;
      case 'type':
        item.render = (value: any) => {
          return (
            <div>
              {value === 'REQUEST'
                ? '补货特价'
                : value === 'DELIVERY_OUT'
                  ? '调出特价'
                  : ''}
            </div>
          );
        };
        break;
      case 'supplier_fee_status':
        item.render = (value: any) => {
          return (
            <div className={` ${value ? 'success' : 'info'}`}>
              {value ? '是' : '否'}
            </div>
          );
        };
        break;
      case 'memo':
        item.render = (value: any, record: any) => {
          return (
            <Tooltip placement="topLeft" autoAdjustOverflow title={value}>
              <div className="info"> {value}</div>
            </Tooltip>
          );
        };
        break;
    }
    return item;
  };

  useEffect(() => {
    openModal();
  }, []);

  const openModal = () => {
    const fid = state?.fid;
    const jumper = state?.jumpType;
    if (jumper && jumper == 'item') {
      setRecord({ fid: fid });
      pageModalRef.current?.setOpen(true);
    }
  };
  return (
    <>
      <XlbProPageModal
        ref={pageModalRef}
        Content={({ onClose }) => {
          return (
            <Item
              onBack={(isFresh) => {
                if (isFresh) {
                  pageConatainerRef?.current?.pageContainerRef?.current?.fetchData?.();
                }

                pageModalRef.current?.setOpen(false);
              }}
              record={record}
            ></Item>
          );
        }}
      >
        <div></div>
      </XlbProPageModal>
      <XlbProPageContainer
        ref={pageConatainerRef}
        timeTypeValueEnum={{
          0: 'create_date',
          1: 'audit_date',
          2: 'handle_date',
          3: 'invalid_date',
        }}
        searchFieldProps={{
          initialValues: {
            time_type: 0,
            create_date: [
              dayjs()?.format('YYYY-MM-DD') + ' 00:00:00',
              dayjs()?.format('YYYY-MM-DD') + ' 23:59:59',
            ],
            store_ids: [JSON.parse(localStorage.userInfo).value.store_id] || [],
          },
          formList: () => [
            {
              id: 'dateCommon',
              label: '日期范围',
              name: 'create_date',
              fieldProps: {
                resultFormat: 'YYYY-MM-DD HH:mm:ss',
              },
            },
            {
              id: 'timeType',
              label: '时间类型',
              name: 'time_type',
              fieldProps: {
                options: Options3,
              },
            },
            {
              id: ErpFieldKeyMap?.SingleStatus,
              label: '单据状态',
              name: 'state',
            },
            {
              id: ErpFieldKeyMap?.erpFidTooltip,
            },
            {
              id: ErpFieldKeyMap?.erpOrgIds,
              label: '配送组织',
              hidden: !enable_organization,
            },
            {
              id: ErpFieldKeyMap.erpAreaCodes,
              name: 'supply_area_codes',
              label: '行政区域',
            },
            {
              id: ErpFieldKeyMap.businessArea,
              name: 'supply_business_area_ids',
              label: '业务区域',
            },
            {
              id: ErpFieldKeyMap.erpStoreIdsForArea,
              name: 'store_ids',
              label: '配送门店',
            },
            {
              id: ErpFieldKeyMap?.erpOrgIds,
              label: '应用组织',
              name: 'supply_org_ids',
              hidden: !enable_organization,
              onChange: (e, formInstance: any) => {
                if (e?.length) {
                  formInstance.setFieldsValue({
                    supply_store_ids: [],
                  });
                }
              },
            },
            {
              id: ErpFieldKeyMap.erpStoreIds,
              name: 'supply_store_ids',
              label: '应用门店',
              fieldProps: (form) => {
                const data = {
                  org_ids: form.getFieldValue('supply_org_ids') || [],
                };
                if (!form.getFieldValue('supply_org_ids')) {
                  delete data.org_ids;
                }
                return {
                  dialogParams: {
                    type: 'store',
                    dataType: 'lists',
                    isMultiple: true,
                    data: data,
                  },
                  fieldNames: {
                    idKey: 'id',
                    nameKey: 'store_name',
                  },
                };
              },
            },
            { id: 'erpitemIds' },
            {
              id: ErpFieldKeyMap?.ErpSpecialPriceMode,
              label: '特价模式',
              name: 'type',
            },
            { id: 'commonInput', label: '制单人', name: 'create_by' },
            { id: 'commonInput', label: '审核人', name: 'audit_by' },
            {
              id: 'commonSelect',
              label: '生成费用单',
              name: 'supplier_fee_status',
              fieldProps: {
                options: [
                  {
                    label: '是',
                    value: 'true',
                  },
                  {
                    label: '否',
                    value: 'false',
                  },
                ],
              },
            },
            {
              label: '过滤',
              id: ErpFieldKeyMap.erpIsDefault,
              fieldProps: {
                options: [
                  {
                    label: '仅查询有效期内单据',
                    value: 'flag',
                  },
                  {
                    label: '仅查询剩余商品大于0的单据',
                    value: 'have_remain_quantity',
                  },
                ],
              },
            },
          ],
        }}
        extra={(context) => {
          const { loading, setLoading, fetchData, selectRowKeys } = context;
          return (
            <XlbButton.Group>
              {hasAuth(['配送特价', '编辑']) && (
                <XlbButton
                  style={{ order: 2 }}
                  type="primary"
                  onClick={() => {
                    setRecord({ fid: 1 });
                    pageModalRef.current?.setOpen(true);
                  }}
                  icon={<XlbIcon size={16} name="jia" />}
                >
                  新增
                </XlbButton>
              )}
              {hasAuth(['配送特价', '编辑']) && (
                <XlbButton
                  style={{ order: 5 }}
                  type="primary"
                  disabled={!selectRowKeys?.length}
                  onClick={() => {
                    const select_len = Array.isArray(selectRowKeys)
                      ? selectRowKeys.length
                      : 0;
                    if (select_len > 1) {
                      XlbTipsModal({
                        tips: '复制不支持批量操作',
                        onOk: () => {
                          return true;
                        },
                      });
                    } else {
                      XlbTipsModal({
                        tips: `是否确认复制单据"${selectRowKeys?.[0]}"?`,
                        onOk: async () => {
                          const res = await Api.copyInfo({
                            fid: selectRowKeys?.[0],
                          });
                          if (res.code === 0) {
                            // message.success('复制成功')
                            XlbTipsModal({
                              tips: `已生成新的单据"${res?.data?.fid}"！`,
                              isCancel: false,
                              onOk: () => {
                                return true;
                              },
                            });

                            fetchData();
                          }
                        },
                      });
                    }
                  }}
                  icon={<XlbIcon size={16} name="fuzhi" />}
                >
                  复制
                </XlbButton>
              )}
              {hasAuth(['配送特价', '编辑']) && (
                <XlbButton
                  style={{ order: 6 }}
                  type="primary"
                  disabled={!selectRowKeys?.length}
                  onClick={() => {
                    const select_len = Array.isArray(selectRowKeys)
                      ? selectRowKeys.length
                      : 0;
                    if (select_len > 1) {
                      XlbTipsModal({
                        tips: '生成费用单不支持批量操作!',
                        onOk: () => {
                          return true;
                        },
                      });
                    } else {
                      XlbTipsModal({
                        tips: `是否确认生成费用单"${selectRowKeys?.[0]}"?`,
                        onOk: async () => {
                          const res = await Api.getCreateSupplierFee({
                            fid: selectRowKeys?.[0],
                          });
                          if (res.code === 0) {
                            message.success('生成费用单成功');
                            fetchData();
                          }
                        },
                      });
                    }
                  }}
                  icon={<XlbIcon size={16} name="shengchengXXdan" />}
                >
                  生成费用单
                </XlbButton>
              )}
              {hasAuth(['配送特价', '编辑']) && (
                <XlbButton
                  style={{ order: 7 }}
                  type="primary"
                  onClick={() => setAutoConfig(true)}
                  icon={<XlbIcon size={16} name="shijian" />}
                >
                  自动特价配置
                </XlbButton>
              )}
            </XlbButton.Group>
          );
        }}
        // 删除
        deleteFieldProps={{
          name: '删除',
          order: 3,
          url: hasAuth(['配送特价', '删除'])
            ? '/erp/hxl.erp.deliveryspecialprice.batchdelete'
            : '',
          params: (selectRowKeys: any) => {
            return {
              fids: selectRowKeys,
            };
          },
          beforeTips: (selectRowKeys: string[], selectRow: any[]) => {
            if (selectRow?.some((item) => item.state !== 'INIT')) {
              return XlbTipsModal({
                tips: '只能删除单据状态为制单的单据!',
                isCancel: true,
                onOk: () => {
                  return true;
                },
              });
            }
            return XlbTipsModal({
              tips: `已选择${selectRow?.length || 0}张单据，是否确认删除？`,
              isCancel: true,
              onOk: () => {
                return true;
              },
            });
          },
        }}
        // 导出
        exportFieldProps={{
          order: 4,
          url: hasAuth(['配送特价', '导出'])
            ? '/erp/hxl.erp.deliveryspecialprice.export'
            : '',
          fileName: '配送特价导出.xlsx',
        }}
        copy
        tableFieldProps={{
          url: '/erp/hxl.erp.deliveryspecialprice.page',
          primaryKey: 'fid',
          tableColumn: () => {
            return tableList?.map((v) => tableRender(v));
          },
          selectMode: 'multiple',
          showColumnsSetting: true,
          keepDataSource: false,
        }}
      />
      <AutomaticConfiguration visible={autoConfig} setVisible={setAutoConfig} />
    </>
  );
};
export default DeliverySpecialPrice;

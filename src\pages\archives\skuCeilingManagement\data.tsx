import {
  SearchFormType,
  XlbIcon,
  XlbInputNumber,
  XlbTableColumnProps,
  XlbTooltip,
} from '@xlb/components';
const name = 'category_ids';

// let disabled = false;
export const formList: SearchFormType[] = [
  // {
  //   label: '汇总条件',
  //   name: 'summary_types',
  //   type: 'select',
  //   multiple: true,
  //   onChange: (e, form) => {
  //     console.log(form);
  //     form.setFieldValue(name, undefined);
  //     disabled = e.length === 0;
  //     // 设置
  //   },
  //   options: [
  //     { label: '一级分类', value: '1' },
  //     { label: '二级分类', value: '2' },
  //     { label: '三级分类', value: '3' },
  //     { label: '采购类型', value: '4' },
  //   ],
  // },
  {
    label: '商品分类',
    name: name,
    type: 'inputDialog',
    treeModalConfig: {
      title: '选择商品分类', // 标题
      url: '/erp/hxl.erp.category.find', // 请求地址
      dataType: 'lists',
      checkable: true, // 是否多选
      primaryKey: 'id',
      params: {
        enabled: true,
        level: 1,
      },
      width: 360, // 模态框宽度
    } as any,
    width: 200,
  },
];

export const detailForm: SearchFormType[] = [
  {
    label: '商品一级分类',
    type: 'input',
    name: 'level_one_category_name',
    disabled: true,
  },
  {
    label: '商品二级分类',
    name: 'level_two_category_name',
    disabled: true,
  },
  {
    label: '商品三级分类',
    name: 'level_three_category_name',
    disabled: true,
  },
  {
    label: 'SKU上限',
    name: 'limit_num',
    type: 'inputNumber',
    width: 120,
  },
  {
    label: '已有SKU',
    name: 'exist_sku', // exist_sku_num
    disabled: true,
  },
];

export const detailColumns: XlbTableColumnProps<any>[] = [
  {
    name: '序号',
    code: '_index',
    width: 50,
    align: 'center',
  },
  {
    name: '采购类型',
    code: 'type',
    width: 160,
    features: { sortable: true },
  },
  {
    name: 'SKU上限',
    code: 'limit_value',
    width: 180,
    features: { sortable: true },
    render(text, record) {
      if (record._click) {
        let max = 99999;
        let min = 0;
        const getMaxOrMin = (v = 0, num = 0) => {
          return typeof v === 'number' ? v : num;
        };
        if (record.type === '集采品') {
          max = getMaxOrMin(record.collective_purchase_up_limit_num, 99999);
          min = getMaxOrMin(record.collective_purchase_down_limit_num, 0);
        } else if (record.type === '地采品') {
          max = getMaxOrMin(record.ground_purchase_up_limit_num, 99999);
          min = getMaxOrMin(record.ground_purchase_down_limit_num, 0);
        } else if (record.type === '店采品') {
          max = getMaxOrMin(record.shop_purchase_up_limit_num, 99999);
          min = getMaxOrMin(record.shop_purchase_down_limit_num, 0);
        }
        return (
          <>
            <div style={{ display: 'flex', gap: '5px', alignItems: 'center' }}>
              <XlbInputNumber
                precision={0}
                step={1}
                max={max}
                min={min}
                defaultValue={text}
                onChange={(e) => {
                  record.limit_value = e;
                }}
                onClick={(e) => e.stopPropagation()}
                onPressEnter={(e) => e.stopPropagation()}
              ></XlbInputNumber>
              <XlbTooltip
                title={
                  record.type +
                  '可输入最大值为' +
                  max +
                  '；可输入最小值为' +
                  min
                }
              >
                <XlbIcon size={16} name="bangzhu" />
              </XlbTooltip>
            </div>
          </>
        );
        // limit_num
      } else {
        return record.limit_value;
      }
    },
  },
  {
    name: '已存在SKU',
    code: 'exist_sku_value',
    width: 160,
    features: { sortable: true },
  },
];

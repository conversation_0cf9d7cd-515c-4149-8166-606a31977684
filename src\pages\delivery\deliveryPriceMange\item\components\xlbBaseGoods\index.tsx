import { columnWidthEnum } from '@/data/common/constant';
import useDrag from '@/hooks/useDrag';
import { XlbModal, XlbTable, XlbTableColumnProps } from '@xlb/components';
import { useEffect, useState } from 'react';
import styles from './index.less';
import Api from './server';
const XlbBaseGoods = (props: any) => {
  const { visible, handCancel, params } = props;
  const [rowData, setRowData] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [pagin, setPagin] = useState({
    pageSize: 200,
    pageNum: 1,
    total: 0,
  });
  const priceType = [
    {
      label: '按比例',
      value: 'RATIO',
    },
    {
      label: '按金额',
      value: 'MONEY',
    },
    {
      label: '固定金额',
      value: 'FIXED_MONEY',
    },
  ];
  const dateGoodsColumns: XlbTableColumnProps<any>[] = [
    {
      name: '序号',
      code: '_index',
      width: columnWidthEnum.INDEX,
      align: 'center',
    },
    {
      name: '门店',
      code: 'store_name',
      width: columnWidthEnum.STORE_NAME,
      features: { sortable: true },
      align: 'left',
    },
    {
      name: '配送单位',
      code: 'unit',
      width: 120,
      features: { sortable: true },
      align: 'left',
    },
    {
      name: '档案采购价',
      code: 'purchase_price',
      width: 110,
      features: { sortable: true },
      align: 'right',
      render(value, record, index) {
        return (
          <div className="info overwidth">
            {value !== null ? value?.toFixed(4) : '-'}
          </div>
        );
      },
    },
    {
      name: '配送价类型',
      code: 'type',
      width: 120,
      features: { sortable: true },
      align: 'left',
      render(value, record, index) {
        return (
          <div className="info overwidth">
            {value ? priceType.find((v) => v.value === value)?.label : '-'}
          </div>
        );
      },
    },
    {
      name: '数值',
      code: 'value',
      width: 110,
      features: { sortable: true },
      align: 'right',
      render(value, record, index) {
        return (
          <div className="info overwidth">
            {value !== null ? value?.toFixed(4) : '-'}
          </div>
        );
      },
    },
    {
      name: '配送价',
      code: 'price',
      width: 110,
      features: { sortable: true },
      align: 'right',
      render(value, record, index) {
        return (
          <div className="info overwidth">
            {value !== null ? value?.toFixed(4) : '0.0000'}
          </div>
        );
      },
    },
    {
      name: '基本单位',
      code: 'basic_unit',
      width: 100,
      features: { sortable: true },
      align: 'right',
    },
    {
      name: '基本单位数值',
      code: 'basic_unit_value',
      width: 150,
      features: { sortable: true },
      align: 'right',
      render(value, record, index) {
        return (
          <div className="info overwidth">
            {value !== null ? value?.toFixed(4) : '0.0000'}
          </div>
        );
      },
    },
    {
      name: '基本单位配送价',
      code: 'basic_unit_price',
      width: 150,
      features: { sortable: true },
      align: 'right',
      render(value, record, index) {
        return (
          <div className="info overwidth">
            {value !== null ? value?.toFixed(4) : '0.0000'}
          </div>
        );
      },
    },
  ];
  useEffect(() => {
    visible && getData(1);
  }, [visible]);
  const getData = async (page_number: number) => {
    setIsLoading(true);
    const res = await Api.getGoodsPrice({ ...params, ...pagin });
    if (res.code === 0) {
      setRowData(res.data.content || []);
      setPagin({
        ...pagin,
        pageNum: page_number,
        total: res.data.total_elements,
      });
    }
    setIsLoading(false);
  };
  useEffect(() => {
    if (!visible) return;
    useDrag('.ant-modal-header', '.ant-modal-content');
  }, [visible]);

  return (
    <XlbModal
      title={'查看配送价'}
      visible={visible}
      width={900}
      onCancel={handCancel}
      isCancel
      footer={null}
      maskClosable={true}
      onOk={handCancel}
      centered
    >
      <div
        className={rowData?.length ? styles.table_box : styles.table_empty_box}
      >
        <XlbTable
          loading={isLoading}
          style={{ maxHeight: 300, overflowY: 'auto' }}
          columns={dateGoodsColumns}
          dataSource={rowData}
          total={rowData?.length}
        ></XlbTable>
      </div>
    </XlbModal>
  );
};
export default XlbBaseGoods;

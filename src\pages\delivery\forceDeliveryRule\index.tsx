import { hasAuth } from '@/utils';
import { wujieBus } from '@/wujie/utils';
import NiceModal from '@ebay/nice-modal-react';
import {
  XlbBasicForm,
  XlbButton,
  XlbCopyBoard,
  XlbForm,
  XlbIcon,
  XlbMessage,
  XlbPageContainer,
  XlbTable,
  XlbTabs,
  XlbTipsModal,
  type XlbTableColumnProps,
} from '@xlb/components';
import { useNavigation } from '@xlb/max';
import { useRef, useState, type FC } from 'react';
import LookRules from './components/LookRules';
import addPCRules from './components/addPCRules/index';
import addRules from './components/addRules/index';
import editSCMRules from './components/editSCMRules/index';
import api from './server';

const Index: FC<{ title: string }> = () => {
  const [form] = XlbBasicForm.useForm();
  const [formList] = XlbBasicForm.useForm();
  const pageRef = useRef<any>(null);
  const [tabKey, setTabKey] = useState('tp');
  // 数据缓存
  const [tpTotalPagin, setTpTotalPagin] = useState({
    pageSize: 200,
    pageNum: 1,
    total: 0,
  });
  const [pcTotalPagin, setPcTotalPagin] = useState({
    pageSize: 200,
    pageNum: 1,
    total: 0,
  });
  const [xpTotalPagin, setXpTotalPagin] = useState({
    pageSize: 200,
    pageNum: 1,
    total: 0,
  });
  const [tpRowData, setTpRowData] = useState<[]>([]);
  const [pcRowData, setPcRowData] = useState<[]>([]);
  const [xpRowData, setXpRowData] = useState<[]>([]);
  const changeKey = (e: string, context: any) => {
    switch (tabKey) {
      case 'tp':
        setTpRowData(pageRef.current?.dataSource);
        setTpTotalPagin(pageRef.current?.pagin);
        break;
      case 'pc':
        setPcRowData(pageRef.current?.dataSource);
        setPcTotalPagin(pageRef.current?.pagin);
        break;
      case 'xp':
        setXpRowData(pageRef.current?.dataSource);
        setXpTotalPagin(pageRef.current?.pagin);
        break;
    }
    setTabKey(e);
    pageRef.current?.setSelectRow([]);
    pageRef.current?.setSelectRowKeys([]);
    switch (e) {
      case 'tp':
        context?.setDataSource(tpRowData);
        context?.setPagin(tpTotalPagin);
        break;
      case 'pc':
        context?.setDataSource(pcRowData);
        context?.setPagin(pcTotalPagin);
        break;
      case 'xp':
        context?.setDataSource(xpRowData);
        context?.setPagin(xpTotalPagin);
        break;
    }
  };
  const prevPost = (data: any) => {
    return {
      ...data,
      ...formList.getFieldsValue(true),
    };
  };
  const tableColumn: XlbTableColumnProps<any>[] = [
    {
      name: '序号',
      code: '_index',
      width: 65,
      align: 'center',
    },
    {
      name: '规则名称',
      code: 'rule_name',
      width: 160,
      features: { sortable: true },
      render(text, record, index) {
        return (
          <span
            className="link cursors"
            onClick={() =>
              NiceModal.show(NiceModal.create(addRules), {
                fetchData: pageRef?.current?.fetchData,
                id: record?.id,
              })
            }
          >
            {text}
          </span>
        );
      },
    },
    {
      name: '自动统配规则',
      code: 'item_scope_rule_name',
      width: 360,
      features: { sortable: true },
    },
    {
      name: '规则状态',
      code: 'enable_status',
      width: 160,
      features: {
        sortable: true,
      },
      render(text, record, index) {
        return (
          <div style={{ color: text === '启用' ? '#00b42b' : '#1D2129' }}>
            {text}
          </div>
        );
      },
    },
    {
      name: '最后修改人',
      code: 'update_by',
      width: 160,
      features: { sortable: true },
    },
    {
      name: '最新编辑时间',
      code: 'update_time',
      width: 220,
      features: { sortable: true, format: 'TIME' },
    },
    {
      name: '操作',
      width: 160,
      code: 'action',
      features: {
        sortable: false,
      },
      render(text, record, index) {
        return (
          <div>
            <XlbButton type="link" onClick={() => LookStore(record)}>
              查看应用门店
            </XlbButton>
          </div>
        );
      },
    },
  ];
  const tableColumn2: XlbTableColumnProps<any>[] = [
    {
      name: '序号',
      code: '_index',
      width: 65,
      align: 'center',
    },
    {
      name: '规则名称',
      code: 'rule_name',
      width: 160,
      features: { sortable: true },
      render(text, record, index) {
        return (
          <span
            className="link cursors"
            onClick={() =>
              NiceModal.show(NiceModal.create(addPCRules), {
                fetchData: pageRef?.current?.fetchData,
                id: record?.id,
              })
            }
          >
            {text}
          </span>
        );
      },
    },
    {
      name: '规则状态',
      code: 'enabled',
      width: 160,
      features: {
        sortable: true,
      },
      render(text, record, index) {
        return (
          <div style={{ color: text ? '#00b42b' : '#1D2129' }}>
            {text ? '启用' : '停用'}
          </div>
        );
      },
    },
    {
      name: '最后修改人',
      code: 'update_by',
      width: 160,
      features: { sortable: true },
    },
    {
      name: '最新编辑时间',
      code: 'update_time',
      width: 220,
      features: { sortable: true, format: 'TIME' },
    },
  ];
  const tableColumn3: XlbTableColumnProps<any>[] = [
    {
      name: '序号',
      code: '_index',
      width: 65,
      align: 'center',
    },
    {
      name: '门店',
      code: 'store_name',
      width: 160,
      features: { sortable: true },
    },
    {
      name: '商品代码',
      code: 'item_code',
      width: 180,
      features: { sortable: true },
      render(text, record, index) {
        return (
          <XlbCopyBoard text={text}>
            {hasAuth(['商品档案', '查询']) ? (
              <span
                className="link cursors"
                onClick={(e) => {
                  e.stopPropagation();
                  const { navigate } = useNavigation();
                  navigate(
                    '/xlb_erp/goodsFiles/item',
                    {
                      record: {
                        ...record,
                        id: record?.item_id,
                        type: 'edit',
                      },
                    },
                    'xlb_erp',
                    true,
                  );
                }}
              >
                {text}
              </span>
            ) : (
              <span>{text}</span>
            )}
          </XlbCopyBoard>
        );
      },
    },
    {
      name: '商品名称',
      code: 'item_name',
      width: 180,
      features: {
        sortable: true,
      },
    },
    {
      name: '采购规格',
      code: 'item_spec',
      width: 160,
      features: { sortable: true },
    },
    {
      name: '自动统配数量',
      code: 'quantity',
      width: 220,
      features: { sortable: true, format: 'QUANTITY' },
    },
    {
      name: '操作',
      width: 160,
      code: 'action',
      features: {
        sortable: false,
      },
      render(text, record, index) {
        return (
          <div>
            <XlbButton
              type="link"
              onClick={() =>
                NiceModal.show(NiceModal.create(editSCMRules), {
                  fetchData: pageRef?.current?.fetchData,
                  id: record?.id,
                })
              }
            >
              编辑
            </XlbButton>
          </div>
        );
      },
    },
  ];
  const LookStore = async (record: any) => {
    pageRef.current?.setLoading(true);
    const res = await api.ruleStore({ id: record.id });
    pageRef.current?.setLoading(false);
    if (res?.code === 0) {
      XlbTipsModal({
        title: '查看应用门店',
        width: 800,
        keyboard: false,
        isConfirm: false,
        maskClosable: true,
        tips: (
          <>
            <div
              style={{
                marginBottom: 5,
                display: 'flex',
                justifyContent: 'flex-start',
              }}
            >
              <XlbButton
                label="导出"
                type="primary"
                onClick={(e) => handleExport(record, e)}
                disabled={res?.data?.length === 0}
                icon={<XlbIcon name="daochu" />}
              />
            </div>
            <XlbTable
              columns={[
                { name: '序号', code: '_index', width: 65, align: 'center' },
                { name: '代码', code: 'store_code' },
                { name: '名称', code: 'store_name', width: 210 },
                { name: '门店分组', code: 'store_group_name' },
                { name: '组织', code: 'org_name' },
                {
                  name: '经营类型',
                  code: 'management_type',
                  render: (value: any) => {
                    return (
                      <div>
                        {value === '0' ? '直营' : value === '1' ? '加盟' : ''}
                      </div>
                    );
                  },
                },
              ]}
              dataSource={res?.data}
              total={res?.data?.length}
              pageSize={200}
              style={{ maxHeight: 'calc(100vh - 346px)' }}
            />
          </>
        ),
      });
    }
  };
  const handleExport = async (record: any, e: any) => {
    const res = await api.ruleStoreExport({ id: record.id });
    if (res.code === 0) {
      wujieBus?.$emit('xlb_erp-event', { code: 'downloadEnd', target: e });
      XlbMessage.success(res?.data);
    }
  };
  const handleDelete = async (selectedRowKeys: any) => {
    await XlbTipsModal({
      isConfirm: true,
      isCancel: true,
      tips: '确定要删除自动统配规则吗?',
      onOk: async () => {
        const res =
          tabKey === 'tp'
            ? await api.delete({ ids: selectedRowKeys })
            : tabKey === 'pc'
              ? await api.deleteExclude({ ids: selectedRowKeys })
              : await api.scmDelete({ ids: selectedRowKeys });
        if (res?.code === 0) {
          pageRef.current?.fetchData();
          XlbMessage.success('删除成功');
        }
      },
    });
  };

  return (
    <XlbPageContainer // 查询
      url={
        tabKey === 'tp'
          ? '/erp/hxl.erp.forcedelivery.rule.page'
          : tabKey === 'pc'
            ? '/erp/hxl.erp.forcedelivery.exclude.page'
            : tabKey === 'xp'
              ? '/erp/hxl.erp.forcedelivery.scm.item.page'
              : ''
      }
      changeColumnAndResetDataSource={false}
      tableColumn={
        tabKey === 'tp'
          ? tableColumn
          : tabKey === 'pc'
            ? tableColumn2
            : tabKey === 'xp'
              ? tableColumn3
              : tableColumn
      }
      prevPost={prevPost}
      immediatePost={false}
    >
      <XlbPageContainer.ToolBtn showColumnsSetting>
        {(context: any) => {
          const { loading, fetchData, selectRowKeys } = context;

          pageRef.current = context;
          return (
            <XlbButton.Group>
              {hasAuth(['自动统配规则', '查询']) && (
                <XlbButton
                  label={'查询'}
                  disabled={loading}
                  type="primary"
                  onClick={() => fetchData()}
                  icon={<XlbIcon name="sousuo" />}
                />
              )}
              {hasAuth(['自动统配规则', '编辑']) && tabKey === 'tp' && (
                <XlbButton
                  type="primary"
                  onClick={() => {
                    NiceModal.show(NiceModal.create(addRules), {
                      fetchData: pageRef?.current?.fetchData,
                    });
                  }}
                  disabled={loading}
                  icon={<XlbIcon size={16} name="jia" />}
                >
                  新增自动统配
                </XlbButton>
              )}
              {hasAuth(['自动统配规则', '编辑']) && tabKey === 'pc' && (
                <XlbButton
                  type="primary"
                  onClick={() => {
                    NiceModal.show(NiceModal.create(addPCRules), {
                      fetchData: pageRef?.current?.fetchData,
                    });
                  }}
                  disabled={loading}
                  icon={<XlbIcon size={16} name="jia" />}
                >
                  新增自动排除
                </XlbButton>
              )}
              <XlbButton
                type="primary"
                onClick={() => {
                  XlbTipsModal({
                    title: '查看规则效果',
                    width: 1000,
                    maskClosable: true,
                    keyboard: false,
                    isConfirm: false,
                    tips: <LookRules />,
                  });
                }}
                disabled={loading}
                icon={<XlbIcon size={16} name="xianshimima" />}
              >
                查看规则效果
              </XlbButton>
              {hasAuth(['自动统配规则', '删除']) && (
                <XlbButton
                  type="primary"
                  onClick={() => {
                    handleDelete(selectRowKeys);
                  }}
                  disabled={loading || selectRowKeys?.length === 0}
                  icon={<XlbIcon size={16} name="shanchu" />}
                >
                  删除
                </XlbButton>
              )}
            </XlbButton.Group>
          );
        }}
      </XlbPageContainer.ToolBtn>

      <XlbPageContainer.SearchForm>
        <XlbForm
          formList={[
            {
              label: '关键字',
              name: 'keyword',
              type: 'input',
            },
            {
              label: '规则状态',
              name: 'enabled',
              type: 'select',
              options: [
                { label: '启用', value: 1 },
                { label: '禁用', value: 0 },
              ],
            },
          ]}
          form={formList}
          isHideDate={true}
        />
      </XlbPageContainer.SearchForm>

      <XlbPageContainer.ToolBtnNoStyle>
        {(context: any) => {
          const { fetchData } = context;
          return (
            <>
              <XlbTabs
                defaultActiveKey={tabKey}
                style={{ paddingLeft: '16px' }}
                activeKey={tabKey}
                onTabClick={(e) => {
                  changeKey(e, context);
                }}
                items={[
                  {
                    label: '自动统配',
                    key: 'tp',
                  },
                  {
                    label: '自动排除',
                    key: 'pc',
                  },
                  {
                    label: 'SCM同步新品统配',
                    key: 'xp',
                  },
                ]}
              />
            </>
          );
        }}
      </XlbPageContainer.ToolBtnNoStyle>

      <XlbPageContainer.Table
        selectMode="multiple"
        primaryKey={'id'}
        key={tabKey}
      />
    </XlbPageContainer>
  );
};

export default Index;

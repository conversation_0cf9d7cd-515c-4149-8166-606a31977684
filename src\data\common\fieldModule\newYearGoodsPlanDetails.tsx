export const NewYearGoodsPlanDetailsMap = {
  erpItemIds: 'erpItemIds',
  // 采购经营范围组织单选
  erpPurchase: 'erpPurchase',
  // 采购计划 - 多选
  purchasePlans: 'purchasePlans',
  // 表单里直接存入商品信息
  erpItem: 'erpItem',
  //
  erpHiddenItemId: 'erpHiddenItemId',
  // 采购计划
  purchasePlanBuyNumber: 'purchasePlanBuyNumber',
  // 采购金额
  purchasePlanMoney: 'purchasePlanMoney',
  // 采购计划 - 单选
  purchasePlan: 'purchasePlan',
  // 采购计划 - 组织最多选到二级
  purchasePlanOrg: 'purchasePlanOrg'
}

export const newYearGoodsPlanDetailsConfig: any[] = [
  {
    tag: 'ERP',
    label: '商品',
    id: NewYearGoodsPlanDetailsMap?.erpItemIds,
    name: 'item_ids',
    fieldProps: {
      dialogParams: {
        type: 'item',
        dataType: 'lists',
        isMultiple: true
      }
    },
    dependencies: ['type'],
    componentType: 'inputDialog'
  },
  {
    tag: 'ERP',
    label: '组织',
    id: NewYearGoodsPlanDetailsMap.erpPurchase,
    name: 'org_id',
    componentType: 'select',
    fieldProps: {
      allowClear: true
    },
    request: async (formValues: any, anybaseURL: any, globalFetch: any) => {
      const res = await globalFetch.post(anybaseURL + '/erp-mdm/hxl.erp.org.find', {
        company_ids: formValues.company_ids || '',
        level: 2
      })
      if (res.code == 0) {
        return res.data.map((item: any) => {
          return {
            label: item.name,
            value: item.id
          }
        })
      }
      return []
    }
  },
  {
    label: '采购计划',
    id: NewYearGoodsPlanDetailsMap.purchasePlan,
    name: 'purchase_plan_id',
    componentType: 'inputDialog',
    fieldProps: {
      dialogParams: {
        dataType: 'list',
        type: 'purchasePlan',
        isLeftColumn: false,
        isMultiple: false
      }
    },
    formItemProps: {
      normalize(data: any) {
        return Array.isArray(data) ? data?.[0] : undefined
      }
    }
  },
  {
    tag: 'ERP',
    label: '采购计划',
    id: NewYearGoodsPlanDetailsMap.purchasePlans,
    name: 'purchase_plan_ids',
    componentType: 'inputDialog',
    fieldProps: {
      dialogParams: {
        dataType: 'list',
        type: 'purchasePlan',
        isLeftColumn: false,
        isMultiple: true
      }
    },
    dependencies: ['summary_types']
  },
  {
    tag: 'ERP',
    id: NewYearGoodsPlanDetailsMap.erpItem,
    name: 'itemInfo',
    label: '商品',
    fieldProps: {
      dialogParams: {
        type: 'goods',
        dataType: 'lists'
      }
    },
    formItemProps: {
      getValueFromEvent(value: any, options: any) {
        return options?.[0]
      },
      getValueProps(value: any) {
        const id = value?.id
        return {
          value: id
        }
      }
    },
    componentType: 'inputDialog'
  },
  {
    tag: 'ERP',
    id: NewYearGoodsPlanDetailsMap.erpHiddenItemId,
    componentType: 'input',
    name: 'item_id',
    hidden: true,
    dependencies: ['itemInfo'],
    request(value: any) {
      return value
    },
    handleDefaultValue(data: any) {
      return data?.itemInfo?.id
    }
  },
  {
    tag: 'ERP',
    id: NewYearGoodsPlanDetailsMap.purchasePlanBuyNumber,
    name: 'quantity',
    label: '采购计划',
    componentType: 'inputNumber',
    fieldProps: {
      min: 0
    }
    // dependencies: ['itemInfo', 'money'],
    // request(value) {
    //   console.log('value', value)
    //   return value
    // },
    // handleDefaultValue(data) {
    //   console.log('采购计划-data', data)
    //   const purchase_price = data?.itemInfo?.purchase_price
    //   const money = data?.money
    //   let quantity: number | undefined = undefined
    //   if (purchase_price > 0 && money > 0) {
    //     quantity = Math.ceil(money / purchase_price)
    //   }
    //   return quantity
    // }
  },
  {
    tag: 'ERP',
    id: NewYearGoodsPlanDetailsMap.purchasePlanMoney,
    name: 'money',
    label: '金额（元）',
    componentType: 'inputNumber',
    fieldProps: {
      min: 0
    },
    dependencies: ['itemInfo', 'quantity'],
    request(value: any) {
      return value
    },
    handleDefaultValue(data: any) {
      const purchase_price = data?.itemInfo?.purchase_price
      const quantity = data?.quantity
      let money: number | undefined = undefined
      if (purchase_price > 0 && quantity > 0) {
        money = Number(purchase_price) * Number(quantity)
        money = Number(money.toFixed(3))
      }
      return money
    }
  },
  {
    tag: 'ERP',
    id: NewYearGoodsPlanDetailsMap.purchasePlanOrg,
    componentType: 'select',
    name: 'org_ids',
    label: '组织',
    fieldProps: {
      mode: 'multiple'
    },
    request: async (formValues: any, anybaseURL: any, globalFetch: any) => {
      const res = await globalFetch.post(anybaseURL + '/erp-mdm/hxl.erp.org.find', {})
      if (res?.code == 0) {
        return res.data
          ?.filter((v: any) => v.id)
          ?.filter((v: any) => v.level < 3)
          ?.map((item: any) => {
            return {
              label: item.name,
              value: item.id
            }
          })
      }
      return []
    },
    dependencies: ['summary_types']
  }
]
import { formatWith<PERSON>ommas, hasAuth } from '@/utils/kit';
import { LStorage } from '@/utils/storage';
import { wujieBus } from '@/wujie/utils';
import { useBaseParams } from '@/hooks/useBaseParams';
import {
  DeleteOutlined,
  FileAddOutlined,
  SearchOutlined,
  UploadOutlined,
  AuditOutlined
} from '@ant-design/icons';
import {
  ContextState,
  SearchFormType,
  XlbBasicForm,
  XlbButton,
  XlbForm,
  XlbIcon,
  XlbPageContainer,
  XlbProPageModal,
  XlbProPageModalRef,
  XlbTableColumnProps,
  XlbTipsModal,
} from '@xlb/components';
import { XlbPageContainerRef } from '@xlb/components/dist/lowcodes/XlbPageContainer';
import BatchOrderIndex from './components/batchOrder/batchOrder'
import { XlbFetch } from '@xlb/utils';
import { message } from 'antd';
import moment from 'moment';
import { useEffect, useRef, useState } from 'react';
import { InOut, Options1, Options3, tableList } from './data';
import Item from './item/index';
import { deleteBasket } from './server';
const { Table, ToolBtn, SearchForm } = XlbPageContainer;
const TransferDocument = () => {
  const { enable_organization } = useBaseParams((state) => state);
  const [isLoading, setisLoading] = useState<boolean>(false);
  const [itemArr, setItemArr] = useState<XlbTableColumnProps<any>[]>(
    JSON.parse(JSON.stringify(tableList)),
  );
  const [record, setRecord] = useState<any>({});
  const pageModalRef = useRef<XlbProPageModalRef>(null);
  const pageConatainerRef = useRef<XlbPageContainerRef>(null);
  const [form] = XlbBasicForm.useForm();
  const [batchOrderVisible, setBatchOrderVisible] = useState<boolean>(false);

  const formList: SearchFormType[] = [
    {
      width: 372,
      type: 'compactDatePicker',
      label: '日期选择',
      name: 'compactDatePicker',
      allowClear: true,
      // disabled: true,
      // format: "YYYY-MM-DD HH:mm:ss",
      // @ts-ignore
    },
    {
      label: '单据状态',
      name: 'state',
      type: 'select',
      clear: true,
      check: true,
      options: Options1,
    },
    {
      label: '进出方向',
      name: 'flag',
      type: 'select',
      clear: true,
      check: true,
      options: InOut,
    },
    {
      label: '时间类型',
      name: 'time_type',
      type: 'select',
      clear: false,
      check: true,
      options: Options3,
    },
    {
      label: '载具名称',
      name: 'basket_ids',
      type: 'inputDialog',
      dialogParams: {
        type: 'basket',
        dataType: 'lists',
        isLeftColumn: false,
        isMultiple: true,
        primaryKey: 'id',
      },
      clear: false,
      check: true,
    },
    {
      label: '发货组织',
      name: 'out_org_ids',
      type: 'select',
      multiple: true,
      hidden: !enable_organization,
      clear: true,
      check: true,
      options: [],
      // @ts-ignore
      onChange: (e: any, formData: any) => {
        if (e?.length > 0) {
          formData.setFieldsValue({
            out_store_ids: [],
          });
        }
      },
      selectRequestParams: (params: Record<string, any>) => {
        return {
          url: '/erp-mdm/hxl.erp.org.find',
          responseTrans(data) {
            const options = data.map((item: any) => {
              const obj = {
                label: item.name,
                value: item.id,
              };
              return obj;
            });
            return options;
          },
        };
      },
    },
    {
      label: '发货门店',
      name: 'out_store_ids',
      type: 'inputDialog',
      allowClear: true,
      dialogParams: {
        type: 'store',
        dataType: 'lists',
        isLeftColumn: true,
        isMultiple: true,
        primaryKey: 'id',
        data: {
          status: true,
        },
      },
      fieldNames: {
        idKey: 'id',
        nameKey: 'store_name',
      } as any,
    },
    {
      label: '收货门店',
      name: 'store_ids',
      type: 'inputDialog',
      dialogParams: {
        type: 'store',
        dataType: 'lists',
        isLeftColumn: true,
        isMultiple: true,
        primaryKey: 'id',
        data: {
          status: true,
        },
      },
      fieldNames: {
        idKey: 'id',
        nameKey: 'store_name',
      } as any,
      clear: false,
      check: true,
    },
    {
      label: '单号搜索',
      name: 'fid',
      type: 'input',
      tooltip: '单据号不受其他查询条件限制',
      clear: true,
      check: true,
    },
    {
      label: '制单人',
      name: 'create_by',
      type: 'input',
      check: true,
      clear: true,
    },
    {
      label: '审核人',
      name: 'audit_by',
      type: 'input',
      check: true,
      clear: true,
    },
  ];
  const tableRender = (item: any) => {
    switch (item.code) {
      case 'fid':
        item.render = (value: any, record: any, index: any) => {
          return (
            <div className="cursors">
              <span
                className="link cursors"
                onClick={(e) => {
                  e.stopPropagation();
                  setRecord(record);
                  pageModalRef.current?.setOpen(true);
                }}
              >
                {value}
              </span>
            </div>
          );
        };
        break;
      case 'out_org_id':
        item.render = (value: any, record: any, index: any) => {
          return <div className="info">{record.out_org_name}</div>;
        };
        break;
      case 'out_store_id':
        item.render = (value: any, record: any, index: any) => {
          return <div className="info">{record.out_store_name}</div>;
        };
        break;
      case 'store_id':
        item.render = (value: any, record: any, index: any) => {
          return <div className="info">{record.store_name}</div>;
        };
        break;
      case 'flag':
        item.render = (value: any, record: any, index: number) => {
          return <div className="info">{value ? '退货' : '发货'}</div>;
        };
        break;
      case 'money':
        item.render = (value: any, record: any, index: number) => {
          return (
            <div className="info">
              {value ? formatWithCommas(Number(value).toFixed(2)) : '0.00'}
            </div>
          );
        };
        break;
      case 'state':
        item.render = (value: any, record: any, index: any) => {
          const item = Options1.find((v) => v.value === value);
          return (
            <div className={`${item ? item.type : ''}`}>
              {item ? item.label : ''}
            </div>
          );
        };
        break;
    }
    return item;
  };
  const prevPost = () => {
    const { panelValue, compactDatePicker } = form.getFieldsValue(true);
    const time_type = form.getFieldValue('time_type');
    const formatCompactDatePicker = compactDatePicker.map((date, index) => {
      return index === 0 ? `${date} 00:00:00` : `${date} 23:59:59`;
    });
    const data = {
      ...form.getFieldsValue(true),
      create_date: time_type === 'create_date' ? formatCompactDatePicker : null,
      audit_date: time_type === 'audit_date' ? formatCompactDatePicker : null,
      approve_date:
        time_type === 'approve_date' ? formatCompactDatePicker : null,
      handle_date: time_type === 'handle_date' ? formatCompactDatePicker : null,
      fid: form.getFieldValue('fid'),
      flag: form.getFieldValue('flag'),
      state: form.getFieldValue('state'),
      basket_ids: form.getFieldValue('basket_ids')
        ? form.getFieldValue('basket_ids')
        : [],
      out_store_ids: form.getFieldValue('out_store_ids')
        ? form.getFieldValue('out_store_ids')
        : null,
      store_ids: form.getFieldValue('store_ids')
        ? form.getFieldValue('store_ids')
        : null,
      create_by: form.getFieldValue('create_by'),
      audit_by: form.getFieldValue('audit_by'),
    };
    return data;
  };
  const batchaudit = async (selectRow: any) => {
    const data = {
      fids: selectRow?.map((v) => v.fid),
    }
    await XlbTipsModal({
      title: '批量审核',
      tips: `已选择${selectRow?.length}张单据，是否确认批量审核!`,
      onOkBeforeFunction: async () => {
        const res = await XlbFetch.post('/erp/hxl.erp.basketorder.batchaudit', data)
        if (res?.code == 0) {
          if (res?.data?.fail_num > 0) {
            XlbTipsModal({
              tips: (
                <div style={{ display: 'flex', flexDirection: 'column' }}>
                  <span>{res?.data?.error_messages?.join(',')}</span>
                </div>
              )
            })
            return false
          }
          if(res?.data?.success_num > 0){
            message.success('批量审核成功')
            pageConatainerRef?.current?.fetchData?.();
          }
        }
        return true
      }
    })
  }
  const deleteItem = async (selectRow: any) => {
    const notInit = selectRow.find((v) => v.state !== 'INIT');
    console.log(pageConatainerRef,'pageConatainerRef==>');
    
    if (notInit) {
      XlbTipsModal({
        tips: '只能删除单据状态为制单的单据!',
      });
      return false;
    }
    const fidsList = selectRow.map((v) => v.fid);
    await XlbTipsModal({
      tips: `已选择${selectRow?.length}张单据，是否确认删除!`,
      onOkBeforeFunction: async () => {
        const res = await deleteBasket({ fids: fidsList });
        if (res.code === 0) {
          message.success('操作成功');
          pageConatainerRef?.current?.fetchData?.();
        }
        return true;
      },
    });
  };

  // 存储表单数据
  const setFormData = () => {
    LStorage.set('transferDocument', {
      ...form.getFieldsValue(),
      start_time: form.getFieldValue('start_time'),
      end_time: form.getFieldValue('end_time'),
      store_ids: form.getFieldValue('store_ids'),
      out_store_ids: form.getFieldValue('out_store_ids'),
      basket_ids: form.getFieldValue('basket_ids'),
    });
  };
  // 初始化表单数据
  const getFormData = () => {
    const center = LStorage.get('userInfo').store.enable_delivery_center;
    const formData = LStorage.get('transferDocument');
    if (formData) {
      form.setFieldsValue({
        ...formData,
        start_time: moment(formData.start_time),
        end_time: moment(formData.end_time),
      });
    } else {
      form.setFieldsValue({
        time_desc: 0,
        time_type: 'create_date',
        compactDatePicker: [
          moment().format('YYYY-MM-DD'),
          moment().format('YYYY-MM-DD'),
        ],
      });
      setFormData();
    }
  };

  //导出
  const exportItem = async (e: any) => {
    const data = {
      ...prevPost(),
    };
    setisLoading(true);
    const res = await XlbFetch.post('/erp/hxl.erp.basketorder.export', data);
    if (res.code == 0) {
      wujieBus?.$emit('xlb_erp-event', {
        code: 'download',
        target: e,
      });
      message.success('导出受理成功，请前往下载中心查看');
    }
    setisLoading(false);
  };
  itemArr.map((v) => tableRender(v));
  useEffect(() => {
    getFormData();
  }, []);

  return (
    <>
      <XlbProPageModal
        ref={pageModalRef}
        Content={({ onClose }) => {
          return (
            <div>
              <Item
                onBack={(back: boolean) => {
                  if (back) {
                    pageConatainerRef?.current?.fetchData?.();
                  }
                  pageModalRef.current?.setOpen(false);
                }}
                record={record}
              ></Item>
            </div>
          );
        }}
      >
        <div className="xlb-table-clickBtn"></div>
      </XlbProPageModal>
      <XlbPageContainer
        ref={pageConatainerRef}
        url={'/erp/hxl.erp.basketorder.page'}
        tableColumn={itemArr.map((v) => {
          if (v.code === 'out_org_id' && !enable_organization) {
            return { ...v, hidden: true };
          }
          return v;
        })}
        immediatePost={false}
        prevPost={prevPost}
      >
        <ToolBtn showColumnsSetting>
          {({ fetchData, selectRow, dataSource }: ContextState<any>) => {
            return (
              <XlbButton.Group>
                {hasAuth(['物资进出单', '查询']) && (
                  <XlbButton
                    label="查询"
                    type="primary"
                    disabled={isLoading}
                    onClick={() => fetchData()}
                    icon={<XlbIcon name="sousuo" />}
                  ></XlbButton>
                )}
                {hasAuth(['物资进出单', '编辑']) && (
                  <XlbButton
                    label="新增"
                    disabled={isLoading}
                    type="primary"
                    onClick={() => {
                      setRecord({ fid: 1 });
                      pageModalRef.current?.setOpen(true);
                    }}
                    icon={<XlbIcon name="jia" />}
                  ></XlbButton>
                )}
                {hasAuth(['物资进出单', '导出']) && (
                  <XlbButton
                    label="导出"
                    disabled={isLoading || !dataSource?.length}
                    type="primary"
                    onClick={(e) => exportItem(e)}
                    icon={<XlbIcon name="daochu" />}
                  ></XlbButton>
                )}
                {hasAuth(['物资进出单', '删除']) && (
                  <XlbButton
                    label="删除"
                    disabled={!selectRow?.length}
                    onClick={() => deleteItem(selectRow)}
                    type="primary"
                    icon={<XlbIcon name="shanchu" />}
                  ></XlbButton>
                )}
                {hasAuth(['物资进出单', '编辑']) && (
                  <XlbButton
                    label="批量制单"
                    type='primary'
                    disabled={isLoading}
                    onClick={() => setBatchOrderVisible(true)}
                    icon={<XlbIcon name="piliang" />}
                  />
                )}
                {hasAuth(['物资进出单', '审核']) && (
                  <XlbButton
                    label="批量审核"
                    type='primary'
                    disabled={isLoading || !selectRow?.length}
                    // 批量审核
                    onClick={() => batchaudit(selectRow)}
                    icon={<XlbIcon name="shenhe" />}
                  />
                )}
              </XlbButton.Group>
            );
          }}
        </ToolBtn>
        <SearchForm>
          <XlbForm formList={formList} form={form} isHideDate={true} />
        </SearchForm>
        <Table key="fid" primaryKey="fid" selectMode="multiple" />
        <BatchOrderIndex open={batchOrderVisible} setOpen={setBatchOrderVisible} />
      </XlbPageContainer>
    </>
  );
};

export default TransferDocument;

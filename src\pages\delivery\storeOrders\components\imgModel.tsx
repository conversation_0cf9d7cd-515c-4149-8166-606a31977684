import { Fragment, useState, useEffect, useRef } from 'react'
import { Image } from 'antd'
import style from './imgModel.less'
import { XlbModal } from '@xlb/components'
import { imgModelArr } from './data'
import emptyPng from '@/assets/images/404_images/empty.png'
import { XlbIcon } from '@xlb/components'

import { RightOutlined, LeftOutlined } from '@ant-design/icons'
export default function ImgModel(props: any) {
  const { visible, onCancel, record } = props
  const [imgIndex, setIndex] = useState(0)
  const [marginLeft, setMarginLeft] = useState(0)
  const [imgUrl, setImgUrl] = useState('')
  const dom = useRef(null)
  const countUnit = ['采购单位', '库存单位', '配送单位', '批发单位']
  const imgs: any[] = record?.item_files || []
  const item_files = imgs.filter((v: { ref_type: string }) => v.ref_type === 'ITEM_PICTURE')
  // const item_files = [
  //   {
  //     url: 'https://icon.qiantucdn.com/static/images/logo/p-logo-web1.png'
  //   },
  //   {
  //     url: 'https://icon.qiantucdn.com/images/assets/2023-0764b6674b6a4ad_82.png'
  //   },
  //   {
  //     url: 'https://www.baidu.com/img/flexible/logo/pc/<EMAIL>'
  //   },
  //   {
  //     url: 'https://b.bdstatic.com/searchbox/icms/searchbox/img/cheng_boy.png'
  //   },
  //   {
  //     url: 'https://b.bdstatic.com/searchbox/icms/searchbox/img/cheng_girl.png'
  //   },
  //   {
  //     url: 'https://b.bdstatic.com/searchbox/icms/searchbox/img/ci_boy.png'
  //   },
  //   {
  //     url: 'https://b.bdstatic.com/searchbox/icms/searchbox/img/young_boy.png'
  //   },
  //   {
  //     url: 'https://b.bdstatic.com/searchbox/icms/searchbox/img/young_girl.png'
  //   },
  //   {
  //     url: 'https://ms.bdimg.com/pacific/0/pic/248255487_2010787051.jpg?x=0&y=0&h=266&w=399&vh=266.00&vw=399.00&oh=266.00&ow=399.00'
  //   }
  // ]
  https: useEffect(() => {
    setImgUrl(item_files.length ? item_files[0].url : '')
    setIndex(0)
  }, [visible])
  const changeIndex = (t: number) => {
    // const L = item_files?.length
    // t === 1 && setIndex(imgIndex + 1 >= L ? 0 : imgIndex + 1)
    // t === 2 && setIndex(imgIndex - 1 <= 0 ? L : imgIndex - 1)
  }

  const handleClickImg = (index: number) => {
    setIndex(index)
    setImgUrl(item_files[index].url)
  }
  const handleChange = (type = 'right') => {
    const w = 36 + 4 + 2 // width + margin + border
    if (type === 'right') {
      if (item_files.length - imgIndex > 5) {
        setMarginLeft((imgIndex + 1) * w * -1)
      }

      if (imgIndex < item_files.length - 1) {
        handleClickImg(imgIndex + 1)
      }
    } else {
      if (imgIndex > 0) {
        handleClickImg(imgIndex - 1)
        if (marginLeft < 0) {
          setMarginLeft(marginLeft + w)
        }
      }
    }
  }

  return (
    <XlbModal
      maskClosable={true}
      title={'商品信息'}
      visible={visible}
      width={800}
      style={{ height: '400px' }}
      footer={null}
      onCancel={() => {
        onCancel()
        setIndex(0)
      }}
    >
      <div className={style.imgModel_Box}>
        <div className={style.left_Box}>
          {imgUrl && (
            <>
              <Image src={imgUrl} width={200} height={240} />
              <div className={style.img_lunbo} name="轮播">
                <div>
                  <span
                    className={style.icon_img_action}
                    onClick={() => {
                      handleChange('left')
                    }}
                  >
                    <XlbIcon name="zuojiantou" size={18} />
                  </span>
                </div>
                <div className={style.img_list}>
                  <div ref={dom} style={{ display: 'flex', marginLeft: marginLeft + 'px' }}>
                    {item_files.map((e, index) => (
                      <Fragment key={e.url + index}>
                        <div className={style.img_item}>
                          <div
                            style={{
                              display: 'flex'
                            }}
                          >
                            <span
                              onClick={() => {
                                handleClickImg(index)
                              }}
                              style={{ borderColor: index === imgIndex ? '#80A5FF' : '#F2F3F5' }}
                              className={style.img_item_span}
                            >
                              <img
                                className={style.img_lunbo_item}
                                style={{ width: '30px', height: '30px' }}
                                src={e.url ?? ''}
                              />
                            </span>
                          </div>
                        </div>
                      </Fragment>
                    ))}
                  </div>
                </div>

                <div>
                  <span
                    className={style.icon_img_action}
                    onClick={() => {
                      handleChange('right')
                    }}
                  >
                    <XlbIcon name="youjiantou" size={18} />
                  </span>
                </div>
              </div>
            </>
          )}
          <div style={{ display: !imgUrl ? 'block' : 'none' }} className={style.null_Box}>
            <img src={emptyPng} alt="" />
          </div>
        </div>
        <div className={style.right_box}>
          {record.name && <div className={style.title}>{record.name}</div>}
          <div className={style.detail_content}>
            {record &&
              imgModelArr.map((v) => (
                <p className={style.text_label} key={v.label}>
                  <span className={style.text_label}>{v.label ? v.label + ':' : ''}</span>
                  <span className={style.text}>{record[v.value] || ''}</span>
                </p>
              ))}
          </div>
        </div>
      </div>
    </XlbModal>
  )
}

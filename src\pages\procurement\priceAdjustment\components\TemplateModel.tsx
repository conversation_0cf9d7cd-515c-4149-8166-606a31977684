import { LStorage } from '@/utils';
import {
  SearchFormType,
  XlbBasicForm,
  XlbDetailProps,
  XlbDetails,
  XlbForm,
  XlbInputNumber,
  XlbTable,
  XlbTableColumnProps,
  XlbTipsModal,
} from '@xlb/components';
import { toFixed } from '@xlb/utils';
import { Modal, message } from 'antd';
import { Fragment, useEffect, useState } from 'react';
import Api from '../server';
import styles from './TemplateModel.less';
const goodsType = [
  {
    label: '标准商品',
    value: 'STANDARD',
  },
  {
    label: '组合商品',
    value: 'COMBINATION',
  },
  {
    label: '成分商品',
    value: 'COMPONENT',
  },
  {
    label: '制单组合',
    value: 'MAKEBILL',
  },
  {
    label: '分级商品',
    value: 'GRADING',
  },
];
const TemplateModel = (props: any) => {
  const {
    visible,
    handleCancel,
    showDelivery,
    record,
    fid,
    effect_time,
    footer,
  } = props;

  const baseForm = [
    {
      label: '商品代码',
      name: 'item_code',
      style: {
        width: 296,
      },
    },
    {
      label: '商品名称',
      name: 'item_name',
      style: {
        width: 296,
      },
    },
    {
      label: '商品条码',
      name: 'item_bar_code',
      style: {
        width: 296,
      },
    },
    {
      label: '采购规格',
      name: 'item_spec',
      style: {
        width: 296,
      },
    },
    {
      label: '基本单位',
      name: 'basic_unit',
      style: {
        width: 296,
      },
    },
    {
      label: '商品类型',
      name: 'item_type',
      style: {
        width: 296,
      },
      render: (value: any) => {
        return (
          <span>{goodsType.find((item) => item.value === value)?.label}</span>
        );
      },
    },
    {
      label: '商品类别',
      name: 'item_category_name',
      style: {
        width: 296,
      },
    },
    {
      label: '现采购价',
      name: 'purchase_price',
      style: {
        width: 296,
      },
      render: (value: any) => {
        return (
          <span style={{ color: '#ff1128' }}>{(value ?? 0).toFixed(2)}</span>
        );
      },
    },
  ];
  const deliverySchema = [
    ...baseForm,
    {
      label: '现配送价',
      name: 'delivery_price',
      style: {
        width: 296,
      },
      render: (value: any) => {
        return <span>{value ?? '-'}</span>;
      },
    },
    {
      label: '配送毛利率',
      name: 'deliver_gross_profit_rate',
      style: {
        width: 296,
      },
      render: (value: any) => {
        return <span>{value ?? '-'}</span>;
      },
    },
  ];
  const saleSchema = [
    ...baseForm,
    {
      label: '现零售价',
      name: 'sale_price',
      style: {
        width: 296,
      },
      render: (value: any) => {
        return <span>{value ?? '-'}</span>;
      },
    },
    {
      label: '零售毛利率',
      name: 'sale_gross_profit_rate',
      style: {
        width: 296,
      },
      render: (value: any) => {
        return <span>{value ?? '-'}</span>;
      },
    },
  ];
  const schema: XlbDetailProps['schema'] = [
    {
      title: '',
      children: [
        {
          subTitle: '基本信息',
          formList: showDelivery ? deliverySchema : saleSchema,
        },
      ],
    },
  ];

  const schema1: XlbDetailProps['schema'] = [
    {
      title: '',
      children: [
        {
          subTitle: '时间信息',
        },
      ],
    },
  ];
  const schema2: XlbDetailProps['schema'] = [
    {
      title: '',
      children: [
        {
          subTitle: '价格信息',
        },
      ],
    },
  ];
  const formList: SearchFormType[] = [
    {
      type: 'datePicker',
      label: '生效时间',
      name: 'effect_time',
      disabled: true,
      width: 136,
      initialValue: effect_time,
    },
    // {
    //   type: "select",
    //   label: "单位",
    //   name: "delivery_unit",
    //   initialValue: 'DELIVERY',
    //   options: [
    //     { label: "配送单位", value: 'DELIVERY' },
    //     { label: "基本单位", value: 'BASIC' },
    //   ],
    //   width: 136,
    //   disabled: true,
    //   allowClear: false,
    // },
    {
      type: 'select',
      label: '失效后价格',
      name: 'invalid_type',
      initialValue: 0,
      options: [
        { label: '按调整前价格', value: 0 },
        { label: '按档案配送价', value: 1 },
      ],
      width: 136,
      allowClear: false,
    },
    {
      type: 'select',
      label: '配送价类型',
      name: 'delivery_type',
      initialValue: 'RATIO',
      options: [
        { label: '按比例', value: 'RATIO' },
        { label: '按金额', value: 'MONEY' },
        { label: '固定金额', value: 'FIXED_MONEY' },
      ],
      width: 136,
      allowClear: false,
    },
    {
      type: 'inputNumber',
      label: '数值',
      name: 'delivery_value',
      min: 0,
      step: 0.0001,
      width: 136,
    },
  ];
  const tableColumn: XlbTableColumnProps<any>[] = [
    {
      name: '中心最低价',
      code: 'guide_retail_price_min',
      width: 112,
      align: 'right',
      features: { sortable: true },
      lock: true,
      render(value: any) {
        return toFixed(value ?? 0, 'MONEY');
      },
    },
    {
      name: '原最低售价',
      code: 'org_store_retail_price_min',
      width: 112,
      align: 'right',
      features: { sortable: true },
      lock: true,
      render(value: any) {
        return toFixed(value ?? 0, 'MONEY');
      },
    },
    {
      name: '现最低售价',
      code: 'store_retail_price_min',
      width: 140,
      align: 'right',
      features: { sortable: true },
      lock: true,
      render: (_value: any, record: any) => (
        <XlbInputNumber
          key={record?.id}
          width={'100%'}
          min={0}
          max={999999999}
          precision={4}
          type="number"
          defaultValue={_value == '-' ? null : _value}
          onChange={(val) =>
            changeMaxMinPrice(val, record, 'store_retail_price_min')
          }
          suffix="元"
        />
      ),
    },
    {
      name: '中心最高价',
      code: 'guide_retail_price_max',
      width: 112,
      align: 'right',
      features: { sortable: true },
      lock: true,
      render(value: any) {
        return toFixed(value ?? 0, 'MONEY');
      },
    },
    {
      name: '原最高售价',
      code: 'guide_retail_price_max',
      width: 112,
      align: 'right',
      features: { sortable: true },
      lock: true,
      render(value: any) {
        return toFixed(value ?? 0, 'MONEY');
      },
    },
    {
      name: '现最高售价',
      code: 'store_retail_price_max',
      width: 140,
      align: 'right',
      features: { sortable: true },
      lock: true,
      render: (_value: any, record: any) => (
        <XlbInputNumber
          key={record?.id}
          width={'100%'}
          min={0}
          max={999999999}
          precision={4}
          type="number"
          defaultValue={_value == '-' ? null : _value}
          onChange={(val) =>
            changeMaxMinPrice(val, record, 'store_retail_price_max')
          }
          suffix="元"
        />
      ),
    },
  ];
  const tableColumn1: XlbTableColumnProps<any>[] = [
    {
      name: '序号',
      code: '_index',
      width: 70,
      features: { sortable: true },
      lock: true,
    },
    {
      name: '中心指导价',
      code: 'guide_retail_price',
      width: 112,
      align: 'right',
      features: { sortable: true },
      lock: true,
      render(value: any) {
        return toFixed(value ?? 0, 'MONEY');
      },
    },
    {
      name: '原门店零售价',
      code: 'org_store_retail_price',
      width: 132,
      align: 'right',
      features: { sortable: true },
      lock: true,
      render(value: any) {
        return toFixed(value ?? 0, 'MONEY');
      },
    },
    {
      name: '现门店零售价',
      code: 'store_retail_price',
      width: 160,
      align: 'right',
      features: { sortable: true },
      lock: true,
      render: (_value: any, record: any) => (
        <XlbInputNumber
          key={record?.id}
          width={'100%'}
          min={0}
          max={999999999}
          precision={4}
          type="number"
          defaultValue={_value == '-' ? null : _value}
          onChange={(val) => changePrice(val, record, 'store_retail_price')}
          suffix="元"
        />
      ),
    },
  ];

  const userInfo = LStorage.get('userInfo');
  const [data, setData] = useState<any>({});
  const [form] = XlbBasicForm.useForm();
  const [rowData, setRowData] = useState<any[]>([]);
  const [rowData1, setRowData1] = useState<any[]>([]);

  const handleOk = async () => {
    if (!footer) {
      handleCancel();
      return;
    }
    if (showDelivery) {
      const values = form.getFieldsValue();
      const params = {
        fid: fid,
        item_delivery_prices: [
          {
            ...record,
            ...values,
            item_code: record?.item_code,
            item_name: record?.item_name,
            item_id: record?.item_id,
            basic_unit: record?.basic_unit,
            purchase_price: record?.purchase_price,
          },
        ],
      };
      const res = await Api.setDeliveryPrice(params);
      if (res.code == 0) {
        message.success('操作成功');
        handleCancel();
      }
    } else {
      const values = form.getFieldsValue();
      const params = {
        fid,
        item_retail_prices: [
          {
            ...values,
            item_id: record?.item_id,
            item_code: record?.item_code,
            item_name: record?.item_name,
            guide_retail_price_min: rowData[0].guide_retail_price_min ?? 0,
            org_store_retail_price_min:
              rowData[0].org_store_retail_price_min ?? 0,
            store_retail_price_min: rowData[0].store_retail_price_min ?? 0,
            guide_retail_price_max: rowData[0].guide_retail_price_max ?? 0,
            org_store_retail_price_max:
              rowData[0].org_store_retail_price_max ?? 0,
            store_retail_price_max: rowData[0].store_retail_price_max ?? 0,
            guide_retail_price: rowData1[0].guide_retail_price ?? 0,
            guide_retail_price_s: rowData1[1].guide_retail_price ?? 0,
            guide_retail_price_t: rowData1[2].guide_retail_price ?? 0,
            guide_retail_price_f: rowData1[3].guide_retail_price ?? 0,
            org_store_retail_price: rowData1[0].org_store_retail_price ?? 0,
            org_store_retail_price_s: rowData1[1].org_store_retail_price ?? 0,
            org_store_retail_price_t: rowData1[2].org_store_retail_price ?? 0,
            org_store_retail_price_f: rowData1[3].org_store_retail_price ?? 0,
            store_retail_price: rowData1[0].store_retail_price ?? 0,
            store_retail_price_s: rowData1[1].store_retail_price ?? 0,
            store_retail_price_t: rowData1[2].store_retail_price ?? 0,
            store_retail_price_f: rowData1[3].store_retail_price ?? 0,
          },
        ],
      };
      const res = await Api.setRetailPrice(params);
      if (res.code == 0) {
        message.success('操作成功');
        handleCancel();
      }
    }
  };

  // 更改价格
  const changePrice = (val: any, item: any, key: string) => {
    if (Number(val) > 999999999) {
      XlbTipsModal({
        tips: '采购单价不能超过999999999',
      });
      const index = rowData1.findIndex((v: any) => v.id == item.id);
      rowData1[index][key] = rowData1[index][key];
      return;
    }
    const index = rowData1.findIndex((v: any) => v.id == item.id);
    rowData1[index][key] = Number(val);
  };

  const changeMaxMinPrice = (val: any, item: any, key: string) => {
    if (Number(val) > 999999999) {
      XlbTipsModal({
        tips: '采购单价不能超过999999999',
      });
      const index = rowData.findIndex((v: any) => v.id == item.id);
      rowData[index][key] = rowData[index][key];
      return;
    }
    const index = rowData.findIndex((v: any) => v.id == item.id);
    rowData[index][key] = Number(val);
  };

  const initDate = async () => {
    if (showDelivery) {
      form.setFieldsValue({
        ...record?.delivery_prices,
      });
    } else {
      if (!record?.retail_price) {
        const res = await Api.getRetailPrice({
          ids: [record?.item_id],
          store_id: userInfo?.store_id,
        });
        if (res.data.content && res.data.content.length) {
          const item = res.data.content[0];
          setRowData([
            {
              ...item,
              org_store_retail_price: item.org_store_retail_price,
              store_retail_price_min: item?.store_retail_price_min ?? '-',
              store_retail_price_max: item?.store_retail_price_max ?? '-',
            },
          ]);
          const li = [
            {
              id: item.id,
              guide_retail_price: item?.guide_retail_price,
              org_store_retail_price: item?.org_store_retail_price,
              store_retail_price: item?.store_retail_price ?? '-',
            },
            {
              id: item.id + 1,
              guide_retail_price: item?.guide_retail_price_s,
              org_store_retail_price: item?.org_store_retail_price_s,
              store_retail_price: item?.store_retail_price_s ?? '-',
            },
            {
              id: item.id + 2,
              guide_retail_price: item?.guide_retail_price_t,
              org_store_retail_price: item?.org_store_retail_price_t,
              store_retail_price: item?.store_retail_price_t ?? '-',
            },
            {
              id: item.id + 3,
              guide_retail_price: item?.guide_retail_price_f,
              org_store_retail_price: item?.org_store_retail_price_f,
              store_retail_price: item?.store_retail_price_f ?? '-',
            },
          ];
          setRowData1(li ?? []);
        }
      } else {
        setRowData([record?.retail_price]);
        const li = [
          {
            id: record?.item_id,
            guide_retail_price: record?.retail_price.guide_retail_price,
            org_store_retail_price: record?.retail_price.org_store_retail_price,
            store_retail_price: record?.retail_price.store_retail_price ?? '-',
          },
          {
            id: record?.item_id + 1,
            guide_retail_price: record?.retail_price.guide_retail_price_s,
            org_store_retail_price:
              record?.retail_price.org_store_retail_price_s,
            store_retail_price:
              record?.retail_price.store_retail_price_s ?? '-',
          },
          {
            id: record?.item_id + 2,
            guide_retail_price: record?.retail_price.guide_retail_price_t,
            org_store_retail_price:
              record?.retail_price.org_store_retail_price_t,
            store_retail_price:
              record?.retail_price.store_retail_price_t ?? '-',
          },
          {
            id: record?.item_id + 3,
            guide_retail_price: record?.retail_price.guide_retail_price_f,
            org_store_retail_price:
              record?.retail_price.org_store_retail_price_f,
            store_retail_price:
              record?.retail_price.store_retail_price_f ?? '-',
          },
        ];
        setRowData1(li);
      }
    }
  };

  useEffect(() => {
    initDate();
    console.log('此时的呼呼呼', showDelivery);
  }, [visible]);

  return (
    <div>
      <Modal
        title={showDelivery ? '配送价调整' : '零售调价'}
        visible={visible}
        maskClosable={true}
        onOk={handleOk}
        onCancel={handleCancel}
        cancelText={'关闭'}
        width={800}
        className={styles.modal}
        style={{ top: showDelivery ? 140 : 40 }}
      >
        <XlbDetails data={record} schema={schema} showBack={false} />
        {showDelivery ? (
          <Fragment>
            <XlbDetails data={data} schema={schema2} showBack={false} />
            <XlbForm
              isHideDate
              style={{ marginTop: 12 }}
              form={form}
              formList={formList}
            />
          </Fragment>
        ) : (
          <Fragment>
            <XlbDetails data={data} schema={schema1} showBack={false} />
            <XlbForm
              isHideDate
              style={{ marginTop: 12 }}
              form={form}
              formList={formList.slice(0, 2)}
            />
            <XlbDetails data={data} schema={schema2} showBack={false} />
            <XlbTable
              dataSource={rowData}
              columns={tableColumn}
              hideOnSinglePage
              style={{ height: 88 }}
            />
            <XlbTable
              dataSource={rowData1}
              columns={tableColumn1}
              hideOnSinglePage
              style={{ height: 200 }}
            />
          </Fragment>
        )}
      </Modal>
    </div>
  );
};

export default TemplateModel;

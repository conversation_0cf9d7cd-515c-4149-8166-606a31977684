import { useModal } from '@ebay/nice-modal-react';
import {
  SearchFormType,
  XlbBasicForm,
  XlbButton,
  XlbForm,
  XlbIcon,
  XlbModal,
  XlbPageContainer,
} from '@xlb/components';
import { useRef } from 'react';
interface IProps {
  fid: string;
}
const storeModal = (props: IProps) => {
  const { fid } = props;
  const [formModel] = XlbBasicForm.useForm<any>();
  const modal = useModal();
  const pageRef = useRef<any>(null);
  const storeFormList: SearchFormType[] = [
    {
      label: '关键字',
      name: 'keywords',
      type: 'input',
      width: 200,
      placeholder: '请输入',
    },
  ];
  return (
    <XlbModal
      width={800}
      open={modal.visible}
      title={'应用门店'}
      isCancel={false}
      keyboard={false}
      onOk={async () => {
        modal.hide();
      }}
      onCancel={() => {
        modal.hide();
      }}
    >
      <div style={{ height: 600, position: 'relative' }}>
        <XlbPageContainer
          url={'/erp/hxl.erp.campaign.store.page'}
          tableColumn={[
            {
              name: '序号',
              code: '_index',
              width: 80,
              align: 'center',
            },
            {
              name: '代码',
              code: 'store_code',
              width: 120,
            },
            {
              name: '名称',
              code: 'store_name',
              width: 120,
            },
            {
              name: '营业执照名称',
              code: 'license_name',
              width: 300,
            },
            {
              name: '执照类型',
              code: 'license_type',
              width: 100,
              render: (value) => {
                const typeOfLicense: any = {
                  COMPANY: '企业',
                  PERSONAL: '个体',
                };
                return (
                  <div className="info">
                    {value ? typeOfLicense[value] : ''}
                  </div>
                );
              },
            },
            {
              name: '门店分组',
              code: 'store_group_name',
              width: 120,
              render: (value: any, record: any) => {
                return <>{record?.store_group?.name}</>;
              },
            },
            {
              name: '配送类型',
              code: 'delivery_type',
              width: 120,
              render(text) {
                const obj: any = {
                  JOIN: '加盟',
                  DIRECT: '直营',
                };
                return obj[text];
              },
            },
            {
              name: '经营类型',
              code: 'management_type',
              width: 100,
              render: (text) =>
                text == '0' ? '直营' : text === '1' ? '加盟' : null,
            },
          ]}
          prevPost={() => {
            return {
              fid: fid,
              keywords: formModel?.getFieldValue('keywords'),
            };
          }}
          immediatePost={true}
        >
          <div
            style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'flex-start',
            }}
          >
            <XlbPageContainer.SearchForm>
              <XlbForm
                formList={storeFormList}
                form={formModel}
                isHideDate={true}
              />
            </XlbPageContainer.SearchForm>
            <XlbPageContainer.ToolBtnNoStyle>
              {(current) => {
                return (
                  <XlbButton
                    label="查询"
                    type="primary"
                    onClick={() => {
                      current.fetchData();
                    }}
                    // 定位
                    // style={{ position: 'absolute', left: 260, top: 1 }}
                    icon={<XlbIcon name="sousuo" />}
                  />
                );
              }}
            </XlbPageContainer.ToolBtnNoStyle>
          </div>
          <XlbPageContainer.Table
            key="id"
            selectMode="single"
            primaryKey="id"
          />
        </XlbPageContainer>
      </div>
    </XlbModal>
  );
};
export default storeModal;

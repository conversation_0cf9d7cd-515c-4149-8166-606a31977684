import { Modal, Button, Progress } from 'antd'

const ProgressModal = (props: any) => {
  const { progress, onCancel, ...rest } = props
  return (
    <Modal
      open={progress.open}
      centered
      keyboard={false}
      width={450}
      wrapClassName={'xlbDialog'}
      closable={false}
      maskClosable={false}
      zIndex={1000}
      footer={null}
    >
      <div style={{ padding: '8px' }} className="col-flex">
        {progress?.tips && <div> 正在操作：<span style={{ fontWeight: 'bold' }}>{progress.tips}</span></div>}
        <Progress percent={progress.percentNum} status="active" showInfo={false} size={'default'} />
        {progress?.tips && <span style={{ color: '#000', textAlign: 'center' }}>{Number(progress.percentNum).toFixed(2)} %</span>}
      </div>
    </Modal>
  )
}
export default ProgressModal
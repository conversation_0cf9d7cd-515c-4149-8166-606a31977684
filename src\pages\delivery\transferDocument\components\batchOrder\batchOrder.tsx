import { LStorage } from '@/utils/storage'
import {
  XlbBasicData,
  XlbBasicForm,
  XlbButton,
  XlbIcon,
  XlbImportModal,
  XlbInputNumber,
  XlbMessage,
  XlbModal,
  XlbProForm,
  XlbSelect,
  XlbTable,
  XlbTipsModal
} from '@xlb/components'
import { Checkbox, Form } from 'antd'
import Decimal from 'decimal.js'
import { cloneDeep } from 'lodash'
import { useEffect, useRef, useState } from 'react'
import { batchOrderSave } from '../../server'
import styles from './batchOrder.less'
import { itemTableList } from './data'
import { ErpFieldKeyMap } from '@/data/common/fieldListConfig'
import { Ls } from 'dayjs'
import { XlbFetch } from '@xlb/utils'

const BatchOrderIndex = (props: any) => {
  const formRef = useRef<any>(null)
  const { open, setO<PERSON> } = props
  const [formUpdate] = Form.useForm()
  const [rowData, setRowData] = useState<any[]>([])
  const [isLoading, setIsLoading] = useState<boolean>(false)
  const [isEdit, setIsEdit] = useState<boolean>(false)
  const [itemArr, setItemArr] = useState<any>(JSON.parse(JSON.stringify(itemTableList)))
  const [goodList, setGoodList] = useState<any>([]) //商品数组
  const [selectedStoreList, setSelectedStoreList] = useState<string[]>([]) //已选择门店
  const [selectedGoodList, setSelectedGoodList] = useState<number[]>([]) //已选择商品
  const [inventoryReady, setInventoryReady] = useState<boolean>(false)
  const [chooseGoodsList, setChooseGoodsList] = useState<any>([]) //选择的商品列表
  const modalRef = useRef(null)
  const userInfo = LStorage.get('userInfo')
  const [basicForm] = XlbBasicForm.useForm()
  const getMult = (num1: number, num2: number, precision: number = 4) => {
    return new Decimal(num1 || 0)
      .mul(new Decimal(num2 || 0))
      .toNumber()
      ?.toFixed(precision)
  }

  //操作删除行
  const showDeleteModal = (record: any) => {
    // 删除选中的门店
    const updatedRowData = rowData.filter((store: any) => store.out_store_id !== (record.id || record.out_store_id))

    // 若门店删光，清空商品相关数据，仅保留 itemArr 的第一个元素
    if (updatedRowData.length === 0) {
      setGoodList([])
      setItemArr([itemArr[0]]) // ✅ 只保留第一个元素
    }
    // updatedRowData 中不存在的门店，从 selectedStoreList 中删除
    setSelectedStoreList((prev) => prev.filter((store: any) => record.out_store_id != store))
    setRowData(updatedRowData)
  }

  const storeStrongRender = (item: any) => {
    if (item.children && item.children.length) {
      item.children.map((v: any) => {
        switch (v?.code) {
          case 'index':
            v.render = (value: any, record: any, index: number) => {
              return <div>{index + 1}</div>
            }
            break
          case 'action':
            v.render = (value: any, record: any, index: number) => {
              return (
                <div>
                  <XlbButton
                    icon={<XlbIcon name="shanchu" />}
                    type="text"
                    onClick={(e) => {
                      e.stopPropagation()
                      showDeleteModal(record)
                    }}
                  />
                </div>
              )
            }
            break
        }
      })
    }
  }

  const onChange = (e: any, list: any, i: number) => {
    const key = Number(e.target.value)

    // 获取modal中的table
    const checkBoxs = modalRef?.current
      ?.querySelector('thead')
      .querySelector('.art-table-header-row')!
      .getElementsByClassName('art-table-header-cell')

    if (key === 9999 || key === 1111) {
      for (let k = 0; k < checkBoxs.length - 1; k++) {
        k > 0 && (checkBoxs[k].querySelector('input[type="checkbox"]')!.checked = e.target.checked)
      }
      setSelectedGoodList(key === 9999 ? goodList.map((v) => v.id) : [])
    } else {
      setSelectedGoodList((prevList) => {
        const index = prevList.indexOf(key)
        if (index === -1) {
          return [...prevList, key]
        } else {
          const newList = [...prevList]
          newList.splice(index, 1)
          return newList
        }
      })
      setChooseGoodsList(list)
    }
  }
  const inputChange = (
    e: number | string | null,
    index: number,
    renderv: any,
    record: any,
    type: string
  ) => {
    setRowData((prev) => {
      // 深拷贝防止直接修改旧数据
      const list = cloneDeep(prev)
      if (type === 'quantity') {
        // 确保 record 和 renderv 都有效
        if (!record || !renderv || !record.detail) return list

        // 找到当前修改项的载具
        const curItem = record.detail.find((v) => v.basket_id === renderv)
        if (!curItem) return list // 如果没找到对应的 item，直接返回原数据

        // 获取当前修改项的最大库存
        const InventoryQuantity =
          curItem?.store_stock_map?.[record?.id || record?.store_id || record?.out_store_id]
        if (InventoryQuantity === undefined) {
          // 如果找不到最大库存，显示错误提示
          XlbTipsModal({ tips: '未找到当前商品的最大库存' })
          return list
        }
        // 判断输入的数量是否超过最大库存
        if (e > InventoryQuantity) {
          // 提示并重置数量
          XlbTipsModal({
            tips: '当前设置库存大于当前载具最大库存，请重新设置'
          })
          curItem.quantity = 0 // 重置当前项数量
          list[index][`${renderv}_initial_quantity`] = 0
          return list
        }
        // 更新对应的 quantity
        curItem.quantity = e
        // 更新 list 中的数量
        list[index][`${renderv}_initial_quantity`] = e
      }

      // 返回更新后的数据
      return list
    })
  }
  const handleKeyDown = (e: any) => {
    if (e.key === 'Enter') {
      e.preventDefault()
    }
  }
  const handleImportData = async (data) => {
    const newData = data?.content?.map((v) => {
      return {
        ...v,
        detail: v.details
      }
    })
    newData?.forEach((storeData) => {
      const { out_store_id, out_store_name, details } = storeData // 提取门店信息和载具详情
      // 更新 rowData（门店数据）
      setRowData((prevRows) => {
        const updatedRows = [...prevRows] // 克隆当前的 rowData

        // 查找门店是否已经存在
        const existingStoreIndex = prevRows.findIndex((store) => store.id === out_store_id)

        if (existingStoreIndex >= 0) {
          // 如果门店已存在，更新其 detail 和其它信息
          const updatedStore = { ...updatedRows[existingStoreIndex] }
          updatedStore.store_name = out_store_name

          // 遍历导入的载具详情，更新载具数量（覆盖）
          details.forEach((detail) => {
            const { basket_id, basket_name, quantity } = detail

            // 查找该载具是否已经在门店的 detail 中
            const existingDetailIndex = updatedStore.detail.findIndex(
              (d) => d.basket_id === basket_id
            )

            if (existingDetailIndex >= 0) {
              // 如果该载具已存在，覆盖数量
              updatedStore.detail[existingDetailIndex].quantity = quantity || 0 // 确保有值
            } else {
              // 如果该载具不存在，新增该载具到门店的 detail 中
              updatedStore.detail.push({
                basket_id,
                basket_name,
                quantity: quantity || 0
              })
            }
          })

          // 更新门店数据
          updatedRows[existingStoreIndex] = updatedStore
        } else {
          // 如果门店不存在，则新增门店
          const newStore = {
            out_store_id: out_store_id,
            store_name: out_store_name,
            detail: details.map((detail) => {
              return {
                ...detail,
                basket_id: detail.basket_id,
                basket_name: detail.basket_name,
                quantity: detail.quantity || 0
              }
            })
          }

          updatedRows.push(newStore)
        }

        return updatedRows // 更新 rowData
      })

      // 更新 itemArr（载具数据）
      setItemArr((prevItems) => {
        const updatedItemArr = [...prevItems]

        details.forEach((detail) => {
          const { basket_id, basket_name, price, quantity } = detail

          // 查找载具是否已存在 itemArr 中
          const existingItemIndex = updatedItemArr.findIndex((item) => item.code === basket_id)

          if (existingItemIndex < 0) {
            // 如果载具不存在，则新增载具列
            const newItem = {
              title: (
                <span className={styles.checkText}>
                  <input
                    className={styles.diyCheckBox}
                    type="checkbox"
                    key={basket_id}
                    value={basket_id}
                    onChange={(e) => onChange(e, details, basket_id)}
                  />
                  {basket_name}
                </span>
              ),
              code: basket_id,
              id: basket_id,
              width: 300,
              align: 'center',
              children: [
                {
                  title: `单价:${price || '0.00'}/件`,
                  code: basket_id,
                  width: 160,
                  align: 'center',
                  children: [
                    {
                      title: '门店确认数量',
                      code: `${basket_id}_initial_quantity`,
                      align: 'center',
                      render: (value, record, index) => {
                        return record._edit ? (
                          <XlbInputNumber
                            onKeyDown={handleKeyDown}
                            min={0}
                            precision={4}
                            formatter={(value) => {
                              return `${value}`.replace(/[^0-9.]/g, '')
                            }}
                            value={value}
                            parser={(value) => {
                              return value ? value.replace(/[^0-9.]/g, '') : ''
                            }}
                            onClick={(e) => {
                              e.stopPropagation()
                            }}
                            onChange={(e) => inputChange(e, index, basket_id, record, 'quantity')}
                          />
                        ) : (
                          <div style={{ textAlign: 'right' }}>
                            {value ||
                              record?.detail?.find((d) => d.basket_id === basket_id)?.quantity ||
                              0}
                          </div>
                        )
                      }
                    }
                  ]
                }
              ],
              obj: detail
            }

            updatedItemArr.push(newItem)

          } else {
            // 如果载具已存在，覆盖数量
            updatedItemArr[existingItemIndex].quantity = quantity || 0
          }
        })

        return updatedItemArr // 更新 itemArr
      })
    })
  }

  const handleAddClick = async () => {
    const list = await XlbBasicData({
      isMultiple: true,
      type: 'basketPreparation',
      url: '/erp/hxl.erp.basketorder.stock.find',
      dataType: 'lists',
      primaryKey: 'basket_id',
      isLeftColumn: false,
      fieldNames: {
        idKey: 'basket_id',
        nameKey: 'basket_name'
      },
      data: {
        enabled: true,
        in_store_id: formRef?.current?.getFieldValue('store_id') || '',
        store_ids: rowData?.map((v) => v.id || v.out_store_id || v.store_id)
      }
    })

    if (list) {
      const existingItemIds = new Set(itemArr.slice(1).map((item: any) => item.basket_id))
      const newItemArr = [itemArr[0], ...itemArr.slice(1)] // 先保留头部和旧商品

      // 追加新的商品列结构
      list.forEach((item: any) => {
        if (!existingItemIds.has(item.basket_id)) {
          const newCol = {
            title: (
              <span className={styles.checkText}>
                <input
                  className={styles.diyCheckBox}
                  type="checkbox"
                  key={item.basket_id}
                  value={item.basket_id}
                  onChange={(e: any) => onChange(e, list, item.basket_id)}
                />
                {item.basket_name}
              </span>
            ),
            code: item.basket_id,
            id: item.basket_id,
            width: 300,
            align: 'center',
            children: [
              {
                title: `单价:${item.price || '0.00'}/件`,
                code: item.basket_id,
                width: 160,
                align: 'center',
                children: [
                  {
                    title: '门店确认数量',
                    code: `${item.basket_id}_initial_quantity`,
                    align: 'center',
                    render: (value: any, record: any, index: any) => {
                      return record._edit ? (
                        <XlbInputNumber
                          onKeyDown={handleKeyDown}
                          min={0}
                          precision={4}
                          formatter={(value) => {
                            return `${value}`.replace(/[^0-9.]/g, '')
                          }}
                          value={value}
                          parser={(value) => {
                            return value ? value.replace(/[^0-9.]/g, '') : ''
                          }}
                          onChange={(e) =>
                            inputChange(e, index, item.basket_id, record, 'quantity')
                          }
                          onClick={(e) => {
                            e.stopPropagation()
                          }}
                        />
                      ) : (
                        <div style={{ textAlign: 'right' }}>{value}</div>
                      )
                    }
                  }
                ]
              }
            ],
            obj: item
          }

          newItemArr.push(newCol)
        }
      })

      setItemArr([...newItemArr])
      setGoodList(JSON.parse(JSON.stringify(newItemArr.slice(1))))

      // 更新每个门店中的 detail 和 数量字段
      setRowData((prevRows) => {
        return prevRows.map((store) => {
          const updatedDetail = [...(store.detail || [])]
          const detailIds = new Set(updatedDetail.map((d) => d.basket_id || d.id))

          list.forEach((item: any) => {
            const keyQty = `${item.id}_initial_quantity`

            // 设置商品数量，如果没设置过才赋值
            store[keyQty] = store[keyQty] ?? item.quantity ?? 0

            // 如果这个商品不在门店的 detail 中，才添加进去
            if (!detailIds.has(item.id)) {
              updatedDetail.push({
                ...item,
                basket_id: item.basket_id,
                basket_name: item.basket_name
              })
            }
          })

          return {
            ...store,
            detail: updatedDetail,
            _click: false,
            _edit: false
          }
        })
      })
    }
  }
  const handleBatchStore = async () => {
    const list = await XlbBasicData({
      isMultiple: true,
      url: '/erp-mdm/hxl.erp.store.short.page',
      primaryKey: 'id',
      type: 'store',
      dataType: 'lists',
      data: {
        status: true,
        center_flag: false
      },
      fieldNames: {
        idKey: 'id',
        nameKey: 'name'
      }
    })
    if (list) {
      const addedStoreMap: Record<string, any> = {}
      // 将已有 rowData 中的门店先填入 map
      rowData.forEach((store) => {
        addedStoreMap[store.store_id] = { ...store }
      })

      // 遍历新选中的门店，追加到 map 中
      list.forEach((v: any) => {
        const storeId = v.id
        if (!addedStoreMap[storeId]) {
          addedStoreMap[storeId] = {
            ...v,
            out_store_id: v.id,
            out_store_name: v.store_name,
            detail: [],
            _click: false,
            _edit: false
          }
        }
      })

      // 如果先选了商品，给新旧门店赋初始数量，并同步 detail
      if (itemArr.length > 1) {
        Object.values(addedStoreMap).forEach((store: any) => {
          const updatedDetail = [...(store.detail || [])]
          const detailIds = new Set(updatedDetail.map((d) => d.basket_id))

          itemArr.forEach((item: any) => {
            const keyQty = `${item.basket_id}_initial_quantity`
            // 设置数量
            store[keyQty] = store[keyQty] ?? item?.obj?.initial_quantity

            // 如果这个商品不在门店 detail 中，加入进去
            if (!detailIds.has(item.basket_id)) {
              updatedDetail.push({
                ...item.obj,
                basket_id: item.basket_id,
                basket_name: item.obj?.name || item.name
              })
            }
          })

          store.detail = updatedDetail
        })
      }

      // 过滤掉临时行 newRow 的项
      const finalStoreList = Object.values(addedStoreMap).filter((item: any) => !item.newRow)

      // 设置最终的 rowData（保留旧的 + 新增的，去重后的）
      setRowData([...finalStoreList])
    }
  }

  // 删除
  const deleteGoods = () => {
    if (selectedGoodList.length === 0 && selectedStoreList.length === 0) {
      return XlbMessage.error('请先选择要删除的门店或商品！')
    }
    if (selectedStoreList.length > 0) {
      setRowData((prevRowData) => {
        const newRowData = prevRowData.filter((item, index) => {
          // 判断 selectedStoreList 中每个元素的类型（通过 id 或 index）
          return !selectedStoreList.some((selectedItem) => {
            if (typeof selectedItem === 'string') {
              // 如果是导入的门店，通过 index 删除
              return index.toString() === selectedItem // index 转换为字符串来匹配
            } else if (typeof selectedItem === 'number') {
              // 如果是手动新增的门店，通过 id 删除
              return item.id === selectedItem
            }
            return false // 如果都不是，默认不删除
          })
        })

        // 如果删除后没有门店，清空品项和商品表头
        if (!newRowData.length) {
          setGoodList([])
          setItemArr([itemArr[0]])
        }

        return newRowData
      })

      // 清空选中的门店列表
      setSelectedStoreList([])
    }

    // 处理商品删除
    if (selectedGoodList.length > 0) {
      // 处理 itemArr
      const newItemArr = itemArr.filter(
        (item: any) => !selectedGoodList.includes(item.id || item.out_store_id)
      )

      setItemArr(newItemArr)
      setGoodList(newItemArr.slice(1))

      // 处理 rowData
      setRowData((prevRowData) => {
        return prevRowData.map((row) => {
          const newRow = { ...row }

          // 删除与选中商品相关的字段
          selectedGoodList.forEach((id) => {
            delete newRow[`${id}_initial_quantity`]
          })

          // 从 detail 中移除对应商品
          if (Array.isArray(newRow.detail)) {
            newRow.detail = newRow.detail.filter((item: any) => !selectedGoodList.includes(item.id))
          }

          return newRow
        })
      })

      // 清空选择列表
      setSelectedGoodList([])
    }
  }
  // 取消
  const onCancel = () => {
    formRef?.current?.resetFields()
    formUpdate.resetFields()
    setGoodList([])
    setSelectedGoodList([])
    setRowData([])
    setItemArr(JSON.parse(JSON.stringify(itemTableList)))
    setOpen(false)
    setIsEdit(false)
  }

  // 确定
  const onConfirm = async () => {
    if (!rowData?.length) {
      // 保存前校验
      XlbMessage.error(`数据不能为空`)
      return
    }
    // 数量修改丢了
    const newRowData = rowData.map((item) => {
      // 对每个 item 的 detail 数组进行处理
      const updatedDetail = item.detail.map((v) => {
        if (!item[`${v.basket_id}_initial_quantity`]) {
          return v
        }
        if (v.quantity !== item[`${v.basket_id}_initial_quantity`]) {
          return {
            ...v, // 保留原有属性
            quantity: item[`${v.basket_id}_initial_quantity`] // 更新 quantity
          }
        }
        return v // 如果 quantity 没有变化，保持原有 item
      })

      // 返回包含更新后的 detail 的新的 item 对象
      return {
        ...item, // 保留 item 中的其他字段
        detail: updatedDetail, // 更新后的 detail
        details: updatedDetail,
        flag: true // 发货
      }
    })

    const data = {
      store_id: formRef?.current?.getFieldValue('store_id'),
      memo: formRef?.current?.getFieldValue('memo'),
      flag: true,
      content: newRowData
    }
    // setIsLoading(true)
    const res = await XlbFetch.post('/erp/hxl.erp.basketorder.batchsave', data)
    setIsLoading(false)
    if (res.code === 0) {
      if (!res?.data?.error_messages?.length) {
        XlbMessage.success(`批量制单成功，已生成${res?.data?.success_num}张单据`)
      } else {
        XlbTipsModal({
          tips: `${res.data.error_messages.join('、')}`
        })
        return false
      }
      onCancel()
    }
  }

  if (rowData?.length) {
    itemArr.map((v: any) => storeStrongRender(v))
  }
  itemArr[0].title = (
    <Checkbox
      disabled={
        !goodList?.length
        // goodList.filter((v) => v.obj.generate_replenishment_order_state).length === goodList.length
      }
      value={selectedGoodList.length === goodList.length ? 1111 : 9999}
      checked={goodList.length && selectedGoodList.length === goodList.length}
      onChange={(e: any) => onChange(e, goodList, null)}
    >
      载具名称
    </Checkbox>
  )

  return (
    <XlbModal
      open={open}
      centered
      // @ts-ignore
      width={'90%'}
      isCancel={true}
      className={styles.dragModel}
      wrapClassName="xlbDialog"
      title={'物资进出单批量制单'}
      confirmLoading={isLoading}
      onCancel={() => onCancel()}
      onOk={() => onConfirm()}
    >
      <div ref={modalRef} style={{ marginTop: '12px' }} className={styles.formWrapCus}>
        <XlbProForm
          initialValues={{
            flag: true
          }}
          formList={[
            {
              id: ErpFieldKeyMap?.transferDocumentBatchOrder,
              label: '门店',
              onChange: (e: any) => {
                setIsEdit(!!e)
              },
              fieldProps: {
                style: {
                  width: 265
                }
              }
            },
            {
              id: 'commonSelect',
              label: '进出方向',
              name: 'flag',
              disabled: true,
              fieldProps: {
                width: 180,
                options: [
                  {
                    label: '进货',
                    value: false
                  },
                  {
                    label: '退货',
                    value: true
                  }
                ]
              }
            }
          ]}
          ref={formRef}
        />
        <div style={{ marginLeft: 16 }}>
          <XlbButton.Group>
            <XlbButton
              label="批量添加发货门店"
              onClick={() => handleBatchStore()}
              type="primary"
              disabled={!isEdit || rowData?.some((i) => i?.detail?.length > 0)}
              icon={<XlbIcon name="jia" />}
            />
            <XlbButton
              label="添加载具"
              disabled={!rowData?.length}
              type="primary"
              onClick={() => handleAddClick()}
              icon={<XlbIcon name="jia" />}
            />
            <XlbButton
              label="批量设置"
              // disabled={!rowData?.length}
              type="primary"
              disabled={!selectedGoodList.length}
              onClick={async () => {
                // 找到所有 store_stock_map 中的最小库存
                const minStock = rowData
                  .map((store) => {
                    // 遍历每个 store 的 detail 数组
                    return store.detail.map((item) => {
                      // 获取每个 item 中 store_stock_map 的所有库存值
                      return Math.min(...Object.values(item.store_stock_map))
                    })
                  })
                  .flat()
                const overallMinStock = Math.min(...minStock)
                const bool = await XlbTipsModal({
                  title: '批量设置',
                  width: 320,
                  tips: (
                    <>
                      <XlbBasicForm form={basicForm}>
                        <XlbBasicForm.Item name="quantity" label="数量">
                          <XlbInputNumber step={1} precision={0}></XlbInputNumber>
                        </XlbBasicForm.Item>
                      </XlbBasicForm>
                      <div style={{ marginTop: 10 }}>
                        当前载具所能设置最大库存为：{overallMinStock}
                      </div>
                    </>
                  ),
                  onOkBeforeFunction: async () => {
                    const values = basicForm.getFieldsValue()
                    const { quantity } = values
                    if (quantity == void 0 || quantity <= 0) {
                      XlbMessage.error('数量需大于0')
                      return false
                    }
                    if (quantity > overallMinStock) {
                      XlbMessage.error('超出可设置的最大库存数量')
                      return false
                    }
                    setRowData((prevRowData) => {
                      return prevRowData.map((row) => {
                        const updatedRow = { ...row }
                        // 更新 row 的每个被选中的商品字段
                        selectedGoodList.forEach((goodId) => {
                          updatedRow[`${goodId}_initial_quantity`] = quantity
                        })
                        updatedRow.detail = row.detail.map((item) => {
                          if (selectedGoodList.includes(item.id)) {
                            return {
                              ...item,
                              quantity: quantity
                            }
                          }
                          return item
                        })

                        return updatedRow
                      })
                    })
                    basicForm.setFieldsValue({
                      quantity: void 0
                    })
                    return true
                  }
                })
              }}
              icon={<XlbIcon name="jia" />}
            />
            <XlbButton
              label="导入"
              type="primary"
              disabled={!isEdit || rowData?.some((i) => i?.detail?.length > 0)}
              onClick={async () => {
                const res = await XlbImportModal({
                  importUrl: `${process.env.BASE_URL}/erp/hxl.erp.basketorder.import`,
                  templateUrl: `${process.env.BASE_URL}/erp/hxl.erp.basketorder.template.download`,
                  templateName: '物资进出单导入模板',
                  params: {
                    in_store_id: formRef?.current?.getFieldValue('store_id') || ''
                  },
                  callback: (res: any) => {
                    if (res.code !== 0) return
                    handleImportData(res.data)
                  }
                })
              }}
              icon={<XlbIcon name="daoru" />}
            />
            <XlbButton
              label="删除"
              disabled={selectedGoodList.length === 0 && selectedStoreList.length === 0}
              onClick={() => deleteGoods()}
              type="primary"
              icon={<XlbIcon name="shanchu" />}
            />
          </XlbButton.Group>
          {/* table */}
          <div className={rowData.length ? styles.table_box : ''} style={{ marginTop: 8 }}>
            <XlbTable
              dataSource={rowData}
              columns={itemArr}
              rowKey="store_id"
              key={rowData.length + '——' + goodList.length}
              isLoading={isLoading}
              emptyCellHeight={document.body.clientHeight - 335}
              style={{ height: 'calc(100vh - 335px - 120px)' }}
              hideOnSinglePage={true}
              selectMode="multiple"
              selectedRowKeys={selectedStoreList}
              onSelectRow={(value) => {
                setSelectedStoreList(value)
              }}
            />
          </div>
        </div>
      </div>
    </XlbModal>
  )
}

export default BatchOrderIndex
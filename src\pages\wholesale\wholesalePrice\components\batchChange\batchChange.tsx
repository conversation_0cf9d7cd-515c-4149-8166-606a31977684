import { useBaseParams } from '@/hooks/useBaseParams';
import useDrag from '@/hooks/useDrag';
import { hasAuth } from '@/utils';
import { LStorage } from '@/utils/storage';
import { useModal } from '@ebay/nice-modal-react';
import {
  XlbBasicForm,
  XlbButton,
  XlbImportModal,
  XlbInputDialog,
  XlbInputNumber,
  XlbModal,
  XlbRadio,
  XlbSelect,
  XlbTipsModal,
} from '@xlb/components';
import { XlbFetch } from '@xlb/utils';
import { message } from 'antd';
import { useEffect, useState } from 'react';
import { batchUpdateWholeSalePrice } from '../../server';
import style from './batchChange.less';
const BatchChange = (props: any) => {
  const { fetchData, currentTab } = props;
  const [form] = XlbBasicForm.useForm();
  const modal = useModal();
  const [loading, setloading] = useState<boolean>(false);
  const { enable_organization } = useBaseParams((state) => state);
  const [type, setType] = useState<string>('');
  const [type2, setType2] = useState<string>('');
  const [type3, setType3] = useState<string>('');
  const [type4, setType4] = useState<string>('');
  const [orgList, setOrgList] = useState<any[]>([]);
  const [isFold, setIsFold] = useState<boolean>(false);

  const handleOk = async () => {
    if (!form.getFieldValue('revise_store_names')) {
      XlbTipsModal({
        tips: `请先选择门店`,
        isConfirm: true,
        isCancel: false,
      });
      return;
    }
    if (
      currentTab === 'CustumerPrice' &&
      !form.getFieldValue('revise_cus_names')
    ) {
      XlbTipsModal({
        tips: `请先选择批发客户`,
        isConfirm: true,
        isCancel: false,
      });
      return;
    }
    const radiovalue = form.getFieldValue('radioValue');
    if (!radiovalue) {
      XlbTipsModal({
        tips: `请先选择商品`,
        isConfirm: true,
        isCancel: false,
      });
      return;
    } else {
      if (
        form.getFieldValue('radioValue') == 2 &&
        !form.getFieldValue('item_category_names')
      ) {
        XlbTipsModal({
          tips: `请先选择商品类别`,
          isConfirm: true,
          isCancel: false,
        });
        return;
      }
      if (
        form.getFieldValue('radioValue') == 3 &&
        !form.getFieldValue('item_names')
      ) {
        XlbTipsModal({
          tips: `请先选择商品档案`,
          isConfirm: true,
          isCancel: false,
        });
        return;
      }
    }

    const data = {
      client_ids:
        currentTab === 'StorePrice' ? [] : form.getFieldValue('revise_cus_ids'),
      unit_type: form.getFieldValue('unit_type'),
      type: form.getFieldValue('type') ? form.getFieldValue('type') : undefined,
      type2: form.getFieldValue('type2') ? form.getFieldValue('type2') : undefined,
      type3: form.getFieldValue('type3') ? form.getFieldValue('type3') : undefined,
      type4: form.getFieldValue('type4') ? form.getFieldValue('type4') : undefined,
      value: form.getFieldValue('value'),
      value2: form.getFieldValue('value2'),
      value3: form.getFieldValue('value3'),
      value4: form.getFieldValue('value4'),
      store_ids: form.getFieldValue('revise_store_ids'),
      item_ids:
        form.getFieldValue('radioValue') === 3 &&
        form.getFieldValue('item_names')
          ? form.getFieldValue('item_ids')
          : undefined,
      item_category_ids:
        form.getFieldValue('radioValue') === 2 &&
        form.getFieldValue('item_category_names')
          ? form.getFieldValue('item_category_ids')
          : undefined,
      org_id: enable_organization ? form.getFieldValue('org_id') : undefined,
    };
    setloading(true);
    const res = await batchUpdateWholeSalePrice(data);
    setloading(false);
    if (res?.code === 0) {
      message.success('更新成功');
      modal.resolve(false);
      modal.hide();
      fetchData();
      form.setFieldsValue({
        radioValue: '',
      });
      form.resetFields();
      setIsFold(false);
    }
  };

  // 导入门店
  const importStores = async () => {
    const res = await XlbImportModal({
      importUrl: `${process.env.ERP_URL}/erp-mdm/hxl.erp.storename.import`,
      templateUrl: `${process.env.ERP_URL}/erp-mdm/hxl.erp.storecodetemplate.download`,
      templateName: '门店导入模板',
      callback: (res: any) => {
        if (res.code !== 0) return;
        form.setFieldsValue({
          revise_store_ids: res?.data?.store_ids ? res?.data?.store_ids : [],
          revise_store_names: res?.data?.store_names_str
            ? res?.data?.store_names_str
            : '',
        });
      },
    });
  };
  // 导入商品
  const importShort = async () => {
    const res = await XlbImportModal({
      importUrl: `${process.env.ERP_URL}/erp/hxl.erp.items.batchimport`,
      templateUrl: `${process.env.ERP_URL}/erp/hxl.erp.item.shorttemplate.download`,
      templateName: '商品导入模板',
      callback: (res: any) => {
        if (res.code !== 0) return;
        form.setFieldsValue({
          item_ids: res?.data?.items?.map((v: any) => v.id),
          item_names: res?.data?.items?.map((v: any) => v.name)?.join(','),
        });
      },
    });
  };
  useEffect(() => {
    getOrgList();
  }, [enable_organization]);
  const getOrgList = async () => {
    if (enable_organization) {
      const res = await XlbFetch.post('/erp-mdm/hxl.erp.org.find',{});
      if (res?.code == 0) {
        const org_list = res.data.map((i: any) => ({
          value: i.id,
          label: i.name,
        }));
        setOrgList([...org_list]);
      }
    }
  };
  useEffect(() => {
    form.setFieldsValue({
      revise_store_names:
        LStorage.get('userInfo')?.query_stores &&
        LStorage.get('userInfo')?.query_stores.length === 1
          ? LStorage.get('userInfo')?.query_stores[0]?.store_name
          : undefined,
      revise_store_ids:
        LStorage.get('userInfo')?.query_stores &&
        LStorage.get('userInfo')?.query_stores.length === 1
          ? [LStorage.get('userInfo')?.query_stores[0]?.id]
          : undefined,
    });
    setType('');
    setType2('');
    setType3('');
    setType4('');
  }, []);
  const Valuechange = (key: any, value: any) => {
    form.setFieldValue(key, value);
  };
  const Valuechanges = (key: any, value: any) => {
    console.log('🚀 ~ ValueChanges ~ key:', key);

    const typeMap: Record<
      string,
      { valueKey: string; setter: (val: any) => void }
    > = {
      type: { valueKey: 'value', setter: setType },
      type2: { valueKey: 'value2', setter: setType2 },
      type3: { valueKey: 'value3', setter: setType3 },
      type4: { valueKey: 'value4', setter: setType4 },
    };

    if (typeMap[key]) {
      setIsFold(value === 'RATIO');
      form.setFieldValue(typeMap[key].valueKey, 0);
      typeMap[key].setter(value);
    }

    form.setFieldValue(key, value);
  };

  useDrag('.ant-modal-header', '.ant-modal-content');

  return (
    <XlbModal
      title={'批量修改'}
      centered
      open={modal.visible}
      maskClosable={false}
      onOk={handleOk}
      keyboard={false}
      isCancel={true}
      onCancel={() => {
        form.resetFields();
        modal.resolve(false);
        modal.hide();
        setIsFold(false);
      }}
      width={500}
      wrapClassName="xlbDialog"
      confirmLoading={loading}
    >
      <XlbBasicForm
        form={form}
        labelCol={{ span: 8 }}
        style={{ paddingTop: '10px' }}
      >
        <div className={style.box}>
          <p className={style.title}>
            {currentTab === 'StorePrice' ? '修改门店' : '修改客户'}
          </p>
          {currentTab === 'CustumerPrice' ? (
            <XlbBasicForm.Item
              name="revise_cus_names"
              style={{ display: 'inline-block', marginLeft: '32px' }}
              label="修改客户"
              colon={false}
            >
              <XlbInputDialog
                dialogParams={{
                  type: 'wholesaler',
                  isMultiple: true,
                  data: {
                    status: 1,
                  },
                }}
                onChange={(val: any, list: any) => {
                  if (!val) {
                    form.setFieldsValue({
                      revise_cus_ids: undefined,
                      revise_cus_names: undefined,
                    });
                    return;
                  }
                  form.setFieldsValue({
                    revise_cus_ids: val,
                    revise_cus_names: list.map((i: any) => i.name).join(','),
                  });
                }}
                fieldNames={{
                  idKey: 'id',
                  nameKey: 'name',
                }}
                width={180}
              />
            </XlbBasicForm.Item>
          ) : null}
          {enable_organization && (
            <XlbBasicForm.Item
              name="org_id"
              style={{ display: 'inline-block', margin: '0 10px 0 32px' }}
              label="组织"
              colon={false}
            >
              {/* orgList */}
              <XlbSelect
                style={{ width: 180, paddingTop: 2, paddingBottom: 2 }}
                size="small"
                onChange={(value) => Valuechange('org_id', value)}
                placeholder="请选择"
              >
                {orgList.map((item) => (
                  <XlbSelect.Option key={item.value} value={item.value}>
                    {item.label}
                  </XlbSelect.Option>
                ))}
              </XlbSelect>
            </XlbBasicForm.Item>
          )}
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <XlbBasicForm.Item
              name="revise_store_ids"
              style={{ display: 'inline-block', margin: '0 10px 0 32px' }}
              label="修改门店"
              colon={false}
            >
              <XlbInputDialog
                dialogParams={{
                  type: 'store',
                  isMultiple: true,
                  data: {
                    status: 1,
                  },
                }}
                fieldNames={{
                  idKey: 'id',
                  nameKey: 'store_name',
                }}
                onChange={(val: any, list: any) => {
                  if (!val) {
                    form.setFieldsValue({
                      revise_store_ids: undefined,
                      revise_store_names: undefined,
                    });
                    return;
                  }
                  form.setFieldsValue({
                    revise_store_ids: val,
                    revise_store_names: list
                      .map((i: any) => i.store_name)
                      .join(','),
                  });
                }}
                width={180}
              />
            </XlbBasicForm.Item>
            <XlbButton
              size="small"
              onClick={() => importStores()}
              style={{ marginLeft: -24 }}
              type='text'
            >
              导入
            </XlbButton>
          </div>
        </div>
        <div className={style.box} style={{ marginBottom: '10px',paddingLeft:14 }}>
          <p className={style.title}>选择商品</p>
          <XlbBasicForm.Item name="radioValue">
            <XlbRadio.Group>
              <XlbRadio value={1}>全部商品</XlbRadio>
              <div
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  margin: '10px 0 ',
                }}
              >
                <XlbRadio value={2}>商品类别</XlbRadio>
                <XlbBasicForm.Item name="item_category_names" noStyle>
                  <XlbInputDialog
                    treeModalConfig={{
                      // @ts-ignore
                      title: '选择商品分类', // 标题
                      topLevelTreeCheckable: true,
                      url: '/erp/hxl.erp.category.find', // 请求地址
                      dataType: 'lists',
                      checkable: true, // 是否多选
                      primaryKey: 'id',
                      data: {
                        enabled: true,
                      },
                    }}
                    onChange={(val: any, list: any) => {
                      if (!val) {
                        form.setFieldsValue({
                          item_category_ids: undefined,
                          item_category_names: undefined,
                        });
                        return;
                      }
                      form.setFieldsValue({
                        item_category_ids: val,
                        item_category_names: list
                          .map((i: any) => i.name)
                          .join(','),
                      });
                    }}
                    width={180}
                  />
                </XlbBasicForm.Item>
              </div>
              <div style={{ display: 'flex', alignItems: 'center' }}>
                <XlbRadio value={3} style={{ flex: 1 }}>
                  商品档案
                </XlbRadio>
                <XlbBasicForm.Item name="item_ids" noStyle>
                  <XlbInputDialog
                    dialogParams={{
                      type: 'goods',
                      isMultiple: true,
                      primaryKey: 'id',
                      dataType: 'lists',
                      data: {
                        enabled: true,
                      },
                    }}
                    onChange={(val: any, list: any) => {
                      if (!val) {
                        form.setFieldsValue({
                          item_ids: undefined,
                          item_names: undefined,
                        });
                        return;
                      }
                      form.setFieldsValue({
                        item_ids: val,
                        item_names: list.map((i: any) => i.name).join(','),
                      });
                    }}
                    width={180}
                  />
                </XlbBasicForm.Item>
                <XlbButton
                  size="small"
                  onClick={() => importShort()}
                  style={{ marginLeft: 8 }}
                  type='text'
                >
                  导入
                </XlbButton>
              </div>
            </XlbRadio.Group>
          </XlbBasicForm.Item>
        </div>
        <div className={style.box}>
          <p className={style.title}>修改数据</p>
          <XlbBasicForm.Item
            name={'unit_type'}
            label="单位"
            style={{ marginLeft: 50 }}
            colon={false}
            initialValue={'WHOLESALE'}
          >
            <XlbSelect
              style={{ width: 180, paddingTop: 2, paddingBottom: 2 }}
              size="small"
              onChange={(value) => Valuechange('unit_type', value)}
              placeholder="请选择"
            >
              <XlbSelect.Option value={'WHOLESALE'}>批发单位</XlbSelect.Option>
              <XlbSelect.Option value={'BASIC'}>基本单位</XlbSelect.Option>
            </XlbSelect>
          </XlbBasicForm.Item>
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <XlbBasicForm.Item
              name={'type'}
              label="批发价类型1"
              colon={false}
              // style={{ display: 'inline-block', margin: '0' }}
              initialValue=""
            >
              <XlbSelect
                style={{ width: 180, paddingTop: 0, paddingBottom: 0}}
                size="small"
                onChange={(value) => Valuechanges('type', value)}
                allowClear
                placeholder="请选择"
              >
                <XlbSelect.Option value={'NULL'}>　</XlbSelect.Option>
                <XlbSelect.Option value={'RATIO'}>按比例(%)</XlbSelect.Option>
                <XlbSelect.Option value={'MONEY'}>按金额</XlbSelect.Option>
                <XlbSelect.Option value={'FIXED_MONEY'}>
                  固定金额
                </XlbSelect.Option>
              </XlbSelect>
            </XlbBasicForm.Item>
            {hasAuth(['批发价/批发价', '查询']) ? (
              <>
                {' 数值：'}
                <XlbBasicForm.Item name={'value'}>
                  <XlbInputNumber
                    disabled={
                      !type ||
                      type == 'NULL' ||
                      !hasAuth(['批发价/批发价', '编辑'])
                    }
                    defaultValue={0}
                    controls={false}
                    min={0}
                    size="small"
                    step={0.0001}
                    // width={60}
                    // stringMode
                    style={{
                      width: 60,
                      borderRadius: 5,
                      paddingBottom: 2,
                      paddingTop: 2,
                    }}
                    onChange={(value) => Valuechange('value', value)}
                  />
                </XlbBasicForm.Item>
              </>
            ) : (
              <span>数值：**** </span>
            )}
          </div>
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <XlbBasicForm.Item
              name={'type2'}
              label="批发价类型2"
              colon={false}
              // style={{ display: 'inline-block', margin: '0' }}
              initialValue=""
            >
              <XlbSelect
                style={{ width: 180, paddingTop: 0, paddingBottom: 0 }}
                size="small"
                onChange={(value) => Valuechanges('type2', value)}
                allowClear
                placeholder="请选择"
              >
                <XlbSelect.Option value={'NULL'}>　</XlbSelect.Option>
                <XlbSelect.Option value={'RATIO'}>按比例(%)</XlbSelect.Option>
                <XlbSelect.Option value={'MONEY'}>按金额</XlbSelect.Option>
                <XlbSelect.Option value={'FIXED_MONEY'}>
                  固定金额
                </XlbSelect.Option>
              </XlbSelect>
            </XlbBasicForm.Item>
            {hasAuth(['批发价/批发价', '查询']) ? (
              <>
                {' 数值：'}
                <XlbBasicForm.Item name={'value2'}>
                  <XlbInputNumber
                    disabled={
                      !type2 ||
                      type2 == 'NULL' ||
                      !hasAuth(['批发价/批发价', '编辑'])
                    }
                    defaultValue={0}
                    controls={false}
                    min={0}
                    size="small"
                    step={0.0001}
                    // stringMode
                    style={{
                      width: 60,
                      borderRadius: 5,
                      paddingBottom: 2,
                      paddingTop: 2,
                    }}
                    onChange={(value) => Valuechange('value2', value)}
                  />
                </XlbBasicForm.Item>
              </>
            ) : (
              <span>数值：**** </span>
            )}
          </div>
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <XlbBasicForm.Item
              name={'type3'}
              label="批发价类型3"
              colon={false}
              // style={{ display: 'inline-block', margin: '0' }}
              initialValue=""
            >
              <XlbSelect
                style={{ width: 180, paddingTop: 0, paddingBottom: 0 }}
                size="small"
                onChange={(value) => Valuechanges('type3', value)}
                allowClear
                placeholder="请选择"
              >
                <XlbSelect.Option value={'NULL'}>　</XlbSelect.Option>
                <XlbSelect.Option value={'RATIO'}>按比例(%)</XlbSelect.Option>
                <XlbSelect.Option value={'MONEY'}>按金额</XlbSelect.Option>
                <XlbSelect.Option value={'FIXED_MONEY'}>
                  固定金额
                </XlbSelect.Option>
              </XlbSelect>
            </XlbBasicForm.Item>
            {hasAuth(['批发价/批发价', '查询']) ? (
              <>
                {' 数值：'}
                <XlbBasicForm.Item name={'value3'}>
                  <XlbInputNumber
                    disabled={
                      !type3 ||
                      type3 == 'NULL' ||
                      !hasAuth(['批发价/批发价', '编辑'])
                    }
                    defaultValue={0}
                    controls={false}
                    min={0}
                    size="small"
                    step={0.0001}
                    // stringMode
                    style={{
                      width: 60,
                      borderRadius: 5,
                      paddingBottom: 2,
                      paddingTop: 2,
                    }}
                    onChange={(value) => Valuechange('value3', value)}
                  />
                </XlbBasicForm.Item>
              </>
            ) : (
              <span>数值：**** </span>
            )}
          </div>
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <XlbBasicForm.Item
              name={'type4'}
              label="批发价类型4"
              colon={false}
              style={{ display: 'inline-block', margin: '0' }}
              initialValue=""
            >
              <XlbSelect
                style={{ width: 180, paddingTop: 0, paddingBottom: 0 }}
                size="small"
                onChange={(value) => Valuechanges('type4', value)}
                allowClear
                placeholder="请选择"
              >
                <XlbSelect.Option value={'NULL'}>　</XlbSelect.Option>
                <XlbSelect.Option value={'RATIO'}>按比例(%)</XlbSelect.Option>
                <XlbSelect.Option value={'MONEY'}>按金额</XlbSelect.Option>
                <XlbSelect.Option value={'FIXED_MONEY'}>
                  固定金额
                </XlbSelect.Option>
              </XlbSelect>
            </XlbBasicForm.Item>
            {hasAuth(['批发价/批发价', '查询']) ? (
              <>
                {' 数值：'}
                <XlbBasicForm.Item
                  name={'value4'}
                  style={{ display: 'inline-block', marginRight: 0 }}
                >
                  <XlbInputNumber
                    disabled={
                      !type4 ||
                      type4 == 'NULL' ||
                      !hasAuth(['批发价/批发价', '编辑'])
                    }
                    defaultValue={0}
                    controls={false}
                    min={0}
                    size="small"
                    step={0.0001}
                    // stringMode
                    style={{
                      width: 60,
                      borderRadius: 5,
                      paddingBottom: 2,
                      paddingTop: 2,
                    }}
                    onChange={(value) => Valuechange('value4', value)}
                  />
                </XlbBasicForm.Item>
              </>
            ) : (
              <span>数值：**** </span>
            )}
          </div>
        </div>
      </XlbBasicForm>
    </XlbModal>
  );
};
export default BatchChange;

.pagePurchaseContainer {
  .formItemJudgeFilter {
    :global
      .ant-form-item-row
      .ant-form-item-control-input
      .ant-form-item-control-input-content {
      display: flex;
      align-items: center;
      flex-wrap: wrap;
    }
  }
  .formItemWord {
    margin: 0 4px;
  }

  .fieldset {
    border: 1px solid #d8d8d8;
    width: 246px;
    border-radius: 8px;
    padding: 10px 10px 0;
    margin: 16px 0 0 16px;
    line-height: 1;
  }
  .legend {
    margin: 0 auto;
    width: 50%;
    text-align: center;
    font-size: 14px;
    border: none;
    color: #000000;
  }
  :global .ant-form-item.custom-form-item {
    margin-bottom: 4px !important;
    .ant-form-item-label {
      width: auto;
    }
  }
}

.form_container_wrapper_storeItem {
  width: 100%;
  .form_container_transferDocument {
    width: 100%;
    max-width: 1700px;
    :global .ant-form-item-label {
      min-width: 120px !important;
    }
    :global {
      .xlb-ant-form-inline {
        display: unset !important;
      }
      .xlb-ant-form-item-label {
        flex-shrink: 0 !important;
      }
      .xlb-input-dialog {
        width: 100% !important;
      }
      .-item-explain-error {
        color: #f53f3f !important;
      }
    }
  }
  .demolition {
    display: inline-block;
    width: 14px;
    height: 14px;
    font-size: 12px;
    background-color: #f53f3f;
    text-align: center;
    color: aliceblue;
    border-radius: 2px;
    line-height: 14px;
  }
}
.amount_box {
  position: absolute;
  top: 50px;
  left: 200px;
  color: @color_danger;
  span {
    font-size: 16px;
    margin-left: 20px;
  }
}
@media (max-width: 991.98px) {
  .form_container_transferDocument {
    width: 845px; /* 小于 992px 时保持最小宽度，触发滚动条 */
    min-width: 845px;
  }
}

import {
  SearchFormType,
  XlbBasicForm,
  XlbBlueBar,
  XlbButton,
  XlbForm,
  XlbIcon,
  XlbMessage,
  XlbPageContainer,
  XlbTable,
} from '@xlb/components';
import { LStorage } from '@xlb/utils';
import { debounce } from 'lodash-es';
import { useState } from 'react';
import { detailColumns, detailForm } from '../data';
import Api from './../server';
const { ToolBtn } = XlbPageContainer;
const Index = (props: { data: any; close: any }) => {
  const [form] = XlbBasicForm.useForm();
  const { data, close } = props;
  const [dataSource, setData] = useState<any[]>([]);
  const [itemForm, setFormArr] = useState<SearchFormType[]>(detailForm);
  const { org_id, category_id, operator } = data;
  const [loading, setLoading] = useState(false);
  const save = () => {
    const [
      {
        id,
        category_id,
        org_id,
        limit_value: collective_purchase_limit_num,
        collective_purchase_down_limit_num,
        up_limit_num,
      },
      {
        limit_value: ground_purchase_limit_num,
        ground_purchase_down_limit_num,
      },
      { limit_value: shop_purchase_limit_num, shop_purchase_down_limit_num },
    ] = dataSource;
    const limit_num = form.getFieldValue('limit_num') || up_limit_num; // 上限最大值
    const getCurrentLimitValue = (cur: any, dowm: any) => {
      if (typeof cur !== 'number') {
        return dowm || 0;
      } else {
        return cur;
      }
    };

    if (typeof limit_num === 'number') {
      const numTotal =
        getCurrentLimitValue(
          collective_purchase_limit_num,
          collective_purchase_down_limit_num,
        ) +
        getCurrentLimitValue(
          ground_purchase_limit_num,
          ground_purchase_down_limit_num,
        ) +
        getCurrentLimitValue(
          shop_purchase_limit_num,
          shop_purchase_down_limit_num,
        );
      if (numTotal > limit_num) {
        XlbMessage.error(
          '采购类型信息 【SKU上限或其最小值】合计不能超过' + limit_num,
        );
        return;
      }
    }

    // {
    //     "limit_num": 100,
    //     "collective_purchase_limit_num": 10,
    //     "ground_purchase_limit_num": 8,
    //     "shop_purchase_limit_num": 7
    // }
    setLoading(true);
    Api.update({
      id,
      category_id,
      org_id,
      company_id: LStorage.get('userInfo').company_id,
      collective_purchase_limit_num,
      ground_purchase_limit_num,
      shop_purchase_limit_num,
    })
      .then((res) => {
        console.log(res);
        if (res.code === 0) {
          XlbMessage.success('保存成功');
          close();
        }
      })
      .finally(() => {
        setLoading(false);
      });
  };

  return (
    <section style={{}}>
      <XlbPageContainer
        url={'/erp-mdm/hxl.erp.org.skulimit.read'}
        immediatePost={true}
        tableColumn={detailColumns}
        prevPost={() => {
          setLoading(true);
          return {
            org_id,
            category_id,
            company_id: LStorage.get('userInfo').company_id,
            operator,
            page_number: undefined,
            page_size: undefined,
          };
        }}
        afterPost={(data) => {
          setLoading(false);
          form.setFieldsValue(data || {});
          itemForm.forEach((_) => {
            if (_.name === 'limit_num') {
              // 设置上下限
              _.max = data.up_limit_num || undefined;
              _.min = data.down_limit_num || undefined;
            }
          });
          setFormArr([...itemForm]);
          const jicai = {
            type: '集采品',
            limit_value: data.collective_purchase_limit_num,
            exist_sku_value: data.collective_purchase_exist_sku_num,
            ...data,
          }; // 集采品
          // ! 此处id都是一样的 👆 👇
          const dicai = {
            type: '地采品',
            limit_value: data.ground_purchase_limit_num,
            exist_sku_value: data.ground_purchase_exist_sku_num,
            ...data,
          }; // 地采品
          const diancai = {
            type: '店采品',
            limit_value: data.shop_purchase_limit_num,
            exist_sku_value: data.shop_purchase_exist_sku_num,
            ...data,
          }; // 店采品
          setData([jicai, dicai, diancai]);
          return [jicai, dicai, diancai];
        }}
      >
        <ToolBtn>
          {() => {
            return (
              <XlbButton.Group>
                <XlbButton
                  type="primary"
                  label="保存"
                  loading={loading}
                  onClick={save}
                  icon={<XlbIcon name="baocun" />}
                />
                <XlbButton
                  type="primary"
                  label="返回"
                  loading={loading}
                  onClick={() => close()}
                  icon={<XlbIcon name="fanhui" />}
                />
              </XlbButton.Group>
            );
          }}
        </ToolBtn>

        <XlbBlueBar hasMargin title="商品分类信息"></XlbBlueBar>
        <XlbForm
          isHideDate
          onChange={debounce((e) => {
            if (e.target.id === 'limit_num') {
              const [{ up_limit_num, down_limit_num }] = dataSource;
              if (
                up_limit_num &&
                down_limit_num &&
                Number(e.target.value) < down_limit_num
              ) {
                //
              }
            }
            console.log(e.target.id === 'limit_num', e.target.value);
          }, 200)}
          form={form}
          formList={itemForm}
        ></XlbForm>
        <XlbBlueBar hasMargin title="采购类型信息"></XlbBlueBar>
        <XlbTable
          loading={loading}
          selectMode="single"
          keepDataSource={true}
          dataSource={dataSource}
          columns={detailColumns}
        ></XlbTable>
      </XlbPageContainer>
    </section>
  );
};
export default Index;
